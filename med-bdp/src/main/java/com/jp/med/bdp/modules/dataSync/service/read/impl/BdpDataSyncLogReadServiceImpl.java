package com.jp.med.bdp.modules.dataSync.service.read.impl;

import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageHelper;
import com.jp.med.bdp.modules.config.dto.BdpInfoCfgDto;
import com.jp.med.bdp.modules.config.mapper.read.BdpInfoCfgReadMapper;
import com.jp.med.bdp.modules.config.vo.BdpInfoCfgVo;
import com.jp.med.bdp.modules.dataSync.dto.BdpFieldsMapping;
import com.jp.med.bdp.modules.dataSync.dto.DataSyncCfgDto;
import com.jp.med.bdp.modules.dataSync.dto.DataSyncDto;
import com.jp.med.bdp.modules.dataSync.dto.dataSyncCfg.Connection;
import com.jp.med.bdp.modules.dataSync.dto.dataSyncCfg.Content;
import com.jp.med.bdp.modules.dataSync.dto.dataSyncCfg.Operator;
import com.jp.med.bdp.modules.dataSync.dto.dataSyncCfg.Parameter;
import com.jp.med.bdp.modules.util.CiphertextUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.bdp.modules.dataSync.mapper.read.BdpDataSyncLogReadMapper;
import com.jp.med.bdp.modules.dataSync.dto.BdpDataSyncLogDto;
import com.jp.med.bdp.modules.dataSync.vo.BdpDataSyncLogVo;
import com.jp.med.bdp.modules.dataSync.service.read.BdpDataSyncLogReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class BdpDataSyncLogReadServiceImpl extends ServiceImpl<BdpDataSyncLogReadMapper, BdpDataSyncLogDto> implements BdpDataSyncLogReadService {

    @Autowired
    private BdpDataSyncLogReadMapper bdpDataSyncLogReadMapper;


    @Override
    public List<BdpDataSyncLogVo> queryList(BdpDataSyncLogDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return bdpDataSyncLogReadMapper.queryList(dto);
    }



}
