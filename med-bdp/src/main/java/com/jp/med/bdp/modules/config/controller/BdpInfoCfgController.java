package com.jp.med.bdp.modules.config.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bdp.modules.config.dto.BdpInfoCfgDto;
import com.jp.med.bdp.modules.config.service.read.BdpInfoCfgReadService;
import com.jp.med.bdp.modules.config.service.write.BdpInfoCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 数据抽取配置
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 11:11:03
 */
@Api(value = "数据抽取配置", tags = "数据抽取配置")
@RestController
@RequestMapping("bdpInfoCfg")
public class BdpInfoCfgController {

    @Autowired
    private BdpInfoCfgReadService bdpInfoCfgReadService;

    @Autowired
    private BdpInfoCfgWriteService bdpInfoCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询数据抽取配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BdpInfoCfgDto dto){
        return CommonResult.success(bdpInfoCfgReadService.queryList(dto));
    }

    @ApiOperation("获取key")
    @PostMapping("/getKey")
    public CommonResult<?> getKey(@RequestBody BdpInfoCfgDto dto){
        return CommonResult.success(bdpInfoCfgReadService.getKey(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增数据抽取配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BdpInfoCfgDto dto){
        bdpInfoCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改数据抽取配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BdpInfoCfgDto dto){
        bdpInfoCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除数据抽取配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BdpInfoCfgDto dto){
        bdpInfoCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

}
