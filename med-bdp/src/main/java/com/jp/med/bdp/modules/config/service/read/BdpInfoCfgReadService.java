package com.jp.med.bdp.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bdp.modules.config.dto.BdpInfoCfgDto;
import com.jp.med.bdp.modules.config.vo.BdpInfoCfgVo;

import java.util.List;

/**
 * 数据抽取配置
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 11:11:03
 */
public interface BdpInfoCfgReadService extends IService<BdpInfoCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BdpInfoCfgVo> queryList(BdpInfoCfgDto dto);

    /**
     * 获取key
     * @param dto
     * @return
     */
    String getKey(BdpInfoCfgDto dto);
}

