package com.jp.med.bdp.modules.medRcdQA.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * 病案首页问卷调查选项组配置
 * <AUTHOR>
 * @email -
 * @date 2024-03-15 10:23:01
 */
@Data
@TableName("bdp_mr_qs_group_cfg" )
public class BdpMrQsGroupCfgDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 选项组编码 */
    @TableField("opt_group_code")
    private String optGroupCode;

    /** 选项组名称 */
    @TableField("opt_group_name")
    private String optGroupName;

    /** 上级选项组编码 */
    @TableField("prnt_opt_group_code")
    private String prntOptGroupCode;

    /** 选项组类型(1:目录,2:选项) */
    @TableField("opt_type")
    private String optType;

    /** 选项 */
    @TableField("opts")
    private String opts;

    /** 备注 */
    @TableField("remarks")
    private String remarks;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 启用标志 */
    @TableField("active_flag")
    private String activeFlag;

    /** 选项数组 */
    @TableField(exist = false)
    private List<String> optsArr;
}
