package com.jp.med.bdp.modules.medRcdQA.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/26 22:17
 * @description:
 */
@Data
public class BdpMrQsTempDetail {

    /** id */
    private Integer id;

    /** 模板id */
    private Integer tmplId;

    /** 说明 */
    private String desc;

    /** 选项组编码 */
    private String optGroupCode;

    /** 步骤 */
    private Integer step;

    /** 跳到第几步(-1为结束) */
    private Integer toStep;

    /** 创建人 */
    private String crter;

    /** 创建时间 */
    private String createTime;

    /** 医疗机构id */
    private String hospitalId;

    /** 启用标志 */
    private String activeFlag;

    /** 页面选项id */
    private String itemId;

    /** 当前锚点 */
    private String href;

    /** 跳转的锚点 */
    private String toHref;

    /** 跳转的步骤描述 */
    private String toHrefDesc;

    /** 跳转的选项 */
    private String skipOpt;

    /** 跳转的选项 */
    private List<String> skipOptArr;

    /** 选项组 */
    private List<String> checkbox;

    /** 是否跳转至结束 */
    private Boolean skipToEnd;

    /** 是否跳转至结束 1:是 0：否 */
    private String skipToEndStr;

    /** 逻辑判断的id */
    private String logicStepId;

    /** 逻辑步骤选择的选项 */
    private String logicCheckOptGroupCode;

    /** 值来源类型 1：选项 2：输入 */
    private String sourceType;

    /** 选择的值 */
    private String checkOptGroupCode;
}
