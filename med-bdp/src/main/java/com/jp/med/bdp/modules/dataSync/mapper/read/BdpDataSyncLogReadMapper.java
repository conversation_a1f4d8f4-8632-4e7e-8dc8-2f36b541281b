package com.jp.med.bdp.modules.dataSync.mapper.read;

import com.jp.med.bdp.modules.dataSync.dto.BdpDataSyncLogDto;
import com.jp.med.bdp.modules.dataSync.vo.BdpDataSyncLogVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 数据抽取日志
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 19:31:32
 */
@Mapper
public interface BdpDataSyncLogReadMapper extends BaseMapper<BdpDataSyncLogDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BdpDataSyncLogVo> queryList(BdpDataSyncLogDto dto);
}
