package com.jp.med.bdp.modules.dataSync.dto;

import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName DataSyncDto
 * @Description 数据抽取类
 * <AUTHOR>
 * @Date 2024/3/7 10:44
 * @Version 1.0
 */
@Getter
@Setter
public class DataSyncDto extends CommonQueryDto {

    /** 源 */
    private Integer sourceDb;
    /** 目标 */
    private Integer targetDb;
    /** 源数据表 */
    private String sourceTableName;
    /** 目标数据表 */
    private String targetTableName;
    /** 源库url */
    private String sourceUrl;
    /** 目标库url */
    private String targetUrl;
    /** 源表字段 */
    private String sourceColumn;
    /** 目标表字段 */
    private String targetColumn;
    /** 通道数(控制抽取速度) */
    private String channel;
    /** 错误数量限制 */
    private String errorRecord;
    /** 查询条件 */
    private String where;
    /** 数据库映射 */
    private List<BdpFieldsMapping> fields;


}
