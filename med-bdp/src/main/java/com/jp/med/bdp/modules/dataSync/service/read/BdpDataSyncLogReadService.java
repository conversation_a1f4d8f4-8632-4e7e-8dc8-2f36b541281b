package com.jp.med.bdp.modules.dataSync.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bdp.modules.dataSync.dto.BdpDataSyncLogDto;
import com.jp.med.bdp.modules.dataSync.dto.DataSyncDto;
import com.jp.med.bdp.modules.dataSync.vo.BdpDataSyncLogVo;

import java.util.List;

/**
 * 数据抽取日志
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 19:31:32
 */
public interface BdpDataSyncLogReadService extends IService<BdpDataSyncLogDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BdpDataSyncLogVo> queryList(BdpDataSyncLogDto dto);


}

