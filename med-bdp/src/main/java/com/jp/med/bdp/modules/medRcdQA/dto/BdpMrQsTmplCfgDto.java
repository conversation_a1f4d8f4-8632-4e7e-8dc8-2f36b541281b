package com.jp.med.bdp.modules.medRcdQA.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.bdp.modules.medRcdQA.entity.BdpMrQsTempDetail;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * 病案首页问卷调查模板配置
 * <AUTHOR>
 * @email -
 * @date 2024-03-17 21:38:25
 */
@Data
@TableName("bdp_mr_qs_tmpl_cfg" )
public class BdpMrQsTmplCfgDto extends CommonQueryDto {

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /** 详情id */
    @TableField("detail_id")
    private Integer detailId;

    /** 类别 */
    @TableField("caty")
    private String caty;

    /** 模板编码 */
    @TableField("tmpl_code")
    private String tmplCode;

    /** 模板名称 */
    @TableField("tmpl_name")
    private String tmplName;

    /** 备注 */
    @TableField("remarks")
    private String remarks;

    /** 取值口径 */
    @TableField("val_cail")
    private String valCail;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 启用标志 */
    @TableField("active_flag")
    private String activeFlag;

    /** 更新时间 */
    @TableField("update_time")
    private String updateTime;

    /** 主诊取值口径 */
    @TableField(exist = false)
    private String primaryCodeCail;

    /** 其他诊断取值口径 */
    @TableField(exist = false)
    private String otherCodeCail;

    /** 模板编码或名称 */
    @TableField(exist = false)
    private String tmpl;

    /** 详情 */
    @TableField(exist = false)
    private List<BdpMrQsTempDetail> details;

    /** ids */
    @TableField(exist = false)
    private List<Integer> tmplIds;

    /** 是否新增 false为修改 */
    @TableField(exist = false)
    private Boolean add;

}
