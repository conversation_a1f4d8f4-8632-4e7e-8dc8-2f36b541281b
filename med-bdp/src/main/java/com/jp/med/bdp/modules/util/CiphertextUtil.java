package com.jp.med.bdp.modules.util;

import com.jp.med.common.exception.AppException;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * @ClassName CiphertextUtil
 * @Description 密文工具
 * <AUTHOR>
 * @Date 2024/3/13 11:50
 * @Version 1.0
 */
@Slf4j
public class CiphertextUtil {

    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final String ENCODING = "UTF-8";
    private static final String SECRETKEY = "IV6PTHGOGPHHUZA1";
    private static final String INITIALIZATIONVECTOR = "rzJqa04WO4uEKNio";

    public static Map<String, String> createKey(){
        try {
            Map<String, String> keyMap = new HashMap<>();
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
            keyPairGenerator.initialize(2048);
            KeyPair keyPair = keyPairGenerator.genKeyPair();
            PrivateKey privateKey = keyPair.getPrivate();
            PublicKey publicKey = keyPair.getPublic();
            String privateKeyStr = Base64.getEncoder().encodeToString(privateKey.getEncoded());
            String publicKeyStr = Base64.getEncoder().encodeToString(publicKey.getEncoded());
            keyMap.put(publicKeyStr, privateKeyStr);
            return keyMap;
        }catch (Exception e){
            log.error(e.toString());
            throw new AppException("获取失败");
        }
    }

    public static String rsaDecrypt(String msg,String privateKeyStr){
        try {
            KeyFactory kf = KeyFactory.getInstance("RSA");
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKeyStr.getBytes()));
            PrivateKey privateKey = kf.generatePrivate(keySpec);
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] encryptedBytes = Base64.getDecoder().decode(msg);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        }catch (Exception e){
            log.error(e.toString());
            throw new AppException("获取失败");
        }
    }


    public static String aesEncrypt(String plainText){
        try {
            SecretKeySpec aesKey = new SecretKeySpec(SECRETKEY.getBytes(ENCODING), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(INITIALIZATIONVECTOR.getBytes(StandardCharsets.UTF_8));
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, aesKey, ivSpec);
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(ENCODING));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        }catch (Exception e){
            log.error(e.toString());
            throw new AppException("出现异常");
        }
    }

    public static String aesDecrypt(String encryptedBase64Text){
        try {
            Key aesKey = new SecretKeySpec(SECRETKEY.getBytes(ENCODING), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(INITIALIZATIONVECTOR.getBytes(ENCODING));
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, aesKey, ivSpec);
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedBase64Text);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, ENCODING);
        }catch (Exception e){
            log.error(e.toString());
            throw new AppException("出现异常");
        }
    }


}
