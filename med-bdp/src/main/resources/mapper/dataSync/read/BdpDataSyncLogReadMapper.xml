<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bdp.modules.dataSync.mapper.read.BdpDataSyncLogReadMapper">

    <select id="queryList" resultType="com.jp.med.bdp.modules.dataSync.vo.BdpDataSyncLogVo">
        select
            a.id as id,
            a.source as source,
            a.target as target,
            a.operator as operator,
            a.begntime as begntime,
            a.endtime as endtime,
            a.total as total,
            a.status as status,
            a.hospital_id as hospitalId,
            a.selc as selc
        from bdp_data_sync_log a
    </select>

</mapper>
