<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bdp.modules.medRcdQA.mapper.read.BdpMrQsTmplCfgReadMapper">

    <!-- 查询数据 -->
    <select id="queryList" resultType="com.jp.med.bdp.modules.medRcdQA.vo.BdpMrQsTmplCfgVo">
        select  a.id as id,
                a.caty as caty,
                a.tmpl_code as tmplCode,
                a.tmpl_name as tmplName,
                a.remarks as remarks,
                a.val_cail as valCail,
                a.crter as crter,
                a.create_time as createTime,
                a.hospital_id as hospitalId,
                a.active_flag as activeFlag
        from bdp_mr_qs_tmpl_cfg a
        where active_flag = '1'
        <if test="tmpl != null and tmpl != ''">
            and (a.tmpl_code like CONCAT('%', #{tmpl,jdbcType=VARCHAR}, '%') or
                 a.tmpl_name like CONCAT('%', #{tmpl,jdbcType=VARCHAR}, '%')
            )
        </if>
        <if test="tmplCode != null and tmplCode != ''">
            and a.tmpl_code = #{tmplCode,jdbcType=VARCHAR}
        </if>
        <if test="caty != null and caty != ''">
            and a.caty = #{caty,jdbcType=VARCHAR}
        </if>
        ORDER BY a.tmpl_code
    </select>

    <!-- 通过id查询详情 -->
    <select id="queryDetailById"
            resultType="com.jp.med.bdp.modules.medRcdQA.entity.BdpMrQsTempDetail">
        SELECT  a.id as id,
                a.tmpl_id as tmplId,
                a.desc as desc,
                a.opt_group_code as optGroupCode,
                a.step as step,
                a.to_step as toStep,
                a.item_id as itemId,
                a.href as href,
                a.to_href as toHref,
                a.to_href_desc as toHrefDesc,
                a.crter as crter,
                a.create_time as createTime,
                a.hospital_id as hospitalId,
                a.active_flag as activeFlag,
                a.skip_opt as skipOpt,
                a.skip_to_end_str as skipToEndStr,
                a.logic_step_id as logicStepId,
                a.logic_check_opt_group_code as logicCheckOptGroupCode,
                a.source_type as sourceType
        FROM bdp_mr_qs_tmpl_detail_cfg a
        <where>
            <if test="tmplIds != null and tmplIds.size() > 0">
                AND a.tmpl_id IN
                <foreach collection="tmplIds" item="tmplId" open="(" close=")" separator=",">
                    #{tmplId}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 获取所有类型 -->
    <select id="queryTypes" resultType="java.lang.String">
        SELECT caty FROM bdp_mr_qs_tmpl_cfg WHERE active_flag = '1' GROUP BY caty
    </select>

</mapper>
