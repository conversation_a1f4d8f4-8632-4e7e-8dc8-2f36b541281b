<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bdp.modules.medRcdQA.mapper.write.BdpMrQsTmplCfgWriteMapper">

    <!-- 写入模板详情 -->
    <insert id="insertDetail">
        INSERT INTO bdp_mr_qs_tmpl_detail_cfg(
            tmpl_id,
            "desc",
            opt_group_code,
            step,
            to_step,
            item_id,
            href,
            to_href,
            to_href_desc,
            crter,
            create_time,
            hospital_id,
            active_flag,
            skip_opt,
            skip_to_end_str,
            logic_step_id,
            logic_check_opt_group_code,
            source_type
        )
        VALUES(
               #{tmplId,jdbcType=INTEGER},
               #{desc,jdbcType=VARCHAR},
               #{optGroupCode,jdbcType=VARCHAR},
               #{step,jdbcType=INTEGER},
               #{toStep,jdbcType=INTEGER},
               #{itemId,jdbcType=VARCHAR},
               #{href,jdbcType=VARCHAR},
               #{toHref,jdbcType=VARCHAR},
               #{toHrefDesc,jdbcType=VARCHAR},
               #{crter,jdbcType=VARCHAR},
               to_char(now(), 'YYYY-MM-DD HH24:MI:SS'),
               #{hospitalId,jdbcType=VARCHAR},
               #{activeFlag,jdbcType=VARCHAR},
               #{skipOpt,jdbcType=VARCHAR},
               #{skipToEndStr,jdbcType=VARCHAR},
               #{logicStepId,jdbcType=VARCHAR},
               #{logicCheckOptGroupCode,jdbcType=VARCHAR},
               #{sourceType,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 删除详情 -->
    <delete id="deleteDetail">
        DELETE FROM bdp_mr_qs_tmpl_detail_cfg
        WHERE tmpl_id = (
            SELECT id
            FROM bdp_mr_qs_tmpl_cfg
            WHERE tmpl_code = #{tmplCode,jdbcType=VARCHAR}
        )
    </delete>

</mapper>
