import requests
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import urllib3

# Disable InsecureRequestWarning
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# --- 配置 ---
URL = 'https://hrp.zjxrmyy.cn:18090/back/core/sys/menu/querySysMenuTree'
HEADERS = {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Authorization': 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhZG1pbiIsImNyZWF0ZWQiOjE3NDQyODM0MDQxOTMsInJvbGVzIjpbeyJhdXRob3JpdHkiOiJIUk0tQURNSU4ifSx7ImF1dGhvcml0eSI6IkFNUy1BRE1JTiJ9LHsiYXV0aG9yaXR5IjoiSFJNLUFETUlOIn0seyJhdXRob3JpdHkiOiJIUk0tREVQVC1BRE1JTiJ9LHsiYXV0aG9yaXR5IjoiQk1TLUFETUlOIn0seyJhdXRob3JpdHkiOiJQTVMtQURNSU4ifSx7ImF1dGhvcml0eSI6IkFNUy1BRE1JTiJ9LHsiYXV0aG9yaXR5IjoiQU1TLVhYSy1BRE1JTiJ9LHsiYXV0aG9yaXR5IjoiRUNTLUFETUlOIn1dLCJleHAiOjE3NDQzMjY5NDksInVzZXJJZCI6MSwidXNlcm5hbWUiOiJhZG1pbiJ9.Jaa7HvTGxKudZVC4lV52CCrWwigMmOaVsfanFEP-n7jh82WkA0Pt2dm3vsFC2cMEnE8TlZE5GAS-j6mMY1U18w', # 注意：这个Token可能会过期！
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json',
    'Origin': 'https://hrp.zjxrmyy.cn:18090',
    'Pragma': 'no-cache',
    'Referer': 'https://hrp.zjxrmyy.cn:18090/hrp/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
    'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
}
DATA = json.dumps({"sysType":"0","routePath":"/sys/needOrBug"}) # 将原始字符串数据转换为JSON字符串

TOTAL_REQUESTS = 100000  # 总请求数
CONCURRENCY = 10      # 并发数
TIMEOUT_SECONDS = 30  # 请求超时时间（秒）

# --- 全局统计 ---
success_count = 0
failure_count = 0
total_time_spent = 0.0

# --- 请求函数 ---
def send_request(request_id):
    """发送单个请求并返回结果"""
    global success_count, failure_count, total_time_spent
    start_time = time.time()
    try:
        # print(f"发送请求 {request_id}...") # 如果需要详细日志可以取消注释
        response = requests.post(URL, headers=HEADERS, data=DATA, timeout=TIMEOUT_SECONDS, verify=False) # 添加 verify=False 忽略 SSL 证书验证
        response.raise_for_status() # 如果状态码不是 2xx，则抛出异常
        end_time = time.time()
        time_spent = end_time - start_time
        # print(f"请求 {request_id} 成功，耗时: {time_spent:.4f} 秒") # 如果需要详细日志可以取消注释
        success_count += 1
        total_time_spent += time_spent
        return {"id": request_id, "status": "success", "time_spent": time_spent}
    except requests.exceptions.RequestException as e:
        end_time = time.time()
        time_spent = end_time - start_time
        print(f"请求 {request_id} 失败: {e}")
        failure_count += 1
        total_time_spent += time_spent # 失败的请求也计入总时间
        return {"id": request_id, "status": "failure", "error": str(e), "time_spent": time_spent}

# --- 主逻辑 ---
if __name__ == "__main__":
    print(f"🚀 开始压力测试...")
    print(f"目标 URL: {URL}")
    print(f"总请求数: {TOTAL_REQUESTS}")
    print(f"并发数: {CONCURRENCY}")
    print("-" * 30)

    start_overall_time = time.time()
    futures = []
    with ThreadPoolExecutor(max_workers=CONCURRENCY) as executor:
        for i in range(TOTAL_REQUESTS):
            futures.append(executor.submit(send_request, i + 1))

        # 这里可以添加一个进度条，但为了简单起见，暂时省略
        # for future in as_completed(futures):
        #     result = future.result() # 获取结果，也可以在这里处理结果

    # 等待所有任务完成 (as_completed 已经隐式等待了)
    # 或者使用 wait:
    # from concurrent.futures import wait
    # wait(futures)

    end_overall_time = time.time()
    overall_duration = end_overall_time - start_overall_time

    print("\n" + "-" * 30)
    print("📊 测试结果:")
    print(f"总耗时: {overall_duration:.4f} 秒")
    print(f"成功请求数: {success_count}")
    print(f"失败请求数: {failure_count}")
    if success_count > 0:
      average_time = total_time_spent / TOTAL_REQUESTS # 计算包含失败请求在内的平均时间
      print(f"平均响应时间 (所有请求): {average_time:.4f} 秒")
      # 如果只想计算成功请求的平均时间：
      # average_success_time = sum(f.result()['time_spent'] for f in futures if f.result()['status'] == 'success') / success_count
      # print(f"平均响应时间 (仅成功请求): {average_success_time:.4f} 秒")
    requests_per_second = TOTAL_REQUESTS / overall_duration if overall_duration > 0 else 0
    print(f"每秒请求数 (RPS): {requests_per_second:.2f}")
    print("✅ 测试完成！")
