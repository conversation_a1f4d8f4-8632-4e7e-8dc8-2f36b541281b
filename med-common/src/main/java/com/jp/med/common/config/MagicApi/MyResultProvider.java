package com.jp.med.common.config.MagicApi;

import org.ssssssss.magicapi.core.context.RequestEntity;
import org.ssssssss.magicapi.core.interceptor.ResultProvider;
import org.ssssssss.magicapi.core.model.JsonBean;

//@Component
public class MyResultProvider implements ResultProvider {

    @Override
    public Object buildResult(RequestEntity requestEntity, int code, String message, Object data) {
        long timestamp = System.currentTimeMillis();
        return new JsonBean<>(code, message, data, (int) (timestamp - requestEntity.getRequestTime()));
    }

    @Override
    public Object buildException(RequestEntity requestEntity, Throwable throwable) {
        return buildResult(requestEntity, 500, "系统内部出现错误");
    }
}