package com.jp.med.common.entity.user;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * @BelongsProject: mednback
 * @BelongsPackage: com.jp.med.common.entity.user
 * @Author: artist
 * @CreateTime: 2023-04-27
 * @Description: 预算管理权限
 * @Version: 1.0
 */
@Getter
@Setter
public class BmsUser {

    /** 预算管理系统组织架构ID */
    private String bmsOrgId;
    /**  预算管理系统组织类型 */
    private String deptType;
    /** 科室映射关系 */
    private Map<String,String> mapping = new HashMap<>();

}
