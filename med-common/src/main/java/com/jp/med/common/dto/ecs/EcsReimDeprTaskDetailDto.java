package com.jp.med.common.dto.ecs;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 折旧任务明细
 * <AUTHOR>
 * @email -
 * @date 2025-02-19 15:12:46
 */
@Data
@TableName("ecs_reim_depr_task_detail" )
public class EcsReimDeprTaskDetailDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** $column.comments */
    @TableField("task_id")
    private Integer taskId;

    /** 科室代码 */
    @TableField("dept_code")
    private String deptCode;

    /** 科室名称 */
    @TableField("dept_name")
    private String deptName;

    /** 资产代码 */
    @TableField("asset_type_code")
    private String assetTypeCode;

    /** 资产名称 */
    @TableField("asset_type_name")
    private String assetTypeName;

    /** 资金来源代码 */
    @TableField("source_code")
    private String sourceCode;

    /** 资金来源名称 */
    @TableField("source_name")
    private String sourceName;

    /** 开始使用年度 */
    @TableField("open_year")
    private String openYear;

    /** 开始使用日期 */
    @TableField("open_date")
    private String openDate;

    /** 金额 */
    @TableField("amt")
    private BigDecimal amt;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

}
