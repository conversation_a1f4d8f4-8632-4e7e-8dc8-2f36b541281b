package com.jp.med.common.vo;

import com.jp.med.common.entity.audit.AuditDetail;
import lombok.Data;

import java.util.List;

/**
 * 审核配置表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-27 17:25:49
 */
@Data
public class AuditCfgVo {

    /**
     * id
     */
    private Long id;

    /**
     * 流程名称
     */
    private String flowName;

    /**
     * 描述
     */
    private String desc;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 医疗机构id
     */
    private String hospitalId;

    /**
     * 有效标志
     */
    private String activeFlag;

    /**
     * 系统ID
     */
    private Long systemId;

    /**
     * 流程编码
     */
    private String flowCode;

    /**
     * 审核详情
     */
    private List<AuditDetail> details;

}
