package com.jp.med.common.dto.app;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

/**
 * APP消息
 * <AUTHOR>
 * @email -
 * @date 2023-12-19 17:13:58
 */
@Data
@TableName("app_msg" )
public class AppMsgDto extends CommonQueryDto {

    /** id */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** 消息类型编码 */
    @TableField("msg_type_code")
    private String msgTypeCode;

    /** 标题 */
    @TableField("title")
    private String title;

    /** 内容 */
    @TableField("content")
    private String content;

    /** 数据 */
    @TableField("payload")
    private String payload;

    /** 是否已读 */
    @TableField("read")
    private String read;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 接收人 */
    @TableField("recer")
    private String recer;

    /** 接收时间 */
    @TableField("recer_time")
    private String recerTime;

    /** 状态，
     * AUDIT时：1：审核成功 0：审核失败
     * */
    @TableField("stas")
    private String stas;

}
