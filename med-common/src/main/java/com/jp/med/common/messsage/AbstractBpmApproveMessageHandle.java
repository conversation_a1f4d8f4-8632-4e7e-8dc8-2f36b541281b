package com.jp.med.common.messsage;

import cn.hutool.core.util.StrUtil;
import com.jp.med.common.dto.bpm.BpmProcessInstanceStatus;
import com.jp.med.common.dto.bpm.setBpmMessageLogHandleStatusDto;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.BpmProcessInstanceFeignApi;
import com.jp.med.common.ienum.BpmProcessInstanceStatusEnum;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import static com.jp.med.common.constant.RabbitMQConfig.BPM_EXCHANGE_NAME;
import static com.jp.med.common.constant.RabbitMQConfig.BPM_MESSAGE_KEY;

/**
 * 📝 BPM审批消息处理抽象类
 *
 * 该抽象类用于处理BPM工作流审批消息，定义了通用的消息接收和处理流程。
 * 提供了多个抽象方法供子类实现，以便在不同的BPM任务状态下执行特定的业务逻辑。
 *
 * 主要功能：
 * - 自动创建和绑定RabbitMQ队列
 * - 处理不同状态的BPM审批消息
 * - 提供异常处理和错误日志记录
 * 
 * 子类必须实现所有抽象方法，并在这些方法中定义具体的处理逻辑。
 */
@Slf4j
@Component
public abstract class AbstractBpmApproveMessageHandle {

    public final String[] processIdentifier = null;

    @Resource
    private RabbitAdmin rabbitAdmin;

    @Resource
    private ConnectionFactory connectionFactory;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private BpmProcessInstanceFeignApi bpmProcessInstanceFeignApi;

    /**
     * 🔑 获取流程标识
     *
     * 用于标识处理的具体流程。子类必须通过覆盖此方法来指定不同的流程标识。
     *
     * @return 流程标识数组
     */
    public abstract String[] getProcessIdentifier();

    /**
     * 🚀 初始化方法
     *
     * 用于创建队列并将其绑定到特定的路由键。此方法会在类被初始化后自动调用。
     * 为每个流程标识创建对应的消息队列并绑定到交换机。
     */
    @PostConstruct
    public void init() {
        String[] processIdentifiers = this.getProcessIdentifier();
        if (processIdentifiers != null && processIdentifiers.length > 0) {
            for (String identifier : processIdentifiers) {
                if (StrUtil.isNotBlank(identifier)) {
                    log.info("📬 创建消息队列绑定，流程标识: {}", identifier);

                    // 创建队列
                    Queue queue = new Queue(identifier, false);
                    rabbitAdmin.declareQueue(queue);

                    // 绑定队列到特定的路由键模式
                    String bindingKey = BPM_MESSAGE_KEY + "." + identifier;
                    Binding binding = BindingBuilder.bind(queue)
                            .to(new TopicExchange(BPM_EXCHANGE_NAME))
                            .with(bindingKey);
                    rabbitAdmin.declareBinding(binding);
                }
            }
            rabbitTemplate.setChannelTransacted(true);
        } else {
            throw new AppException("❌ 流程标识(processIdentifier)为空，请检查配置");
        }
    }

    /**
     * 📨 消息接收方法
     *
     * 子类需要实现的消息队列监听方法，需要添加注解 {@RabbitListener(queues = processIdentifier)}
     * 并在实现中调用 receiveMessage0(msg) 方法处理消息。
     *
     * @param msg     业务消息对象
     * @param message 原始消息对象
     * @param channel 消息通道
     * @throws Exception 处理异常
     */
    @SuppressWarnings("all")
    public abstract void onMessage(BpmProcessInstanceStatus msg, Message message, Channel channel) throws Exception;

    /**
     * 📋 处理接收到的BPM审批消息
     *
     * 根据消息中的状态，调用相应的处理方法。
     *
     * @param message 包含BPM审批实例状态的消息对象
     */
    public void receiveMessage0(BpmProcessInstanceStatus message) {
        BpmProcessInstanceStatusEnum statusEnum = BpmProcessInstanceStatusEnum.fromStatus(message.getStatus());

        log.info("📩 收到流程消息: {} - {} - {}",
                statusEnum != null ? statusEnum.getDesc() : "未知状态",
                message.getProcessDefinitionKey(),
                message);

        if (statusEnum == null) {
            log.warn("⚠️ 未知的状态码: {}", message.getStatus());
            return;
        }

        try {
            // 根据状态调用对应的处理方法
            switch (statusEnum) {
                case CREATE:
                    handleCreate(message);
                    break;
                case APPROVE:
                    handleApproved(message);
                    break;
                case REJECT:
                    handleRejected(message);
                    break;
                case RUNNING:
                    handleRunning(message);
                    break;
                case CANCEL:
                    handleCancelled(message);
                    break;
                default:
                    handleUnhandledStatus(statusEnum, message);
                    break;
            }
        } catch (Exception e) {
            handleException(e, message);
            throw new AppException(e.getMessage());
        }

        // 更新消息处理状态
        setBpmMessageLogHandleStatusDto reqDTO = new setBpmMessageLogHandleStatusDto();
        reqDTO.setHandleStatus(1); // 处理成功
        reqDTO.setBusinessKey(message.getBusinessKey());
        reqDTO.setStatus(message.getStatus());
        reqDTO.setProcessInstanceId(message.getProcessInstanceId());
        bpmProcessInstanceFeignApi.setHandleStatus(reqDTO);

        log.info("✅ 消息处理完成: {} - 状态: {}", message.getProcessDefinitionKey(), message.getStatus());
    }

    /**
     * 🆕 处理流程创建状态
     *
     * @param message 包含BPM审批实例状态的消息对象
     */
    protected abstract void handleCreate(BpmProcessInstanceStatus message);

    /**
     * ✅ 处理审批通过状态
     *
     * @param message 包含BPM审批实例状态的消息对象
     */
    protected abstract void handleApproved(BpmProcessInstanceStatus message);

    /**
     * ❌ 处理审批拒绝状态
     *
     * @param message 包含BPM审批实例状态的消息对象
     */
    protected abstract void handleRejected(BpmProcessInstanceStatus message);

    /**
     * 🔄 处理审批中状态
     *
     * @param message 包含BPM审批实例状态的消息对象
     */
    protected abstract void handleRunning(BpmProcessInstanceStatus message);

    /**
     * 🚫 处理审批取消状态
     *
     * @param message 包含BPM审批实例状态的消息对象
     */
    protected abstract void handleCancelled(BpmProcessInstanceStatus message);

    /**
     * 处理未知状态
     */
    private void handleUnknownStatus(BpmProcessInstanceStatus message) {
        String errorMessage = "❓ 未知状态: " + message.getStatus();
        log.error(errorMessage);
        saveErrorToDatabase(errorMessage, message);
    }

    /**
     * 处理未实现的状态
     */
    private void handleUnhandledStatus(BpmProcessInstanceStatusEnum statusEnum, BpmProcessInstanceStatus message) {
        String errorMessage = "⚠️ 未处理的状态: " + statusEnum.getDesc();
        log.error(errorMessage);
        saveErrorToDatabase(errorMessage, message);
    }

    /**
     * 处理异常情况
     */
    private void handleException(Exception e, BpmProcessInstanceStatus message) {
        String errorMessage = "❗ 处理消息时发生异常: " + e.getMessage();
        log.error(errorMessage, e);
        saveErrorToDatabase(errorMessage, message);
    }

    /**
     * 将错误信息保存到数据库
     */
    private void saveErrorToDatabase(String errorMessage, BpmProcessInstanceStatus message) {
        log.info("💾 保存流程事件处理错误: {}.{} - {}",
                message.getBusinessKey(),
                message.getBusinessKey(),
                errorMessage);

        setBpmMessageLogHandleStatusDto reqDTO = new setBpmMessageLogHandleStatusDto();
        reqDTO.setHandleStatus(2); // 处理失败
        reqDTO.setBusinessKey(message.getBusinessKey());
        reqDTO.setProcessInstanceId(message.getProcessDefinitionKey());
        reqDTO.setStatus(message.getStatus());
        bpmProcessInstanceFeignApi.setHandleStatus(reqDTO);
    }
}