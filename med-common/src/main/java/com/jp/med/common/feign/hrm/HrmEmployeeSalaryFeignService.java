package com.jp.med.common.feign.hrm;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.vo.EmployeeReallySalaryVo;
import com.jp.med.common.vo.HrpSalaryTask;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@RefreshScope
@FeignClient(name = "HrmEmployeeSalaryFeignService",url = "${custom.gateway.med-hrm-service-uri}")
public interface HrmEmployeeSalaryFeignService {

    @PostMapping("/employeeSalary/queryReallySalaryDetail")
    CommonResult<List<EmployeeReallySalaryVo>> queryReallySalaryDetail(@RequestBody HrpSalaryTask dto);
}
