package com.jp.med.common.enums.ecs;

import org.apache.commons.lang3.StringUtils;

/**
 * 发票信息状态枚举
 */
public enum InvoStatusEnum {

    CAN_REIM("1","可报销"),

    HAS_REIMED("2","已报销"),

    REPEAT_INVO("3","重复发票"),

    INVO_IN_AUDIT("4","发票报销中"),

    INVO_MSG_ERROR("5","发票信息不规范"),

    MANUAL_AUDIT_NO_PASS("6","人工审核不通过"),

    OCR_ERROR("7","OCR识别失败"),

    VERIFY_ERROR("8","发票校验不通过");

    private String code;

    private String message;

    InvoStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static String getMessage(String code) {
        for (InvoStatusEnum invoStatusEnum : InvoStatusEnum.values()) {
            if (StringUtils.equals(code, invoStatusEnum.code)) {
                return invoStatusEnum.message;
            }
        }
        return null;
    }
}
