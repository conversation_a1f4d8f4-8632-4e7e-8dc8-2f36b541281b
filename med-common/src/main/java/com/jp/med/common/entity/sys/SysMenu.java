package com.jp.med.common.entity.sys;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/16 12:10
 * @description: 系统菜单
 */
@Data
@TableName("sys_menu")
public class SysMenu implements Serializable {

    /** id */
    @TableId(value = "menu_id", type = IdType.AUTO)
    private Long id;

    /** 父级ID */
    @TableField(value = "parent_id", updateStrategy = FieldStrategy.IGNORED)
    private Long parentId;

    /** 父级菜单名称 */
    @TableField(exist = false)
    private String parentMenuName;

    /** 菜单名称 */
    @TableField("name")
    private String menuName;

    /** 菜单路径（配置的前端访问路径） */
    @TableField("url")
    private String menuUrl;

    /** 组件路径（配置的前端访问路径） */
    @TableField("comp_url")
    private String compUrl;

    /** 授权列表 */
    private String perms;

    /** 菜单类型 0：菜单 1：按钮 */
    @TableField("type")
    private String menuType;

    /** 菜单图标 */
    @TableField("icon")
    private String menuIcon;

    /** appIcon 属性 */
    @TableField("app_icon")
    private String appIcon;

    /** 菜单排序号 */
    @TableField(value = "order_num", updateStrategy = FieldStrategy.IGNORED)
    private String menuOrderNum;

    /** 键值 */
    @TableField(exist = false)
    private String key;

    /** 是否缓存 */
    @TableField("keep_alive")
    private String keepAlive;

    /** 系统Id */
    @TableField("system_id")
    private Long systemId;

    /** 子集 */
    @TableField(exist = false)
    private List<SysMenu> children;

    /**是否可以选中*/
    @TableField(exist = false)
    private Boolean disabled;

    /** 是否白名单 */
    @TableField("whitelist")
    private String whitelist;

    /** 是否隐藏 */
    @TableField("hide")
    private String hide;

    /** 系统类型(0:web系统,1:app系统) */
    @TableField("sys_type")
    private String sysType;

    /**
     * icon_svg
     */
//    @TableField("icon_svg")
//    private String iconSvg;

}
