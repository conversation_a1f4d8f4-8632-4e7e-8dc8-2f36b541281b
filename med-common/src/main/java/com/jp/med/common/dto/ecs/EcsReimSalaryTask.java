package com.jp.med.common.dto.ecs;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 应发工资报销任务
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 14:45:03
 */
@Data
@TableName("ecs_reim_salary_task")
public class EcsReimSalaryTask extends CommonQueryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId("id")
    private Integer id;

    /**
     * 工资任务id
     **/
    @TableField("salary_id")
    private Integer salaryId;

    /**
     * 工资任务发放月份
     */
    @TableField("ff_mth")
    private String ffMth;

    /**
     * 工资条数
     */
    @TableField("num")
    private Integer num;

    /**
     * 应发合计汇总
     */
    @TableField("should_pay")
    private BigDecimal shouldPay;

    /**
     * 扣款合计汇总
     */
    @TableField("reduce_pay")
    private BigDecimal reducePay;

    /**
     * 实发合计汇总
     */
    @TableField("real_pay")
    private BigDecimal realPay;

    /**
     * 企业缴纳合计
     */
//    @TableField("entp_pay")
    @TableField(exist = false)
    private BigDecimal entpPay;

    /**
     * 工会会费合计
     */
//    @TableField("entp_pay")
    @TableField(exist = false)
    private BigDecimal laborUnion;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 制表人
     */
    @TableField("crter")
    private String crter;

    /**
     * 制表时间
     */
    @TableField("crte_time")
    private String crteTime;

    /**
     * 是否报销
     */
    @TableField("reim_flag")
    private String reimFlag;

    /**
     * 报销id
     **/
    @TableField("reim_id")
    private Integer reimId;

    @TableField("type")
    private String type;

    /**
     * 工资任务明细项
     **/
    @TableField(exist = false)
    private List<EcsReimSalaryTaskDetail> details;

    /**
     * ids
     */
    @TableField(exist = false)
    private List<Integer> ids;

}
