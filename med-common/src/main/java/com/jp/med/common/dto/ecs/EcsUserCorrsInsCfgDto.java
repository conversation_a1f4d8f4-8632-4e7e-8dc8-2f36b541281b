package com.jp.med.common.dto.ecs;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

/**
 * 费用报销用户-往来单位映射配置表
 * <AUTHOR>
 * @email -
 * @date 2024-12-30 10:36:18
 */
@Data
@TableName("ecs_user_corrs_ins_cfg" )
public class EcsUserCorrsInsCfgDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 员工编号 */
    @TableField("emp_code")
    private String empCode;

    /** 往来单位 */
    @TableField("ins_code")
    private String insCode;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 年度 */
    @TableField("year")
    private String year;

    /** 医疗机构 */
    @TableField("hospital_id")
    private String hospitalId;

    /** 启用标志 1：启用 0：弃用 */
    @TableField("active_flag")
    private String activeFlag;

}
