package com.jp.med.common.dto.ecs;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

/**
 * 药品账期
 * <AUTHOR>
 * @email -
 * @date 2025-02-21 10:36:30
 */
@Data
@TableName("ecs_drug_account_period" )
public class EcsDrugAccountPeriodDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 账期 */
    @TableField("account_period")
    private String accountPeriod;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

}
