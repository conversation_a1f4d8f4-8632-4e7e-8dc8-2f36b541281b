package com.jp.med.common.dto.ecs.drug;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.common.entity.audit.AuditDetail;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 药品报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:45
 */
@Data
@TableName("ecs_drug_reim_detai" )
public class EcsDrugReimDetaiDto extends CommonQueryDto {

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 供货单位 */
    @TableField("spler")
    private String spler;

    /** 合计金额小写(元) */
    @TableField("sum")
    private BigDecimal sum;

    /** 合计金额大写 */
    @TableField("cap_sum")
    private String capSum;

    /** 付款说明 */
    @TableField("pay_istr")
    private String payIstr;

    /** 审核批次号 */
    @TableField("audit_bchno")
    private String auditBchno;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("craete_time")
    private String craeteTime;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 报销期号 */
    @TableField("issue")
    private String issue;

    /** 上级批次号 */
    @TableField("parent_audit_bchno")
    private String parentAuditBchno;

    /**
     * 状态 1：审核中 2：审核成功 3：审核失败 4：待付款 5：已付款
     */
    @TableField("status")
    private String status;

    /**
     * 药品付款证明记录id
     */
    @TableField("drug_pay_id")
    private String drugPayId;

    /**
     * 付款类型 1：非集采 2：集采
     */
    @TableField("drug_pay_type")
    private String drugPayType;

    /**
     * 支付方式 1：现金
     */
    @TableField("pay_method")
    private String payMethod;

    /** 上传付款单说明 */
    @TableField("pay_istr2")
    private String payIstr2;

    /** 更新时间 **/
    @TableField("update_time")
    private String updateTime;

    /** 推送消息内容 */
    @TableField(exist = false)
    private String pushContent;

    /** 供货单位个数 */
    @TableField(exist = false)
    private Integer splerNum;

    /** 入库单号 */
    @TableField(exist = false)
    private String stoinNum;

    /** 是否批量审核 */
    @TableField(exist = false)
    private String batchAudit;

    /** 审核状态，1：成功，2：失败，3审核中 */
    @TableField(exist = false)
    private String auditState;

    /** 入库单号 */
    @TableField(exist = false)
    private List<String> stoinNumList;

    /** 入库单ids */
    @TableField(exist = false)
    private List<Integer> stoinIds;

    /** 批量新增时使用 */
    @TableField(exist = false)
    private List<EcsDrugReimDetaiDto> batchData;

    /** 子集 */
    @TableField(exist = false)
    private List<AuditDetail> childrenDetails;

    /** 入库单对应发票 */
    @TableField(exist = false)
    private Map<String, List<MultipartFile>> stoinNumMap;

    /** 入库单对应发票信息 非文件 **/
    @TableField(exist = false)
    private List<EcsStoinDto> stoinDtos;

    /** 付款证明文件 */
    @TableField(exist = false)
    private List<MultipartFile> payRcptFiles;

    /** ids 根据需求设定 **/
    @TableField(exist = false)
    private List<Integer> ids;

    /** 库房类型 **/
    @TableField(exist = false)
    private List<String> stoinTypeStr;

    /** 审核标志 **/
    @TableField(exist = false)
    private String auditFlag;

    /** 报销开始日期 **/
    @TableField(exist = false)
    private String startDate;

    /** 报销结束日期 **/
    @TableField(exist = false)
    private String endDate;

    /** 是否扎帐 **/
    @TableField(exist = false)
    private String isCloseAccount;

    /** 是否集采 **/
    @TableField(exist = false)
    private String cpFlag;

    /** 药品id 逗号拼接字符串 1,2,3  **/
    @TableField(exist = false)
    private String drugIdsStr;

    //------BPM流程需要参数START--------
    @TableField(exist = false)
    private Map<String,String> bpmParams;

    /**
     * 发起人自选审批人 Map
     * <p>
     * key：taskKey 任务编码
     * value：审批人的数组
     * 例如：{ taskKey1 :[1, 2] }，则表示 taskKey1 这个任务，提前设定了，由 use    rId 为 1,2 的用户进行审批
     */
    @TableField(exist = false)
    private Map<String, List<String>> startUserSelectAssignees;

    /**
     * 对应的流程编号
     * <p>
     * 关联 ProcessInstance 的 id 属性
     */
    @TableField("process_instance_id")
    private String processInstanceId;
    //------BPM流程需要参数-END--------

}
