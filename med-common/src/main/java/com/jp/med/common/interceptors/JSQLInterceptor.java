package com.jp.med.common.interceptors;

import com.jp.med.common.dto.common.CommonQueryDto;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.plugin.Interceptor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/25 11:42
 * @description:
 */
public interface JSQLInterceptor extends Interceptor {

    /**
     * 获取新sql方法
     * @param commonQueryDto 参数
     * @param boundSql 原sql
     * @return
     */
     String getNewSql(CommonQueryDto commonQueryDto, BoundSql boundSql);
}
