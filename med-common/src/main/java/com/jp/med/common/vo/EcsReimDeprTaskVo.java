package com.jp.med.common.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 费用报销-折旧凭证
 * <AUTHOR>
 * @email -
 * @date 2025-02-07 16:08:24
 */
@Data
public class EcsReimDeprTaskVo {

	/** id */
	private Integer id;

	/** 发起科室 **/
	private String launchDept;

	/** 发起科室名称 **/
	private String launchDeptName;

	/** 发起日期 **/
	private String launchDate;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 更新者 */
	private String upder;

	/** 更新时间 */
	private String updateTime;

	private String crterName;

	/** 是否已生成辅助项目 **/
	private String hasAssts;

	/** 报销、凭证生成标识 **/
	private String reimFlag;

	/** 总金额 **/
	private BigDecimal amt;

	/** 附件编号 **/
	private String attCode;

	/** 凭证号 **/
	private String pzh;

	/** 凭证号id **/
	private String idpzh;

	/** 会计期间 **/
	private String kjqj;

}
