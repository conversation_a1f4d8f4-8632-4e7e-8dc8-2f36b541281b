package com.jp.med.common.dto.ecs;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 合同报销任务详情
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 17:13:38
 */
@Data
@TableName("ecs_reim_contract_task_detail" )
public class EcsReimContractTaskDetail extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 任务id */
    @TableField("task_id")
    private Integer taskId;

    /** 报销摘要 */
    @TableField("reim_abst")
    private String reimAbst;

    /** 报销科室 */
    @TableField("org_id")
    private String orgId;

    /** 报销类别 */
    @TableField("reim_type")
    private String reimType;

    /** 报销金额 */
    @TableField("reim_amt")
    private BigDecimal reimAmt;

    /** 附件 */
    @TableField("att")
    private String att;

}
