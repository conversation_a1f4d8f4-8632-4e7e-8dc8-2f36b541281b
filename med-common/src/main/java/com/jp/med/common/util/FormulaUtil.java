package com.jp.med.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Stack;


public class FormulaUtil {

    /**
     * 基本公式计算
     * @param input 数据公式
     * @return 计算结果
     */
    public static BigDecimal formula(String input) {
        input = input.replaceAll("（", "(");
        input = input.replaceAll("）", ")");
        String[] inputs = input.split("");
        StringBuilder format = new StringBuilder();
        for (String s : inputs) {
            if (s.equals(" ")) {
                continue;
            } else if (s.equals("(") || s.equals(")") || s.equals("+") || s.equals("-") || s.equals("*") || s.equals("/")) {
                format.append(" ").append(s).append(" ");
            } else {
                format.append(s);
            }
        }
        List<String> strings = changeInfixExpressionToPostfixExpression(format.toString());
        Stack<String> stack = new Stack<String>();
        for (String string : strings) {
            switch (string) {
                case "+": {
                    BigDecimal a = new BigDecimal(stack.pop());
                    BigDecimal b = new BigDecimal(stack.pop());
                    stack.add(b.add(a).toString());
                    break;
                }
                case "-": {
                    BigDecimal a = new BigDecimal(stack.pop());
                    BigDecimal b = new BigDecimal(stack.pop());
                    stack.add(b.subtract(a).toString());
                    break;
                }
                case "*": {
                    BigDecimal a = new BigDecimal(stack.pop());
                    BigDecimal b = new BigDecimal(stack.pop());
                    stack.add(b.multiply(a).toString());
                    break;
                }
                case "/": {
                    BigDecimal a = new BigDecimal(stack.pop());
                    BigDecimal b = new BigDecimal(stack.pop());
                    if (BigDecimal.ZERO.compareTo(a) == 0){
                        return BigDecimal.ZERO;
                    }
                    stack.add(b.divide(a, 8, RoundingMode.HALF_DOWN).toString());
                    break;
                }
                default:
                    stack.add(string);
                    break;
            }
        }
        return new BigDecimal(stack.pop()).setScale(2, RoundingMode.HALF_DOWN);
    }

    private static List<String> changeInfixExpressionToPostfixExpression(String input) {
        List<String> resultList = new ArrayList<String>();
        Stack<String> tempStack = new Stack<String>();
        String[] splitArray = input.split(" ");
        for (String s : splitArray) {
            if (s.isEmpty()) {
                continue;
            }
            //如果字符是右括号的话,说明前面一定有过左括号,将栈里第一个左括号之前全部添加到List里
            switch (s) {
                case ")":
                    while (!tempStack.peek().equals("(")) {
                        resultList.add(tempStack.pop());
                    }
                    tempStack.pop();//去除前面的左括号

                    break;
                case "(":
                    //如果是左括号,那么直接添加进去
                    tempStack.add("(");
                    break;
                case "+":
                case "-":
                    //如果是加减号,还需要再判断
                    if (tempStack.empty() || tempStack.peek().equals("(")) {
                        tempStack.add(s);
                    } else if (tempStack.peek().equals("+") || tempStack.peek().equals("-")) {
                        //读临时栈里的顶部数据,如果也是加减就取出来一个到结果列,这个放临时栈,如果是乘除就开始取到右括号为止
                        resultList.add(tempStack.pop());
                        tempStack.add(s);
                    } else {
                        while (!tempStack.empty()) {
                            if (tempStack.peek().equals("(")) {
                                break;
                            } else {
                                resultList.add(tempStack.pop());
                            }
                        }
                        tempStack.add(s);
                    }
                    break;
                case "*":
                case "/":
                    //如果是乘除
                    if (!tempStack.empty()) {
                        //判断临时栈里栈顶是啥,如果是乘除,取一个出来到结果列,添这个进临时栈
                        if (tempStack.peek().equals("*") || tempStack.peek().equals("/")) {
                            resultList.add(tempStack.pop());
                        }
                    }
                    tempStack.add(s);
                    break;
                default:
                    //说明是非符号,都添加进去
                    resultList.add(s);
                    break;
            }
        }
        //遍历完了,把临时stack里的东西都加到结果stack里去
        while (!tempStack.empty()) {
            resultList.add(tempStack.pop());
        }
        return resultList;
    }
}
