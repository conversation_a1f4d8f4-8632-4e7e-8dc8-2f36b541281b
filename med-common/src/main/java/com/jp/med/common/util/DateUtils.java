package com.jp.med.common.util;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 日期处理
 */
public class DateUtils {
    /**
     * 时间格式(yyyy-MM-dd)
     */
    public final static String DATE_PATTERN = "yyyy-MM-dd";
    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static String format(Date date) {
        return format(date, DATE_PATTERN);
    }

    public static String format(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }

        /**
     * 格式化 LocalDateTime 为指定的字符串格式
     *
     * @param dateTime 需要格式化的 LocalDateTime 对象
     * @param pattern  需要的日期格式，例如 "yyyy-MM-dd"
     * @return 格式化后的字符串，或 null 如果输入为 null
     */
    public static String format(LocalDateTime dateTime, String pattern) {
        if (dateTime == null) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return dateTime.format(formatter);
    }
    /**
     * 获取两个时间范围的日期
     *
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    public static List<String> getDatesBetween(String startDateStr, String endDateStr) {
        // 将日期字符串转换为 LocalDate 对象
        // 使用 ISO_LOCAL_DATE_TIME 格式化器

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN);
        LocalDate startDate = LocalDate.parse(startDateStr, formatter);
        LocalDate endDate = LocalDate.parse(endDateStr, formatter);

        // 计算两个日期之间的天数
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);

        // 创建一个列表来存储日期
        List<String> dates = new ArrayList<>();

        // 循环遍历从开始日期到结束日期的所有日期
        for (int i = 0; i <= daysBetween; i++) {
            // 将日期格式化为字符串
            String localDateTime = startDate.plusDays(i).toString();
            // 将格式化后的日期添加到列表中
            dates.add(localDateTime);
        }

        // 返回日期列表
        return dates;
    }
}
