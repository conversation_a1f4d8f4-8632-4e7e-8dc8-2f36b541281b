package com.jp.med.common.util;

import java.io.IOException;
import java.io.InputStream;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;

/**
 * <AUTHOR>
 */
public class SqlSessionFactoryUtil {

    private static SqlSessionFactory sqlSessionFactory;

    private static void initSqlSessionFactory(String resource) {
        InputStream inputStream = null;
        try {
            inputStream = Resources.getResourceAsStream(resource);
            sqlSessionFactory = new SqlSessionFactoryBuilder().build(inputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

//获取连接
    public static SqlSession getSqlSession() {
        if (sqlSessionFactory == null){
            throw new RuntimeException("sqlSessionFactory is null");
        }else {
            return sqlSessionFactory.openSession();
        }
    }

    //获取连接
    public static SqlSession getSqlSessionWithConfig(String resource) {
        initSqlSessionFactory(resource);
        return sqlSessionFactory.openSession();
    }

    //sql提交
    public static void commitSqlSession(SqlSession sqlSession) {
        if (sqlSession != null) {
            sqlSession.commit();
        }
    }

    //关闭连接
    public static void closeSqlSession(SqlSession sqlSession) {
        if (sqlSession != null) {
            sqlSession.close();
        }
    }


}

