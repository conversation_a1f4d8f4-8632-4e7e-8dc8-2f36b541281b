package com.jp.med.common.dto.ecs;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * 科研经费报销任务详情表
 * <AUTHOR>
 * @email -
 * @date 2024-11-26 05:30:48
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ecs_research_funding_task_detail" )
public class EcsResearchFundingTaskDetailDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** $column.comments */
    @TableField("task_id")
    private Integer taskId;

    /** $column.comments */
    @TableField("reim_abstract")
    private String reimAbstract;

    /** $column.comments */
    @TableField("org_id")
    private String orgId;

    /** $column.comments */
    @TableField("reim_type")
    private String reimType;

    /** $column.comments */
    @TableField("reim_amt")
    private BigDecimal reimAmt;

    /** $column.comments */
    @TableField("att")
    private String att;

}
