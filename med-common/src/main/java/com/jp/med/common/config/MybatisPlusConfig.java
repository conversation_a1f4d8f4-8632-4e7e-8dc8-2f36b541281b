package com.jp.med.common.config;

import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
import com.jp.med.common.interceptors.HospitalInterceptor;
import com.jp.med.common.interceptors.JSQLInterceptorProxy;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/14 15:18
 * @description:
 */
@Configuration
public class MybatisPlusConfig{

    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;

    @Autowired
    PageHelperAutoConfiguration pageHelperAutoConfiguration;

    @PostConstruct
    public void registerInterceptor() {
        JSQLInterceptorProxy hospitalInterceptor = new JSQLInterceptorProxy(new HospitalInterceptor());
        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
            org.apache.ibatis.session.Configuration configuration = sqlSessionFactory.getConfiguration();
            configuration.addInterceptor(hospitalInterceptor);
        }
    }
}
