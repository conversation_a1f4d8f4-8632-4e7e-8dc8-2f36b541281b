package com.jp.med.common.feign;

import com.jp.med.common.dto.core.AuditCfgDto;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.vo.AuditCfgVo;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/11 15:44
 * @description: 审核远程调用
 */
@RefreshScope
@FeignClient(name = "AuditFeignService",url = "${custom.gateway.med-core-service-uri}")
public interface AuditFeignService {

    /**
     * 获取审核详情
     * @param dto
     * @return
     */
    @PostMapping("/audit/getAuditDetails")
    List<AuditDetail> getAuditDetails(@RequestBody AuditDetail dto);

    /**
     * 获取子集
     * @param dto
     * @return
     */
    @PostMapping("/audit/queryChildren")
    List<AuditDetail> queryChildren(@RequestBody AuditDetail dto);

    /**
     * 审核
     * @param dto
     * @return
     */
    @PostMapping("/audit/saveAuditDetail")
    CommonFeignResult saveAuditDetail(@RequestBody AuditDetail dto);

    /**
     * 不推送消息的审核
     * @param dto
     * @return
     */
    @PostMapping("/audit/notPushSaveAuditDetail")
    CommonFeignResult notPushSaveAuditDetail(@RequestBody AuditDetail dto);

    /**
     * 更改上级审核批次号
     * @param dto
     * @return
     */
    @PostMapping("/audit/updateParentBecho")
    CommonFeignResult updateParentBecho(@RequestBody AuditDetail dto);

    /**
     * 流程列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/auditCfg/list")
    CommonResult<List<AuditCfgVo>> auditCfgList(@RequestBody AuditCfgDto dto);
}
