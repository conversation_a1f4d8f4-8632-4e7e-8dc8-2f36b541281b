package com.jp.med.common.vo;

import lombok.Data;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 18:14:02
 */
@Data
public class EcsReimFixedAsstDetailVo {

	/** id */
	private Integer id;

	/** 固定科目id */
	private Integer fixedAsstId;

	/** 支付类型代码 */
	private String payTypeCode;

	/** 支付类型名称 */
	private String payTypeName;

	/** 会计科目代码 */
	private String actigSubCode;

	/** 会计科目名称 */
	private String actigSubName;

	/** 科目类型 */
	private String actigSys;

	/** 科室代码 */
	private String deptCode;

	/** 科室名称 */
	private String deptName;

	/** 往来单位代码 */
	private String relCoCode;

	/** 往来单位名称 */
	private String relCoName;

	/** 功能科目代码 */
	private String funSubCode;

	/** 功能科目名称 */
	private String funSubName;

	/** 经济科目代码 */
	private String econSubCode;

	/** 经济科目名称 */
	private String econSubName;

	/** 项目代码 */
	private String projCode;

	/** 项目名称 */
	private String projName;

	/** 现金流量代码 */
	private String cashFlowCode;

	/** 现金流量名称 */
	private String cashFlowName;

	/** 金额类型 */
	private String actigAmtType;

	/** 摘要 */
	private String abst;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

}
