package com.jp.med.common.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jp.med.common.config.jackson.JsonBigDecimalFormatAnn;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 折旧(摊销)分配
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ErpAmsPropertyDepr2Vo {

    /**
     * ID
     */
    private Integer id;

    /**
     * 资产代码
     */
    private String faCodes;

    /**
     * 科室代码
     */
    @ExcelProperty(index = 1)
    private String deptUseCode;

    /**
     * 科室名称
     */
    @ExcelProperty(index = 2)
    private String deptUseName;

    /**
     * 资产类型代码
     */
    @ExcelProperty(index = 3)
    private String assetTypeCode;

    /**
     * 资产类型名称
     */
    @ExcelProperty(index = 4)
    private String assetTypeName;

    /**
     * 来源代码
     */
    @ExcelProperty(index = 5)
    private String sourceCode;

    /**
     * 来源名称
     */
    @ExcelProperty(index = 6)
    private String sourceName;


    private String openingDate;

    /**
     * 本月计提原值
     */
    @JsonBigDecimalFormatAnn
    @ExcelProperty(index = 9)
    private BigDecimal monthDeprAmt;

    /**
     * 上月计提原值
     */
    @JsonBigDecimalFormatAnn
    @ExcelProperty(index = 7)
    private BigDecimal lastMonthDeprAmt;
    /**
     * 上月原值变化
     */
    @JsonBigDecimalFormatAnn
    @ExcelProperty(index = 8)
    private BigDecimal lastMonthDeprAmtChange;

    /**
     * 折旧/摊销额
     */
    @JsonBigDecimalFormatAnn
    @ExcelProperty(index = 10)
    private BigDecimal deprAmt;

    /**
     * 月折旧/摊销率%
     */
//    @JsonBigDecimalFormatAnn
    @ExcelProperty(index = 11)
    private BigDecimal deprRate;

    /**
     * 累计折旧/摊销
     */
    @JsonBigDecimalFormatAnn
    @ExcelProperty(index = 12)
    private BigDecimal deprAmtSum;

    private Boolean isSummary;

    public ErpAmsPropertyDepr2Vo(BigDecimal deprAmtSum, BigDecimal deprAmt, BigDecimal lastMonthDeprAmt,
                                 BigDecimal lastMonthDeprAmtChange, BigDecimal monthDeprAmt) {
        this.setDeprAmtSum(deprAmtSum);
        this.setLastMonthDeprAmt(lastMonthDeprAmtChange);
        this.setLastMonthDeprAmt(lastMonthDeprAmt);
        this.setMonthDeprAmt(monthDeprAmt);
        this.setDeprAmt(deprAmt);
    }

    public static ErpAmsPropertyDepr2Vo summarize(List<ErpAmsPropertyDepr2Vo> assets) {
        BigDecimal deprAmtSum = BigDecimal.ZERO;
        BigDecimal deprAmt = BigDecimal.ZERO;
        BigDecimal lastMonthDeprAmt = BigDecimal.ZERO;
        BigDecimal lastMonthDeprAmtChange = BigDecimal.ZERO;
        BigDecimal monthDeprAmt = BigDecimal.ZERO;

        for (ErpAmsPropertyDepr2Vo asset : assets) {
            deprAmtSum = deprAmtSum.add(asset.getDeprAmtSum());
            deprAmt = deprAmt.add(asset.getDeprAmt());
            lastMonthDeprAmt = lastMonthDeprAmt.add(asset.getLastMonthDeprAmt());
            lastMonthDeprAmtChange = lastMonthDeprAmtChange.add(asset.getLastMonthDeprAmtChange());
            monthDeprAmt = monthDeprAmt.add(asset.getMonthDeprAmt());
        }

        return new ErpAmsPropertyDepr2Vo(deprAmtSum, deprAmt, lastMonthDeprAmt, lastMonthDeprAmtChange, monthDeprAmt);
    }

    @Data
    @AllArgsConstructor
    public static class Summary {
        private BigDecimal deprAmtSum;
        private BigDecimal deprAmt;
        private BigDecimal lastMonthDeprAmt;
        private BigDecimal lastMonthDeprAmtChange;
        private BigDecimal monthDeprAmt;
    }
}
