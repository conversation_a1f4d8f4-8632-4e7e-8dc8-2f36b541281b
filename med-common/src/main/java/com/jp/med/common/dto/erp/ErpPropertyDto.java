package com.jp.med.common.dto.erp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.converter.IntegerConverter;
import com.jp.med.common.converter.NumberConverter;
import com.jp.med.common.dto.common.CommonExtendFormDto;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 资产详情
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-10-09 19:59:36
 */
@Data
@TableName("ams_property")
public class ErpPropertyDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    @ExcelProperty("ID")
    @ExcelIgnore
    private Integer id;

    /** 资产编码 */
    @TableField(value = "asset_code", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("资产代码")
    private String assetCode;

    /** 父项资产码 */
    @TableField(value = "sa_code", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String saCode;

    /** 资产名称 */
    @TableField(value = "asset_name", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("资产名称")
    private String assetName;

    /** 固定资产码 */
    @TableField(value = "fa_code", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("卡片编号")
    private String faCode;

    /** 序列号 */
    @TableField(value = "serial_number", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("序列号")
    private String serialNumber;

    /** 资产类型 */
    @TableField(value = "asset_type", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("类别代码")
    private String assetType;

    /** 资产类别 */
    @TableField(value = "asset_category", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("资产类别")
    private String assetCategory;

    /** 资产状态 */
    @TableField(value = "asset_status", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("资产状态")
    private String assetStatus;

    /** 资产型号 */
    @TableField(value = "asset_mol", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("型号")
    private String assetMol;

    /** 资产原值 */
    @TableField(value = "asset_nav", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "原值", converter = NumberConverter.class)
    private BigDecimal assetNav;

    /** 设备净值 */
    @TableField(value = "nbv", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "净值", converter = NumberConverter.class)
    private BigDecimal nbv;

    /** 设备残值 */
    @TableField(value = "rv", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "净残值", converter = NumberConverter.class)
    private BigDecimal rv;

    /** 通用名 */
    @TableField(value = "genname", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("通用名")
    private String genname;

    /** 规格 */
    @TableField(value = "spec", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("规格")
    private String spec;

    /** 增加方式 */
    @TableField(value = "incr_way", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("增加方式")
    private String incrWay;

    /** 资金来源 */
    @TableField(value = "source", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("资金来源")
    private String source;

    /* 财政补助金 */
    @TableField(value = "financial_assistance_funds", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "财政补助金", converter = NumberConverter.class)
    private String financialAssistanceFunds;
    /* 自有资金 */
    @TableField(value = "own_funds", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "自有资金", converter = NumberConverter.class)
    private String ownFunds;

    /** 其他编号(科室内的编号) */
    @TableField(value = "other_ref", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("其他编号(科室内的编号)")
    private String otherRef;

    /** 供应商 */
    @TableField(value = "supplier", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("供应商")
    private String supplier;

    /** 供应商联系人 */
    @TableField(value = "supplier_coner", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("供应商联系人")
    private String supplierConer;

    /** 供应商电话 */
    @TableField(value = "supplier_tel", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("供应商电话")
    private String supplierTel;

    /** 售后工程师 */
    @TableField(value = "fse", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("售后工程师")
    private String fse;

    /** 售后电话 */
    @TableField(value = "cs", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("售后电话")
    private String cs;

    /** 品牌 */
    @TableField(value = "brand", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("品牌")
    private String brand;

    /** 生产厂家 */
    @TableField(value = "manufacturer", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("生产厂家")
    private String manufacturer;

    /** 计量单位 */
    @TableField(value = "unit", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("计量单位")
    private String unit;

    /** 配置描述 */
    @TableField(value = "describe", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("配置描述")
    private String describe;

    /** 使用科室 */
    @TableField(value = "dept_use", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("使用部门代码")
    private String deptUse;

    /** 科室 */
    @TableField(value = "dept", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("部门代码")
    private String dept;

    /** 存放区域 */
    @TableField(value = "storage_area", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("存放地点")
    private String storageArea;

    /** 位置 */
    @TableField(value = "location", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("位置")
    private String location;

    /** 建议使用年限 */
    @TableField(value = "exp", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "建议使用年限", converter = NumberConverter.class)
    private BigDecimal exp;

    /**
     * 已使用年限
     */
    @TableField(value = "used_exp", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "已使用年限", converter = NumberConverter.class)
    private BigDecimal usedExp;

    /** 采购单价 */
    @TableField(value = "unit_price", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "单价", converter = NumberConverter.class)
    private BigDecimal unitPrice;

    /** 折旧方式 */
    @TableField(value = "dm", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "折旧（摊销）方法")
    private String dm;

    /**
     * 折旧年限 预计使用年限
     */
    @TableField(value = "ul", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "折旧年限", converter = NumberConverter.class)
    private BigDecimal ul;

    /**
     * 累计折旧
     */
    @TableField(value = "dep", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "累计折旧（摊销）", converter = NumberConverter.class)
    private BigDecimal dep;

    /**
     * 维保状态
     */
    @TableField(value = "am_status", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("维保状态")
    private String amStatus;

    /**
     * 脱保日期
     */
    @TableField(value = "oow_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("保修截止日期")
    private String oowDate;

    /**
     * 注册证号
     */
    @TableField(value = "rcn", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("注册证号")
    private String rcn;

    /**
     * 出厂编号
     */
    @TableField(value = "sn", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("出厂编号")
    private String sn;

    /**
     * 保修期限
     */
    @TableField(value = "wp", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("保修期限")
    private String wp;

    /** 合同编码 */
    @TableField(value = "cntr_code", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("合同编码")
    private String cntrCode;

    /** 合同名称 */
    @TableField(value = "cntr_name", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("合同名称")
    private String cntrName;

    /** 发票号 */
    @TableField(value = "invono", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("发票号")
    private String invono;

    /** 原厂保修 */
    @TableField(value = "oemw", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("原厂保修")
    private String oemw;

    /** 采购日期 */
    @TableField(value = "purc_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("购置时间")
    private String purcDate;

    /** 安装日期 */
    @TableField(value = "inst_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("安装日期")
    private String instDate;

    /** 启用日期(yyyy-MM-dd) */
    @TableField(value = "opening_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("开始使用日期")
    private String openingDate;

    /** 入库日期 */
    @TableField(value = "stoin_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("入库日期")
    private String stoinDate;

    /** 验收日期 */
    @TableField(value = "acp_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("验收日期")
    private String acpDate;

    /** 出库日期 */
    @TableField(value = "stoout_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("出库日期")
    private String stooutDate;

    /** 设备用途 */
    @TableField(value = "asset_used", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("设备用途")
    private String assetUsed;

    /** 自筹金额 */
    @TableField(value = "oop", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "自筹金额", converter = NumberConverter.class)
    private BigDecimal oop;

    /** 财政拨款 */
    @TableField(value = "gf", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "财政拨款", converter = NumberConverter.class)
    private BigDecimal gf;

    /**
     * 科研经费
     */
    @TableField(value = "research_funding", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "科研经费", converter = NumberConverter.class)
    private BigDecimal researchFunding;

    /** 教学经费 */
    @TableField(value = "tef", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "教学经费", converter = NumberConverter.class)
    private BigDecimal tef;

    /** 是否成本核算 */
    @TableField(value = "adjact", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("是否成本核算")
    private String adjact;

    /** 是否进口 */
    @TableField(value = "imp", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("是否进口")
    private String imp;

    /** 二维码 */
    @TableField(value = "uid", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("二维码")
    private String uid;

    /** 第三方编号 */
    @TableField(value = "tpn", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("第三方编号")
    private String tpn;

    /** 在保类型 */
    @TableField(value = "wts", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("在保类型")
    private String wts;

    /** 买保开始日期 */
    @TableField(value = "ipd", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("买保开始日期")
    private String ipd;

    /** 买保结束日期 */
    @TableField(value = "epd", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("买保结束日期")
    private String epd;

    /** 生产日期 */
    @TableField(value = "pd", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("生产日期")
    private String pd;

    /** 负责人 */
    @TableField(value = "resper", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("负责人")
    private String resper;

    /** 便用状态 */
    @TableField(value = "us", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("便用状态")
    private String us;

    /** 财务分类 */
    @TableField(value = "fc", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("财务分类")
    private String fc;

    /** 医械分类 */
    @TableField(value = "mdr", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("医械分类")
    private String mdr;

    /** 风险等级 */
    @TableField(value = "rsl", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("风险等级")
    private String rsl;

    /** 管理分类 */
    @TableField(value = "cm", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("管理分类")
    private String cm;

    /** 生命支持 */
    @TableField(value = "ls", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("生命支持")
    private String ls;

    /** 是否应急设备 */
    @TableField(value = "ed", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("是否应急设备")
    private String ed;

    /** 中医诊疗 */
    @TableField(value = "tcm", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("中医诊疗")
    private String tcm;

    /** 检验设备 */
    @TableField(value = "te", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("检验设备")
    private String te;

    /** 特种设备 */
    @TableField(value = "se", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("特种设备")
    private String se;

    /** 辐射设备 */
    @TableField(value = "rs", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("辐射设备")
    private String rs;

    /** 辅助分类 */
    @TableField(value = "ac", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("辅助分类")
    private String ac;

    /** 附属设备 */
    @TableField(value = "ae", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("有无附属设备")
    private String ae;

    /** 出厂日期 */
    @TableField(value = "manu_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("出厂日期")
    private String manuDate;

    /** 是否计量设备 */
    @TableField(value = "me", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("是否计量设备")
    private String me;

    /** 计量编码 */
    @TableField(value = "mc", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("计量编码")
    private String mc;

    /**
     * 数量
     */
    @TableField(value = "cnt", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "数量", converter = NumberConverter.class)
    // @ExcelProperty("数量")
    private BigDecimal cnt;

    /** 残值率 */
    @TableField(value = "resr", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "残值率", converter = NumberConverter.class)
    private BigDecimal resr;

    /** 建筑面积 */
    @TableField(value = "area", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "建筑面积", converter = NumberConverter.class)
    private BigDecimal area;

    /** 发证日期 */
    @TableField(value = "issucert_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("发证日期")
    private String issucertDate;

    /** 权属证明 */
    @TableField(value = "proof_of_title", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("权属证明")
    private String proofOfTitle;

    /** 权属证书编号 */
    @TableField(value = "certificate_num", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("权属证书编号")
    private String certificateNum;

    /** 权属年限 */
    @TableField(value = "tenure", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "权属年限", converter = NumberConverter.class)
    private BigDecimal tenure;

    /** 坐落位置 */
    @TableField(value = "loc", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("坐落位置")
    private String loc;

    /** 土地使用权类型 */
    @TableField(value = "land_use_rights_type", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("土地使用权类型")
    private String landUseRightsType;

    /** 产权形式 */
    @TableField(value = "property_form", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("产权形式")
    private String propertyForm;

    /** 医疗机构编号 */
    @TableField(value = "hospital_id", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("医疗机构编码")
    private String hospitalId;

    /** 月折旧率 */
    @TableField(value = "deprrat_mon", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "月折旧（摊销）率", converter = NumberConverter.class)
    private BigDecimal deprratMon;

    @TableField(value = "auto_gen_deprrat_mon")
    private boolean autoGenDeprratMon;

    /** 月折旧额 */
    @TableField(value = "depr_mon", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "月折旧（摊销）额", converter = NumberConverter.class)
    private BigDecimal deprMon;

    @TableField(value = "auto_gen_depr_mon")
    private boolean autoGendeprMon;

    /** 已使用月份 */
    @TableField(value = "used_mon", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "已用月份", converter = IntegerConverter.class)
    private Integer usedMon;

    /** 车牌照号 */
    @TableField(value = "lpn", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("车牌照号")
    private String lpn;

    /** 减少方式 */
    @TableField(value = "redc_way", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("减少方式")
    private String redcWay;

    /** 录入人 */
    @TableField(value = "inpter", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("录入人")
    private String inpter;

    /** 录入日期 */
    @TableField(value = "inpt_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("录入日期")
    private String inptDate;

    /** 是否已编制凭证 */
    @TableField(value = "is_prepare_vouchers", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("是否已编制凭证")
    private String isPrepareVouchers;

    /** 是否已审核 */
    @TableField(value = "is_chk", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("是否已审核")
    private String isChk;

    /** 审核人 */
    @TableField(value = "chker", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("审核人")
    private String chker;

    /** 备注 */
    @TableField(value = "memo", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("备注")
    private String memo;

    /** 土地面积 */
    @TableField(value = "land_area", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "土地面积", converter = NumberConverter.class)
    private BigDecimal landArea;

    /** 权属证书所有权人 */
    @TableField(value = "co_owner", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("权属证书所有权人")
    private String coOwner;

    /** 土地证载明面积 */
    @TableField(value = "zkc", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "土地证载明面积", converter = NumberConverter.class)
    private BigDecimal zkc;

    /** 入账形式 */
    @TableField(value = "entry_form", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("入帐形式")
    private String entryForm;

    /** 土地来源 */
    @TableField(value = "land_souc", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("土地来源")
    private String landSouc;

    /** 使用方向 */
    @TableField(value = "usage", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("使用方向")
    private String usage;

    /** 权属性质 */
    @TableField(value = "property_char", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("权属性质")
    private String propertyChar;

    /** 权属证书发证时间 */
    @TableField(value = "co_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("权属证书发证时间")
    private String coDate;

    /** 权属所有人 */
    @TableField(value = "property_owner", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("权属所有人")
    private String propertyOwner;

    /** 建筑结构 */
    @TableField(value = "building_structure", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("建筑结构")
    private String buildingStructure;

    /** 使用状况 */
    @TableField(value = "use", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("使用状况")
    private String use;

    /** 价值类型 */
    @TableField(value = "value_type", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("价值类型")
    private String valueType;

    /** 发动机号 */
    @TableField(value = "engine_no", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("发动机号码")
    private String engineNo;

    /** 资产大类 */
    @TableField(value = "asset_classification", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("资产类型代码")
    private String assetClassification;

    /** 土地使用权面积 */
    @TableField(value = "land_usage_area", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "土地使用权面积", converter = NumberConverter.class)
    private BigDecimal landUsageArea;

    /** 土地使用权人 */
    @TableField(value = "land_user", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("土地使用权人")
    private String landUser;

    /** 地类(用途) */
    @TableField(value = "land_used", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("地类(用途)")
    private String landUsed;

    /** 房屋所有权人 */
    @TableField(value = "owner_of_house", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("房屋所有权人")
    private String ownerOfHouse;

    /** 记账凭证号 */
    @TableField(value = "avn", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("记账凭证号")
    private String avn;

    /** 设备用途 */
    @TableField(value = "dev_used", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("设备用途")
    private String devUsed;

    /** 车辆识别代码 */
    @TableField(value = "vin", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("车辆识别代码")
    private String vin;

    /** 是否拆分卡片 */
    @TableField(value = "is_split", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("是否拆分卡片")
    private String isSplit;

    /** 是否已注销 */
    @TableField(value = "is_canc", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("是否已注销 1已注销 0未注销")
    private String isCanc;

    /** 注销人 */
    @TableField(value = "canc_psn", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("注销人")
    private String cancPsn;

    /** 注销日期 */
    @TableField(value = "canc_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("注销日期")
    private String cancDate;

    /** 证书号 */
    @TableField(value = "cert_no", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("证书号")
    private String certNo;

    /**
     * 已折旧月份
     */
    @TableField(value = "depr_m", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "已折旧（摊销）月份", converter = IntegerConverter.class)
    private Integer deprM;

    /** 保存地点 */
    @TableField(value = "storage_location", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "保存地点")
    private String storageLocation;

    /** 取得日期 */
    @TableField(value = "obtain_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "取得日期")
    private String obtainDate;

    /** 取得方式 */
    @TableField(value = "obtain_way", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "取得方式")
    private String obtainWay;

    /** 财务入账日期 */
    @TableField(value = "entry_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "入账日期")
    private String entryDate;

    /** 财务入账状态 */
    @TableField(value = "entry_status", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "财务入账状态")
    private String entryStatus;

    /** 竣工日期 */
    @TableField(value = "completed_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "竣工日期")
    private String completedDate;

    /** 设计用途 */
    @TableField(value = "dsgn_used", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "设计用途")
    private String dsgnUsed;

    /** 持证人 */
    @TableField(value = "holder", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "持证人")
    private String holder;

    /** 会计凭证号 */
    @TableField(value = "jvn", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "会计凭证号")
    private String jvn;

    /** 采购组织形式 */
    @TableField(value = "pof", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "采购组织形式")
    private String pof;

    /** 折旧状态 */
    @TableField(value = "depr_status", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "折旧状态")
    private String deprStatus;

    /** 其中:取暖面积 */
    @TableField(value = "heating_area", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "取暖面积")
    private String heatingArea;

    /** 其中:危房面积 */
    @TableField(value = "danger_area", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "危房面积")
    private String dangerArea;

    /** 非财政拨款 */
    @TableField(value = "un_gf", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "非财政拨款", converter = NumberConverter.class)
    private BigDecimal unGf;

    /** 闲置面积 */
    @TableField(value = "idle_area", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "闲置面积", converter = NumberConverter.class)
    private BigDecimal idleArea;

    /** 自用面积 */
    @TableField(value = "self_area", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "自用面积", converter = NumberConverter.class)
    private BigDecimal selfArea;

    /** 出借面积 */
    @TableField(value = "lend_area", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "出借面积", converter = NumberConverter.class)
    private BigDecimal lendArea;

    /** 出租面积 */
    @TableField(value = "hire_area", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "出租面积", converter = NumberConverter.class)
    private BigDecimal hireArea;

    /** 其他面积 */
    @TableField(value = "other_area", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "其他面积", converter = NumberConverter.class)
    private BigDecimal otherArea;

    /** 行驶证登记日期 */
    @TableField(value = "regis_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "行驶证登记日期")
    private String regisDate;

    /** 车辆产地 */
    @TableField(value = "vehicle_Origin", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "车辆产地")
    private String vehicleOrigin;

    /** 车辆品牌 */
    @TableField(value = "vehicle_brand", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "车辆品牌")
    private String vehicleBrand;

    /** 排气量 */
    @TableField(value = "displacement", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "排气量")
    private String displacement;

    /** 车辆用途 */
    @TableField(value = "vehicle_usage", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "车辆用途")
    private String vehicle_usage;

    /** 出版社 */
    @TableField(value = "press", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "出版社")
    private String press;

    /** 出版日期 */
    @TableField(value = "publication_date", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "出版日期")
    private String publicationDate;

    /** 档案号 */
    @TableField(value = "file_no", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "档案号")
    private String fileNo;

    /** 保存年限(档案) */
    @TableField(value = "storage_period", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "保存年限(档案)", converter = IntegerConverter.class)
    private Integer storagePeriod;

    /** 来源地(文物) */
    @TableField(value = "origin", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "来源地(文物)")
    private String origin;

    /** 藏品年代 */
    @TableField(value = "collection_age", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "藏品年代")
    private String collectionAge;

    /** 文物等级 */
    @TableField(value = "cocr", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "文物等级")
    private String cocr;

    /** 栽种年龄 */
    @TableField(value = "planting_age", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "栽种年龄")
    private String plantingAge;

    /** 栽种年份 */
    @TableField(value = "planting_year", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "栽种年份")
    private String plantingYear;

    /** 纲属科 */
    @TableField(value = "genus", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "纲属科")
    private String genus;

    /** 产地(动植物) */
    @TableField(value = "producer", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "产地(动植物)")
    private String producer;

    /** 分摊面积 */
    @TableField(value = "shared_area", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "分摊面积")
    private BigDecimal sharedArea;

    /** 独用面积 */
    @TableField(value = "exclusive_area", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "独用面积", converter = NumberConverter.class)
    private BigDecimal exclusiveArea;

    /** 土地级次 */
    @TableField(value = "land_level", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "土地级次")
    private String landLevel;

    /** 编制情况 */
    @TableField(value = "compilation_status", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty(value = "编制情况")
    private String compilationStatus;

    /** 类型(1:固定资产2:非固定资产) */
    @TableField(value = "type", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelIgnore
    private String type;

    /** 资产类型(新) */
    @TableField(value = "asset_type_n", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelIgnore
    private String assetTypeN;

    /**
     * 预入库状态
     */
    @TableField(value = "pre_warehousing", insertStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelIgnore
    private String preWarehousing;

    @TableField(exist = false)
    @ExcelIgnore
    private String asset;

    @TableField(exist = false)
    @ExcelIgnore
    private List<Integer> ids;

    @TableField(exist = false)
    @ExcelIgnore
    private List<String> faCodes;

    @TableField(exist = false)
    @ExcelIgnore
    private List<String> excludeFaCodes;

    @TableField(exist = false)
    @ExcelIgnore
    private MultipartFile[] files;

    /** 查询科室字段 */
    @TableField(exist = false)
    @ExcelIgnore
    private String deptField;

    /** 科室编码 */
    @TableField(exist = false)
    @ExcelIgnore
    private String deptCode;

    /** 入库数量 */
    @TableField(exist = false)
    @ExcelIgnore
    private Integer stockNumber;

    /** 扩充条件 */
    @TableField(exist = false)
    @ExcelIgnore
    private List<CommonExtendFormDto> extendForm;

    /** 时间范围 */
    @TableField(exist = false)
    @ExcelIgnore
    private String[] inptDateRange;

    /**
     * 汇总查询
     */
    @TableField(exist = false)
    @ExcelIgnore
    private String summary;

    /** 第三方入库人名称 */
    @TableField(exist = false)
    private String thirdPartyInUserName;

    /** 第三方入库部门 */
    @TableField(exist = false)
    private String thirdPartyInDept;
    // private String thirdPartyInUserName;

    /**
     * 扫码入库id
     */
    @TableField(exist = false)
    private String qrId;

    /**
     * 盘盈
     */
    @TableField(value = "is_profit", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String isProfit;

    /** 盘点ams_intr_task_id */
    @TableField(exist = false)
    private String amsIntrTaskId;

    @TableField(exist = false)
    private Integer scrapId;

    @TableField(exist = false)
    private String scrapFacode;

    @TableField(exist = false)
    /**
     * 代理审批部门
     */
    private List<String> behalfApprovalDepts;
    /**
     * 代管分类
     */
    @TableField(exist = false)
    @ExcelIgnore
    private Collection<String> proxyAssetType;
    /**
     * 代管新分类
     */
    @TableField(exist = false)
    @ExcelIgnore
    private Collection<String> proxyAssetTypeN;

    /**
     * 拆分数量
     */
    @TableField(exist = false)
    @ExcelIgnore
    private Integer splitCount;

    /**
     * 是否可以拆分
     */
    @TableField(exist = false)
    @ExcelIgnore
    private Boolean canSplit;

    /**
     * scrapAssetType
     * 报废资产类型
     */
    @TableField(exist = false)
    @ExcelIgnore
    private String scrapAssetType;

}
