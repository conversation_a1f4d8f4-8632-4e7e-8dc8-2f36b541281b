package com.jp.med.common.dto.ecs;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

/**
 * 费用报销会计科目固定项明细
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 18:14:02
 */
@Data
@TableName("ecs_reim_fixed_asst_detail" )
public class EcsReimFixedAsstDetailDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 固定科目id */
    @TableField("fixed_asst_id")
    private Integer fixedAsstId;

    /** 支付类型代码 */
    @TableField("pay_type_code")
    private String payTypeCode;

    /** 支付类型名称 */
    @TableField("pay_type_name")
    private String payTypeName;

    /** 会计科目代码 */
    @TableField("actig_sub_code")
    private String actigSubCode;

    /** 会计科目名称 */
    @TableField("actig_sub_name")
    private String actigSubName;

    /** 科目类型 */
    @TableField("actig_sys")
    private String actigSys;

    /** 科室代码 */
    @TableField("dept_code")
    private String deptCode;

    /** 科室名称 */
    @TableField("dept_name")
    private String deptName;

    /** 往来单位代码 */
    @TableField("rel_co_code")
    private String relCoCode;

    /** 往来单位名称 */
    @TableField("rel_co_name")
    private String relCoName;

    /** 功能科目代码 */
    @TableField("fun_sub_code")
    private String funSubCode;

    /** 功能科目名称 */
    @TableField("fun_sub_name")
    private String funSubName;

    /** 经济科目代码 */
    @TableField("econ_sub_code")
    private String econSubCode;

    /** 经济科目名称 */
    @TableField("econ_sub_name")
    private String econSubName;

    /** 项目代码 */
    @TableField("proj_code")
    private String projCode;

    /** 项目名称 */
    @TableField("proj_name")
    private String projName;

    /** 现金流量代码 */
    @TableField("cash_flow_code")
    private String cashFlowCode;

    /** 现金流量名称 */
    @TableField("cash_flow_name")
    private String cashFlowName;

    /** 金额类型 */
    @TableField("actig_amt_type")
    private String actigAmtType;

    /** 摘要 */
    @TableField("abst")
    private String abst;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 资金性质 **/
    @TableField("fund_type")
    private String fundType;

    /** 资金性质名称 **/
    @TableField("fund_type_name")
    private String fundTypeName;

}
