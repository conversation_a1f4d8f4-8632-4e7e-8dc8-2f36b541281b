package com.jp.med.common.util;

import cn.hutool.json.JSONUtil;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.ienum.ResultCode;

import java.util.Objects;

/**
 * @ClassName FeignExcuteUtil
 * @Description feign返回处理
 * <AUTHOR>
 * @Date 2024/3/12 21:37
 * @Version 1.0
 */
public class FeignExecuteUtil{


    public static CommonFeignResult execute(CommonFeignResult result){
        if (!Objects.isNull(result.get("code")) && ResultCode.SUCCESS.getCode() != (Integer) result.get("code")){
            throw new AppException("接口调用失败" + JSONUtil.toJsonStr(result));
        }
        return result;
    }
}
