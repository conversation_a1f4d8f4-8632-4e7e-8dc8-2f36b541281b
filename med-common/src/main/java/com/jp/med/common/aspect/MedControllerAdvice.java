package com.jp.med.common.aspect;

import com.jp.med.common.feign.SysLogFeignService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

//@ControllerAdvice
public class MedControllerAdvice {

    @Autowired
    private SysLogFeignService sysLogFeignService;

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Exception exceptionHandler(Exception e) {

        return e;
    }
}
