/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package com.jp.med.common.entity.common;

import com.jp.med.common.ienum.ResultCode;
import org.apache.http.HttpStatus;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 返回数据
 * <AUTHOR>
 */
public class CommonResultMap extends HashMap<String, Object> implements Serializable {
	private static final long serialVersionUID = 1L;

	public CommonResultMap() {
		put("code", ResultCode.SUCCESS.getCode());
		put("msg", ResultCode.SUCCESS.getMessage());
	}

	public static CommonResultMap failed() {
		return failed(HttpStatus.SC_INTERNAL_SERVER_ERROR, "未知异常，请联系管理员");
	}

	public static CommonResultMap failed(String msg) {
		return failed(HttpStatus.SC_INTERNAL_SERVER_ERROR, msg);
	}

	public static CommonResultMap failed(int code, String msg) {
		CommonResultMap r = new CommonResultMap();
		r.put("code", code);
		r.put("msg", msg);
		return r;
	}

	public static CommonResultMap success(String msg) {
		CommonResultMap r = new CommonResultMap();
		r.put("msg", msg);
		return r;
	}

	public static CommonResultMap success(Map<String, Object> map) {
		CommonResultMap r = new CommonResultMap();
		r.putAll(map);
		return r;
	}

	public static CommonResultMap success() {
		return new CommonResultMap();
	}

	public CommonResultMap put(String key, Object value) {
		super.put(key, value);
		return this;
	}
}
