<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sfm_back</artifactId>
        <groupId>com.jp.medical</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <groupId>com.jp.med</groupId>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>med-common</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <description>公共模块</description>

    <properties>
            <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <spring-cloud-alibaba.version>2.2.10-RC1</spring-cloud-alibaba.version>
        <spring-cloud.version>Hoxton.SR12</spring-cloud.version>
        <mysql.version>8.0.17</mysql.version>
        <pg.version>42.5.4</pg.version>
        <sqlserver.version>12.6.1.jre11</sqlserver.version>
        <mybatis-plus.version>3.5.3</mybatis-plus.version>
        <lombok.version>1.18.26</lombok.version>
        <httpcore.version>4.4.12</httpcore.version>
        <commons-lang.version>2.6</commons-lang.version>
        <commons-coll.version>4.1</commons-coll.version>
        <swagger.version>2.7.0</swagger.version>
        <jedis.version>2.9.0</jedis.version>
        <redis.version>2.3.12.RELEASE</redis.version>
        <actuator.version>2.3.5.RELEASE</actuator.version>
        <bootstrap.version>4.0.1</bootstrap.version>
        <fastjson.version>1.2.62</fastjson.version>
        <jwt.version>0.9.0</jwt.version>
        <hutool.version>5.8.29</hutool.version>
        <pagehelper.version>1.4.6</pagehelper.version>
        <easy-poi>4.1.2</easy-poi>
        <druid.version>1.2.20</druid.version>
		<sharding-jdbc.version>4.1.1</sharding-jdbc.version>
        <jaxb.version>2.3.0</jaxb.version>
        <activation.version>1.1.1</activation.version>
        <minio.version>8.2.1</minio.version>
        <aspectjweaver.version>1.9.19</aspectjweaver.version>
        <tomcat-embed-core.version>9.0.69</tomcat-embed-core.version>
        <feign.version>2.2.9.RELEASE</feign.version>
        <sleuth.version>2.2.8.RELEASE</sleuth.version>
        <!--        <activiti.version>7.1.0.M6</activiti.version>-->
        <zxing.version>3.3.0</zxing.version>
        <easy-excel>2.2.10</easy-excel>
        <pinyin.version>2.5.0</pinyin.version>
        <poi-tl.version>1.9.1</poi-tl.version>
        <seata.version>2.0.0</seata.version>
        <seata-start.version>2.2.10-RC1</seata-start.version>
        <ofdrw-converter.version>2.0.7</ofdrw-converter.version>
        <oracle.version>19.8.0.0</oracle.version>
        <orai18n.version>19.8.0.0</orai18n.version>
    </properties>

    <dependencies>
        <!-- bootstrap -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
            <version>${bootstrap.version}</version>
        </dependency>

        <!-- 远程调用 -->
        <dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
			<version>${feign.version}</version>
		</dependency>

        <!-- redis begin -->
        <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
            <version>${redis.version}</version>
		</dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${jedis.version}</version>
        </dependency>
        <!-- redis end -->

        <!-- 启动检测redis，mysql是否可用 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <version>${actuator.version}</version>
        </dependency>

        <!-- nacos begin -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!-- nacos end -->

        <!-- seata begin -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
            <version>${seata-start.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>seata-spring-boot-starter</artifactId>
                    <groupId>io.seata</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-spring-boot-starter</artifactId>
            <version>${seata.version}</version>
        </dependency>
        <!-- seata end -->

        <!-- mybatis-plus-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
            <exclusions>
				<exclusion>
					<groupId>com.baomidou</groupId>
					<artifactId>mybatis-plus-generator</artifactId>
				</exclusion>
			</exclusions>
        </dependency>

        <!-- swagger begin -->
        <dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>${swagger.version}</version>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>${swagger.version}</version>
		</dependency>
        <!-- swagger end -->

        <!-- alibaba fastjson -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <!-- jwt -->
        <dependency>
          <groupId>io.jsonwebtoken</groupId>
          <artifactId>jjwt</artifactId>
          <version>${jwt.version}</version>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>${zxing.version}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>${httpcore.version}</version>
        </dependency>


        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>${commons-lang.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>${commons-coll.version}</version>
        </dependency>

        <!-- pg驱动 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${pg.version}</version>
        </dependency>

        <!-- oracle 驱动 -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>${oracle.version}</version>
        </dependency>

        <!-- oracle 字符集支持 -->
        <dependency>
            <groupId>com.oracle.database.nls</groupId>
            <artifactId>orai18n</artifactId>
            <version>${orai18n.version}</version>
        </dependency>

        <!--sqlServer驱动-->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>${sqlserver.version}</version> <!-- 使用适当的版本号 -->
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>${pagehelper.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <version>${easy-poi}</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-web</artifactId>
            <version>${easy-poi}</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-annotation</artifactId>
            <version>${easy-poi}</version>
        </dependency>

        <dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>${druid.version}</version>
		</dependency>

        <!-- minio -->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>${minio.version}</version>
        </dependency>

        <!-- aop -->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>${aspectjweaver.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>${tomcat-embed-core.version}</version>
            <scope>compile</scope>
        </dependency>

        <!-- easyexcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easy-excel}</version>
        </dependency>

        <!-- 链路追踪 -->
        <dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-zipkin</artifactId>
            <version>${sleuth.version}</version>
		</dependency>

        <!--activiti依赖配置-->
        <!--        <dependency>-->
        <!--			<groupId>org.activiti</groupId>-->
        <!--			<artifactId>activiti-spring-boot-starter</artifactId>-->
        <!--			<version>${activiti.version}</version>-->
        <!--			<exclusions>-->
        <!--				<exclusion>-->
        <!--					<groupId>org.mybatis</groupId>-->
        <!--					<artifactId>mybatis</artifactId>-->
        <!--				</exclusion>-->
        <!--			</exclusions>-->
        <!--		</dependency>-->

        <!-- 拼音 -->
        <dependency>
          <groupId>com.belerweb</groupId>
          <artifactId>pinyin4j</artifactId>
          <version>${pinyin.version}</version>
        </dependency>

        <!-- Poi-tl Word 模板引擎-->
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>${poi-tl.version}</version>
        </dependency>

        <!-- ODF文件转PDF、图片等类型 -->
        <dependency>
            <groupId>org.ofdrw</groupId>
            <artifactId>ofdrw-converter</artifactId>
            <version>${ofdrw-converter.version}</version>
        </dependency>

        <!-- Apache POI 依赖 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${easy-poi}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${easy-poi}</version>
        </dependency>

        <!-- iTextPDF -->
        <dependency>
			<groupId>aspose-words</groupId>
			<artifactId>aspose-words</artifactId>
			<version>15.8.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/src/main/resources/lib/aspose-words-15.8.0-jdk16.jar</systemPath>
		</dependency>

        <!-- aspose-slides -->
        <dependency>
            <groupId>aspose-slides</groupId>
            <artifactId>aspose-slides</artifactId>
            <version>21.10</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/aspose-slides-21.10.jar</systemPath>
        </dependency>

        <!-- aspose-cells -->
        <dependency>
            <groupId>aspose-cells</groupId>
            <artifactId>aspose-cells</artifactId>
            <version>20.7</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/aspose-cells-20.7.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>RELEASE</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>33.2.1-jre</version>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
            <version>2.3.12.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.rabbitmq</groupId>
            <artifactId>amqp-client</artifactId>
            <version>5.14.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.ssssssss</groupId>
            <artifactId>magic-api</artifactId>
            <version>2.1.1</version>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
