package com.jp.med.gateway.config.security.manager;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.util.RedisUtil;
import com.jp.med.gateway.constans.AuthConst;
import com.jp.med.gateway.service.UserDetailsServiceImpl;
import com.jp.med.gateway.util.JwtTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.authorization.ReactiveAuthorizationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.web.server.authorization.AuthorizationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/17 15:48
 * @description:
 */
@Slf4j
@Component
public class DefaultAuthorizationManager implements ReactiveAuthorizationManager<AuthorizationContext> {

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Override
    public Mono<AuthorizationDecision> check(Mono<Authentication> authentication, AuthorizationContext authorizationContext) {

        return authentication.map(auth -> {
            // 判断是否有用户信息，如果没有则认为没有登录或已退出登录，当前已修改为token判断，如果单点登录可修改为用户名判断
            if (ObjectUtil.isEmpty(RedisUtil.get(jwtTokenUtil.getRealToken(authorizationContext.getExchange())))) {
                return new AuthorizationDecision(false);
            }
            ServerWebExchange exchange = authorizationContext.getExchange();
            ServerHttpRequest request = exchange.getRequest();
            String path = request.getURI().getPath();
            request.mutate().header(MedConst.HEADER_SYS_KEY, getSysIdentity(path));
            return new AuthorizationDecision(true);
        }).defaultIfEmpty(new AuthorizationDecision(false));
    }

    @Override
    public Mono<Void> verify(Mono<Authentication> authentication, AuthorizationContext object) {
        return check(authentication, object)
                .filter(AuthorizationDecision::isGranted)
                .switchIfEmpty(Mono.defer(() -> {
                    String body = JSONObject.toJSONString(CommonResult.failed(AuthConst.PERMISSION_DENIED));
                    return Mono.error(new AccessDeniedException(body));
                })).flatMap(d -> Mono.empty());
    }

    /**
     * 获取系统标识
     *
     * @param path 请求地址
     * @return
     */
    private String getSysIdentity(String path) {
        if (path.contains(MedConst.SYS_IDENTITY_BMS)) {
            return MedConst.SYS_IDENTITY_BMS;
        } else if (path.contains(MedConst.SYS_IDENTITY_HRM)) {
            return MedConst.SYS_IDENTITY_HRM;
        } else if (path.contains(MedConst.SYS_IDENTITY_AMS)) {
            return MedConst.SYS_IDENTITY_AMS;
        } else if (path.contains(MedConst.SYS_IDENTITY_COST)) {
            return MedConst.SYS_IDENTITY_COST;
        }
        return "";
    }

    /**
     * 检查权限（未完全做）
     * 最优做法sys_menu的url配置的是页面的url，但是request只能获取到请求参数，所以要做到统一后台权限控制可以
     * 1.sys_menu的perms换成角色名称（因为当前auth的getAuthorities设置的就是角色名称）
     * 2.每次请求后台时根据页面url和账号匹配功能是否具有权限
     * 如果按照1和2做会有个问题，就是页面的按钮权限无法做，所以需要第三步分开精确控制权限
     * 3.精确控制权限使用注解解决（自定义）
     *
     * @param auth 认证信息
     * @param path 请求路径
     * @return
     */
    private boolean checkPath(Authentication auth, String path) {
//        CommonFeignDto feignDto = CommonFeignDto.build();
//        feignDto.put("username", auth.getPrincipal());
//        feignDto.put("url", path);
//        //TODO 每次请求都去查询的权限，后面放入redis，结合前端一起更改
//        CommonFeignResult result = userDetailsService.userFeignService.queryUserAuthByUsername(feignDto);
//        List<String> roles = (List<String>) result.get(CommonFeignResult.DATA_KEY);
//        // 只需要判断能不能查到role就行，因为sql写的时候就按照url和用户进行查询的
//        if (roles.size() > 0) {
//            return true;
//        }
        return true;
    }
}
