package com.jp.med.mmis.modules.exportFileManage.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.mmis.modules.exportFileManage.mapper.read.MmisExportAttachmentReadMapper;
import com.jp.med.mmis.modules.exportFileManage.dto.MmisExportAttachmentDto;
import com.jp.med.mmis.modules.exportFileManage.vo.MmisExportAttachmentVo;
import com.jp.med.mmis.modules.exportFileManage.service.read.MmisExportAttachmentReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class MmisExportAttachmentReadServiceImpl extends ServiceImpl<MmisExportAttachmentReadMapper, MmisExportAttachmentDto> implements MmisExportAttachmentReadService {

    @Autowired
    private MmisExportAttachmentReadMapper mmisExportAttachmentReadMapper;

    @Override
    public List<MmisExportAttachmentVo> queryList(MmisExportAttachmentDto dto) {
        return mmisExportAttachmentReadMapper.queryList(dto);
    }

    @Override
    public List<MmisExportAttachmentVo> queryPageList(MmisExportAttachmentDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return mmisExportAttachmentReadMapper.queryList(dto);
    }

}
