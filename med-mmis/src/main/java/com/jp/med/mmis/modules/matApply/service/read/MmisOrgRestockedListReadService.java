package com.jp.med.mmis.modules.matApply.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.mmis.modules.matApply.dto.MmisOrgRestockedListDto;
import com.jp.med.mmis.modules.matApply.vo.MmisOrgRestockedListVo;

import java.util.List;

/**
 * 科室待补货清单
 * <AUTHOR>
 * @email -
 * @date 2024-10-28 15:23:58
 */
public interface MmisOrgRestockedListReadService extends IService<MmisOrgRestockedListDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<MmisOrgRestockedListVo> queryList(MmisOrgRestockedListDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<MmisOrgRestockedListVo> queryPageList(MmisOrgRestockedListDto dto);

    List<MmisOrgRestockedListVo> queryPageAllList(MmisOrgRestockedListDto dto);

    List<MmisOrgRestockedListVo> noPageListNoOrg(MmisOrgRestockedListDto dto);
}

