package com.jp.med.mmis.modules.useCodeCfg.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.DateUtil;
import com.jp.med.mmis.modules.useCodeCfg.dto.MmisInvTypeCfgDto;
import com.jp.med.mmis.modules.useCodeCfg.mapper.read.MmisInvTypeCfgReadMapper;
import com.jp.med.mmis.modules.useCodeCfg.mapper.write.MmisInvTypeCfgWriteMapper;
import com.jp.med.mmis.modules.useCodeCfg.service.write.MmisInvTypeCfgWriteService;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 入出库类型配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-01-24 17:26:53
 */
@Service
@Transactional(readOnly = false)
public class MmisInvTypeCfgWriteServiceImpl extends ServiceImpl<MmisInvTypeCfgWriteMapper, MmisInvTypeCfgDto> implements MmisInvTypeCfgWriteService {

    @Autowired
    private MmisInvTypeCfgWriteMapper typeCfgWriteMapper;

    @Autowired
    private MmisInvTypeCfgReadMapper typeCfgReadMapper;

    @Override
    public void saveDto(MmisInvTypeCfgDto dto) {
        //判断类型
        // 字典1入，0出
        if ("1".equals(dto.getInvType())) {
            String maxRcode = typeCfgReadMapper.queryMaxRCcode("R");
            if (!Objects.isNull(maxRcode)) {
                String newRcode = maxRcode.substring(1);
                Integer newCode = Integer.parseInt(newRcode) + 1;
                dto.setTypeCode("R" + newCode.toString());
            } else {
                dto.setTypeCode("R1");
            }
        } else {
            String maxCcode = typeCfgReadMapper.queryMaxRCcode("C");
            if (!Objects.isNull(maxCcode)) {
                String newCcode = maxCcode.substring(1);
                Integer nCode = Integer.parseInt(newCcode) + 1;
                dto.setTypeCode("C" + nCode.toString());
            } else {
                dto.setTypeCode("C1");
            }
        }
        dto.setHospitalId(dto.getHospitalId());
        //设置提出人的信息
        if (dto.getSysUser() != null) {
            if (MedConst.DEV_NAME.equals(dto.getSysUser().getUsername())) {
                //如果是管理员新增提出需求，则empid设为null
                dto.setCter(null);
            } else {
                dto.setCter(dto.getSysUser().getUsername());
            }
        }
        dto.setCreateTime(DateUtil.getCurrentTime("yyyy-MM-dd HH:mm:ss"));
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_0);
        typeCfgWriteMapper.insert(dto);
    }

    @Override
    public void removeDtoById(MmisInvTypeCfgDto dto) {
//设置提出人的信息
        if (dto.getSysUser() != null) {
            if (MedConst.DEV_NAME.equals(dto.getSysUser().getUsername())) {
                //如果是管理员新增提出需求，则empid设为null
                dto.setDelter(null);
            } else {
                dto.setDelter(dto.getSysUser().getUsername());
            }
        }
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        dto.setDeleteTime(DateUtil.getCurrentTime("yyyy-MM-dd HH:mm:ss"));
        typeCfgWriteMapper.updateById(dto);
    }

    @Override
    public void updateDtoById(MmisInvTypeCfgDto dto) {
        //设置提出人的信息
        if (dto.getSysUser() != null) {
            if (MedConst.DEV_NAME.equals(dto.getSysUser().getUsername())) {
                //如果是管理员新增提出需求，则empid设为null
                dto.setUpdtr(null);
            } else {
                dto.setUpdtr(dto.getSysUser().getUsername());
            }
        }
        dto.setUpdateTime(DateUtil.getCurrentTime("yyyy-MM-dd HH:mm:ss"));
        typeCfgWriteMapper.updateById(dto);
    }
}
