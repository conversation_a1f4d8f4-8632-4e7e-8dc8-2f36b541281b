package com.jp.med.mmis.modules.wrhsManagers.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.mmis.modules.wrhsManagers.dto.MmisWarehouseManagerMappingDto;
import com.jp.med.mmis.modules.wrhsManagers.service.read.MmisWarehouseManagerMappingReadService;
import com.jp.med.mmis.modules.wrhsManagers.service.write.MmisWarehouseManagerMappingWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 仓库代码与管理人员对应关系表
 * <AUTHOR>
 * @email -
 * @date 2025-02-25 20:48:33
 */
@Api(value = "仓库代码与管理人员对应关系表", tags = "仓库代码与管理人员对应关系表")
@RestController
@RequestMapping("mmisWarehouseManagerMapping")
public class MmisWarehouseManagerMappingController {

    @Autowired
    private MmisWarehouseManagerMappingReadService mmisWarehouseManagerMappingReadService;

    @Autowired
    private MmisWarehouseManagerMappingWriteService mmisWarehouseManagerMappingWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询仓库代码与管理人员对应关系表")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody MmisWarehouseManagerMappingDto dto){
        return CommonResult.paging(mmisWarehouseManagerMappingReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询仓库代码与管理人员对应关系表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody MmisWarehouseManagerMappingDto dto){
        return CommonResult.success(mmisWarehouseManagerMappingReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增仓库代码与管理人员对应关系表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody MmisWarehouseManagerMappingDto dto){
        mmisWarehouseManagerMappingWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改仓库代码与管理人员对应关系表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody MmisWarehouseManagerMappingDto dto){
        mmisWarehouseManagerMappingWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除仓库代码与管理人员对应关系表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody MmisWarehouseManagerMappingDto dto){
        mmisWarehouseManagerMappingWriteService.removeById(dto);
        return CommonResult.success();
    }

}
