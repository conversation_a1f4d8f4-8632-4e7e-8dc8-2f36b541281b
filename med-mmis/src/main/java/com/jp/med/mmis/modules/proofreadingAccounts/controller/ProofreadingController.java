package com.jp.med.mmis.modules.proofreadingAccounts.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.mmis.modules.proofreadingAccounts.dto.ProofreadingRequestDTO;
import com.jp.med.mmis.modules.proofreadingAccounts.dto.ProofreadingResultDTO;
import com.jp.med.mmis.modules.proofreadingAccounts.service.ProofreadingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 物资对账功能控制器
 * 
 * <AUTHOR>
 * @date 2023-03-25
 */
@Api(value = "物资对账功能", tags = "物资对账功能")
@RestController
@RequestMapping("/proofreading")
public class ProofreadingController {

	@Autowired
	private ProofreadingService proofreadingService;

	/**
	 * 执行对账操作
	 * 
	 * @param request 对账请求参数，包含期号、物资唯一码列表等
	 * @return 对账结果，包含差异记录和统计信息
	 */
	@ApiOperation("执行物资对账操作")
	@PostMapping("/execute")
	public CommonResult<ProofreadingResultDTO> executeProofreading(@RequestBody ProofreadingRequestDTO request) {
		// 参数校验
		if (request.getSettlePeriodNum() == null || request.getSettlePeriodNum().isEmpty()) {
			return CommonResult.failed("期号不能为空");
		}

		// 执行对账操作
		ProofreadingResultDTO result = proofreadingService.executeProofreading(request);
		return CommonResult.success(result);
	}

	/**
	 * 手动备份当前库存数据
	 * 
	 * @param request 备份请求参数，包含备份类型和期号
	 * @return 备份结果
	 */
	@ApiOperation("手动备份当前库存数据")
	@PostMapping("/backup")
	public CommonResult<?> backupCurrentStock(@RequestBody ProofreadingRequestDTO request) {
		// 参数校验
		if (request.getBackupType() == null || request.getBackupType().isEmpty()) {
			return CommonResult.failed("备份类型不能为空");
		}
		if (request.getSettlePeriodNum() == null || request.getSettlePeriodNum().isEmpty()) {
			return CommonResult.failed("期号不能为空");
		}

		// 执行备份操作
		proofreadingService.backupCurrentStock(request.getBackupType(), request.getSettlePeriodNum());
		return CommonResult.success();
	}

	/**
	 * 查询对账差异数据
	 * 
	 * @param request 查询请求参数，包含期号和物资唯一码列表
	 * @return 差异数据列表
	 */
	@ApiOperation("查询对账差异数据")
	@PostMapping("/calculateDifference")
	public CommonResult<?> calculateDifference(@RequestBody ProofreadingRequestDTO request) {
		// 参数校验
		if (request.getSettlePeriodNum() == null || request.getSettlePeriodNum().isEmpty()) {
			return CommonResult.failed("期号不能为空");
		}
		if (request.getMatUniqueCodes() == null || request.getMatUniqueCodes().isEmpty()) {
			return CommonResult.failed("物资唯一码列表不能为空");
		}

		// 计算差异
		return CommonResult.success(proofreadingService.calculateDifference(
				request.getMatUniqueCodes(), request.getSettlePeriodNum()));
	}

	/**
	 * 更新库存数据（根据对账结果）
	 * 
	 * @param request 更新请求参数，包含需要更新的物资信息
	 * @return 更新结果
	 */
	@ApiOperation("更新库存数据")
	@PostMapping("/updateStock")
	public CommonResult<?> updateStockData(@RequestBody ProofreadingResultDTO request) {
		// 参数校验
		if (request.getItems() == null || request.getItems().isEmpty()) {
			return CommonResult.failed("更新项不能为空");
		}

		// 执行更新操作
		proofreadingService.updateStockData(request.getItems());
		return CommonResult.success();
	}

	/**
	 * 查询物资库存对账差异
	 * 基于库存查询和对账差异计算，返回完整的对账结果
	 * 
	 * @param request 查询请求参数，包含仓库代码、库位代码等条件
	 * @return 对账差异结果
	 */
	@ApiOperation("查询物资库存对账差异")
	@PostMapping("/queryStockDifference")
	public CommonResult<ProofreadingResultDTO> queryStockDifference(@RequestBody ProofreadingRequestDTO request) {
		// 参数校验 - 不再要求期号必填
//		if (request.getWrhsCode() == null && request.getWrhsAddr() == null
//				&& request.getItemNum() == null && request.getName() == null) {
//			return CommonResult.failed("请至少提供一个查询条件：仓库代码、库位代码、物资编码或物资名称");
//		}

		try {
			// 调用服务层执行查询
			ProofreadingResultDTO result = proofreadingService.queryStockDifference(request);
			return CommonResult.success(result);
		} catch (Exception e) {
			return CommonResult.failed(e.getMessage());
		}
	}
}