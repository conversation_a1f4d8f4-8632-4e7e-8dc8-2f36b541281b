# 物资对账功能开发规划文档 📋

## 一、功能概述 🎯

### 1.1 功能描述

物资对账功能用于核对和修正物资库存数据，通过比对基准表（mmis_last_stock_temp）和出入库记录，计算实际库存数据，并更新物资库存表（mmis_material_sum）。

### 1.2 业务流程

1. 接收前端传入的物资唯一编码集合
2. 备份当前物资库存表数据
3. 根据唯一编码查询相关出入库记录
4. 基于基准表数据和出入库记录计算实际库存
5. 更新物资库存表数据

### 1.3 核心接口说明

#### 1.3.1 出库记录查询接口

- 接口路径：`/mmisOutboundApply/queryOutBoundApplyDetailsBy2Code`
- 请求方式：POST
- 功能描述：根据物资编号或唯一编号查询出库明细
- 查询条件：
  - 出库状态：已出库或已确认
  - 审核状态：已审核
  - 未删除记录
- 返回字段：
  - 出库明细信息（ID、数量、单价等）
  - 出库单信息（单据号、申请人、时间等）

#### 1.3.2 入库记录查询接口

- 接口路径：`/mmisAsetStorage/queryStorageRecordsByMat2Code`
- 请求方式：POST
- 功能描述：根据物资编号或唯一编号查询入库明细
- 查询条件：
  - 入库状态：已入库
  - 审核状态：已审核
  - 未删除记录
- 返回字段：
  - 入库明细信息（ID、数量、单价等）
  - 入库单信息（单据号、供应商、时间等）

## 二、开发规划 📅

### 2.1 数据库设计阶段

- [ ] 创建物资库存备份表（mmis_material_sum_backup）
  - 字段设计：
    - id：主键
    - backup_time：备份时间
    - backup_type：备份类型
    - settle_period_num：结账期号
    - item_num：货号
    - num：数量
    - name：物资名称
    - modspec：规格
    - wrhs_addr：库位地址
    - wrhs_code：仓库代码
    - meter_code：计量方式
    - price：单价
    - amt：金额
    - mat_unique_code：物资唯一编号
    - crter：创建人
    - create_time：创建时间
    - hospital_id：医院 ID
    - is_deleted：删除标记
  - 索引设计：
    - settle_period_num_idx：结账期号索引
    - hospital_id_idx：医院 ID 索引
    - backup_time_idx：备份时间索引
- [ ] 编写备份表相关 SQL 脚本
- [ ] 编写测试用例：备份表创建和数据插入测试

### 2.2 后端开发阶段

#### 2.2.1 实体类开发

- [ ] 创建 MaterialSumBackup 实体类
  - 包含所有备份表字段
  - 添加字段注释
  - 实现序列化接口
- [ ] 创建 MaterialSum 实体类
  - 包含库存表字段
  - 添加字段注释
  - 实现序列化接口
- [ ] 创建 DTO 类
  - ProofreadingRequestDTO：对账请求参数
  - ProofreadingResponseDTO：对账响应结果
  - BackupRequestDTO：备份请求参数
  - BackupResponseDTO：备份响应结果
- [ ] 编写测试用例：实体类序列化和反序列化测试

#### 2.2.2 Service 层开发

- [ ] 创建 MaterialSumBackupService 接口
  - saveBackup：保存备份数据
  - queryBackup：查询备份数据
  - deleteBackup：删除备份数据
- [ ] 实现 MaterialSumBackupServiceImpl 类
  - 实现备份相关业务逻辑
  - 添加事务管理
  - 添加异常处理
- [ ] 创建 MaterialSumService 接口
  - updateStock：更新库存数据
  - calculateStock：计算库存数据
  - validateStock：验证库存数据
- [ ] 实现 MaterialSumServiceImpl 类
  - 实现库存相关业务逻辑
  - 添加事务管理
  - 添加异常处理
- [ ] 编写测试用例：Service 层业务逻辑测试

#### 2.2.3 Controller 层开发

- [ ] 创建 MaterialSumController 类
  - 添加接口文档注解
  - 添加参数验证
  - 添加权限控制
- [ ] 实现对账接口
  - 路径：/proofreading/execute
  - 功能：执行对账操作
  - 参数：物资唯一编码列表
  - 返回：对账结果
- [ ] 实现备份接口
  - 路径：/proofreading/backup
  - 功能：备份库存数据
  - 参数：备份参数
  - 返回：备份结果
- [ ] 编写测试用例：接口功能测试

#### 2.2.4 Mapper 层开发

- [ ] 创建 MaterialSumBackupMapper 接口
  - 定义备份相关 SQL 方法
  - 添加方法注释
- [ ] 创建 MaterialSumMapper 接口
  - 定义库存相关 SQL 方法
  - 添加方法注释
- [ ] 编写 XML 映射文件
  - 实现 SQL 语句
  - 添加动态 SQL
  - 优化查询性能
- [ ] 编写测试用例：SQL 语句测试

### 2.3 业务逻辑实现

- [ ] 实现数据备份逻辑
  - 获取当前库存数据
  - 生成备份记录
  - 保存备份数据
- [ ] 实现出入库查询逻辑
  - 调用出库记录查询接口
  - 调用入库记录查询接口
  - 合并查询结果
- [ ] 实现库存计算逻辑
  - 基于基准表数据
  - 计算出入库差额
  - 更新库存数据
- [ ] 实现数据更新逻辑
  - 验证更新数据
  - 执行更新操作
  - 记录更新日志
- [ ] 编写测试用例：完整业务流程测试

### 2.4 异常处理

- [ ] 定义业务异常类
  - ProofreadingException：对账异常
  - BackupException：备份异常
  - ValidationException：数据验证异常
- [ ] 实现全局异常处理
  - 统一异常处理
  - 异常日志记录
  - 友好错误提示
- [ ] 编写测试用例：异常处理测试

### 2.5 日志记录

- [ ] 实现操作日志记录
  - 记录对账操作
  - 记录备份操作
  - 记录更新操作
- [ ] 实现错误日志记录
  - 记录异常信息
  - 记录错误堆栈
  - 记录操作上下文
- [ ] 编写测试用例：日志记录测试

## 三、开发规范 📚

### 3.1 代码规范

1. 遵循阿里巴巴 Java 开发手册
2. 使用统一的代码格式化工具
3. 编写详细的代码注释
4. 遵循 RESTful API 设计规范
5. 使用统一的命名规范
6. 保持代码简洁清晰

### 3.2 测试规范

1. 每个功能模块必须编写单元测试
2. 测试用例必须覆盖正常流程和异常流程
3. 测试结果必须记录在测试文档中
4. 测试覆盖率要求达到 80%以上
5. 性能测试要求响应时间小于 1 秒
6. 并发测试要求支持 100 用户同时操作

### 3.3 文档规范

1. 及时更新开发文档
2. 记录重要的业务决策
3. 记录已知问题和解决方案
4. 编写详细的接口文档
5. 编写部署文档
6. 编写运维手册

## 四、TODO 清单 📝

### 4.1 数据库设计

- [ ] 创建备份表
- [ ] 设计索引
- [ ] 编写 SQL 脚本
- [ ] 编写测试用例
- [ ] 优化表结构
- [ ] 添加数据约束

### 4.2 后端开发

- [ ] 创建实体类
- [ ] 实现 Service 层
- [ ] 实现 Controller 层
- [ ] 实现 Mapper 层
- [ ] 编写测试用例
- [ ] 优化代码结构
- [ ] 添加性能优化

### 4.3 业务实现

- [ ] 实现数据备份
- [ ] 实现出入库查询
- [ ] 实现库存计算
- [ ] 实现数据更新
- [ ] 编写测试用例
- [ ] 优化业务逻辑
- [ ] 添加数据验证

### 4.4 异常处理

- [ ] 实现异常处理
- [ ] 编写测试用例
- [ ] 优化错误提示
- [ ] 完善异常日志

### 4.5 日志记录

- [ ] 实现日志记录
- [ ] 编写测试用例
- [ ] 优化日志格式
- [ ] 添加日志分析

## 五、测试计划 🧪

### 5.1 单元测试

- [ ] 实体类测试
- [ ] Service 层测试
- [ ] Controller 层测试
- [ ] Mapper 层测试
- [ ] 工具类测试
- [ ] 异常处理测试

### 5.2 集成测试

- [ ] 接口测试
- [ ] 数据库操作测试
- [ ] 异常处理测试
- [ ] 性能测试
- [ ] 并发测试
- [ ] 压力测试

### 5.3 性能测试

- [ ] 并发测试
- [ ] 大数据量测试
- [ ] 响应时间测试
- [ ] 资源占用测试
- [ ] 稳定性测试
- [ ] 压力测试

## 六、部署计划 🚀

### 6.1 环境准备

- [ ] 准备测试环境
- [ ] 准备预发布环境
- [ ] 准备生产环境
- [ ] 配置环境变量
- [ ] 准备部署脚本
- [ ] 准备监控工具

### 6.2 部署步骤

- [ ] 数据库脚本部署
- [ ] 应用部署
- [ ] 配置更新
- [ ] 服务启动
- [ ] 健康检查
- [ ] 性能监控

### 6.3 回滚计划

- [ ] 准备回滚脚本
- [ ] 制定回滚流程
- [ ] 测试回滚方案
- [ ] 准备应急预案
- [ ] 制定恢复策略
- [ ] 记录回滚日志

## 七、注意事项 ⚠️

1. 每个开发阶段完成后，必须更新 TODO 清单
2. 所有测试用例必须记录测试结果
3. 重要决策必须记录在文档中
4. 代码提交前必须进行代码审查
5. 定期进行代码合并和冲突解决
6. 保持代码版本控制
7. 注意数据安全性
8. 关注系统性能
9. 做好文档维护
10. 保持团队沟通

## 八、联系方式 📞

- 项目负责人：[待补充]
- 技术负责人：[待补充]
- 测试负责人：[待补充]
- 运维负责人：[待补充]
- 产品负责人：[待补充]
