package com.jp.med.mmis.modules.integration.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.Date;

import lombok.Data;

/**
 * M1003物资出库接口数据备份表(用于撤销操作)
 * 
 * <AUTHOR>
 * @email -
 * @date 2025-04-21 11:35:22
 */
@Data
public class MmisOutboundBackupVo {

    /** $column.comments */
    private Integer id;

    /** 工单ID，对应HRP系统工单 */
    private String workId;

    /** 工单编码 */
    private String workCode;

    /** 医院ID */
    private String hospitalId;

    /** 来源系统 */
    private String sourceSystem;

    /** 生成的出库单号 */
    private String outboundId;

    /** 系统内部出库申请ID，关联mmis_outbound_apply表 */
    private Integer applyId;

    /** 请求时间 */
    private Date requestTime;

    /** 状态：ACTIVE(生效)|REVOKED(已撤销) */
    private String status;

    /** 完整物资列表JSON数据 */
    private JsonNode consumableList;

    /** 撤销时间 */
    private Date revokeTime;

    /** 撤销原因 */
    private String revokeReason;

    /** 撤销操作人 */
    private String revokeOperator;

    /** 处理结果：SUCCESS|FAILED */
    private String processResult;

    /** 错误信息 */
    private String errorMessage;

}
