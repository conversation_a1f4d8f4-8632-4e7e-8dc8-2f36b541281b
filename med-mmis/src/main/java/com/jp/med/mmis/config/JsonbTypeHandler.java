package com.jp.med.mmis.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * JsonNode类型处理器，用于处理PostgreSQL的jsonb类型与Java的JsonNode类型的互相转换
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
@MappedTypes(JsonNode.class)
@MappedJdbcTypes(JdbcType.OTHER)
public class JsonbTypeHandler extends BaseTypeHandler<JsonNode> {

	private static final ObjectMapper objectMapper = new ObjectMapper();

	@Override
	public void setNonNullParameter(PreparedStatement ps, int i, JsonNode parameter, JdbcType jdbcType)
			throws SQLException {
		PGobject jsonObject = new PGobject();
		jsonObject.setType("jsonb");
		try {
			jsonObject.setValue(objectMapper.writeValueAsString(parameter));
		} catch (JsonProcessingException e) {
			throw new SQLException("Error processing JSONB", e);
		}
		ps.setObject(i, jsonObject);
	}

	@Override
	public JsonNode getNullableResult(ResultSet rs, String columnName) throws SQLException {
		String json = rs.getString(columnName);
		if (json == null) {
			return null;
		}
		try {
			return objectMapper.readValue(json, JsonNode.class);
		} catch (JsonProcessingException e) {
			throw new SQLException("Error parsing JSONB", e);
		}
	}

	@Override
	public JsonNode getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
		String json = rs.getString(columnIndex);
		if (json == null) {
			return null;
		}
		try {
			return objectMapper.readValue(json, JsonNode.class);
		} catch (Exception e) {
			throw new SQLException("Error parsing JSONB", e);
		}
	}

	@Override
	public JsonNode getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
		String json = cs.getString(columnIndex);
		if (json == null) {
			return null;
		}
		try {
			return objectMapper.readValue(json, JsonNode.class);
		} catch (Exception e) {
			throw new SQLException("Error parsing JSONB", e);
		}
	}
}