package com.jp.med.mmis.modules.matBack.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.mmis.modules.matBack.dto.MmisBackApplyDetailDto;
import com.jp.med.mmis.modules.matBack.service.read.MmisBackApplyDetailReadService;
import com.jp.med.mmis.modules.matBack.service.write.MmisBackApplyDetailWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 物资退货申请详情
 * <AUTHOR>
 * @email -
 * @date 2024-10-21 21:41:31
 */
@Api(value = "物资退货申请详情", tags = "物资退货申请详情")
@RestController
@RequestMapping("mmisBackApplyDetail")
public class MmisBackApplyDetailController {

    @Autowired
    private MmisBackApplyDetailReadService mmisBackApplyDetailReadService;

    @Autowired
    private MmisBackApplyDetailWriteService mmisBackApplyDetailWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询物资退货申请详情")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody MmisBackApplyDetailDto dto){
        return CommonResult.paging(mmisBackApplyDetailReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询物资退货申请详情")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody MmisBackApplyDetailDto dto){
        return CommonResult.success(mmisBackApplyDetailReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增物资退货申请详情")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody MmisBackApplyDetailDto dto){
        mmisBackApplyDetailWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改物资退货申请详情")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody MmisBackApplyDetailDto dto){
        mmisBackApplyDetailWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除物资退货申请详情")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody MmisBackApplyDetailDto dto){
        mmisBackApplyDetailWriteService.removeById(dto);
        return CommonResult.success();
    }

}
