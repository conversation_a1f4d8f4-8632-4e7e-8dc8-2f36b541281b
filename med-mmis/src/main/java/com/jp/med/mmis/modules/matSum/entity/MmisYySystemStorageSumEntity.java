package com.jp.med.mmis.modules.matSum.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2025-01-02 21:47:03
 */
@Data
@TableName("mmis_yy_system_storage_sum")
public class MmisYySystemStorageSumEntity {

	/** 部门代码 */
	@TableId("org_id")
	private String orgId;

	/** 部门名称 */
	@TableField("org_name")
	private String orgName;

	/** 仓库代码 */
	@TableField("wscode")
	private String wscode;

	/** 仓库名称 */
	@TableField("wsname")
	private String wsname;

	/** 合计 */
	@TableField("sum")
	private BigDecimal sum;

	/** 地址易耗品 */
	@TableField("lowmat")
	private BigDecimal lowmat;

	/** 其他材料 */
	@TableField("othermat")
	private BigDecimal othermat;

	/** 一般设备 */
	@TableField("normalmat")
	private BigDecimal normalmat;

	/** 专用设备 */
	@TableField("specmat")
	private BigDecimal specmat;

	/** 创建时间 */
	@TableField("create_time")
	private String createTime;



	@TableField("hospital_id")
	private String hospitalId;

	@TableField(exist = false)
	private String month;

	@TableField(exist =false)
	private String startTime;

	@TableField(exist =false)
	private String endTime;
}
