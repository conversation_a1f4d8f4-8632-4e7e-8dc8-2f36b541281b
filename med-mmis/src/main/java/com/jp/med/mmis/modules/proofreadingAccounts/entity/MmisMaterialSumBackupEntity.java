package com.jp.med.mmis.modules.proofreadingAccounts.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 物资库存备份表，用于存储物资库存数据的备份记录
 * <AUTHOR>
 * @email -
 * @date 2025-03-25 17:21:36
 */
@Data
@TableName("mmis_material_sum_backup")
public class MmisMaterialSumBackupEntity {

	/** 主键ID */
	@TableId("id")
	private Integer id;

	/** 备份时间，格式：yyyy-MM-dd HH:mm:ss */
	@TableField("backup_time")
	private String backupTime;

	/** 备份类型（对账前备份/手动备份/系统备份等） */
	@TableField("backup_type")
	private String backupType;

	/** 期号（与对账期号对应，格式：yyyyMM） */
	@TableField("settle_period_num")
	private String settlePeriodNum;

	/** 物资编码，关联物资主数据 */
	@TableField("item_num")
	private String itemNum;

	/** 数量 */
	@TableField("num")
	private BigDecimal num;

	/** 物资名称 */
	@TableField("name")
	private String name;

	/** 规格型号 */
	@TableField("modspec")
	private String modspec;

	/** 库位代码，关联库位表 */
	@TableField("wrhs_addr")
	private String wrhsAddr;

	/** 仓库代码，关联仓库表 */
	@TableField("wrhs_code")
	private String wrhsCode;

	/** 计量单位代码，关联计量单位表 */
	@TableField("meter_code")
	private String meterCode;

	/** 单价，单位：元 */
	@TableField("price")
	private BigDecimal price;

	/** 金额，单位：元 */
	@TableField("amt")
	private BigDecimal amt;

	/** 物资唯一码，用于物资唯一性标识 */
	@TableField("mat_unique_code")
	private String matUniqueCode;

	/** 创建人，记录操作人 */
	@TableField("crter")
	private String crter;

	/** 创建时间，格式：yyyy-MM-dd HH:mm:ss */
	@TableField("create_time")
	private String createTime;

	/** 医院ID，用于多医院数据隔离 */
	@TableField("hospital_id")
	private String hospitalId;

	/** 逻辑删除标志：0-未删除，1-已删除 */
	@TableField("is_deleted")
	private Integer isDeleted;

}
