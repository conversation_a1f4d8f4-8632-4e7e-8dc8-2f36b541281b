package com.jp.med.mmis.modules.integration.service.impl.read;

import com.jp.med.mmis.modules.integration.dto.ConsumableDTO;
import com.jp.med.mmis.modules.integration.dto.ConsumableTypeDTO;
import com.jp.med.mmis.modules.integration.mapper.read.ConsumableIntegrationReadMapper;
import com.jp.med.mmis.modules.integration.service.read.ConsumableIntegrationReadService;
import com.jp.med.mmis.modules.integration.dto.MmisOutboundBackupDto;
import com.jp.med.mmis.modules.integration.vo.MmisOutboundBackupVo;
import com.jp.med.mmis.modules.integration.service.read.MmisOutboundBackupReadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 物资对接读服务实现类
 */
@Service
@Slf4j
public class ConsumableIntegrationReadServiceImpl implements ConsumableIntegrationReadService {

	@Autowired
	private ConsumableIntegrationReadMapper readMapper;

	@Autowired
	private MmisOutboundBackupReadService outboundBackupReadService;

	/**
	 * 获取所有耗材分类
	 * 
	 * @param hospitalId 医院ID
	 * @return 耗材分类列表
	 */
	@Override
	public List<ConsumableTypeDTO> getAllConsumableTypes(String hospitalId) {
		log.info("获取医院[{}]的所有物资分类数据", hospitalId);
		return readMapper.getAllConsumableTypes(hospitalId);
	}

	/**
	 * 获取所有耗材项
	 * 
	 * @param hospitalId 医院ID
	 * @return 耗材项列表
	 */
	@Override
	public List<ConsumableDTO> getAllConsumables(String hospitalId) {
		log.info("获取医院[{}]的所有物资项目数据", hospitalId);
		return readMapper.getAllConsumables(hospitalId);
	}

	@Override
	public List<Map<String, Object>> getPendingConfirmationWorkIds() {
		log.info("获取待确认出库的工单列表 (workCode, outboundOrderNumber)");
		List<Map<String, Object>> result = readMapper.selectActivePreOutboundWorkIdsFromStaging();
		log.info("待确认工单列表查询结果数量: {}", result != null ? result.size() : 0);
		return result;
	}

	@Override
	public List<Map<String, Object>> getRevocableWorkIds() {
		log.info("获取所有可撤销的工单列表（包括预出库和已确认出库状态）");
		List<Map<String, Object>> result = readMapper.selectRevocableWorkIds();
		log.info("可撤销工单列表查询结果数量: {}", result != null ? result.size() : 0);
		return result;
	}

	@Override
	public List<MmisOutboundBackupVo> queryOutboundBackup(Integer id, Integer applyId, String workId) {
		log.info("查询出库备份记录: id={}, applyId={}, workId={}", id, applyId, workId);
		MmisOutboundBackupDto queryDto = new MmisOutboundBackupDto();
		queryDto.setId(id);
		queryDto.setApplyId(applyId);
		queryDto.setWorkId(workId);
		return outboundBackupReadService.queryList(queryDto);
	}

	@Override
	public boolean checkWorkIdExistsInStaging(String workId) {
		log.debug("检查work_id在staging表中是否存在: workId={}", workId);
		return readMapper.checkWorkIdExistsInStaging(workId);
	}

	@Override
	public boolean checkWorkIdExistsInBackup(String workId) {
		log.debug("检查work_id在backup表中是否存在: workId={}", workId);
		return readMapper.checkWorkIdExistsInBackup(workId);
	}

	@Override
	public String getStagingStatusByWorkId(String workId) {
		log.debug("查询work_id在staging表中的状态: workId={}", workId);
		return readMapper.getStagingStatusByWorkId(workId);
	}

	@Override
	public String getBackupStatusByWorkId(String workId) {
		log.debug("查询work_id在backup表中的状态: workId={}", workId);
		return readMapper.getBackupStatusByWorkId(workId);
	}
}