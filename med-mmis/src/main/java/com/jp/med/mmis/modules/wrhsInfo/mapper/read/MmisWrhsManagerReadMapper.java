package com.jp.med.mmis.modules.wrhsInfo.mapper.read;

import com.jp.med.mmis.modules.wrhsInfo.dto.MmisWrhsManagerDto;
import com.jp.med.mmis.modules.wrhsInfo.vo.MmisWrhsManagerVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 物资仓库管理员信息
 * <AUTHOR>
 * @email -
 * @date 2024-05-20 10:09:38
 */
@Mapper
public interface MmisWrhsManagerReadMapper extends BaseMapper<MmisWrhsManagerDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<MmisWrhsManagerVo> queryList(MmisWrhsManagerDto dto);

	List<MmisWrhsManagerVo> queryManagers(String wrhsCode);
}
