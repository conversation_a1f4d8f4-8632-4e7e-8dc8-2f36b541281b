package com.jp.med.mmis.modules.asetInfo.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;

import lombok.Data;

/**
 * 物资资料
 *
 * <AUTHOR>
 * @email -
 * @date 2024-01-25 17:10:46
 */
@Data
@TableName("mmis_aset_info")
public class MmisAsetInfoEntity {

    /**
     * $column.comments
     */
    @TableId("id")
    private Integer id;

    /**
     * 物资代码
     */
    @TableField("code")
    private String code;

    /**
     * 物资名称
     */
    @TableField("name")
    private String name;

    /**
     * 助记码
     */
    @TableField("easy_code")
    private String easyCode;

    /**
     * 物资类别
     */
    @TableField("aset_type")
    private String asetType;

    /**
     * 计量方式
     */
    @TableField("mtr_type")
    private String mtrType;

    /**
     * 计量单位
     */
    @TableField("mtr_unit")
    private String mtrUnit;

    /**
     * 型号规格
     */
    @TableField("modspec")
    private String modspec;

    /**
     * 产地
     */
    @TableField("orplc")
    private String orplc;

    /**
     * 生产厂商
     */
    @TableField("prdr")
    private String prdr;

    /**
     * 物资别名
     */
    @TableField("aset_alis")
    private String asetAlis;

    /**
     * 物资条码
     */
    @TableField("aset_barc")
    private String asetBarc;

    /**
     * 物资品牌
     */
    @TableField("aset_brad")
    private String asetBrad;

    /**
     * 保质期
     */
    @TableField("exprin_date")
    private String exprinDate;

    /**
     * 注册商标
     */
    @TableField("rgt_brad")
    private String rgtBrad;

    /**
     * 最低储量
     */
    @TableField("min_reserve")
    private String minReserve;

    /**
     * 最高储量
     */
    @TableField("max_reserve")
    private String maxReserve;

    /**
     * 最佳储量
     */
    @TableField("best_reserve")
    private String bestReserve;

    /**
     * 参考价格
     */
    @TableField("ref_price")
    private BigDecimal refPrice;

    /**
     * 重要程度
     */
    @TableField("imp_deg")
    private String impDeg;

    /**
     * 使用状态
     */
    @TableField("use_status")
    private String useStatus;

    /**
     * 是否套件
     */
    @TableField("is_kit")
    private String isKit;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 图片
     */
    @TableField("att")
    private String att;

    /**
     * 图片名称
     */
    @TableField("att_name")
    private String attName;

    /**
     * 创建人
     */
    @TableField("crter")
    private String crter;

    /**
     * 创建时间
     */
    @TableField("crter_time")
    private String crterTime;

    /**
     * 更新人
     */
    @TableField("updtr")
    private String updtr;

    /**
     * 更新时间
     */
    @TableField("updtr_time")
    private String updtrTime;

    /**
     * 逻辑删除标志
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 医疗机构id
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 生产日期
     */
    @TableField("mfg_date")
    private String mfgDate;

    /**
     * 来源单号
     */
    @TableField("source_num")
    private String sourceNum;

    /**
     * 补货阈值
     */
    @TableField("replenish_num")
    private BigDecimal replenishNum;

    @TableField(exist = false)
    private Boolean reduSort;

}
