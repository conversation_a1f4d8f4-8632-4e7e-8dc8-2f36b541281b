package com.jp.med.mmis.modules.matReceipt.entity;

import com.aspose.slides.Collections.Generic.List;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 物资入库
 *
 * <AUTHOR>
 * @email -
 * @date 2024-02-19 15:01:07
 */
@Data
@TableName("mmis_aset_storage")
public class MmisAsetStorageEntity {

    /**
     * id
     */
    @TableId("id")
    private Integer id;

    /**
     * 进货单号
     */
    @TableField("po_num")
    private String poNum;

    /**
     * 采购验收单号
     */
    @TableField("receipt_num")
    private String receiptNum;

    /**
     * 开单日期
     */
    @TableField("bill_date")
    private String billDate;

    /**
     * 供应商
     */
    @TableField("supplier_name")
    private String supplierName;

    /**
     * 供应商id
     */
    @TableField("supplier_id")
    private Integer supplierId;

    /**
     * 单据号
     */
    @TableField("docment_num")
    private String docmentNum;

    /**
     * 手工单号
     */
    @TableField("manual_doc_num")
    private String manualDocNum;

    /**
     * 运单号
     */
    @TableField("track_num")
    private String trackNum;

    /**
     * 发票单号
     */
    @TableField("invoice_num")
    private String invoiceNum;

    /**
     * $column.comments
     */
    @TableField("remark")
    private String remark;

    /**
     * 业务部门（申请人科室id）
     */
    @TableField("appy_org_id")
    private String appyOrgId;

    /**
     * 业务员（ 申请人的员工编号）
     */
    @TableField("appyer")
    private String appyer;

    /**
     * 仓库代码
     */
    @TableField("wrhs_code")
    private String wrhsCode;

    /**
     * 业务类别(入出库类型code)
     */
    @TableField("type_code")
    private String typeCode;

    /**
     * $column.comments
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * $column.comments
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 审核批次号
     */
    @TableField("audit_bchno")
    private String auditBchno;

    /**
     * 审核状态
     */
    @TableField("chk_state")
    private String chkState;

    /**
     * 发票记录表ID（多个）
     */
    @TableField("invo_id")
    private String invoId;


    /**
     * 发票附件（多个）
     */
    @TableField("att")
    private String att;

    /**
     * 发票附件（多个）
     */
    @TableField("att_name")
    private String attName;


    /**
     * 入库状态
     */
    @TableField("in_status")
    private String inStatus;

    /**
     * 入库人
     */
    @TableField("in_emp")
    private String inEmp;

    /**
     * 入库执行人部门
     */
    @TableField("in_org_id")
    private String inOrgId;

    /**
     * 入库时间
     */
    @TableField("in_time")
    private String inTime;

    /**
     * 入库备注
     */
    @TableField("in_remark")
    private String inRemark;



    /**
     * 费用报销状态： 0处理中，1已报销，2 报销取消
     */
    @TableField("reim_status_flag")
    private String reimStatusFlag;


    /**
     * 结账期号
     */
    @TableField("settle_period_num")
    private String settlePeriodNum;

    // 用户有权限的仓库代码列表
    private List<String> wrhsNeedShow;

}
