package com.jp.med.mmis.modules.common.controller;

import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.common.CommonFeignResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.mmis.modules.common.dto.MmisAuditRcdfmDto;
import com.jp.med.mmis.modules.common.service.read.MmisAuditRcdfmReadService;
import com.jp.med.mmis.modules.common.service.write.MmisAuditRcdfmWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * 审核明细表
 *
 * <AUTHOR>
 * @email -
 * @date 2024-02-19 14:12:12
 */
@Api(value = "审核明细表", tags = "审核明细表")
@RestController
@RequestMapping("mmisAuditRcdfm")
public class MmisAuditRcdfmController {

    @Autowired
    private MmisAuditRcdfmReadService mmisAuditRcdfmReadService;

    @Autowired
    private MmisAuditRcdfmWriteService mmisAuditRcdfmWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询审核明细表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody MmisAuditRcdfmDto dto) {
        return CommonResult.paging(mmisAuditRcdfmReadService.queryList(dto));
    }


    /**
     * 保存
     */
    @ApiOperation("新增审核明细表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody MmisAuditRcdfmDto dto) {
        mmisAuditRcdfmWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改审核明细表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody MmisAuditRcdfmDto dto) {
        mmisAuditRcdfmWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除审核明细表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody MmisAuditRcdfmDto dto) {
        mmisAuditRcdfmWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("审核结果通知")
    @PostMapping("/complete")
    public CommonFeignResult complete(@RequestBody AuditDetail dto){
        mmisAuditRcdfmWriteService.complete(dto);
        return CommonFeignResult.build();
    }
}
