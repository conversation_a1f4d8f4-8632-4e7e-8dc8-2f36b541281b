package com.jp.med.mmis.modules.matSum.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.mmis.modules.matSum.dto.MmisYySystemOutboundSumDto;
import com.jp.med.mmis.modules.matSum.vo.MmisYySystemOutboundSumVo;

import java.util.List;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2025-01-02 21:47:03
 */
public interface MmisYySystemOutboundSumReadService extends IService<MmisYySystemOutboundSumDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<MmisYySystemOutboundSumVo> queryList(MmisYySystemOutboundSumDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<MmisYySystemOutboundSumVo> queryPageList(MmisYySystemOutboundSumDto dto);
}

