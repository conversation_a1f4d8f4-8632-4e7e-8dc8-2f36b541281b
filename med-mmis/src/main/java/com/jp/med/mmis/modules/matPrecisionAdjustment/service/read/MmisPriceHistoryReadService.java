package com.jp.med.mmis.modules.matPrecisionAdjustment.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.mmis.modules.matPrecisionAdjustment.dto.MmisPriceHistoryDto;
import com.jp.med.mmis.modules.matPrecisionAdjustment.vo.MmisPriceHistoryVo;

import java.util.List;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2025-04-02 15:58:37
 */
public interface MmisPriceHistoryReadService extends IService<MmisPriceHistoryDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<MmisPriceHistoryVo> queryList(MmisPriceHistoryDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<MmisPriceHistoryVo> queryPageList(MmisPriceHistoryDto dto);
}

