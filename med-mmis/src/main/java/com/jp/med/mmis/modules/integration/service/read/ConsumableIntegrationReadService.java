package com.jp.med.mmis.modules.integration.service.read;

import com.jp.med.mmis.modules.integration.dto.ConsumableDTO;
import com.jp.med.mmis.modules.integration.dto.ConsumableTypeDTO;
import com.jp.med.mmis.modules.integration.dto.ConsumableItemDTO;
import com.jp.med.mmis.modules.integration.vo.MmisOutboundBackupVo;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

/**
 * 物资对接读服务接口
 */
public interface ConsumableIntegrationReadService {

	/**
	 * 获取所有物资分类
	 * 
	 * @param hospitalId 医院ID
	 * @return 物资分类列表
	 */
	List<ConsumableTypeDTO> getAllConsumableTypes(String hospitalId);

	/**
	 * 获取所有物资项目
	 * 
	 * @param hospitalId 医院ID
	 * @return 物资项目列表
	 */
	List<ConsumableDTO> getAllConsumables(String hospitalId);

	/**
	 * 查询待确认的预出库工单列表 (workCode 和 outboundOrderNumber)
	 * 
	 * @return 包含 workCode 和 outboundOrderNumber 的 Map 列表
	 */
	List<Map<String, Object>> getPendingConfirmationWorkIds();

	/**
	 * 查询所有可撤销的工单列表（包括预出库状态和已确认出库状态）
	 * 
	 * @return 包含 workId, outboundOrderNumber 和 status 的 Map 列表
	 *         status为"-1"表示预出库状态，"1"表示已确认出库状态
	 */
	List<Map<String, Object>> getRevocableWorkIds();

	/**
	 * 根据ID/申请ID/工单ID查询出库备份记录
	 * 
	 * @param id      记录ID (可选)
	 * @param applyId 申请ID (可选)
	 * @param workId  工单ID (可选)
	 * @return 出库备份记录列表
	 */
	List<MmisOutboundBackupVo> queryOutboundBackup(
			@Param("id") Integer id,
			@Param("applyId") Integer applyId,
			@Param("workId") String workId);

	/**
	 * 检查work_id是否已存在于mmis_outbound_staging表中
	 * 
	 * @param workId 工单ID
	 * @return 存在返回true，不存在返回false
	 */
	boolean checkWorkIdExistsInStaging(String workId);

	/**
	 * 检查work_id是否已存在于mmis_outbound_backup表中
	 * 
	 * @param workId 工单ID
	 * @return 存在返回true，不存在返回false
	 */
	boolean checkWorkIdExistsInBackup(String workId);

	/**
	 * 根据work_id查询mmis_outbound_staging表中的记录状态
	 * 
	 * @param workId 工单ID
	 * @return 暂存状态(ACTIVE/CONFIRMED/EXPIRED/CANCELLED)，不存在返回null
	 */
	String getStagingStatusByWorkId(String workId);

	/**
	 * 根据work_id查询mmis_outbound_backup表中的记录状态
	 * 
	 * @param workId 工单ID
	 * @return 备份状态(ACTIVE/REVOKED)，不存在返回null
	 */
	String getBackupStatusByWorkId(String workId);
}