package com.jp.med.mmis.modules.matSum.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 物资汇总表
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-03-06 17:03:16
 */
@Data
@TableName("mmis_material_sum")
public class MmisMaterialSumDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 货号(物资代码) */
    @TableField("item_num")
    private String itemNum;

    /** 库存数量 */
    @TableField("num")
    private BigDecimal num;

    /** 运输成本 */
    // @TableField("freight_cost")
    // private BigDecimal freightCost;

    /** 物资名称 */
    @TableField("name")
    private String name;

    /** 规格 */
    @TableField("modspec")
    private String modspec;

    /** 库位代码(仓库内的位置) */
    @TableField("wrhs_addr")
    private String wrhsAddr;

    /** 仓库代码(几号仓库) */
    @TableField("wrhs_code")
    private String wrhsCode;

    /** 计量单位编码(对应常用代码中的计量方式:不是计量单位) */
    @TableField("meter_code")
    private String meterCode;

    /** 单价(类比物资信息中的参考进价) */
    @TableField("price")
    private BigDecimal price;

    /** 金额(适配老系统的) */
    @TableField("amt")
    private BigDecimal amt;

    /** 金额(物资的金额：不是简单的进价*数量，操作员自己输入) */
    // @TableField("amt")
    // private BigDecimal amt;

    /** 每件细数(配合计量单位，如1张1 张的入库，1个..，默认为1.) */
    @TableField("item_count")
    private Integer itemCount;

    /** 生产日期 */
    // @TableField("mfg_date")
    // private String mfgDate;
    //
    // /** 到期日（和物资信息的那个同名字段不同，这里不是保质期，而是具体时间） */
    // @TableField("exprin_date")
    // private String exprinDate;
    //
    // /** 物资品牌 */
    // @TableField("aset_brad")
    // private String asetBrad;
    //
    // /** 来源单号 */
    // @TableField("source_num")
    // private String sourceNum;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 修改人 */
    @TableField("updtr")
    private String updtr;

    /** 修改时间 */
    @TableField("update_time")
    private String updateTime;

    /** 逻辑删除标志 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 组织id */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 对应物资辅助表的唯一详细信息，价格对应
     */
    @TableField("mat_unique_code")
    private String matUniqueCode;

    @TableField(exist = false)
    private String asetType;

    @TableField(exist = false)
    private String orgID;

    @TableField(exist = false)
    private String invType;

    @TableField(exist = false)
    private String refPriceLeval;

    @TableField(exist = false)
    private String startTime;

    @TableField(exist = false)
    private String endTime;

    /** 添加filterZeroStock字段 */
    @TableField(exist = false)
    private Boolean filterZeroStock;

    /** 添加warehouseCodes字段用于多选库房查询 */
    @TableField(exist = false)
    private List<String> warehouseCodes;

    /** 精度控制（小数点后位数） */
    @TableField(exist = false)
    private Integer precision;

}
