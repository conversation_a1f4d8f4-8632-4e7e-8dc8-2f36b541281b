package com.jp.med.mmis.modules.wrhsInfo.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.mmis.modules.wrhsInfo.mapper.read.MmisWrhsManagerReadMapper;
import com.jp.med.mmis.modules.wrhsInfo.vo.MmisWrhsManagerVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.mmis.modules.wrhsInfo.mapper.read.MmisWrhsInfoReadMapper;
import com.jp.med.mmis.modules.wrhsInfo.dto.MmisWrhsInfoDto;
import com.jp.med.mmis.modules.wrhsInfo.vo.MmisWrhsInfoVo;
import com.jp.med.mmis.modules.wrhsInfo.service.read.MmisWrhsInfoReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class MmisWrhsInfoReadServiceImpl extends ServiceImpl<MmisWrhsInfoReadMapper, MmisWrhsInfoDto> implements MmisWrhsInfoReadService {

    @Autowired
    private MmisWrhsInfoReadMapper mmisWrhsInfoReadMapper;

    @Autowired
    private MmisWrhsManagerReadMapper mmisWrhsManagerReadMapper;

    @Override
    public List<MmisWrhsInfoVo> queryList(MmisWrhsInfoDto dto) {
        return mmisWrhsInfoReadMapper.queryList(dto);
    }

    @Override
    public List<MmisWrhsInfoVo> queryPageList(MmisWrhsInfoDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return mmisWrhsInfoReadMapper.queryList(dto);
    }

    @Override
    public List<MmisWrhsInfoVo> managerList(MmisWrhsInfoDto dto) {
        return mmisWrhsInfoReadMapper.queryManagerList(dto);
    }

    @Override
    public MmisWrhsInfoVo querywrhsAndManagerDetail(MmisWrhsInfoDto dto) {
        MmisWrhsInfoVo mmisWrhsInfoVo = mmisWrhsInfoReadMapper.queryOneByID(dto.getId());
        String wrhsCode = mmisWrhsInfoVo.getWrhsCode();
       List<MmisWrhsManagerVo> mmisWrhsManagerVos = mmisWrhsManagerReadMapper.queryManagers(wrhsCode);
        mmisWrhsInfoVo.setWrhsManagerList(mmisWrhsManagerVos);
       return mmisWrhsInfoVo;
    }
}
