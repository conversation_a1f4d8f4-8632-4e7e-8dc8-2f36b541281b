package com.jp.med.mmis.modules.matReceipt.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.mmis.modules.matReceipt.mapper.read.MmisInvoRcdReadMapper;
import com.jp.med.mmis.modules.matReceipt.dto.MmisInvoRcdDto;
import com.jp.med.mmis.modules.matReceipt.vo.MmisInvoRcdVo;
import com.jp.med.mmis.modules.matReceipt.service.read.MmisInvoRcdReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class MmisInvoRcdReadServiceImpl extends ServiceImpl<MmisInvoRcdReadMapper, MmisInvoRcdDto> implements MmisInvoRcdReadService {

    @Autowired
    private MmisInvoRcdReadMapper mmisInvoRcdReadMapper;

    @Override
    public List<MmisInvoRcdVo> queryList(MmisInvoRcdDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return mmisInvoRcdReadMapper.queryList(dto);
    }

    @Override
    public List<MmisInvoRcdVo> noPageList(MmisInvoRcdDto dto) {
        return mmisInvoRcdReadMapper.queryList(dto);
    }
}
