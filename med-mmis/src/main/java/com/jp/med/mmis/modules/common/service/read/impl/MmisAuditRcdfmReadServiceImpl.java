package com.jp.med.mmis.modules.common.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.exception.AppException;
import com.jp.med.mmis.modules.common.mapper.write.MmisAuditRcdfmWriteMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.mmis.modules.common.mapper.read.MmisAuditRcdfmReadMapper;
import com.jp.med.mmis.modules.common.dto.MmisAuditRcdfmDto;
import com.jp.med.mmis.modules.common.vo.MmisAuditRcdfmVo;
import com.jp.med.mmis.modules.common.service.read.MmisAuditRcdfmReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class MmisAuditRcdfmReadServiceImpl extends ServiceImpl<MmisAuditRcdfmReadMapper, MmisAuditRcdfmDto> implements MmisAuditRcdfmReadService {

    @Autowired
    private MmisAuditRcdfmReadMapper mmisAuditRcdfmReadMapper;

    @Autowired
    private MmisAuditRcdfmWriteMapper mmisAuditRcdfmWriteMapper;

    @Override
    public List<MmisAuditRcdfmVo> queryList(MmisAuditRcdfmDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return mmisAuditRcdfmReadMapper.queryList(dto);
    }

    @Override
    public List<MmisAuditRcdfmVo> getAuditDetails(String auditBchno, String empCode) {
        MmisAuditRcdfmDto mmisAuditRcdfmDto = new MmisAuditRcdfmDto();
        mmisAuditRcdfmDto.setBchno(auditBchno);
        mmisAuditRcdfmDto.setChker(empCode);
        List<MmisAuditRcdfmVo> auditRcdfmVos = mmisAuditRcdfmReadMapper.queryAuditDetail(mmisAuditRcdfmDto);
        if (CollectionUtil.isEmpty(auditRcdfmVos)) {
            throw new AppException("您不在审核名单中");
        }
        return auditRcdfmVos;
    }

}
