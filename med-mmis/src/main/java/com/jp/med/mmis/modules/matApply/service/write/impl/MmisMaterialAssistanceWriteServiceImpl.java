package com.jp.med.mmis.modules.matApply.service.write.impl;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.util.DateUtil;
import org.apache.commons.lang.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.mmis.modules.matApply.mapper.write.MmisMaterialAssistanceWriteMapper;
import com.jp.med.mmis.modules.matApply.dto.MmisMaterialAssistanceDto;
import com.jp.med.mmis.modules.matApply.service.write.MmisMaterialAssistanceWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 物资帮领映射表
 * <AUTHOR>
 * @email -
 * @date 2024-12-30 05:55:12
 */
@Service
@Transactional(readOnly = false)
public class MmisMaterialAssistanceWriteServiceImpl extends ServiceImpl<MmisMaterialAssistanceWriteMapper, MmisMaterialAssistanceDto> implements MmisMaterialAssistanceWriteService {

@Autowired
private MmisMaterialAssistanceWriteMapper mapper;

	@Override
	public void saveDto(MmisMaterialAssistanceDto dto) {
		//申请人
		HrmUser hrmUser = dto.getSysUser().getHrmUser();
		String appyer = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
		
		//将科室数组用逗号拼接
		if (dto.getAssistedOrgs() != null && dto.getAssistedOrgs().length > 0) {
			String orgIds = String.join(",", dto.getAssistedOrgs());
			dto.setAssistedOrg(orgIds);
		}
		dto.setCrter(appyer);
		dto.setUpdtr(appyer);
		dto.setHospitalId(dto.getHospitalId());
		String currentTime = DateUtil.getCurrentTime(null);
		dto.setCreateTime(currentTime);
		dto.setUpdateTime(currentTime);
		mapper.insert(dto);
	}
}
