package com.jp.med.mmis.modules.matAccounting.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.common.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.mmis.modules.matAccounting.mapper.read.MmisAccountingResultReadMapper;
import com.jp.med.mmis.modules.matAccounting.dto.MmisAccountingResultDto;
import com.jp.med.mmis.modules.matAccounting.vo.MmisAccountingResultVo;
import com.jp.med.mmis.modules.matAccounting.service.read.MmisAccountingResultReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class MmisAccountingResultReadServiceImpl extends ServiceImpl<MmisAccountingResultReadMapper, MmisAccountingResultDto> implements MmisAccountingResultReadService {

    @Autowired
    private MmisAccountingResultReadMapper mmisAccountingResultReadMapper;

    @Override
    public List<MmisAccountingResultVo> queryList(MmisAccountingResultDto dto) {
        return mmisAccountingResultReadMapper.queryList(dto);
    }

    @Override
    public List<MmisAccountingResultVo> queryPageList(MmisAccountingResultDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        String month = dto.getMonth();
        if (month != null && !month.isEmpty()) {
            dto.setStartTime(month + "-01");
            // 获取月份的最后一天
            String[] yearMonth = month.split("-");
            int year = Integer.parseInt(yearMonth[0]);
            int monthNum = Integer.parseInt(yearMonth[1]);
            int lastDay;
            if (monthNum == 2) {
                // 处理闰年
                lastDay = ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0) ? 29 : 28;
            } else if (monthNum == 4 || monthNum == 6 || monthNum == 9 || monthNum == 11) {
                lastDay = 30;
            } else {
                lastDay = 31;
            }
            dto.setEndTime(month + "-" + lastDay);
        }
        return mmisAccountingResultReadMapper.queryList(dto);
    }

}
