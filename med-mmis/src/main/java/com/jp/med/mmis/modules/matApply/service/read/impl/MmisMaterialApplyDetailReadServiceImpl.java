package com.jp.med.mmis.modules.matApply.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.mmis.modules.matApply.dto.MmisMaterialApplyDto;
import com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyDetailVoNewKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.mmis.modules.matApply.mapper.read.MmisMaterialApplyDetailReadMapper;
import com.jp.med.mmis.modules.matApply.dto.MmisMaterialApplyDetailDto;
import com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyDetailVo;
import com.jp.med.mmis.modules.matApply.service.read.MmisMaterialApplyDetailReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class MmisMaterialApplyDetailReadServiceImpl extends ServiceImpl<MmisMaterialApplyDetailReadMapper, MmisMaterialApplyDetailDto> implements MmisMaterialApplyDetailReadService {

    @Autowired
    private MmisMaterialApplyDetailReadMapper mmisMaterialApplyDetailReadMapper;

    @Override
    public List<MmisMaterialApplyDetailVo> queryList(MmisMaterialApplyDetailDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return mmisMaterialApplyDetailReadMapper.queryList(dto);
    }

    @Override
    public List<MmisMaterialApplyDetailVo> reqDetailList(MmisMaterialApplyDto dto) {
        return mmisMaterialApplyDetailReadMapper.queryListByApplyId(dto.getId());
    }

    @Override
    public List<MmisMaterialApplyDetailVoNewKey> detailsListNewKey(MmisMaterialApplyDto dto) {
        return mmisMaterialApplyDetailReadMapper.queryListNewKeyByApplyId(dto.getId());
    }

    /**
     * 自动从库中选取相应物资拿货：
     * 选取规则
     * @param dto
     * @return
     */
    @Override
    public List<MmisMaterialApplyDetailVo> autoDetailsList(MmisMaterialApplyDto dto) {
        return null;
    }

    @Override
    public List<MmisMaterialApplyDetailVo> waitComfirmList(MmisMaterialApplyDto dto) {
        return mmisMaterialApplyDetailReadMapper.waitComfirmList(dto);
    }

    /**
     * 根据单个详情，查能够用于出库的物资（查该货号对应的多个不同价格的物资）
     * @param dto
     * @return
     */
    @Override
    public List<MmisMaterialApplyDetailVo> queryCanUseMatByDetail(MmisMaterialApplyDto dto) {
        return mmisMaterialApplyDetailReadMapper.queryCanUseMatByDetail(dto);
    }
}
