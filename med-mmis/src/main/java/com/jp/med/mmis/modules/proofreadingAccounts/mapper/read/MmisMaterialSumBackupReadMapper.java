package com.jp.med.mmis.modules.proofreadingAccounts.mapper.read;

import com.jp.med.mmis.modules.proofreadingAccounts.dto.MmisMaterialSumBackupDto;
import com.jp.med.mmis.modules.proofreadingAccounts.vo.MmisMaterialSumBackupVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 物资库存备份表，用于存储物资库存数据的备份记录
 * <AUTHOR>
 * @email -
 * @date 2025-03-25 17:21:36
 */
@Mapper
public interface MmisMaterialSumBackupReadMapper extends BaseMapper<MmisMaterialSumBackupDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<MmisMaterialSumBackupVo> queryList(MmisMaterialSumBackupDto dto);
}
