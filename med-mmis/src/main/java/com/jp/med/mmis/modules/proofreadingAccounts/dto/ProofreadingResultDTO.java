package com.jp.med.mmis.modules.proofreadingAccounts.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 对账结果DTO
 * 
 * <AUTHOR>
 * @date 2023-03-25
 */
@Data
@ApiModel(value = "对账结果")
public class ProofreadingResultDTO {

	/**
	 * 对账明细项列表
	 */
	@ApiModelProperty(value = "对账明细项列表")
	private List<ProofreadingItemDTO> items;

	/**
	 * 期号（与对账期号对应，格式：yyyyMM）
	 */
	@ApiModelProperty(value = "期号", example = "202303")
	private String settlePeriodNum;

	/**
	 * 备份时间，格式：yyyy-MM-dd HH:mm:ss
	 */
	@ApiModelProperty(value = "备份时间", example = "2023-03-25 15:30:00")
	private String backupTime;

	/**
	 * 备份类型
	 */
	@ApiModelProperty(value = "备份类型", example = "对账前备份")
	private String backupType;

	/**
	 * 总记录数
	 */
	@ApiModelProperty(value = "总记录数", example = "100")
	private Integer totalCount;

	/**
	 * 差异记录数
	 */
	@ApiModelProperty(value = "差异记录数", example = "5")
	private Integer diffCount;

	/**
	 * 盘盈记录数
	 */
	@ApiModelProperty(value = "盘盈记录数", example = "3")
	private Integer profitCount;

	/**
	 * 盘亏记录数
	 */
	@ApiModelProperty(value = "盘亏记录数", example = "2")
	private Integer lossCount;

	/**
	 * 总金额差异
	 */
	@ApiModelProperty(value = "总金额差异", example = "150.00")
	private BigDecimal totalAmountDiff;

	/**
	 * 医院ID
	 */
	@ApiModelProperty(value = "医院ID", example = "zjxrmyy")
	private String hospitalId;
}