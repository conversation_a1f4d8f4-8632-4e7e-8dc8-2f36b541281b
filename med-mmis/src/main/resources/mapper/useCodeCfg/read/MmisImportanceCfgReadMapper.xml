<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.useCodeCfg.mapper.read.MmisImportanceCfgReadMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.mmis.modules.useCodeCfg.vo.MmisImportanceCfgVo" id="importanceCfgMap">
        <result property="id" column="id"/>
        <result property="impCode" column="imp_code"/>
        <result property="impName" column="imp_name"/>
        <result property="activeFlag" column="active_flag"/>
        <result property="cter" column="cter"/>
        <result property="createTime" column="create_time"/>
        <result property="updtr" column="updtr"/>
        <result property="updateTime" column="update_time"/>
        <result property="delter" column="delter"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.mmis.modules.useCodeCfg.vo.MmisImportanceCfgVo">
        select id          as id,
               imp_code    as impCode,
               imp_name    as impName,
               active_flag as activeFlag,
               cter        as cter,
               create_time as createTime,
               updtr       as updtr,
               update_time as updateTime,
               delter      as delter,
               delete_time as deleteTime,
               hospital_id as hospitalId
        from mmis_importance_cfg
        where (active_flag IS NULL OR active_flag != '1')
    </select>

    <select id="queryMaxCode" resultType="string">
        select imp_code as impCode
        from mmis_importance_cfg
        where (active_flag IS NULL OR active_flag != '1')
        order by imp_code desc limit 1
    </select>

    <!--  查询重要程度listvo  -->
    <select id="queryImportanceCfg" resultType="com.jp.med.mmis.modules.useCodeCfg.vo.MmisInvTypeCfgVo">
        SELECT
        imp_name as impName,
        imp_code as impCode,
        hospital_id as hospitalId
        FROM
        mmis_importance_cfg
        <where>
            (active_flag IS NULL OR active_flag != '1')
            <if test="impName != null and impName != ''">
                and imp_name =#{impName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>
