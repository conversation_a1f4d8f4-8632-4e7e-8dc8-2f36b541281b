<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.useCodeCfg.mapper.read.MmisMeteringAssistReadMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.mmis.modules.useCodeCfg.vo.MmisMeteringAssistVo" id="meteringAssistMap">
        <result property="id" column="id"/>
        <result property="meterCode" column="meter_code"/>
        <result property="meterName" column="meter_name"/>
        <result property="meterRatio" column="meter_ratio"/>
        <result property="activeFlag" column="active_flag"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.mmis.modules.useCodeCfg.vo.MmisMeteringAssistVo">
        select id          as id,
               meter_code  as meterCode,
               meter_name  as meterName,
               meter_ratio as meterRatio,
               active_flag as activeFlag,
               hospital_id as hospitalId
        from mmis_metering_assist
    </select>
    <select id="queryMeterAssistCfg" resultType="com.jp.med.mmis.modules.useCodeCfg.vo.MmisMeteringAssistVo">
        SELECT
        id as id,
        meter_name as meterName,
        meter_ratio as meterRatio,
        order_num as orderNum,
        hospital_id as hospitalId
        FROM
        mmis_metering_assist
        <where>
            (active_flag IS NULL OR active_flag != '1')
            <if test="meterName != null and meterName != ''">
                and meter_name =#{meterName,jdbcType=VARCHAR}
            </if>
            <if test="meterCode != null and meterCode != ''">
                and meter_code =#{meterCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

</mapper>
