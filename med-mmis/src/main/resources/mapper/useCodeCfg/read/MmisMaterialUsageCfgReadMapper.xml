<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.useCodeCfg.mapper.read.MmisMaterialUsageCfgReadMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.mmis.modules.useCodeCfg.vo.MmisMaterialUsageCfgVo" id="materialUsageCfgMap">
        <result property="id" column="id"/>
        <result property="usageCode" column="usage_code"/>
        <result property="usageName" column="usage_name"/>
        <result property="activeFlag" column="active_flag"/>
        <result property="cter" column="cter"/>
        <result property="createTime" column="create_time"/>
        <result property="updtr" column="updtr"/>
        <result property="updateTime" column="update_time"/>
        <result property="delter" column="delter"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.mmis.modules.useCodeCfg.vo.MmisMaterialUsageCfgVo">
        select id          as id,
               usage_code  as usageCode,
               usage_name  as usageName,
               active_flag as activeFlag,
               cter        as cter,
               create_time as createTime,
               updtr       as updtr,
               update_time as updateTime,
               delter      as delter,
               delete_time as deleteTime,
               hospital_id as hospitalId
        from mmis_material_usage_cfg
        where (active_flag IS NULL OR active_flag != '1')
    </select>
    <select id="queryMaxCode" resultType="string">
        select usage_code as usageCode
        from mmis_material_usage_cfg
        where (active_flag IS NULL OR active_flag != '1')
        order by usage_code desc limit 1
    </select>

    <!--  查询计量方式listvo  -->
    <select id="queryMaterialUsageCfg" resultType="com.jp.med.mmis.modules.useCodeCfg.vo.MmisMaterialUsageCfgVo">
        SELECT
        usage_name as usageName,
        usage_code as usageCode,
        hospital_id as hospitalId
        FROM
        mmis_material_usage_cfg
        <where>
            (active_flag IS NULL OR active_flag != '1')
            <if test="usageName != null and usageName != ''">
                and usage_name =#{usageName,jdbcType=VARCHAR}
            </if>
        </where>
        order by
        usage_code asc
    </select>
</mapper>
