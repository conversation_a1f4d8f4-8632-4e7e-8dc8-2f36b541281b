<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.matApply.mapper.read.MmisMaterialApplyReadMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyVo" id="materialApplyMap">
        <result property="id" column="id" />
        <result property="appyOrgId" column="appy_org_id" />
        <result property="appyer" column="appyer" />
        <result property="optType" column="opt_type" />
        <result property="isDeleted" column="is_deleted" />
        <result property="hospitalId" column="hospital_id" />
        <result property="auditBchno" column="audit_bchno" />
        <result property="chkState" column="chk_state" />
        <result property="crter" column="crter" />
        <result property="createTime" column="create_time" />
        <result property="applyRemark" column="apply_remark" />
        <result property="outStatus" column="out_status" />
        <result property="outEmp" column="out_emp" />
        <result property="outTime" column="out_time" />
        <result property="outRemark" column="out_remark" />
    </resultMap>
    <select id="queryList" resultType="com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyVo">
        select ap.id as key, ap.id as id, emp.emp_name as appyer, org.org_name as appyOrgId,
        ap.appy_org_id as orgId, ap.hos_campus as hosCampus, ap.opt_type as optType, ap.is_deleted
        as isDeleted, ap.hospital_id as hospitalId, ap.audit_bchno as auditBchno, ap.chk_state as
        chkState, ap.crter as crter, case when position(#{chker,jdbcType=VARCHAR} in f.chker) > 0
        then '1' else '0' end as auditFlag, ap.create_time as createTime, ap.apply_remark as
        applyRemark, ap.out_status as outStatus, ap.out_apply_bchno as outApplyBchno, ap.serial_num
        as serialNum, outb.att, outb.att_name as attName, outb.docment_num as docmentNum,
        outb.bill_date as billDate, outb.manual_doc_num as manualDocNum, outb.purpose as purpose,
        emp1.emp_name as outEmpName, emp1.phone as outEmpPhone, outb.out_emp as outEmp,
        outb.out_time as outTime, outb.out_remark as outRemark, outb.remark as outApplyRemark,
        ap.confirm_time as confirmTime ,ap.wrhs_code as wrhsCode from mmis_material_apply ap LEFT JOIN mmis_outbound_apply
        outb ON ap.out_apply_bchno = outb.audit_bchno LEFT JOIN hrm_employee_info emp ON ap.appyer =
        emp.emp_code LEFT JOIN hrm_employee_info emp1 ON ap.out_emp = emp1.emp_code LEFT JOIN
        hrm_org org ON ap.appy_org_id = org.org_id left join (select a.bchno, a.chker from
        mmis_audit_rcdfm a inner join (select e.bchno, min(e.chk_seq) as seq from mmis_audit_rcdfm e
        where e.chk_state = '0' and e.chk_time is null group by e.bchno) b on a.bchno = b.bchno and
        a.chk_seq = b.seq) f on ap.audit_bchno = f.bchno <where>
            <if test="id != null "> and ap.id = #{id} </if>
             <if
                test="chkState != null and chkState != '' "> and ap.chk_state =
        #{chkState,jdbcType=VARCHAR} </if>
            <if test="audit != null and audit != ''"> AND exists (
        select e.bchno from mmis_audit_rcdfm e where position(#{chker,jdbcType=VARCHAR} in e.chker)
        > 0 and ap.audit_bchno = e.bchno <if
                    test="chkState != null and chkState != '' and chkState == '0'"> and e.chk_state
        = '0' and e.chk_time is null </if> ) </if>
            <if
                test="auditBchno != null and auditBchno != '' "> and ap.audit_bchno =
        #{auditBchno,jdbcType=VARCHAR} </if>
            <if test="crter != null and crter != '' "> and ap.crter
        = #{crter,jdbcType=VARCHAR} </if>
            <if test="createTime != null and createTime != '' "> and
        ap.create_time &lt;= #{crteTime,jdbcType=VARCHAR} </if>
            <if
                test="orgID != null and orgID != ''"> AND ap.appy_org_id = #{orgID,jdbcType=VARCHAR} </if>
            <if
                test="startTime != null and startTime != ''"> AND ap.create_time between
        #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR} </if>
            <if
                test="appyOrgId != null and appyOrgId != '' "> and ap.appy_org_id &lt;=
        #{appyOrgId,jdbcType=VARCHAR} </if>
            <if test="outStatus != null and outStatus != '' "> and
        ap.out_status= #{outStatus,jdbcType=VARCHAR} </if>
            <if
                test="outApplyBchno != null and outApplyBchno != '' "> and (ap.out_apply_bchno IS
        NOT NULL) </if>
        <if test="wrhsCode != null and wrhsCode != '' "> AND ap.wrhs_code =
        #{wrhsCode,jdbcType=VARCHAR}
        </if>
        </where> order by ap.create_time desc </select>

    <select id="selectOneByBchno"
        resultType="com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyVo"> SELECT id as id,
        appy_org_id as appyOrgId, appyer as appyer, opt_type as optType, is_deleted as isDeleted,
        hospital_id as hospitalId, audit_bchno as auditBchno, chk_state as chkState, crter as crter,
        create_time as createTime, apply_remark as applyRemark, out_status as outStatus, out_emp as
        outEmp, out_time as outTime, out_remark as outRemark from mmis_material_apply where
        audit_bchno = #{auditBchno,jdbcType=VARCHAR} </select>

    <!--
    审核状态为1(已审核通过)但是没有传过来出库申请的审核批次号(out_apply_bchno)，表示还没被出库那边处理，当有了表示出库那边已申请了，当outstatus也为1表示出库了  -->
    <select id="queryWaitDealList"
        resultType="com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyVo"> select ap.id as key,
        ap.id as id, emp.emp_name as appyer, org.org_name as appyOrgId, ap.appy_org_id as applyOrgId
        , ap.hos_campus as hosCampus, ap.opt_type as optType, ap.is_deleted as isDeleted,
        ap.hospital_id as hospitalId, ap.audit_bchno as auditBchno, ap.chk_state as chkState,
        ap.crter as crter, ap.create_time as createTime, ap.apply_remark as applyRemark,
        ap.out_status as outStatus, ap.out_emp as outEmp, ap.out_time as outTime, ap.out_remark as
        outRemark, ap.serial_num as serialNum, ap.hos_campus as hosCampus ,ap.wrhs_code as wrhsCode from mmis_material_apply
        ap LEFT JOIN hrm_employee_info emp ON ap.appyer = emp.emp_code LEFT JOIN hrm_org org ON
        ap.appy_org_id = org.org_id left join (select a.bchno, a.chker from mmis_audit_rcdfm a inner
        join (select e.bchno, min(e.chk_seq) as seq from mmis_audit_rcdfm e where e.chk_state = '0'
        and e.chk_time is null group by e.bchno) b on a.bchno = b.bchno and a.chk_seq = b.seq) f on
        ap.audit_bchno = f.bchno <where> ap.chk_state = '1' and ap.out_apply_bchno is NULL <if
                test="appyOrgId != null and appyOrgId != '' "> and ap.appy_org_id =
        #{appyOrgId,jdbcType=VARCHAR} </if>
        </where> order by ap.create_time desc </select>

    <select id="queryApplyerByOutBchno"
        resultType="com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyVo"> SELECT emp.emp_name AS
        empName, org.org_name AS orgName, mat.appy_org_id as appyOrgId, mat.appyer FROM
        mmis_material_apply mat LEFT JOIN hrm_employee_info emp ON mat.appyer = emp.emp_code LEFT
        JOIN hrm_org org ON mat.appy_org_id = org.org_id WHERE mat.out_apply_bchno
        =#{outApplyBchno,jdbcType=VARCHAR} </select>

    <select id="queryApplyByItemNum"
        resultType="com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyDetailVo"> SELECT d.ID AS
        KEY, d.ID, d.apply_id AS applyId, d.item_num AS itemNum, d.apply_num as applyNum, d.price,
        d.remark, d.NAME, d.hospital_id AS hospitalId, s.appy_org_id AS appyOrgId, s.appyer AS
        appyer, s.apply_remark AS applyRemark, s.out_emp AS outEmp, s.out_time AS outTime,
        s.serial_num AS serialNum, s.hos_campus AS hosCampus FROM mmis_material_apply_detail d LEFT
        JOIN mmis_material_apply s ON d.apply_id = s.ID <where> s.chk_state = '1' and
        s.out_apply_bchno is NOT NULL <if
                test="itemNum != null and itemNum != ''"> and d.item_num =
        #{itemNum,jdbcType=VARCHAR} </if>
           <if test="applyId != null"> and d.apply_id = #{applyId} </if>
           <if
                test="applyNum != null"> and d.apply_num = #{applyNum} </if>
           <if test="price != null">
        and d.price = #{price} </if>
            <if test="itemNum != null and itemNum != ''"> and d.item_num =
        #{itemNum} </if>

           <if
                test="remark != null and remark != ''"> and d.remark = #{remark,jdbcType=VARCHAR} </if>
           <if
                test="name != null and name != ''"> and d.name = #{name,jdbcType=VARCHAR} </if>
           <if
                test="hospitalId != null"> and d.hospital_id = #{hospitalId} </if>

           <if
                test="appyOrgId != null and appyOrgId != ''"> and s.appy_org_id =
        #{appyOrgId,jdbcType=VARCHAR} </if>
           <if
                test="appyer != null and appyer != ''"> and s.appyer = #{appyer,jdbcType=VARCHAR} </if>
           <if
                test="applyRemark != null and applyRemark != ''"> and s.apply_remark =
        #{applyRemark,jdbcType=VARCHAR} </if>
           <if test="invType != null and invType != ''"> and
        s.inv_type = #{invType,jdbcType=VARCHAR} </if>
           <if test="outEmp != null and outEmp != ''">
        and s.out_emp = #{outEmp,jdbcType=VARCHAR} </if>
           <if test="outTime != null and outTime != ''">
        and s.out_time = #{outTime,jdbcType=VARCHAR} </if>
           <if
                test="serialNum != null and serialNum != ''"> and s.serial_num =
        #{serialNum,jdbcType=VARCHAR} </if>
           <if test="hosCampus != null and hosCampus != ''"> and
        s.hos_campus = #{hosCampus,jdbcType=VARCHAR} </if>
        </where>
    </select>
</mapper>