<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.matBack.mapper.read.MmisBackApplyReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.mmis.modules.matBack.vo.MmisBackApplyVo" id="backApplyMap">
        <result property="id" column="id"/>
        <result property="appyOrgId" column="appy_org_id"/>
        <result property="appyer" column="appyer"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="auditBchno" column="audit_bchno"/>
        <result property="chkState" column="chk_state"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="applyRemark" column="apply_remark"/>
        <result property="backStatus" column="back_status"/>
        <result property="backEmp" column="back_emp"/>
        <result property="backTime" column="back_time"/>
        <result property="backRemark" column="back_remark"/>
        <result property="matApplyBchno" column="mat_apply_bchno"/>
        <result property="att" column="att"/>
        <result property="attName" column="att_name"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.mmis.modules.matBack.vo.MmisBackApplyVo">
        select
            ab.id as id,
            ab.appy_org_id as appyOrgId,
            ab.appyer as appyer,
            ab.is_deleted as isDeleted,
            ab.hospital_id as hospitalId,
            ab.audit_bchno as auditBchno,
            ab.chk_state as chkState,
            case when position(#{chker,jdbcType=VARCHAR} in f.chker) > 0 then '1' else '0' end as auditFlag,

            ab.crter as crter,
            ab.create_time as createTime,
            ab.apply_remark as applyRemark,
            ab.back_status as backStatus,
            ab.back_emp as backEmp,
            ab.back_time as backTime,
            ab.back_remark as backRemark,
            ab.mat_apply_bchno as matApplyBchno,
            ab.att as att,
            ab.att_name as attName
        from mmis_back_apply ab
                 left join (select a.bchno,
                                   a.chker
                            from mmis_audit_rcdfm a
                                     inner join (select e.bchno,
                                                        min(e.chk_seq) as seq
                                                 from mmis_audit_rcdfm e
                                                 where e.chk_state = '0'
                                                   and e.chk_time is null
                                                 group by e.bchno) b
                                                on a.bchno = b.bchno
                                                    and a.chk_seq = b.seq) f
                           on  ab.audit_bchno = f.bchno

        <where>
        <if test="chkState != null and chkState != '' ">
            and ap.chk_state = #{chkState,jdbcType=VARCHAR}
        </if>
        <if test="audit != null and audit != ''">
            AND exists (
            select e.bchno
            from mmis_audit_rcdfm e
            where position(#{chker,jdbcType=VARCHAR} in e.chker) > 0
            and ab.audit_bchno = e.bchno
            <if test="chkState != null and chkState != '' and chkState == '0'">
                and e.chk_state = '0'
               and e.chk_time is null
            </if>
            )
        </if>
        <if test="auditBchno != null and auditBchno != '' ">
            and ab.audit_bchno = #{auditBchno,jdbcType=VARCHAR}
        </if>
        <if test="applyOrgId != null and applyOrgId != '' ">
            and ab.appy_org_id = #{applyOrgId,jdbcType=VARCHAR}
        </if>
        <if test="applyEmp != null and applyEmp != '' ">
            and ab.appyer = #{applyEmp,jdbcType=VARCHAR}
        </if>
        <if test="applyTime != null and applyTime != '' ">
            and ab.create_time = #{applyTime,jdbcType=VARCHAR}
        </if>
        <if test="crter != null and crter != '' ">
            and ab.crter = #{crter,jdbcType=VARCHAR}
        </if>
        <if test="startTime != null and startTime != ''">
            AND  ab.create_time between  #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR}
        </if>
        <if test="backStatus != null and backStatus != '' ">
            and ab.back_status= #{backStatus,jdbcType=VARCHAR}
        </if>
        <if test="backEmp != null and backEmp != '' ">
            and ab.back_emp= #{backEmp,jdbcType=VARCHAR}
        </if>
        <if test="backTime != null and backTime != '' ">
            and ab.back_time= #{backTime,jdbcType=VARCHAR}
        </if>
        <if test="backRemark != null and backRemark != '' ">
            and ab.back_remark= #{backRemark,jdbcType=VARCHAR}
        </if>
        </where>
        order by ab.create_time desc
    </select>

</mapper>
