<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.matSum.mapper.read.MmisYySystemStorageSumReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.mmis.modules.matSum.vo.MmisYySystemStorageSumVo" id="yySystemStorageSumMap">
        <result property="orgId" column="org_id"/>
        <result property="orgName" column="org_name"/>
        <result property="wscode" column="wscode"/>
        <result property="wsname" column="wsname"/>
        <result property="sum" column="sum"/>
        <result property="lowmat" column="lowmat"/>
        <result property="othermat" column="othermat"/>
        <result property="normalmat" column="normalmat"/>
        <result property="specmat" column="specmat"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.mmis.modules.matSum.vo.MmisYySystemStorageSumVo">
        select
            org_id as orgId,
            org_name as orgName,
            wscode as wscode,
            wsname as wsname,
            sum as sum,
            lowmat as lowmat,
            othermat as othermat,
            normalmat as normalmat,
            specmat as specmat,
               hospital_id as  hospitalId,
            create_time as createTime
        from mmis_yy_system_storage_sum
        <where>
            <if
                    test="month != null and month != '' "> and create_time between
                #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR} </if>
        </where>
    </select>

</mapper>
