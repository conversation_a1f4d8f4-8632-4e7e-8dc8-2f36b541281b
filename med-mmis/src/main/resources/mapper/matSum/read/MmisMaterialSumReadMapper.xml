<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.matSum.mapper.read.MmisMaterialSumReadMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo" id="materialSumMap">
        <result property="id" column="id" />
        <result property="itemNum" column="item_num" />
        <result property="num" column="num" />
        <result property="freightCost" column="freight_cost" />
        <result property="name" column="name" />
        <result property="modspec" column="modspec" />
        <result property="wrhsAddr" column="wrhs_addr" />
        <result property="wrhsCode" column="wrhs_code" />
        <result property="meterCode" column="meter_code" />
        <result property="price" column="price" />
        <result property="amt" column="amt" />
        <result property="itemCount" column="item_count" />
        <result property="mfgDate" column="mfg_date" />
        <result property="exprinDate" column="exprin_date" />
        <result property="asetBrad" column="aset_brad" />
        <result property="sourceNum" column="source_num" />
        <result property="crter" column="crter" />
        <result property="createTime" column="create_time" />
        <result property="updtr" column="updtr" />
        <result property="updateTime" column="update_time" />
        <result property="isDeleted" column="is_deleted" />
        <result property="hospitalId" column="hospital_id" />
    </resultMap>
    <sql id="sqlFields"> a . id as id, a.item_num as itemNum, a.name as name, a.modspec as modspec,
        a.wrhs_addr as wrhsAddr, a.wrhs_code as wrhsCode, a.meter_code as meterCode, a.price as
        price, a.amt as amt,a.item_count as itemCount, a.crter as crter, a.create_time as
        createTime, a.updtr as updtr, a.update_time as updateTime, a.is_deleted as isDeleted,
        a.hospital_id as hospitalId </sql>
    <!-- 已弃用：     a.wrhs_code as wrhsCode,  a.wrhs_code, 这里因为是给物资申请的，物资申请是不需要让用户去选哪个库的，所以这里汇总不根据库位代码汇总  -->
    <!--  这里的品牌应该也会忽略不根据他来统计，待讨论，后续可能会忽略，删掉a.aset_brad即可   -->
    <select id="queryList" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo"> select
        ROW_NUMBER() OVER (ORDER BY a.name DESC ) AS key, a.name , SUM(a.num) as actNum, a.modspec ,
        b.meter_name as meterUnitName, a.meter_code as meterCode, a.price as price, a.hospital_id as
        hospitalId from mmis_material_sum a LEFT JOIN mmis_metering_mode_cfg b ON a.meter_code
        =b.meter_code <where>
            <if test="name != null and name != ''"> AND a.name like concat('%',
        #{name,jdbcType=VARCHAR}, '%') </if>
            <if test="itemNum != null and itemNum != ''"> AND
        a.item_num = #{itemNum} </if>
            <if test="matUniqueCode != null and matUniqueCode != ''"> AND
        a.mat_unique_code = #{matUniqueCode,jdbcType=VARCHAR} </if>
            <if test="isDeleted != null ">
        AND a.is_deleted = #{isDeleted,jdbcType=INTEGER} </if>
            <if
                test="wrhsAddr != null and wrhsAddr != ''"> AND a.wrhs_code =
        #{wrhsAddr,jdbcType=VARCHAR} </if>
            <if
                test="warehouseCodes != null and warehouseCodes.size() > 0"> AND a.wrhs_code IN <foreach
                    item="item" collection="warehouseCodes" open="(" separator="," close=")">
        #{item} </foreach>
            </if>
        </where> group by a.name , a.modspec ,
        b.meter_name, a.price, a.meter_code ,a.hospital_id ORDER BY a.name </select>
    <!--      a.wrhs_code as wrhsCode,  a.wrhs_code, 查询列表页面的。所以可以加上  -->
    <!-- 使用与queryStockCheck相同的理论金额计算方式，添加理论计算的详细数据（initialAmt、storageAmt、outboundAmt），加入理论金额计算的子查询 -->
    <select id="listSearch" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo"> SELECT
        ROW_NUMBER ( ) OVER ( ORDER BY allif.wrhs, A.NAME ) AS KEY, A.NAME, SUM(A.num) AS actNum,
        allif.ref_price AS price, COALESCE(t.target_amt, 0) AS amt, a.amt as oldAmt,allif.easy_code
        as easyCode, allif.modspec, allif.meter_name AS meterUnitName, allif.mtr_type AS meterCode,
        allif.wrhs AS wrhsAddr, allif.wrhs_name AS wrhsAddrName, allif.aset_type AS asetType,
        allif.NAME AS asetTypeName,A.item_num as itemNum, A.mat_unique_code as matUniqueCode,
        A.hospital_id AS hospitalId, COALESCE(t.initial_amt, 0) as initialAmt,
        COALESCE(t.storage_amt, 0) as storageAmt, COALESCE(t.outbound_amt, 0) as outboundAmt FROM
        mmis_material_sum A LEFT JOIN mmis_metering_mode_cfg b ON A.meter_code = b.meter_code LEFT
        JOIN ( SELECT info.code, info.modspec, info.ref_price, info.aset_type, info.mtr_type,
        info.att, info.att_name, info.aset_brad, info.mat_unique_code, cfg.meter_name, TYPE.NAME,
        TYPE.wrhs, wrhs.wrhs_name, info.easy_code FROM mmis_aset_info_assist info LEFT JOIN
        mmis_aset_type TYPE ON info.aset_type = TYPE.code LEFT JOIN mmis_wrhs_info wrhs ON TYPE.wrhs
        = wrhs.wrhs_code LEFT JOIN mmis_metering_mode_cfg cfg ON info.mtr_type = cfg.meter_code
        where cfg.active_flag ='0' ) allif ON A.mat_unique_code = allif.mat_unique_code LEFT JOIN (
        WITH base_stock AS ( SELECT mat_unique_code, num::numeric as base_num, amt::numeric as
        base_amt FROM mmis_last_stock_temp ), storage_sum AS ( SELECT d.mat_unique_code,
        SUM(d.num::numeric) as storage_num, SUM(d.amt::numeric) as storage_amt FROM
        mmis_aset_storage_detail d JOIN mmis_aset_storage s ON d.apply_id = s.id WHERE s.in_status =
        '1' AND s.chk_state = '1' AND (s.is_deleted is NULL or s.is_deleted != 1) GROUP BY
        d.mat_unique_code ), outbound_sum AS ( SELECT d.mat_unique_code, SUM(d.num::numeric) as
        outbound_num, SUM(d.amt::numeric) as outbound_amt FROM mmis_outbound_apply_details d JOIN
        mmis_outbound_apply s ON d.apply_id = s.id WHERE (s.out_status = '1' OR s.out_status = '2')
        AND s.chk_state = '1' AND (s.is_deleted is NULL or s.is_deleted != 1) GROUP BY
        d.mat_unique_code ) SELECT COALESCE(b.mat_unique_code, s.mat_unique_code, o.mat_unique_code)
        as mat_unique_code, CASE WHEN b.mat_unique_code IS NOT NULL THEN b.base_amt ELSE 0 END as
        initial_amt, COALESCE(s.storage_amt, 0) as storage_amt, COALESCE(o.outbound_amt, 0) as
        outbound_amt, CASE WHEN b.mat_unique_code IS NOT NULL THEN b.base_amt +
        COALESCE(s.storage_amt, 0) - COALESCE(o.outbound_amt, 0) ELSE COALESCE(s.storage_amt, 0) -
        COALESCE(o.outbound_amt, 0) END as target_amt FROM base_stock b FULL OUTER JOIN storage_sum
        s ON b.mat_unique_code = s.mat_unique_code FULL OUTER JOIN outbound_sum o ON
        COALESCE(b.mat_unique_code, s.mat_unique_code) = o.mat_unique_code ) t ON A.mat_unique_code
        = t.mat_unique_code <where> b.active_flag ='0' <if
                test="name != null and name != ''"> AND A.name like concat('%',
        #{name,jdbcType=VARCHAR}, '%') or allif.easy_code like concat('%', #{name,jdbcType=VARCHAR},
        '%') </if>
            <if test="itemNum != null and itemNum != ''"> AND A.item_num = #{itemNum} </if>
            <if
                test="asetType != null and asetType != ''"> AND allif.aset_type =
        #{asetType,jdbcType=VARCHAR} </if>
            <if test="wrhsAddr != null and wrhsAddr != ''"> AND
        allif.wrhs = #{wrhsAddr,jdbcType=VARCHAR} </if>
            <if
                test="warehouseCodes != null and warehouseCodes.size() > 0"> AND allif.wrhs IN <foreach
                    item="item" collection="warehouseCodes" open="(" separator="," close=")">
        #{item} </foreach>
            </if>
            <if
                test="refPriceLeval != null and refPriceLeval != ''">
                <choose>
                    <when test="refPriceLeval == 1"> AND (allif.ref_price &lt;= 2000) </when>
                    <otherwise> AND (allif.ref_price &gt; 2000) </otherwise>
                </choose>
            </if>
        </where>
        GROUP BY A.NAME, A.num, allif.easy_code, allif.modspec, allif.meter_name, allif.mtr_type,
        allif.wrhs, allif.wrhs_name, allif.ref_price, allif.aset_type, allif.NAME, A.hospital_id,
        a.amt, A.item_num, A.mat_unique_code, t.initial_amt, t.storage_amt, t.outbound_amt,
        t.target_amt ORDER BY allif.wrhs, A.NAME </select>


    <select id="querySum1" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo"> select
        a.num as num, <include refid="sqlFields">
        </include> from mmis_material_sum a <where> a.mat_unique_code
        =#{matUniqueCode,jdbcType=VARCHAR} </where>
    </select>

    <!--  入库物资汇总  临时结果集  -->
    <select id="queryMatReceiptSum" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo">
        WITH hstod AS ( SELECT sto.in_org_id , org.org_name , sto.type_code, det.apply_id, det.amt,
        det.num, det.ref_Price, det.item_num, det.aset_type, det.parent_code, det.wrhs_name,
        tyinfo.name as faTypeName, sto.in_time, det.wrhs_code, sto.hospital_id FROM
        mmis_aset_storage sto LEFT JOIN hrm_org org ON sto.in_org_id = org.org_id LEFT JOIN ( SELECT
        d.apply_id, d.amt, d.num, d.item_num, info.aset_type, info.ref_Price, ty.parent_code,
        wrhs.wrhs_name, wrhs.wrhs_code FROM mmis_aset_storage_detail d LEFT JOIN
        mmis_aset_info_assist info ON d.mat_unique_code = info.mat_unique_code LEFT JOIN
        mmis_aset_type ty ON info.aset_type = ty.code LEFT JOIN mmis_wrhs_info wrhs ON ty.wrhs =
        wrhs.wrhs_code WHERE (d.is_deleted IS NULL OR d.is_deleted != 1) ) det ON sto.id = det.apply_id LEFT JOIN ( SELECT type.code, type.name FROM
        mmis_aset_type type WHERE type.parent_code IN ('01','03') ) tyinfo ON det.parent_code =
        tyinfo.code <where> sto.in_status = '1' and sto.chk_state ='1' AND (sto.is_deleted IS NULL OR sto.is_deleted != 1) <if
                test="orgID != null and orgID != ''"> AND sto.in_org_id = #{orgID,jdbcType=VARCHAR} </if>
            <if
                test="invType != null and invType != ''"> AND sto.type_code =
        #{invType,jdbcType=VARCHAR} </if>
            <if test="asetType != null and asetType != ''"> AND
        det.aset_type= #{asetType,jdbcType=VARCHAR} </if>
                        <if
                test="wrhsAddr != null and wrhsAddr != ''"> AND det.wrhs_code=
        #{wrhsAddr,jdbcType=VARCHAR} </if>            <if
                test="warehouseCodes != null and warehouseCodes.size() > 0"> AND det.wrhs_code IN <foreach
                    item="item" collection="warehouseCodes" open="(" separator="," close=")">
        #{item} </foreach>
            </if>            <if test="startTime != null and startTime != ''"> AND
        sto.in_time between #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR} </if>
            <if
                test="refPriceLeval != null and refPriceLeval != ''">
                <choose>
                    <when test="refPriceLeval == 1"> AND (det.ref_Price &lt;= 2000) </when>
                    <otherwise> AND (det.ref_Price &gt; 2000) </otherwise>
                </choose>
            </if>
        </where>
        ) SELECT hstod.in_org_id as inOrgId, hstod.org_name as inOrgName, hstod.wrhs_code as
        wrhsCode, hstod.wrhs_name as wrhsName, hstod.hospital_id as hospitalId , SUM(amt) AS
        totalAmt, SUM(CASE WHEN hstod.parent_code IN ('0101','0301') THEN amt ELSE 0 END) AS
        lowPriceAmt, SUM(CASE WHEN hstod.parent_code IN ('0102','0302') THEN amt ELSE 0 END) AS
        otherAmt, SUM(CASE WHEN hstod.parent_code IN ('0103','0303') THEN amt ELSE 0 END) AS
        norEquAmt, SUM(CASE WHEN hstod.parent_code IN ('0104','0304') THEN amt ELSE 0 END) AS
        speEquAmt FROM hstod GROUP BY hstod.in_org_id, hstod.org_name, hstod.wrhs_code,
        hstod.wrhs_name, hstod.hospital_id </select>

    <!--  出库物资汇总 已经出库的 (需要查出申请人的科室，领用表写了出库批次号)  -->
    <select id="queryMatOutSum" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo">
        WITH hout AS ( SELECT ob.audit_bchno, ob.out_target_org_id, ob.out_taget_org, ob.inv_type,
        det.apply_id, det.num, det.ref_Price, ROUND( COALESCE ( det.num * det.ref_Price, NULL ), 6 )
        AS amt, det.item_num, det.aset_type, det.parent_code, det.wrhs_name, tyinfo.NAME AS
        faTypeName, ob.out_time, det.wrhs_code, ob.hospital_id FROM mmis_outbound_apply ob LEFT JOIN
        ( SELECT d.apply_id, d.num, d.item_num, info.aset_type, info.ref_Price, ty.parent_code,
        wrhs.wrhs_name, wrhs.wrhs_code FROM mmis_outbound_apply_details d LEFT JOIN
        mmis_aset_info_assist info ON d.mat_unique_code = info.mat_unique_code LEFT JOIN
        mmis_aset_type ty ON info.aset_type = ty.code LEFT JOIN mmis_wrhs_info wrhs ON ty.wrhs =
        wrhs.wrhs_code ) det ON ob.ID = det.apply_id LEFT JOIN ( SELECT TYPE.code, TYPE.NAME FROM
        mmis_aset_type TYPE WHERE TYPE.parent_code IN ('01','03') ) tyinfo ON det.parent_code =
        tyinfo.code <where> ob.chk_state ='1' and (ob.out_status ='1' OR ob.out_status ='2' ) <if
                test="orgID != null and orgID != ''"> AND mat.appy_org_id =
        #{orgID,jdbcType=VARCHAR} </if>
            <if
                test="invType != null and invType != ''"> AND ob.inv_type =
        #{invType,jdbcType=VARCHAR} </if>
            <if test="asetType != null and asetType != ''"> AND
        det.aset_type= #{asetType,jdbcType=VARCHAR} </if>
            <if
                test="wrhsAddr != null and wrhsAddr != ''"> AND det.wrhs_code=
        #{wrhsAddr,jdbcType=VARCHAR} </if>
            <if
                test="warehouseCodes != null and warehouseCodes.size() > 0"> AND det.wrhs_code IN <foreach
                    item="item" collection="warehouseCodes" open="(" separator="," close=")">
        #{item} </foreach>
            </if>
            <if test="startTime != null and startTime != ''"> AND
        ob.out_time between #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR} </if>
        </where>
        ) SELECT hout.out_target_org_id AS outOrgId, hout.out_taget_org AS outOrgName,
        hout.wrhs_code AS wrhsCode, hout.wrhs_name AS wrhsName, hout.hospital_id AS hospitalId, SUM
        ( amt ) AS totalAmt, SUM ( CASE WHEN hout.parent_code IN ('0101','0301') THEN amt ELSE 0 END
        ) AS lowPriceAmt, SUM ( CASE WHEN hout.parent_code IN ('0102','0302') THEN amt ELSE 0 END )
        AS otherAmt, SUM ( CASE WHEN hout.parent_code IN ('0103','0303') THEN amt ELSE 0 END ) AS
        norEquAmt, SUM ( CASE WHEN hout.parent_code IN ('0104','0304') THEN amt ELSE 0 END ) AS
        speEquAmt FROM hout GROUP BY hout.out_target_org_id, hout.out_taget_org, hout.wrhs_code,
        hout.wrhs_name, hout.hospital_id </select>

    <!--  低值易耗品汇总 低于2000的都算低值易耗品，大于等于2000的不算  -->
    <select id="queryMatLowPriceSum"
        resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo"> </select>

    <!--  物资类目汇总账 已经出库的 (需要查出申请人的科室，领用表写了出库批次号) -->
    <select id="queryMatCatalogSum" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo">
        WITH hout AS ( SELECT ob.audit_bchno, ob.out_target_org_id, ob.out_taget_org, ob.inv_type,
        det.apply_id, det.num, det.ref_Price, ROUND( COALESCE ( det.num * det.ref_Price, NULL ), 6 )
        AS amt, det.item_num, det.aset_type, det.parent_code, det.wrhs_name, tyinfo.NAME AS
        faTypeName, ob.out_time, det.wrhs_code, ob.hospital_id FROM mmis_outbound_apply ob LEFT JOIN
        ( SELECT d.apply_id, d.num, d.item_num, info.aset_type, info.ref_Price, ty.parent_code,
        wrhs.wrhs_name, wrhs.wrhs_code FROM mmis_outbound_apply_details d LEFT JOIN
        mmis_aset_info_assist info ON d.mat_unique_code = info.mat_unique_code LEFT JOIN
        mmis_aset_type ty ON info.aset_type = ty.code LEFT JOIN mmis_wrhs_info wrhs ON ty.wrhs =
        wrhs.wrhs_code ) det ON ob.ID = det.apply_id LEFT JOIN ( SELECT TYPE.code, TYPE.NAME FROM
        mmis_aset_type TYPE WHERE TYPE.parent_code IN ('01','03') ) tyinfo ON det.parent_code =
        tyinfo.code <where> (ob.out_status ='1' OR ob.out_status ='2' ) <if
                test="orgID != null and orgID != ''"> AND ob.out_target_org_id =
        #{orgID,jdbcType=VARCHAR} </if>
            <if
                test="invType != null and invType != ''"> AND ob.inv_type =
        #{invType,jdbcType=VARCHAR} </if>
            <if test="asetType != null and asetType != ''"> AND
        det.aset_type = #{asetType,jdbcType=VARCHAR} </if>
            <if
                test="wrhsAddr != null and wrhsAddr != ''"> AND det.wrhs_code=
        #{wrhsAddr,jdbcType=VARCHAR} </if>
            <if
                test="warehouseCodes != null and warehouseCodes.size() > 0"> AND det.wrhs_code IN <foreach
                    item="item" collection="warehouseCodes" open="(" separator="," close=")">
        #{item} </foreach>
            </if>
            <if test="startTime != null and startTime != ''"> AND
        ob.out_time between #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR} </if>
            <if
                test="refPriceLeval != null and refPriceLeval != ''">
                <choose>
                    <when test="refPriceLeval == 1"> AND (det.ref_Price &lt;= 2000) </when>
                    <otherwise> AND (det.ref_Price &gt; 2000) </otherwise>
                </choose>
            </if>
        </where>
        ) SELECT hout.out_target_org_id AS outOrgId, hout.out_taget_org AS outOrgName,
        hout.wrhs_code AS wrhsCode, hout.wrhs_name AS wrhsName, hout.hospital_id AS hospitalId, SUM(
        CASE WHEN hout.parent_code IN ('0101','0301') THEN amt ELSE 0 END ) AS lowPriceAmt, SUM(
        CASE WHEN hout.aset_type IN ('010101','030101') THEN amt ELSE 0 END ) AS swyp, SUM( CASE
        WHEN hout.aset_type IN ('010102','030102') THEN amt ELSE 0 END ) AS mzp, SUM( CASE WHEN
        hout.parent_code IN ('0102','0302') THEN amt ELSE 0 END ) AS otherAmt, SUM( CASE WHEN
        hout.aset_type IN ('010201','030201') THEN amt ELSE 0 END ) AS dqwx, SUM( CASE WHEN
        hout.aset_type IN ('010202','030202') THEN amt ELSE 0 END ) AS fwwx, SUM( CASE WHEN
        hout.aset_type IN ('010203','030203') THEN amt ELSE 0 END ) AS frcl, SUM( CASE WHEN
        hout.aset_type IN ('010204','030204') THEN amt ELSE 0 END ) AS qcwx, SUM( CASE WHEN
        hout.aset_type IN ('010205','030205') THEN amt ELSE 0 END ) AS qjyp, SUM( CASE WHEN
        hout.aset_type IN ('010206','030206') THEN amt ELSE 0 END ) AS whyp, SUM( CASE WHEN
        hout.aset_type IN ('010207','030207') THEN amt ELSE 0 END ) AS ysp, SUM( CASE WHEN
        hout.aset_type IN ('010208','030208') THEN amt ELSE 0 END ) AS qtcl, SUM( CASE WHEN
        hout.aset_type IN ('010209','030209') THEN amt ELSE 0 END ) AS wjcl, SUM( CASE WHEN
        hout.parent_code IN ('0103','0303') THEN amt ELSE 0 END ) AS norEquAmt, SUM( CASE WHEN
        hout.aset_type IN ('010301','030301') THEN amt ELSE 0 END ) AS jj, SUM( CASE WHEN
        hout.parent_code IN ('0104','0304') THEN amt ELSE 0 END ) AS speEquAmt, SUM( CASE WHEN
        hout.aset_type IN ('010401','030401') THEN amt ELSE 0 END ) AS zysb1, SUM( amt ) AS totalAmt
        FROM hout GROUP BY hout.out_target_org_id, hout.out_taget_org, hout.wrhs_code,
        hout.wrhs_name, hout.hospital_id </select>

    <select id="queryAll" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo"> SELECT *
        FROM mmis_material_sum <where>
            <if test="itemNum != null and itemNum != ''"> AND item_num = #{itemNum,jdbcType=VARCHAR} </if>
            <if
                test="hospitalId != null and hospitalId != ''"> AND hospital_id =
        #{hospitalId,jdbcType=VARCHAR} </if>
            <if
                test="filterZeroStock != null and filterZeroStock == true"> AND (num IS NOT NULL AND
        num > 0) </if>
        </where> ORDER BY create_time ASC </select>

    <select id="test1" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo"> select *
        update mmis_material_sum s set mat_unique_code = concat(s.mat_unique_code, chr(96 + cast(rn
        as INTEGER))) from ( select s2.id, ROW_NUMBER() OVER (partition by s2.item_num order by
        s2.name) as rn from mmis_material_sum s2 inner join mmis_aset_info_assist a on s2.item_num =
        a.code and s2.name = a.name and s2.price != a.ref_price ) t where s.id = t.id </select>

    <select id="test2" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo"> select *
        insert into mmis_aset_info_assist (code, name, ref_price, mat_unique_code) select s.item_num
        as code, s.name, s.price as ref_price, s.mat_unique_code from mmis_material_sum s inner join
        mmis_aset_info_assist a on s.item_num = a.code and s.name = a.name and s.price !=
        a.ref_price order by s.name </select>
    <select id="test3" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo"> select *
        update mmis_aset_info_assist a set modspec = s.modspec, mtr_type = s.meter_code from
        mmis_material_sum s where s.mat_unique_code = a.mat_unique_code and exists ( select 1 from
        mmis_material_sum s2 inner join mmis_aset_info_assist a2 on s2.item_num = a2.code and
        s2.name = a2.name and s2.price != a2.ref_price where s2.id = s.id ) </select>
    <select id="test3" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo"> select *
        update mmis_aset_info_assist a set aset_type = substring(code, 1, 6) </select>
    <select id="test4" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo"> select *
        update mmis_outbound_apply_details d set mat_unique_code = a.mat_unique_code from
        mmis_aset_info_assist a where d.item_num = a.code and d.name = a.name and d.modspec =
        a.modspec and d.price = a.ref_price </select>
    <select id="test5" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo"> select *
        select name, mat_unique_code, count(*) as count from mmis_aset_storage_detail group by name,
        mat_unique_code </select>

    <!-- 查询本月入库总金额和数量 -->
    <select id="queryStorageAmtNum" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo">
        select coalesce(sum(d.amt), 0) as storageamt, coalesce(sum(d.num), 0) as storagenum,
        s.hospital_id as hospitalId from mmis_aset_storage s left join mmis_aset_storage_detail d on
        s.id = d.apply_id where s.in_status = '1' and s.chk_state = '1' and s.is_deleted != 1 and
        s.in_time between #{startTime} and #{endTime} group by s.hospital_id </select>

    <!-- 查询本月出库总金额和数量 -->
    <select id="queryOutboundAmtNum"
        resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo"> select
        coalesce(sum(d.amt), 0) as outboundamt, coalesce(sum(d.num), 0) as outboundnum,
        s.hospital_id as hospitalId from mmis_outbound_apply s left join mmis_outbound_apply_details
        d on s.id = d.apply_id where s.out_status in ('1', '2') and s.chk_state = '1' and
        s.is_deleted != 1 and s.out_time between #{startTime} and #{endTime} group by s.hospital_id </select>

    <!-- 查询本月申领总金额和数量 -->
    <select id="queryApplyAmtNum" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo">
        select coalesce(sum(d.amt), 0) as applyamt, coalesce(sum(d.num), 0) as applynum,
        s.hospital_id as hospitalId from mmis_outbound_apply s left join mmis_outbound_apply_details
        d on s.id = d.apply_id where s.chk_state = '0' and s.is_deleted != 1 and s.create_time
        between #{startTime} and #{endTime} group by s.hospital_id </select>

    <!-- 查询当前库存总金额和数量 -->
    <select id="queryStockAmtNum" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo">
        select coalesce(sum(num * price), 0) as stockamt, coalesce(sum(num), 0) as stocknum,
        hospital_id as hospitalId from mmis_material_sum where is_deleted != 1 group by hospital_id </select>

    <!-- 查询库存预警信息 -->
    <select id="queryStockWarning" resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo">
        SELECT ms.item_num as itemNum, ms.name, ms.modspec, ms.num as stockNum, assist.replenish_num
        as replenishNum, info.aset_type as asetType, type.name as asetTypeName, wrhs.wrhs_name as
        wrhsAddr, cfg.meter_name as meterUnitName ,ms.hospital_id as hospitalId FROM
        mmis_material_sum ms LEFT JOIN mmis_aset_info_assist assist ON ms.mat_unique_code =
        assist.mat_unique_code LEFT JOIN mmis_aset_info info ON ms.item_num = info.code LEFT JOIN
        mmis_aset_type type ON info.aset_type = type.code LEFT JOIN mmis_metering_mode_cfg cfg ON
        ms.meter_code = cfg.meter_code LEFT JOIN ( SELECT type.code, info.wrhs_name FROM
        mmis_aset_type type LEFT JOIN mmis_wrhs_info info ON type.wrhs = info.wrhs_code ) wrhs ON
        info.aset_type = wrhs.code WHERE ms.is_deleted != 1 AND ms.num &lt;= assist.replenish_num <if
            test="hospitalId != null and hospitalId != ''"> AND ms.hospital_id = #{hospitalId} </if>
        ORDER BY ms.num / NULLIF(assist.replenish_num, 0) ASC </select>

    <!-- 查询物资分类金额占比 -->
    <select id="queryMatTypeAmtRatio"
        resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumVo"> WITH type_stats AS (
        SELECT CASE WHEN LEFT(t.code, 2) = '01' THEN '总务库房' WHEN LEFT(t.code, 2) = '03' THEN '二级维修库'
        END AS wrhs_name, CASE WHEN RIGHT(LEFT(t.code, 4), 2) = '01' THEN '低值易耗品' WHEN
        RIGHT(LEFT(t.code, 4), 2) = '02' THEN '其他材料' WHEN RIGHT(LEFT(t.code, 4), 2) = '03' THEN
        '一般设备' WHEN RIGHT(LEFT(t.code, 4), 2) = '04' THEN '专用设备' END AS type_name, SUM(ms.num *
        ms.price) as total_amt, ms.hospital_id FROM mmis_material_sum ms LEFT JOIN mmis_aset_info
        info ON ms.item_num = info.code LEFT JOIN mmis_aset_type t ON info.aset_type = t.code WHERE
        ms.is_deleted != 1 AND info.is_deleted != 1 <if
            test="hospitalId != null and hospitalId != ''"> AND ms.hospital_id = #{hospitalId} </if>
        GROUP BY LEFT(t.code, 2), RIGHT(LEFT(t.code, 4), 2), ms.hospital_id ) SELECT type_name as
        typeName, wrhs_name as wrhsName, total_amt as totalAmt, ROUND( (total_amt * 100.0 /
        NULLIF(SUM(total_amt) OVER (PARTITION BY wrhs_name), 0))::numeric, 2 ) as amtRatio,
        hospital_id as hospitalId FROM type_stats WHERE type_name IS NOT NULL AND wrhs_name IS NOT
        NULL AND total_amt > 0 ORDER BY wrhs_name, total_amt DESC </select>

    <!-- 查询物资对账数据 -->
    <select id="queryStockCheck"
        resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumCheckVO"> WITH base_stock AS (
        SELECT mat_unique_code, num::numeric as base_num, amt::numeric as base_amt FROM
        mmis_last_stock_temp ), storage_sum AS ( SELECT d.mat_unique_code, SUM(d.num::numeric) as
        storage_num, SUM(d.amt::numeric) as storage_amt FROM mmis_aset_storage_detail d JOIN
        mmis_aset_storage s ON d.apply_id = s.id WHERE s.in_status = '1' AND s.chk_state = '1' AND
        (s.is_deleted is NULL or s.is_deleted != 1) GROUP BY d.mat_unique_code ), outbound_sum AS (
        SELECT d.mat_unique_code, SUM(d.num::numeric) as outbound_num, SUM(d.amt::numeric) as
        outbound_amt FROM mmis_outbound_apply_details d JOIN mmis_outbound_apply s ON d.apply_id =
        s.id WHERE (s.out_status = '1' OR s.out_status = '2') AND s.chk_state = '1' AND
        (s.is_deleted is NULL or s.is_deleted != 1) GROUP BY d.mat_unique_code ), theoretical_stock
        AS ( SELECT COALESCE(b.mat_unique_code, s.mat_unique_code, o.mat_unique_code) as
        mat_unique_code, CASE WHEN b.mat_unique_code IS NOT NULL THEN b.base_num ELSE 0 END as
        initial_num, CASE WHEN b.mat_unique_code IS NOT NULL THEN b.base_amt ELSE 0 END as
        initial_amt, COALESCE(s.storage_num, 0) as storage_num, COALESCE(s.storage_amt, 0) as
        storage_amt, COALESCE(o.outbound_num, 0) as outbound_num, COALESCE(o.outbound_amt, 0) as
        outbound_amt, CASE WHEN b.mat_unique_code IS NOT NULL THEN b.base_num +
        COALESCE(s.storage_num, 0) - COALESCE(o.outbound_num, 0) ELSE COALESCE(s.storage_num, 0) -
        COALESCE(o.outbound_num, 0) END as target_num, CASE WHEN b.mat_unique_code IS NOT NULL THEN
        b.base_amt + COALESCE(s.storage_amt, 0) - COALESCE(o.outbound_amt, 0) ELSE
        COALESCE(s.storage_amt, 0) - COALESCE(o.outbound_amt, 0) END as target_amt FROM base_stock b
        FULL OUTER JOIN storage_sum s ON b.mat_unique_code = s.mat_unique_code FULL OUTER JOIN
        outbound_sum o ON COALESCE(b.mat_unique_code, s.mat_unique_code) = o.mat_unique_code )
        SELECT ROW_NUMBER() OVER (ORDER BY A.NAME DESC) AS key, A.NAME, SUM(A.num) AS actNum,
        allif.ref_price AS price, A.amt AS amt, A.amt AS oldAmt, allif.easy_code AS easyCode,
        allif.modspec, allif.meter_name AS meterUnitName, allif.mtr_type AS meterCode, allif.wrhs AS
        wrhsAddr, allif.wrhs_name AS wrhsAddrName, allif.aset_type AS asetType, allif.NAME AS
        asetTypeName, A.item_num AS itemNum, A.mat_unique_code AS matUniqueCode, A.hospital_id AS
        hospitalId, COALESCE(t.initial_num, 0) as initialNum, COALESCE(t.initial_amt, 0) as
        initialAmt, COALESCE(t.storage_num, 0) as storageNum, COALESCE(t.storage_amt, 0) as
        storageAmt, COALESCE(t.outbound_num, 0) as outboundNum, COALESCE(t.outbound_amt, 0) as
        outboundAmt, COALESCE(t.target_num, 0) as targetNum, COALESCE(t.target_amt, 0) as targetAmt,
        COALESCE(t.target_num, 0) - SUM(A.num) as numDiff, COALESCE(t.target_amt, 0) - A.amt as
        amtDiff FROM mmis_material_sum A LEFT JOIN mmis_metering_mode_cfg b ON A.meter_code =
        b.meter_code LEFT JOIN ( SELECT info.code, info.modspec, info.ref_price, info.aset_type,
        info.mtr_type, info.att, info.att_name, info.aset_brad, info.mat_unique_code,
        cfg.meter_name, TYPE.NAME, TYPE.wrhs, wrhs.wrhs_name, info.easy_code FROM
        mmis_aset_info_assist info LEFT JOIN mmis_aset_type TYPE ON info.aset_type = TYPE.code LEFT
        JOIN mmis_wrhs_info wrhs ON TYPE.wrhs = wrhs.wrhs_code LEFT JOIN mmis_metering_mode_cfg cfg
        ON info.mtr_type = cfg.meter_code WHERE cfg.active_flag = '0' ) allif ON A.mat_unique_code =
        allif.mat_unique_code LEFT JOIN theoretical_stock t ON A.mat_unique_code = t.mat_unique_code <where>
        b.active_flag = '0' <if
                test="matUniqueCode != null and matUniqueCode != ''"> AND A.mat_unique_code =
        #{matUniqueCode} </if>
        <if test="name != null and name != ''"> AND A.name LIKE CONCAT('%',
        #{name}, '%') </if>
        </where> GROUP BY A.NAME, A.num, A.amt, allif.easy_code,
        allif.modspec, allif.meter_name, allif.mtr_type, allif.wrhs, allif.wrhs_name,
        allif.ref_price, allif.aset_type, allif.NAME, A.hospital_id, A.item_num, A.mat_unique_code,
        t.initial_num, t.initial_amt, t.storage_num, t.storage_amt, t.outbound_num, t.outbound_amt,
        t.target_num, t.target_amt ORDER BY A.NAME </select>

    <!-- 查询月度理论金额数据 -->
    <select id="queryMonthlyTheoreticalAmount"
        resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumMonthlyTheoreticalVO"> WITH
        base_stock AS ( SELECT COALESCE(SUM(CAST(amt AS numeric)), 0) base_amt FROM
        mmis_last_stock_temp ), month_definitions AS ( SELECT 0 month_order, '2024-12-01'
        start_date, '2025-01-01' end_date, '12月-2024' month_label UNION ALL SELECT 1, '2025-01-01',
        '2025-02-01', '1月-2025' UNION ALL SELECT 2, '2025-02-01', '2025-03-01', '2月-2025' UNION ALL
        SELECT 3, '2025-03-01', '2025-04-01', '3月-2025' UNION ALL SELECT 4, '2025-04-01',
        '2025-05-01', '4月-2025' UNION ALL SELECT 5, '2025-05-01', '2025-06-01', '5月-2025' UNION ALL
        SELECT 6, '2025-06-01', '2025-07-01', '6月-2025' ), storage_data AS ( SELECT
        TO_CHAR(TO_DATE(s.in_time, 'YYYY-MM-DD'), 'YYYY-MM') month_key, SUM(CAST(d.amt AS numeric))
        storage_amt, MAX(s.hospital_id) as hospital_id FROM mmis_aset_storage s JOIN
        mmis_aset_storage_detail d ON d.apply_id = s.id WHERE s.in_status = '1' AND s.chk_state =
        '1' AND (s.is_deleted IS NULL OR s.is_deleted != 1) AND s.in_time >= '2024-12-01' GROUP BY
        TO_CHAR(TO_DATE(s.in_time, 'YYYY-MM-DD'), 'YYYY-MM') ), outbound_data AS ( SELECT
        TO_CHAR(TO_DATE(s.out_time, 'YYYY-MM-DD'), 'YYYY-MM') month_key, SUM(CAST(d.amt AS numeric))
        outbound_amt, MAX(s.hospital_id) as hospital_id FROM mmis_outbound_apply s JOIN
        mmis_outbound_apply_details d ON d.apply_id = s.id WHERE (s.out_status = '1' OR s.out_status
        = '2') AND s.chk_state = '1' AND (s.is_deleted IS NULL OR s.is_deleted != 1) AND s.out_time
        >= '2024-12-01' GROUP BY TO_CHAR(TO_DATE(s.out_time, 'YYYY-MM-DD'), 'YYYY-MM') ), month_data
        AS ( SELECT m.month_order, m.month_label, COALESCE(s.storage_amt, 0) storage_amt,
        COALESCE(o.outbound_amt, 0) outbound_amt, COALESCE(s.hospital_id, o.hospital_id, '0000') as
        hospital_id FROM month_definitions m LEFT JOIN storage_data s ON CASE WHEN m.month_order = 0
        THEN s.month_key = '2024-12' WHEN m.month_order = 1 THEN s.month_key = '2025-01' WHEN
        m.month_order = 2 THEN s.month_key = '2025-02' WHEN m.month_order = 3 THEN s.month_key =
        '2025-03' WHEN m.month_order = 4 THEN s.month_key = '2025-04' WHEN m.month_order = 5 THEN
        s.month_key = '2025-05' WHEN m.month_order = 6 THEN s.month_key = '2025-06' END LEFT JOIN
        outbound_data o ON CASE WHEN m.month_order = 0 THEN o.month_key = '2024-12' WHEN
        m.month_order = 1 THEN o.month_key = '2025-01' WHEN m.month_order = 2 THEN o.month_key =
        '2025-02' WHEN m.month_order = 3 THEN o.month_key = '2025-03' WHEN m.month_order = 4 THEN
        o.month_key = '2025-04' WHEN m.month_order = 5 THEN o.month_key = '2025-05' WHEN
        m.month_order = 6 THEN o.month_key = '2025-06' END ), theoretical_calculation AS ( SELECT
        month_label, storage_amt, outbound_amt, 0 theoretical_amt, month_order, hospital_id FROM
        month_data ) SELECT t.month_label as month, t.storage_amt, t.outbound_amt, CASE WHEN
        t.month_order = 0 THEN (SELECT base_amt FROM base_stock) + t.storage_amt - t.outbound_amt
        WHEN t.month_order = 1 THEN (SELECT base_amt FROM base_stock) + (SELECT storage_amt FROM
        month_data WHERE month_order = 0) - (SELECT outbound_amt FROM month_data WHERE month_order =
        0) + t.storage_amt - t.outbound_amt WHEN t.month_order = 2 THEN (SELECT base_amt FROM
        base_stock) + (SELECT storage_amt FROM month_data WHERE month_order = 0) - (SELECT
        outbound_amt FROM month_data WHERE month_order = 0) + (SELECT storage_amt FROM month_data
        WHERE month_order = 1) - (SELECT outbound_amt FROM month_data WHERE month_order = 1) +
        t.storage_amt - t.outbound_amt WHEN t.month_order = 3 THEN (SELECT base_amt FROM base_stock)
        + (SELECT storage_amt FROM month_data WHERE month_order = 0) - (SELECT outbound_amt FROM
        month_data WHERE month_order = 0) + (SELECT storage_amt FROM month_data WHERE month_order =
        1) - (SELECT outbound_amt FROM month_data WHERE month_order = 1) + (SELECT storage_amt FROM
        month_data WHERE month_order = 2) - (SELECT outbound_amt FROM month_data WHERE month_order =
        2) + t.storage_amt - t.outbound_amt WHEN t.month_order = 4 THEN (SELECT base_amt FROM
        base_stock) + (SELECT storage_amt FROM month_data WHERE month_order = 0) - (SELECT
        outbound_amt FROM month_data WHERE month_order = 0) + (SELECT storage_amt FROM month_data
        WHERE month_order = 1) - (SELECT outbound_amt FROM month_data WHERE month_order = 1) +
        (SELECT storage_amt FROM month_data WHERE month_order = 2) - (SELECT outbound_amt FROM
        month_data WHERE month_order = 2) + (SELECT storage_amt FROM month_data WHERE month_order =
        3) - (SELECT outbound_amt FROM month_data WHERE month_order = 3) + t.storage_amt -
        t.outbound_amt WHEN t.month_order = 5 THEN (SELECT base_amt FROM base_stock) + (SELECT
        storage_amt FROM month_data WHERE month_order = 0) - (SELECT outbound_amt FROM month_data
        WHERE month_order = 0) + (SELECT storage_amt FROM month_data WHERE month_order = 1) -
        (SELECT outbound_amt FROM month_data WHERE month_order = 1) + (SELECT storage_amt FROM
        month_data WHERE month_order = 2) - (SELECT outbound_amt FROM month_data WHERE month_order =
        2) + (SELECT storage_amt FROM month_data WHERE month_order = 3) - (SELECT outbound_amt FROM
        month_data WHERE month_order = 3) + (SELECT storage_amt FROM month_data WHERE month_order =
        4) - (SELECT outbound_amt FROM month_data WHERE month_order = 4) + t.storage_amt -
        t.outbound_amt WHEN t.month_order = 6 THEN (SELECT base_amt FROM base_stock) + (SELECT
        storage_amt FROM month_data WHERE month_order = 0) - (SELECT outbound_amt FROM month_data
        WHERE month_order = 0) + (SELECT storage_amt FROM month_data WHERE month_order = 1) -
        (SELECT outbound_amt FROM month_data WHERE month_order = 1) + (SELECT storage_amt FROM
        month_data WHERE month_order = 2) - (SELECT outbound_amt FROM month_data WHERE month_order =
        2) + (SELECT storage_amt FROM month_data WHERE month_order = 3) - (SELECT outbound_amt FROM
        month_data WHERE month_order = 3) + (SELECT storage_amt FROM month_data WHERE month_order =
        4) - (SELECT outbound_amt FROM month_data WHERE month_order = 4) + (SELECT storage_amt FROM
        month_data WHERE month_order = 5) - (SELECT outbound_amt FROM month_data WHERE month_order =
        5) + t.storage_amt - t.outbound_amt END theoretical_amt, t.hospital_id as hospitalId FROM
        theoretical_calculation t ORDER BY t.month_order </select>

    <!-- 查询物资对账差异详情 -->
    <select id="queryStockCheckDetail"
        resultType="com.jp.med.mmis.modules.matSum.vo.MmisMaterialSumDetailVO"> WITH base_stock AS (
        SELECT mat_unique_code, CAST(num AS numeric) as base_num, CAST(amt AS numeric) as base_amt
        FROM mmis_last_stock_temp WHERE mat_unique_code = #{matUniqueCode} ), mat_info AS ( SELECT
        m.item_num, m.mat_unique_code, m.name as material_name, m.modspec as specification,
        CAST(m.price AS numeric) as unit_price, CAST(m.num AS numeric) as current_num, CAST(m.amt AS
        numeric) as current_amt, i.aset_type, t.name as type_name, c.meter_name as unit_name,
        w.wrhs_name as warehouse_name, m.hospital_id FROM mmis_material_sum m LEFT JOIN
        mmis_aset_info_assist i ON m.mat_unique_code = i.mat_unique_code LEFT JOIN mmis_aset_type t
        ON i.aset_type = t.code LEFT JOIN mmis_metering_mode_cfg c ON m.meter_code = c.meter_code
        LEFT JOIN mmis_wrhs_info w ON t.wrhs = w.wrhs_code WHERE m.mat_unique_code =
        #{matUniqueCode} AND (m.is_deleted IS NULL OR m.is_deleted != 1) ), storage_data AS ( SELECT
        d.mat_unique_code, SUM(CAST(d.num AS numeric)) as storage_num, SUM(CAST(d.amt AS numeric))
        as storage_amt, COUNT(*) as storage_count FROM mmis_aset_storage_detail d JOIN
        mmis_aset_storage s ON d.apply_id = s.id WHERE d.mat_unique_code = #{matUniqueCode} AND
        s.in_status = '1' AND s.chk_state = '1' AND (s.is_deleted IS NULL OR s.is_deleted != 1)
        GROUP BY d.mat_unique_code ), outbound_data AS ( SELECT d.mat_unique_code, SUM(CAST(d.num AS
        numeric)) as outbound_num, SUM(CAST(d.amt AS numeric)) as outbound_amt, COUNT(*) as
        outbound_count FROM mmis_outbound_apply_details d JOIN mmis_outbound_apply s ON d.apply_id =
        s.id WHERE d.mat_unique_code = #{matUniqueCode} AND (s.out_status = '1' OR s.out_status =
        '2') AND s.chk_state = '1' AND (s.is_deleted IS NULL OR s.is_deleted != 1) GROUP BY
        d.mat_unique_code ), theoretical_stock AS ( SELECT COALESCE(b.mat_unique_code,
        s.mat_unique_code, o.mat_unique_code) as mat_unique_code, COALESCE(b.base_num, 0) as
        initial_num, COALESCE(b.base_amt, 0) as initial_amt, COALESCE(s.storage_num, 0) as
        storage_num, COALESCE(s.storage_amt, 0) as storage_amt, COALESCE(o.outbound_num, 0) as
        outbound_num, COALESCE(o.outbound_amt, 0) as outbound_amt, COALESCE(s.storage_count, 0) as
        storage_count, COALESCE(o.outbound_count, 0) as outbound_count, COALESCE(b.base_num, 0) +
        COALESCE(s.storage_num, 0) - COALESCE(o.outbound_num, 0) as theoretical_num,
        COALESCE(b.base_amt, 0) + COALESCE(s.storage_amt, 0) - COALESCE(o.outbound_amt, 0) as
        theoretical_amt FROM base_stock b FULL OUTER JOIN storage_data s ON b.mat_unique_code =
        s.mat_unique_code FULL OUTER JOIN outbound_data o ON COALESCE(b.mat_unique_code,
        s.mat_unique_code) = o.mat_unique_code ) SELECT mi.item_num as itemNum, mi.material_name as
        materialName, mi.specification, mi.unit_name as unitName, mi.type_name as typeName,
        mi.warehouse_name as warehouseName, mi.unit_price as unitPrice, ts.initial_num as
        initialNum, ts.initial_amt as initialAmt, ts.storage_num as storageNum, ts.storage_amt as
        storageAmt, ts.storage_count as storageCount, ts.outbound_num as outboundNum,
        ts.outbound_amt as outboundAmt, ts.outbound_count as outboundCount, ts.theoretical_num as
        theoreticalNum, ts.theoretical_amt as theoreticalAmt, mi.current_num as actualNum,
        mi.current_amt as actualAmt, mi.current_num - ts.theoretical_num as diffNum, mi.current_amt
        - ts.theoretical_amt as diffAmt, CASE WHEN mi.current_num - ts.theoretical_num = 0 THEN
        'NoDeviation' WHEN mi.current_num - ts.theoretical_num > 0 THEN 'ActualGreater' ELSE
        'TheoreticalGreater' END as diffStatus, mi.hospital_id as hospitalId, mi.mat_unique_code as
        matUniqueCode FROM mat_info mi LEFT JOIN theoretical_stock ts ON mi.mat_unique_code =
        ts.mat_unique_code </select>

    <!-- 根据物资唯一编码查询库存 -->
    <select id="selectByMatUniqueCode"
        resultType="com.jp.med.mmis.modules.matSum.dto.MmisMaterialSumDto"> SELECT * FROM
        mmis_material_sum WHERE mat_unique_code = #{matUniqueCode} AND (is_deleted IS NULL OR
        is_deleted != '1') </select>

    <!-- 查询精度转换分析详情 -->
    <select id="queryPrecisionAnalysisDetail"
        resultType="com.jp.med.mmis.modules.matSum.vo.PrecisionAnalysisDetailVo"> WITH
        precision_analysis AS ( SELECT a.id, a.mat_unique_code, a.code, a.name, a.ref_price as
        original_price, ROUND(a.ref_price::numeric, #{precision}) as rounded_price,
        ABS(a.ref_price::numeric - ROUND(a.ref_price::numeric, #{precision})) as price_diff, m.num
        as stock_quantity, m.amt as original_amount, m.hospital_id FROM mmis_aset_info_assist a LEFT
        JOIN mmis_material_sum m ON a.mat_unique_code = m.mat_unique_code WHERE a.ref_price IS NOT
        NULL AND a.is_deleted = 0 AND a.ref_price::text ~ ('^[0-9]+\.[0-9]{' || CAST(#{precision} +
        1 AS text) || ',}$') ) SELECT id, code, name, original_price as originalPrice, rounded_price
        as roundedPrice, price_diff as priceDiff, stock_quantity as stockQuantity, original_amount
        as originalAmount, ROUND(stock_quantity * rounded_price, #{precision}) as roundedAmount,
        CASE WHEN stock_quantity IS NOT NULL THEN ABS(original_amount - ROUND(stock_quantity *
        rounded_price, #{precision})) ELSE 0 END as amountDiff, hospital_id as hospitalId FROM
        precision_analysis WHERE price_diff > 0 ORDER BY price_diff * COALESCE(stock_quantity, 0)
        DESC </select>

    <!-- 查询精度转换分析汇总 -->
    <select id="queryPrecisionAnalysisSummary"
        resultType="com.jp.med.mmis.modules.matSum.vo.PrecisionAnalysisSummaryVo"> WITH
        precision_analysis AS ( SELECT a.mat_unique_code, a.ref_price, ROUND(a.ref_price::numeric,
        #{precision}) as rounded_price, m.num as stock_quantity, m.amt as original_amount,
        m.hospital_id FROM mmis_aset_info_assist a LEFT JOIN mmis_material_sum m ON
        a.mat_unique_code = m.mat_unique_code WHERE a.ref_price IS NOT NULL AND a.is_deleted = 0 AND
        a.ref_price::text ~ '\d+\.\d+' ) SELECT COUNT(DISTINCT mat_unique_code) as
        totalMaterialCount, SUM(original_amount) as totalOriginalAmount, SUM(CASE WHEN
        stock_quantity IS NOT NULL THEN ABS(original_amount - ROUND(stock_quantity * rounded_price,
        #{precision})) ELSE 0 END) as totalAmountDiff, COUNT(CASE WHEN stock_quantity > 0 THEN 1
        END) as affectedMaterialCount, MAX(hospital_id) as hospitalId FROM precision_analysis WHERE
        ABS(ref_price::numeric - ROUND(ref_price::numeric, #{precision})) > 0 </select>
</mapper>