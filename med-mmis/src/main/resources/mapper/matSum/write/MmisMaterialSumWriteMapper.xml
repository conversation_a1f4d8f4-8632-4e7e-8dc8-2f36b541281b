<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.matSum.mapper.write.MmisMaterialSumWriteMapper">


	<update id="updateByItemNum"> UPDATE mmis_material_sum SET name = #{name}, modspec = #{modspec},
		meter_code = #{meterCode} WHERE item_num = #{itemNum} </update>

	<!-- 备份库存数据 -->
	<insert id="backupStockData"
		parameterType="com.jp.med.mmis.modules.matSum.entity.MmisMaterialSumBackup"> INSERT INTO
		mmis_material_sum_backup ( id, backup_time, backup_type, mat_unique_code, original_num,
		original_amt, adjust_num, adjust_amt, remark, crter, create_time, hospital_id, is_deleted )
		VALUES ( #{id}, #{backupTime}, #{backupType}, #{matUniqueCode}, #{originalNum},
		#{originalAmt}, #{adjustNum}, #{adjustAmt}, #{remark}, #{crter}, #{createTime},
		#{hospitalId}, '0' ) </insert>

	<!-- 更新库存数据 -->
	<update id="updateStockData"> UPDATE mmis_material_sum SET num = #{adjustQuantity}, amt =
		#{adjustAmount} WHERE mat_unique_code = #{matUniqueCode} AND (is_deleted IS NULL OR
		is_deleted != '1') </update>
</mapper>