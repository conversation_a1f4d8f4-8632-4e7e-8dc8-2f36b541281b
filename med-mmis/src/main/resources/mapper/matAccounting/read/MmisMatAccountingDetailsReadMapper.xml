<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.matAccounting.mapper.read.MmisMatAccountingDetailsReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.mmis.modules.matAccounting.vo.MmisMatAccountingDetailsVo" id="matAccountingDetailsMap">
        <result property="id" column="id"/>
        <result property="accountingId" column="accounting_id"/>
        <result property="itemNum" column="item_num"/>
        <result property="applyId" column="apply_id"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="updtr" column="updtr"/>
        <result property="updateTime" column="update_time"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="name" column="name"/>
        <result property="meterCode" column="meter_code"/>
        <result property="num" column="num"/>
        <result property="price" column="price"/>
        <result property="amt" column="amt"/>
        <result property="matUniqueCode" column="mat_unique_code"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.mmis.modules.matAccounting.vo.MmisMatAccountingDetailsVo">
        select
            id as id,
            accounting_id as accountingId,
            item_num as itemNum,
            apply_id as applyId,
            crter as crter,
            create_time as createTime,
            updtr as updtr,
            update_time as updateTime,
            hospital_id as hospitalId,
            name as name,
            meter_code as meterCode,
            num as num,
            price as price,
            amt as amt,
            mat_unique_code as matUniqueCode,
            modspec
        from mmis_mat_accounting_details
    </select>

</mapper>
