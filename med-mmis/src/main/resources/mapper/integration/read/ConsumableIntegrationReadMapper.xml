<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jp.med.mmis.modules.integration.mapper.read.ConsumableIntegrationReadMapper">

	<!-- 获取所有耗材分类 -->
	<select id="getAllConsumableTypes"
		resultType="com.jp.med.mmis.modules.integration.dto.ConsumableTypeDTO"> WITH RECURSIVE
		type_tree AS ( SELECT * FROM mmis_aset_type r WHERE r.wrhs = '0002' AND (r.is_deleted IS
		NULL OR r.is_deleted != 1) AND r.hospital_id = #{hospitalId} UNION ALL SELECT c.* FROM
		mmis_aset_type c INNER JOIN type_tree p ON c.parent_code = p.code WHERE (c.is_deleted IS
		NULL OR c.is_deleted != 1) AND c.hospital_id = #{hospitalId} ) SELECT DISTINCT t.code as
		tripartiteConsumableTypeId, t.code, t.name, t.parent_code as parentId, CASE WHEN NOT EXISTS
		( SELECT 1 FROM type_tree child WHERE child.parent_code = t.code ) THEN 1 ELSE 0 END as
		isLeaf, CASE WHEN t.is_deleted = 1 THEN 1 ELSE 0 END as deleteFlag FROM type_tree t ORDER BY
		t.code </select>

	<!-- 获取所有耗材项 -->
	<select id="getAllConsumables"
		resultType="com.jp.med.mmis.modules.integration.dto.ConsumableDTO"> WITH type_tree AS (
		SELECT code FROM mmis_aset_type WHERE (code = '03' OR parent_code = '03' OR code LIKE '03%')
		AND (is_deleted IS NULL OR is_deleted != 1) AND hospital_id = #{hospitalId} ) SELECT
		a.mat_unique_code as tripartiteConsumableId, a.code, a.name, COALESCE(b.meter_name,
		a.mtr_unit) as unit, a.ref_price as cost, a.modspec as specification, CASE WHEN a.use_status
		= '1' THEN 1 ELSE 0 END as isPutaway, CASE WHEN a.is_deleted = 1 THEN 1 ELSE 0 END as
		deleteFlag, a.aset_type as tripartiteConsumableTypeId, COALESCE(ms.num, 0) as stockNum,
		(COALESCE(ms.num, 0) - COALESCE(ms.locked_quantity, 0)) AS theoryNum FROM
		mmis_aset_info_assist a LEFT JOIN mmis_metering_mode_cfg b ON a.mtr_type = b.meter_code LEFT
		JOIN mmis_material_sum ms ON a.mat_unique_code = ms.mat_unique_code AND a.hospital_id =
		ms.hospital_id WHERE (a.is_deleted IS NULL OR a.is_deleted != 1) AND a.hospital_id =
		#{hospitalId} AND EXISTS ( SELECT 1 FROM type_tree t WHERE a.aset_type = t.code ) ORDER BY
		a.code </select>

	<!-- 根据工单ID查询出库申请ID -->
	<select id="getApplyIdByWorkId" resultType="java.lang.Integer"> SELECT id FROM
		mmis_outbound_apply WHERE (docment_num = #{workId} OR manual_doc_num = #{workId}) AND
		(is_deleted IS NULL OR is_deleted != 1) ORDER BY create_time DESC LIMIT 1 </select>

	<!-- 根据审核批次号查询出库申请ID -->
	<select id="getApplyIdByAuditBchno" resultType="java.lang.Integer"> SELECT id FROM
		mmis_outbound_apply WHERE audit_bchno = #{auditBchno} AND (is_deleted IS NULL OR is_deleted
		!= 1) LIMIT 1 </select>

	<!-- 查询物资当前库存 -->
	<select id="getConsumableCurrentStock" resultType="java.math.BigDecimal"> SELECT num FROM
		mmis_material_sum WHERE mat_unique_code = #{consumableId} AND (is_deleted IS NULL OR
		is_deleted != 1) LIMIT 1 </select>

	<!-- 查询物资当前可分配库存 (物理库存 - 锁定库存) -->
	<!-- 假设 mmis_material_sum 表有 num (物理库存) 和 locked_quantity (锁定库存) 字段 -->
	<select id="getConsumableAvailableStock" resultType="java.math.BigDecimal"> SELECT COALESCE(num,
		0) - COALESCE(locked_quantity, 0) AS available_stock FROM mmis_material_sum WHERE
		mat_unique_code = #{consumableId} AND (is_deleted IS NULL OR is_deleted != 1) LIMIT 1 </select>

	<!-- 查询有效的、等待确认的预出库工单列表 (outboundOrderNumber 作为key, work_id 作为value) -->
	<select id="selectActivePreOutboundWorkIdsFromStaging" resultType="java.util.Map"> SELECT
		T.docment_num AS "outboundOrderNumber", T.work_id AS "workId" FROM ( SELECT s.work_id,
		s.docment_num, MAX(s.create_time) AS max_create_time FROM mmis_outbound_staging s INNER JOIN
		mmis_outbound_backup b ON s.apply_id = b.apply_id AND s.work_id = b.work_id WHERE
		s.staging_status = 'ACTIVE' AND s.is_deleted = 0 AND b.process_result = 'SUCCESS' GROUP BY
		s.work_id, s.docment_num ) T ORDER BY T.max_create_time DESC </select>

	<!-- 查询所有可撤销的工单列表（outboundOrderNumber 作为key, workId 作为value） -->
	<select id="selectRevocableWorkIds" resultType="java.util.Map"> 
		SELECT T.docment_num AS "outboundOrderNumber", T.work_id AS "workId", T.status AS "status", T.create_time 
		FROM ( 
			<!-- 预出库状态：从staging表获取ACTIVE状态的记录 -->
			SELECT s.work_id, s.docment_num, '-1' AS status, s.create_time
			FROM mmis_outbound_staging s 
			WHERE s.staging_status = 'ACTIVE'
			
			UNION ALL 
			
			<!-- 已确认出库状态：从backup表获取M1003接口的成功记录 -->
			SELECT b.work_id, b.outbound_id AS docment_num, '1' AS status, 
				TO_CHAR(b.request_time, 'YYYY-MM-DD HH24:MI:SS') AS create_time 
			FROM mmis_outbound_backup b 
			WHERE b.status = 'ACTIVE' AND b.process_result = 'SUCCESS' AND b.interface_code = 'M1003'
		) T 
		ORDER BY T.create_time DESC 
	</select>

	<!-- 检查work_id是否已存在于mmis_outbound_staging表中 -->
	<select id="checkWorkIdExistsInStaging" resultType="java.lang.Boolean"> SELECT COUNT(1) > 0 FROM
		mmis_outbound_staging WHERE work_id = #{workId} AND staging_status = 'ACTIVE' </select>

	<!-- 检查work_id是否已存在于mmis_outbound_backup表中 -->
	<select id="checkWorkIdExistsInBackup" resultType="java.lang.Boolean"> SELECT COUNT(1) > 0 FROM
		mmis_outbound_backup WHERE work_id = #{workId} AND status = 'ACTIVE' </select>

	<!-- 根据work_id查询mmis_outbound_staging表中的记录状态 -->
	<select id="getStagingStatusByWorkId" resultType="java.lang.String"> SELECT staging_status FROM
		mmis_outbound_staging WHERE work_id = #{workId} AND is_deleted = 0 ORDER BY create_time DESC
		LIMIT 1 </select>

	<!-- 根据work_id查询mmis_outbound_backup表中的记录状态 -->
	<select id="getBackupStatusByWorkId" resultType="java.lang.String"> SELECT status FROM
		mmis_outbound_backup WHERE work_id = #{workId} ORDER BY request_time DESC LIMIT 1 </select>

	<!-- 🆕 根据出库单号查询mmis_outbound_staging表中的记录信息 -->
	<select id="getStagingInfoByOrderNumber" resultType="java.util.Map">
		SELECT 
			id,
			apply_id as "applyId",
			work_id as "workId", 
			docment_num as "outboundOrderNumber",
			hospital_id as "hospitalId",
			staging_status as "stagingStatus",
			create_time as "createTime"
		FROM mmis_outbound_staging 
		WHERE docment_num = #{outboundOrderNumber} 
		  AND staging_status = 'ACTIVE' 
		  AND is_deleted = 0 
		ORDER BY create_time DESC 
		LIMIT 1
	</select>

	<!-- 🆕 根据出库单号查询mmis_outbound_backup表中的记录信息 -->
	<select id="getBackupInfoByOrderNumber" resultType="java.util.Map">
		SELECT 
			id,
			work_id as "workId",
			work_code as "workCode", 
			hospital_id as "hospitalId",
			outbound_id as "outboundOrderNumber",
			apply_id as "applyId",
			status,
			consumable_list as "consumableList",
			interface_code as "interfaceCode"
		FROM mmis_outbound_backup 
		WHERE outbound_id = #{outboundOrderNumber} 
		  AND status = 'ACTIVE' 
		ORDER BY request_time DESC 
		LIMIT 1
	</select>

	<!-- 🆕 根据出库单号查询mmis_outbound_apply表中的记录信息 -->
	<select id="getApplyInfoByOrderNumber" resultType="java.util.Map">
		SELECT 
			id,
			docment_num as "outboundOrderNumber",
			manual_doc_num as "workId",
			out_status as "outStatus",
			chk_state as "chkState",
			hospital_id as "hospitalId"
		FROM mmis_outbound_apply 
		WHERE docment_num = #{outboundOrderNumber} 
		  AND (is_deleted IS NULL OR is_deleted != 1) 
		ORDER BY create_time DESC 
		LIMIT 1
	</select>

</mapper> 