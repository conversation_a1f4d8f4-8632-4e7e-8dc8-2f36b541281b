<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.wrhsManagers.mapper.read.MmisWarehouseManagerMappingReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.mmis.modules.wrhsManagers.vo.MmisWarehouseManagerMappingVo" id="warehouseManagerMappingMap">
        <result property="id" column="id"/>
        <result property="wrhsCode" column="wrhs_code"/>
        <result property="manager" column="manager"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.mmis.modules.wrhsManagers.vo.MmisWarehouseManagerMappingVo">
        select
            id as id,
            wrhs_code as wrhsCode,
            manager as manager,
            hospital_id as hospitalId,
            created_at as createdAt,
            updated_at as updatedAt
        from mmis_warehouse_manager_mapping
    </select>

</mapper>
