<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.asetInfo.mapper.write.MmisAsetInfoAssistWriteMapper">

	<update id="updateMatUniqueCode" parameterType="map"> UPDATE mmisAsetInfoAssist SET
		mat_unique_code = code WHERE id = #{id} </update>
	<!-- 下面两个没啥用 -->
	<update id="updateMatUniqueCode1" parameterType="map"> UPDATE mmis_material_sum SET
		mat_unique_code = CONCAT(SUBSTRING(item_num, 1, 6), '202411', SUBSTRING(item_num, 6)) </update>
	<update id="updateMatUniqueCod2" parameterType="map"> UPDATE mmis_material_sum SET item_num =
		mmis_aset_info_assist.code FROM mmis_aset_info_assist WHERE mmis_material_sum.name =
		mmis_aset_info_assist.name; </update>

	<update id="updateEasyCode" parameterType="com.jp.med.mmis.modules.asetInfo.vo.MmisAsetInfoAssistVo">
		update mmis_aset_info_assist <set>
		<if test="easyCode != null and easyCode != ''">easy_code = #{easyCode,jdbcType=VARCHAR},</if>
	</set>
		<where>
			<if test="code != null and code != ''">code = #{code,jdbcType=VARCHAR}</if>
			<if
					test="name != null and name != ''">and name = #{name,jdbcType=VARCHAR}</if>
			<if
					test="id != null ">and id = #{id,jdbcType=INTEGER}</if>
		</where>
	</update>
</mapper>