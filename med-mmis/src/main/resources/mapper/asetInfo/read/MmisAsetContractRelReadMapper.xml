<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.asetInfo.mapper.read.MmisAsetContractRelReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.mmis.modules.asetInfo.vo.MmisAsetContractRelVo" id="asetContractRelMap">
        <result property="id" column="id"/>
        <result property="matUniqueCode" column="mat_unique_code"/>
        <result property="ctUnifiedCode" column="ct_unified_code"/>
        <result property="ctCode" column="ct_code"/>
        <result property="effectStartTime" column="effect_start_time"/>
        <result property="effectEndTime" column="effect_end_time"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.mmis.modules.asetInfo.vo.MmisAsetContractRelVo">
        select
            id as id,
            mat_unique_code as matUniqueCode,
            ct_unified_code as ctUnifiedCode,
            ct_code as ctCode,
            effect_start_time as effectStartTime,
            effect_end_time as effectEndTime,
            hospital_id as hospitalId,
            create_time as createTime,
            update_time as updateTime
        from mmis_aset_contract_rel
    </select>

</mapper>
