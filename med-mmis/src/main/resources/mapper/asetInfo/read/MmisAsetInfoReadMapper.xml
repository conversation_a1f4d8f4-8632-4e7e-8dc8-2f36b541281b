<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.asetInfo.mapper.read.MmisAsetInfoReadMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.mmis.modules.asetInfo.vo.MmisAsetInfoVo" id="asetInfoMap">
        <result property="id" column="id" />
        <result property="code" column="code" />
        <result property="name" column="name" />
        <result property="easyCode" column="easy_code" />
        <result property="asetType" column="aset_type" />
        <result property="mtrType" column="mtr_type" />
        <result property="mtrUnit" column="mtr_unit" />
        <result property="modspec" column="modspec" />
        <result property="orplc" column="orplc" />
        <result property="prdr" column="prdr" />
        <result property="asetAlis" column="aset_alis" />
        <result property="asetBarc" column="aset_barc" />
        <result property="asetBrad" column="aset_brad" />
        <result property="exprinDate" column="exprin_date" />
        <result property="rgtBrad" column="rgt_brad" />
        <result property="minReserve" column="min_reserve" />
        <result property="maxReserve" column="max_reserve" />
        <result property="bestReserve" column="best_reserve" />
        <result property="refPrice" column="ref_price" />
        <result property="impDeg" column="imp_deg" />
        <result property="useStatus" column="use_status" />
        <result property="isKit" column="is_kit" />
        <result property="remark" column="remark" />
        <result property="att" column="att" />
        <result property="attName" column="att_name" />
        <result property="crter" column="crter" />
        <result property="crterTime" column="crter_time" />
        <result property="updtr" column="updtr" />
        <result property="updtrTime" column="updtr_time" />
        <result property="isDeleted" column="is_deleted" />
        <result property="hospitalId" column="hospital_id" />
    </resultMap>
    <!--  加了联系mmis_aset_storage_detail，查询该物资的库存数量  and a.ref_price = sto.price -->
    <select id="queryList" resultType="com.jp.med.mmis.modules.asetInfo.vo.MmisAsetInfoVo"> select
        a.id as id, a.code as key, a.code as code, a.name as name, a.easy_code as easyCode,
        a.aset_type as asetType, a.mtr_type as mtrType, b.meter_name as meterTypeName,
        b.base_unit_coefficient as baseUnitCoefficient, a.mtr_unit as mtrUnit, c.meter_name as
        meterUnitName, a.modspec as modspec, a.orplc as orplc, a.prdr as prdr, a.aset_alis as
        asetAlis, a.aset_barc as asetBarc, a.aset_brad as asetBrad, a.exprin_date as exprinDate,
        a.rgt_brad as rgtBrad, a.min_reserve as minReserve, a.max_reserve as maxReserve,
        a.best_reserve as bestReserve, a.ref_price as refPrice, a.imp_deg as impDeg, a.use_status as
        useStatus, a.is_kit as isKit, a.remark as remark, a.att as att, a.att_name as attName,
        a.crter as crter, a.crter_time as crterTime, a.updtr as updtr, a.updtr_time as updtrTime,
        a.is_deleted as isDeleted, a.hospital_id as hospitalId, a.mfg_date as mfgDate,
        a.is_warehouse as isWarehouse, TO_CHAR( CASE WHEN a.mfg_date IS NULL THEN NULL ELSE
        TO_DATE(a.mfg_date, 'yyyy-MM-dd') + INTERVAL '1 day' * CAST(COALESCE(a.exprin_date, '0')AS
        INTEGER) END, 'yyyy-MM-dd' ) AS expDate, a.source_num as sourceNum, a.replenish_num as
        replenishNum, d.name as asetTypeName, sto.actNum as actNum, wrhs.wrhs as wrhsAddr from
        mmis_aset_info a LEFT JOIN mmis_metering_mode_cfg b ON a.mtr_type = b.meter_code LEFT JOIN
        mmis_metering_assist c ON a.mtr_unit = c.id :: VARCHAR LEFT JOIN mmis_aset_type d ON
        a.aset_type = d.code LEFT JOIN ( SELECT type.code, type.wrhs, info.wrhs_name FROM
        mmis_aset_type type LEFT JOIN mmis_wrhs_info info ON type.wrhs =info.wrhs_code ) wrhs ON
        a.aset_type = wrhs.code LEFT JOIN ( SELECT item_num , SUM( num ) AS actNum FROM
        mmis_material_sum GROUP BY item_num ) sto ON a.code = sto.item_num ::VARCHAR <where>
        (a.is_deleted != 1 OR a.is_deleted IS NULL) AND (b.active_flag != '1' OR b.active_flag IS
        NULL) AND (c.active_flag != '1' OR c.active_flag IS NULL) AND (d.is_deleted != 1 OR
        d.is_deleted IS NULL) <if test="name != null and name != ''"> AND a.name like concat('%',
        #{name,jdbcType=VARCHAR}, '%') or a.easy_code like concat('%', #{name,jdbcType=VARCHAR},
        '%') </if>
            <!--            <if test="easyCode != null and easyCode != ''">-->
            <!--                AND a.easy_code = #{easyCode,jdbcType=VARCHAR}-->
            <!--            </if>-->
            <if
                test="asetType != null and asetType != ''"> AND a.aset_type =
        #{asetType,jdbcType=VARCHAR} </if>
            <if test="code != null and code != ''"> AND a.code =
        #{code,jdbcType=VARCHAR} </if>
            <if test="isWarehouse != null "> AND a.is_warehouse =
        #{isWarehouse,jdbcType=Integer} </if>
            <if test="modspec != null and modspec != ''"> AND
        a.modspec = #{modspec,jdbcType=VARCHAR} </if>
            <if test="asetAlis != null and asetAlis != ''">
        AND a.aset_alis = #{asetAlis,jdbcType=VARCHAR} </if>
            <if test="refPrice != null "> AND
        a.ref_price = #{refPrice,jdbcType=DECIMAL} </if>
            <if test="mtrType != null and mtrType != ''">
        AND a.mtr_type = #{mtrType,jdbcType=VARCHAR} </if>
            <if
                test="mtrType != null and mtrType != ''"> AND a.mtr_type =
        #{mtrType,jdbcType=VARCHAR} </if>
            <if test="wrhsAddr != null and wrhsAddr != ''"> AND
        wrhs.wrhs = #{wrhsAddr,jdbcType=VARCHAR} </if>
            <if
                test="useStatus != null and useStatus != ''"> AND a.use_status =
        #{useStatus,jdbcType=VARCHAR} </if>
            <if test="refPriceLeval != null and refPriceLeval != ''">
                <choose>
                    <when test="refPriceLeval == 1"> AND (a.ref_price &lt;= 2000) </when>
                    <otherwise> AND (a.ref_price &gt; 2000) </otherwise>
                </choose>
            </if>
            <if
                test="checkedCodeList != null and checkedCodeList.size() > 0"> AND a.code IN <foreach
                    collection="checkedCodeList" item="code" open="(" separator="," close=")">
        #{code} </foreach>
            </if>
        </where> order by a.aset_type, a.code asc </select>
    <!--    and a.ref_price = sto.price-->
    <!-- 联查出了样品对应库位  计量方式和计量单位返回字段调换了一下-->
    <select id="queryAsetDetails" resultType="com.jp.med.mmis.modules.asetInfo.vo.MmisAsetInfoVo">
        select a.id as id, a.code as key, a.code as code, a.code as itemNum, a.name as name,
        a.easy_code as easyCode, a.aset_type as asetType, a.mtr_type as mtrType, b.meter_name as
        meterUnitName, c.meter_name as meterUnitName, b.base_unit_coefficient as
        baseUnitCoefficient, a.mtr_unit as mtrUnit, c.meter_name as meterTypeName, a.modspec as
        modspec, a.orplc as orplc, a.prdr as prdr, a.aset_alis as asetAlis, a.aset_barc as asetBarc,
        a.aset_brad as asetBrad, a.exprin_date as exprinDate, a.rgt_brad as rgtBrad, a.min_reserve
        as minReserve, a.max_reserve as maxReserve, a.best_reserve as bestReserve, a.ref_price as
        refPrice, a.imp_deg as impDeg, a.use_status as useStatus, a.is_kit as isKit, a.remark as
        remark, a.att as att, a.att_name as attName, a.crter as crter, a.crter_time as crterTime,
        a.updtr as updtr, a.updtr_time as updtrTime, a.is_deleted as isDeleted, a.hospital_id as
        hospitalId, wrhs.wrhs_name as wrhsAddrName, wrhs.wrhs as wrhsAddr, a.mfg_date as mfgDate,
        a.is_warehouse as isWarehouse, TO_CHAR( CASE WHEN a.mfg_date IS NULL THEN NULL ELSE
        TO_DATE(a.mfg_date, 'yyyy-MM-dd') + INTERVAL '1 day' * CAST(COALESCE(a.exprin_date, '0')AS
        INTEGER) END, 'yyyy-MM-dd' ) AS expDate, a.source_num as sourceNum, d.name as asetTypeName,
        sto.actNum as actNum from mmis_aset_info a LEFT JOIN mmis_metering_mode_cfg b ON a.mtr_type
        = b.meter_code LEFT JOIN mmis_metering_assist c ON a.mtr_unit = c.id :: VARCHAR LEFT JOIN
        mmis_aset_type d ON a.aset_type = d.code LEFT JOIN ( SELECT type.code, type.wrhs,
        info.wrhs_name FROM mmis_aset_type type LEFT JOIN mmis_wrhs_info info ON type.wrhs
        =info.wrhs_code ) wrhs ON a.aset_type = wrhs.code LEFT JOIN ( SELECT item_num , name, SUM(
        num ) AS actNum FROM mmis_material_sum GROUP BY item_num, name ) sto ON a.code =
        sto.item_num ::VARCHAR <where> (a.is_deleted != 1 OR a.is_deleted IS NULL) AND
        (b.active_flag != '1' OR b.active_flag IS NULL) AND (c.active_flag != '1' OR c.active_flag
        IS NULL) AND (d.is_deleted != 1 OR d.is_deleted IS NULL) <if
                test="name != null and name != ''"> AND (a.name like concat('',
        #{name,jdbcType=VARCHAR}, '%') or a.easy_code like concat('', #{name,jdbcType=VARCHAR},
        '%')) </if>
            <if test="asetType != null and asetType != ''"> AND a.aset_type =
        #{asetType,jdbcType=VARCHAR} </if>
            <if test="wrhsAddr != null and wrhsAddr != ''"> AND
        wrhs.wrhs = #{wrhsAddr,jdbcType=VARCHAR} </if>
            <if test="isWarehouse != null "> AND
        a.is_warehouse = #{isWarehouse,jdbcType=Integer} </if>
            <if
                test="checkedCodeList != null and checkedCodeList.size() > 0"> AND a.code IN <foreach
                    collection="checkedCodeList" item="code" open="(" separator="," close=")">
        #{code} </foreach>
            </if>
        </where>
    </select>

    <select id="queryAsetInfoCode" resultType="java.lang.String"> select code from mmis_aset_info
        where code like concat('', #{asetType,jdbcType=VARCHAR}, '%') order by code desc limit 1 </select>
    <select id="queryListByName" parameterType="string"
        resultType="com.jp.med.mmis.modules.asetInfo.vo.MmisAsetInfoVo"> select id, code, name from
        mmis_aset_info <where>
        replace(replace(replace(replace(replace(replace(replace(replace(replace(replace(replace(replace(replace(
        name, '（', ''), '）', ''), ')', ''), '(', ''), '|', ''), '&gt;', ''), '&lt;', ''), '*', ''),
        '`', ''), '/', ''), '\', ''), ':', ''), '?', '') = #{name,jdbcType=VARCHAR} <if
                test="isWarehouse != null "> AND is_warehouse = #{isWarehouse,jdbcType=Integer} </if>
        </where>
        order by code desc LIMIT 1 </select>

    <select id="queryByDto" resultType="com.jp.med.mmis.modules.asetInfo.vo.MmisAsetInfoVo"> select
        a.id as id, a.code as key, a.code as code from mmis_aset_info a <where> name =
        #{name,jdbcType=VARCHAR} <if test="code != null and code != ''"> and code =
        #{code,jdbcType=VARCHAR} </if>
            <if test="refPrice != null and refPrice != ''"> and ref_price
        = #{refPrice,jdbcType=DECIMAL} </if>
            <if test="easyCode != null and easyCode != ''"> and
        easy_code = #{easyCode,jdbcType=VARCHAR} </if>
            <if test="asetType != null and asetType != ''">
        and aset_type = #{asetType,jdbcType=VARCHAR} </if>
            <if
                test="mtrType != null and mtrType != ''"> and mtr_type = #{mtrType,jdbcType=VARCHAR} </if>
            <if
                test="modspec != null and modspec != ''"> and modspec = #{modspec,jdbcType=VARCHAR} </if>
            <if
                test="useStatus != null and useStatus != ''"> and use_status =
        #{useStatus,jdbcType=VARCHAR} </if>
            <if test="isWarehouse != null "> AND a.is_warehouse =
        #{isWarehouse,jdbcType=Integer} </if>
        </where>
    </select>

    <select id="queryAsetToReplenish"
        resultType="com.jp.med.mmis.modules.asetInfo.vo.MmisAsetInfoVo"> SELECT A .ID AS ID, A.code
        AS KEY, wrhs.wrhs AS wrhsAddr, wrhs.wrhs_name AS wrhsName, A.code AS code, A.NAME AS NAME,
        A.easy_code AS easyCode, A.aset_type AS asetType, A.mtr_type AS mtrType, b.meter_name AS
        meterTypeName, A.mtr_unit AS mtrUnit, A.ref_price AS refPrice, A.min_reserve AS minReserve,
        A.modspec AS modspec, A.max_reserve AS maxReserve, A.best_reserve AS bestReserve,
        A.source_num AS sourceNum, A.replenish_num AS replenishNum, A.mfg_date AS mfgDate,
        A.exprin_date AS exprinDate, A.rgt_brad AS rgtBrad, A.is_kit AS isKit, A.remark AS remark,
        A.att AS att, A.att_name AS attName, A.crter AS crter, A.crter_time AS crterTime, A.updtr AS
        updtr, A.updtr_time AS updtrTime, A.is_deleted AS isDeleted, sto.actNum as actNum,
        A.hospital_id AS hospitalId, A.is_warehouse as isWarehouse FROM mmis_aset_info A LEFT JOIN
        mmis_metering_mode_cfg b ON A.mtr_type = b.meter_code LEFT JOIN ( SELECT item_num, SUM ( num
        ) AS actNum FROM mmis_material_sum GROUP BY item_num ) sto ON A.code = sto.item_num ::
        VARCHAR LEFT JOIN mmis_aset_type d ON A.aset_type = d.code LEFT JOIN ( SELECT TYPE .code,
        TYPE.wrhs, info.wrhs_name FROM mmis_aset_type TYPE LEFT JOIN mmis_wrhs_info info ON
        TYPE.wrhs = info.wrhs_code ) wrhs ON A.aset_type = wrhs.code <where> A.is_deleted != 1 AND
        A.use_status = '1' <if
                test="checkedCodeList != null and checkedCodeList.size() > 0"> AND a.code IN <foreach
                    item="item" index="index" collection="checkedCodeList" open="(" separator=","
                    close=")"> #{item} </foreach>
            </if>
            <if
                test="name != null and name != ''"> AND (a.name like concat('',
        #{name,jdbcType=VARCHAR}, '%') or a.easy_code like concat('', #{name,jdbcType=VARCHAR},
        '%')) </if>
            <if
                test="asetType != null and asetType != ''"> AND a.aset_type = #{asetType} </if>
            <if
                test="wrhsAddr != null and wrhsAddr != ''"> AND wrhs.wrhs =
        #{wrhsAddr,jdbcType=VARCHAR} </if>
            <if test="isWarehouse != null "> AND a.is_warehouse =
        #{isWarehouse,jdbcType=INTEGER} </if>


            <if test="stockStatus != null and stockStatus != ''">
                <if test="stockStatus == 1"> AND CAST(sto.actNum AS numeric) &gt; 0 </if>

            <if
                    test="stockStatus == 0"> AND CAST(sto.actNum AS numeric) = 0 </if>
            <if
                    test="stockStatus == 2"> AND CAST(sto.actNum AS numeric) &lt; 0 </if>
            </if>
        </where>
        order by a.code asc </select>


    <select id="findSimilarAset" resultType="com.jp.med.mmis.modules.asetInfo.dto.MmisAsetInfoDto">
        SELECT * FROM mmis_aset_info_assist <where> name = #{name,jdbcType=VARCHAR} <if
                test="easyCode != null and easyCode != ''"> AND easy_code =
        #{easyCode,jdbcType=VARCHAR} </if>
            <if test="asetType != null and asetType != ''"> AND
        aset_type = #{asetType,jdbcType=VARCHAR} </if>
            <if test="mtrType != null and mtrType != ''">
        AND mtr_type = #{mtrType,jdbcType=VARCHAR} </if>
            <if test="mtrUnit != null and mtrUnit != ''">
        AND mtr_unit = #{mtrUnit,jdbcType=VARCHAR} </if>
            <if test="modspec != null and modspec != ''">
        AND modspec = #{modspec,jdbcType=VARCHAR} </if>

            <if test="orplc != null and orplc != ''"> AND
        orplc = #{orplc,jdbcType=VARCHAR} </if>
            <if test="prdr != null and prdr != ''"> AND prdr =
        #{prdr,jdbcType=VARCHAR} </if>
            <if test="asetAlis != null and asetAlis != ''"> AND aset_alis
        = #{asetAlis,jdbcType=VARCHAR} </if>
            <if test="asetBarc != null and asetBarc != ''"> AND
        aset_barc = #{asetBarc,jdbcType=VARCHAR} </if>
            <if test="asetBrad != null and asetBrad != ''">
        AND aset_brad = #{asetBrad,jdbcType=VARCHAR} </if>

            <if
                test="exprinDate != null and exprinDate != ''"> AND exprin_date =
        #{exprinDate,jdbcType=VARCHAR} </if>
            <if test="rgtBrad != null and rgtBrad != ''"> AND
        rgt_brad = #{rgtBrad,jdbcType=VARCHAR} </if>
            <if
                test="minReserve != null and minReserve != ''"> AND min_reserve =
        #{minReserve,jdbcType=VARCHAR} </if>
            <if test="maxReserve != null and maxReserve != ''"> AND
        max_reserve = #{maxReserve,jdbcType=VARCHAR} </if>
            <if
                test="bestReserve != null and bestReserve != ''"> AND best_reserve =
        #{bestReserve,jdbcType=VARCHAR} </if>
            <if test="impDeg != null and impDeg != ''"> AND imp_deg
        = #{impDeg,jdbcType=VARCHAR} </if>
            <if test="useStatus != null and useStatus != ''"> AND
        use_status = #{useStatus,jdbcType=VARCHAR} </if>
            <if test="isKit != null and isKit != ''">
        AND is_kit = #{isKit,jdbcType=VARCHAR} </if>
            <if test="remark != null and remark != ''"> AND
        remark = #{remark,jdbcType=VARCHAR} </if>
            <if test="att != null and att != ''"> AND att =
        #{att,jdbcType=VARCHAR} </if>
            <if test="attName != null and attName != ''"> AND att_name =
        #{attName,jdbcType=VARCHAR} </if>
            <if test="hospitalId != null and hospitalId != ''"> AND
        hospital_id = #{hospitalId,jdbcType=VARCHAR} </if>
            <if test="isWarehouse != null "> AND
        is_warehouse = #{isWarehouse,jdbcType=INTEGER} </if>
        </where>
    </select>

   
</mapper>