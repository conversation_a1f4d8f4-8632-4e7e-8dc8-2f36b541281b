<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.matReceipt.mapper.read.MmisInvoRcdReadMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.mmis.modules.matReceipt.vo.MmisInvoRcdVo" id="invoRcdMap">
        <result property="id" column="id"/>
        <result property="invoCode" column="invo_code"/>
        <result property="invoNum" column="invo_num"/>
        <result property="invoDate" column="invo_date"/>
        <result property="chkCode" column="chk_code"/>
        <result property="att" column="att"/>
        <result property="attName" column="att_name"/>
        <result property="chkState" column="chk_state"/>
        <result property="chkTime" column="chk_time"/>
        <result property="idtfErrMsg" column="idtf_err_msg"/>
        <result property="createTime" column="create_time"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="chkData" column="chk_data"/>
    </resultMap>
    <sql id="fields">
        a
        .
        id
        as id,
            a.invo_code as invoCode,
            a.invo_num as invoNum,
            a.invo_date as invoDate,
            a.chk_code as chkCode,
            a.att as att,
            a.att_name as attName,
            a.chk_state as chkState,
            a.chk_time as chkTime,
            a.idtf_err_msg as idtfErrMsg,
            a.create_time as createTime,
            a.hospital_id as hospitalId,
            a.state as state,
            a.chk_data as chkData
    </sql>
    <select id="queryList" resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisInvoRcdVo">
        select
        <include refid="fields"/>
        from mmis_invo_rcd a
        <where>
            <if test="id != null ">
                and a.id = #{id,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="getInvoiceInfo" resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisInvoRcdVo">
        SELECT
            att,
            att_name as attName
        FROM mmis_invo_rcd
        WHERE CAST(id AS VARCHAR) = #{invoiceId,jdbcType=VARCHAR}


    </select>

    <select id="queryExistsRcd" resultType="com.jp.med.mmis.modules.matReceipt.vo.MmisInvoRcdVo">
        select
        <include refid="fields"/>
        from mmis_invo_rcd a
        where exists (
        select *
        from(
        <foreach collection="list" item="rcd" separator=" union all ">
            select #{rcd.invoCode,jdbcType=VARCHAR} as invoCode,
            #{rcd.invoNum,jdbcType=VARCHAR} as invoNum,
            #{rcd.invoDate,jdbcType=VARCHAR} as invoDate,
            #{rcd.chkCode,jdbcType=VARCHAR} as chkCode
        </foreach>
        ) b
        where a.invo_code = b.invoCode
        and a.invo_num = b.invoNum
        and a.invo_date = b.invoDate
        and a.chk_code = b.chkCode
        )
    </select>
    <!-- 查询是否已经报销 -->
    <select id="queryAlreadyReim" resultType="java.lang.Integer">
        select count(1)
        from mmis_invo_rcd
        where id in (
        <foreach collection="list" item="id" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
        )
        and state = '2'
    </select>
</mapper>
