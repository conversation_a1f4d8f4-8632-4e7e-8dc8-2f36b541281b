# 入库汇总 SQL 分析文档 📊

## 1. 原始 SQL 分析 🔍

`queryMatReceiptSum`是一个查询入库物资汇总的 SQL，它执行以下操作：

1. 创建临时结果集`hstod`，包含入库单和明细信息
2. 关联多个表获取完整信息：
   - `mmis_aset_storage`：入库单表
   - `hrm_org`：组织机构表
   - `mmis_aset_storage_detail`：入库明细表
   - `mmis_aset_info`：物资信息表
   - `mmis_aset_type`：物资类型表
   - `mmis_wrhs_info`：仓库信息表
3. 按照科室和仓库分组统计各类物资金额

**存在问题**：原 SQL 只支持总务库('01'开头)的分类统计，不支持二级维修库('03'开头)的分类统计。

## 2. 测试 SQL 示例 🧪

### 2.1 完整汇总 SQL（支持二级维修库分类）

```sql
WITH hstod AS (
    SELECT
        sto.in_org_id,
        org.org_name,
        sto.type_code,
        det.apply_id,
        det.amt,
        det.num,
        det.ref_Price,
        det.item_num,
        det.aset_type,
        det.parent_code,
        det.wrhs_name,
        tyinfo.name as faTypeName,
        sto.in_time,
        det.wrhs_code,
        sto.hospital_id
    FROM mmis_aset_storage sto
    LEFT JOIN hrm_org org ON sto.in_org_id = org.org_id
    LEFT JOIN (
        SELECT
            d.apply_id,
            d.amt,
            d.num,
            d.item_num,
            info.aset_type,
            info.ref_Price,
            ty.parent_code,
            wrhs.wrhs_name,
            wrhs.wrhs_code
        FROM mmis_aset_storage_detail d
        LEFT JOIN mmis_aset_info info ON d.item_num = info.code
        LEFT JOIN mmis_aset_type ty ON info.aset_type = ty.code
        LEFT JOIN mmis_wrhs_info wrhs ON ty.wrhs = wrhs.wrhs_code
    ) det ON sto.id = det.apply_id
    LEFT JOIN (
        SELECT
            type.code,
            type.name
        FROM mmis_aset_type type
        WHERE type.parent_code IN ('01', '03')  -- 修改此处支持二级维修库
    ) tyinfo ON det.parent_code = tyinfo.code
    WHERE
        sto.in_status = '1'
        AND sto.chk_state = '1'
        AND sto.in_time BETWEEN '2025-02-01' AND '2025-02-28'
)
SELECT
    hstod.in_org_id as inOrgId,
    hstod.org_name as inOrgName,
    hstod.wrhs_code as wrhsCode,
    hstod.wrhs_name as wrhsName,
    hstod.hospital_id as hospitalId,
    SUM(amt) AS totalAmt,
    -- 修改以下几行支持二级维修库分类
    SUM(CASE WHEN hstod.parent_code IN ('0101', '0301') THEN amt ELSE 0 END) AS lowPriceAmt,
    SUM(CASE WHEN hstod.parent_code IN ('0102', '0302') THEN amt ELSE 0 END) AS otherAmt,
    SUM(CASE WHEN hstod.parent_code IN ('0103', '0303') THEN amt ELSE 0 END) AS norEquAmt,
    SUM(CASE WHEN hstod.parent_code IN ('0104', '0304') THEN amt ELSE 0 END) AS speEquAmt
FROM hstod
GROUP BY
    hstod.in_org_id,
    hstod.org_name,
    hstod.wrhs_code,
    hstod.wrhs_name,
    hstod.hospital_id
```

### 2.2 简化版总金额查询 SQL（2025 年 2 月数据）

```sql
SELECT
    SUM(d.amt) AS totalAmt,
    sto.hospital_id
FROM
    mmis_aset_storage sto
JOIN
    mmis_aset_storage_detail d ON sto.id = d.apply_id
WHERE
    sto.in_status = '1'
    AND sto.chk_state = '1'
    AND sto.in_time BETWEEN '2025-02-01' AND '2025-02-28'
GROUP BY
    sto.hospital_id
```

## 3. SQL 功能说明 📝

### 3.1 支持二级维修库的修改点

1. **修改点一**：

   - 原 SQL：`WHERE type.parent_code = '01'`
   - 新 SQL：`WHERE type.parent_code IN ('01', '03')`
   - 说明：允许查询二级维修库的分类代码

2. **修改点二**：
   - 原 SQL：`SUM(CASE WHEN hstod.parent_code = '0101' THEN amt ELSE 0 END) AS lowPriceAmt`
   - 新 SQL：`SUM(CASE WHEN hstod.parent_code IN ('0101', '0301') THEN amt ELSE 0 END) AS lowPriceAmt`
   - 说明：统计时包含二级维修库对应分类的金额

### 3.2 统计指标说明

1. **物资分类对应关系**：

   - 低值易耗品(lowPriceAmt)：总务库(0101) + 二级维修库(0301)
   - 其他材料(otherAmt)：总务库(0102) + 二级维修库(0302)
   - 一般设备(norEquAmt)：总务库(0103) + 二级维修库(0303)
   - 专用设备(speEquAmt)：总务库(0104) + 二级维修库(0304)

2. **分组维度**：
   - 入库科室（in_org_id, org_name）
   - 仓库信息（wrhs_code, wrhs_name）
   - 医院 ID（hospital_id）
 