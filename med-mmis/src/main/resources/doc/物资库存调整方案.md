# 物资库存调整功能实现方案 📋

## 一、功能概述 🎯

### 1.1 功能描述

实现物资库存调整功能，包括数据备份和库存更新两个主要步骤：

1. 将当前库存数据备份到备份表
2. 根据理论值更新库存数据

### 1.2 业务流程

1. 接收前端传入的调整参数
2. 验证参数有效性
3. 备份当前库存数据
4. 更新库存数据为理论值
5. 返回处理结果

## 二、技术方案 💡

### 2.1 数据库设计

#### 2.1.1 备份表结构(mmis_material_sum_backup)

```sql
CREATE TABLE mmis_material_sum_backup (
    id VARCHAR(32) PRIMARY KEY,
    backup_time TIMESTAMP NOT NULL,
    backup_type VARCHAR(20) NOT NULL,
    mat_unique_code VARCHAR(50) NOT NULL,
    original_num DECIMAL(18,2),
    original_amt DECIMAL(18,2),
    adjust_num DECIMAL(18,2),
    adjust_amt DECIMAL(18,2),
    remark VARCHAR(500),
    crter VARCHAR(50),
    create_time TIMESTAMP,
    hospital_id VARCHAR(50),
    is_deleted CHAR(1)
);
```

### 2.2 接口设计

#### 2.2.1 请求参数

```typescript
interface AdjustStockRequest {
  matUniqueCode: string; // 物资唯一编码
  adjustQuantity: number; // 调整数量
  adjustAmount: number; // 调整金额
  remark: string; // 调整备注
}
```

#### 2.2.2 响应参数

```typescript
interface AdjustStockResponse {
  code: number; // 响应码
  message: string; // 响应消息
  data: {
    backupId: string; // 备份记录ID
    adjustTime: string; // 调整时间
    success: boolean; // 是否成功
  };
}
```

### 2.3 实现步骤

1. **Controller 层**

   - 创建新接口 `/mmisMaterialSum/adjustStock`
   - 添加参数验证
   - 调用 Service 层方法

2. **Service 层**

   - 创建调整库存方法
   - 实现事务管理
   - 处理异常情况

3. **Mapper 层**

   - 添加备份数据方法
   - 添加更新库存方法
   - 编写 SQL 语句

4. **异常处理**
   - 参数验证异常
   - 数据不存在异常
   - 更新失败异常

## 三、代码实现 📝

### 3.1 新增 DTO 类

```java
@Data
public class AdjustStockDTO {
    private String matUniqueCode;
    private BigDecimal adjustQuantity;
    private BigDecimal adjustAmount;
    private String remark;
}
```

### 3.2 新增 VO 类

```java
@Data
public class AdjustStockVO {
    private String backupId;
    private String adjustTime;
    private Boolean success;
}
```

### 3.3 新增 Mapper 方法

```java
public interface MmisMaterialSumMapper {
    // 备份库存数据
    int backupStockData(MmisMaterialSumBackup backup);

    // 更新库存数据
    int updateStockData(@Param("matUniqueCode") String matUniqueCode,
                       @Param("adjustQuantity") BigDecimal adjustQuantity,
                       @Param("adjustAmount") BigDecimal adjustAmount);
}
```

### 3.4 新增 Service 方法

```java
public interface MmisMaterialSumService {
    // 调整库存
    AdjustStockVO adjustStock(AdjustStockDTO dto);
}
```

### 3.5 新增 Controller 接口

```java
@ApiOperation("调整物资库存")
@PostMapping("/adjustStock")
public CommonResult<AdjustStockVO> adjustStock(@RequestBody AdjustStockDTO dto) {
    return CommonResult.success(mmisMaterialSumService.adjustStock(dto));
}
```

## 四、注意事项 ⚠️

1. **数据一致性**

   - 使用事务确保备份和更新操作的原子性
   - 添加乐观锁防止并发更新

2. **异常处理**

   - 参数验证失败时返回友好提示
   - 数据不存在时返回明确错误信息
   - 更新失败时回滚事务

3. **日志记录**

   - 记录调整操作日志
   - 记录异常信息
   - 记录操作人信息

4. **性能优化**
   - 添加必要的索引
   - 优化 SQL 语句
   - 控制事务范围

## 五、测试计划 🧪

1. **单元测试**

   - 参数验证测试
   - 业务逻辑测试
   - 异常处理测试

2. **集成测试**

   - 接口功能测试
   - 事务完整性测试
   - 并发操作测试

3. **性能测试**
   - 响应时间测试
   - 并发处理测试
   - 资源占用测试
