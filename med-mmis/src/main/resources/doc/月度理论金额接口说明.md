# 月度理论金额查询接口说明

## 接口说明

本接口用于查询从 2024 年 12 月开始到当前月份的物资理论金额、入库金额和出库金额。系统会自动计算每个月的理论金额，理论金额的计算逻辑为：当月理论金额 = 上月理论金额 + 当月入库金额 - 当月出库金额。

## 接口地址

- 请求路径：`/mmisMaterialSum/queryMonthlyTheoreticalAmount`
- 请求方法：POST
- 参数类型：JSON

## 请求参数

```json
{
  // 可以为空，暂不需要额外参数
}
```

## 返回结果

```json
{
  "code": 200,
  "msg": "成功",
  "data": [
    {
      "month": "Dec-2024", // 月份
      "storageAmt": 0, // 入库金额
      "outboundAmt": 0, // 出库金额
      "theoreticalAmt": 636583.99 // 理论金额
    },
    {
      "month": "Jan-2025",
      "storageAmt": 268182.32,
      "outboundAmt": 167888.71,
      "theoreticalAmt": 736877.6 // 12月理论金额 + 1月入库 - 1月出库
    }
    // ... 更多月份数据
  ]
}
```

## 字段说明

| 字段名         | 类型       | 说明                             |
| -------------- | ---------- | -------------------------------- |
| month          | String     | 月份，格式如：Dec-2024, Jan-2025 |
| storageAmt     | BigDecimal | 当月入库金额                     |
| outboundAmt    | BigDecimal | 当月出库金额                     |
| theoreticalAmt | BigDecimal | 理论库存金额                     |

## 计算逻辑

- 第一个月（2024 年 12 月）：理论金额 = 基准库存金额 + 12 月入库金额 - 12 月出库金额
- 后续月份：理论金额 = 上月理论金额 + 当月入库金额 - 当月出库金额

## 注意事项

1. 基准库存数据来源于 `mmis_last_stock_temp` 表
2. 入库数据查询条件：`in_status='1' AND chk_state='1'`
3. 出库数据查询条件：`(out_status='1' OR out_status='2') AND chk_state='1'`
4. 系统会自动计算从 2024 年 12 月到当前月份的所有数据

## 前端表格配置

以下是前端表格（Table）组件的列配置参考：

### 表格列配置

```typescript
const columns = [
  {
    title: "月份",
    dataIndex: "month",
    key: "month",
    align: "center",
  },
  {
    title: "入库金额",
    dataIndex: "storageAmt",
    key: "storageAmt",
    align: "right",
    render: (text) => (text ? Number(text).toFixed(2) : "0.00"),
  },
  {
    title: "出库金额",
    dataIndex: "outboundAmt",
    key: "outboundAmt",
    align: "right",
    render: (text) => (text ? Number(text).toFixed(2) : "0.00"),
  },
  {
    title: "理论库存金额",
    dataIndex: "theoreticalAmt",
    key: "theoreticalAmt",
    align: "right",
    render: (text) => (text ? Number(text).toFixed(2) : "0.00"),
  },
];
```

### 列字段对照表

| 列标题（title） | 字段标识（key/dataIndex） | 对齐方式 | 说明                 |
| --------------- | ------------------------- | -------- | -------------------- |
| 月份            | month                     | 居中     | 月份名称             |
| 入库金额        | storageAmt                | 右对齐   | 当月入库总金额       |
| 出库金额        | outboundAmt               | 右对齐   | 当月出库总金额       |
| 理论库存金额    | theoreticalAmt            | 右对齐   | 累计计算的理论库存额 |
