# 物资对账接口说明

## 接口基本信息

- **接口 URL**: `/mmisMaterialSum/queryStockCheck`
- **请求方式**: POST
- **接口描述**: 查询物资对账数据，包含实际库存与理论库存的对比信息

## 请求参数

请求体为 JSON 格式，包含以下字段：

| 参数名        | 类型   | 必填 | 说明                   |
| ------------- | ------ | ---- | ---------------------- |
| hospitalId    | String | 否   | 医院 ID                |
| matUniqueCode | String | 否   | 物资唯一编码           |
| name          | String | 否   | 物资名称，支持模糊查询 |

## 响应参数

响应数据为 JSON 格式，包含以下字段：

### 基本信息

| 字段名        | 类型       | 说明                         |
| ------------- | ---------- | ---------------------------- |
| key           | Long       | 序号（按名称降序）           |
| name          | String     | 物资名称                     |
| actNum        | BigDecimal | 实际库存数量                 |
| price         | BigDecimal | 参考单价                     |
| amt           | BigDecimal | 实际库存金额（数量 \* 单价） |
| oldAmt        | BigDecimal | 原始库存金额                 |
| easyCode      | String     | 物资助记码                   |
| modspec       | String     | 规格型号                     |
| meterUnitName | String     | 计量单位名称                 |
| meterCode     | String     | 计量单位代码                 |
| wrhsAddr      | String     | 库房代码                     |
| wrhsAddrName  | String     | 库房名称                     |
| asetType      | String     | 物资类型代码                 |
| asetTypeName  | String     | 物资类型名称                 |
| itemNum       | String     | 物资编号                     |
| matUniqueCode | String     | 物资唯一编码                 |
| hospitalId    | String     | 医院 ID                      |

### 理论库存数据

| 字段名      | 类型       | 说明                       |
| ----------- | ---------- | -------------------------- |
| initialNum  | BigDecimal | 初始库存数量（来自临时表） |
| initialAmt  | BigDecimal | 初始库存金额（来自临时表） |
| storageNum  | BigDecimal | 累计入库数量               |
| storageAmt  | BigDecimal | 累计入库金额               |
| outboundNum | BigDecimal | 累计出库数量               |
| outboundAmt | BigDecimal | 累计出库金额               |
| targetNum   | BigDecimal | 理论库存数量               |
| targetAmt   | BigDecimal | 理论库存金额               |

### 差异数据

| 字段名  | 类型       | 说明                            |
| ------- | ---------- | ------------------------------- |
| numDiff | BigDecimal | 数量差异（理论数量 - 实际数量） |
| amtDiff | BigDecimal | 金额差异（理论金额 - 实际金额） |

## 计算逻辑说明

1. **理论库存计算**:

   - 如果物资在临时表中存在记录：
     - 理论数量 = 初始数量 + 入库数量 - 出库数量
     - 理论金额 = 初始金额 + 入库金额 - 出库金额
   - 如果物资在临时表中不存在记录：
     - 理论数量 = 入库数量 - 出库数量
     - 理论金额 = 入库金额 - 出库金额

2. **差异计算**:
   - 数量差异 = 理论数量 - 实际数量
   - 金额差异 = 理论金额 - 实际金额

## 示例

### 请求示例

```json
{
  "hospitalId": "1001",
  "matUniqueCode": "0101021227006vwx",
  "name": "注射器"
}
```

### 响应示例

```json
{
  "code": 200,
  "msg": "success",
  "data": [
    {
      "key": 1,
      "name": "一次性使用注射器",
      "actNum": 1000.0,
      "price": 0.5,
      "amt": 500.0,
      "oldAmt": 500.0,
      "easyCode": "YCXSYZQ",
      "modspec": "20ml",
      "meterUnitName": "支",
      "meterCode": "001",
      "wrhsAddr": "01",
      "wrhsAddrName": "总务库房",
      "asetType": "010101",
      "asetTypeName": "低值易耗品",
      "itemNum": "0101021227006",
      "matUniqueCode": "0101021227006vwx",
      "hospitalId": "1001",
      "initialNum": 800.0,
      "initialAmt": 400.0,
      "storageNum": 500.0,
      "storageAmt": 250.0,
      "outboundNum": 300.0,
      "outboundAmt": 150.0,
      "targetNum": 1000.0,
      "targetAmt": 500.0,
      "numDiff": 0.0,
      "amtDiff": 0.0
    }
  ]
}
```

## 注意事项

1. 所有金额字段均保留 2 位小数
2. 数量差异和金额差异为正数表示实际库存小于理论库存（库存减少）
3. 差异为负数表示实际库存大于理论库存（库存增加）
4. 查询结果按物资名称降序排列
5. 支持按医院 ID、物资唯一编码和物资名称（模糊）进行筛选

## 前端表格列配置说明

以下是用于构建表格列的字段配置，包含字段名、中文标题和数据类型：

```typescript
const columns = [
  {
    title: "序号",
    dataIndex: "key",
    width: 80,
    fixed: "left",
  },
  {
    title: "物资名称",
    dataIndex: "name",
    width: 200,
    fixed: "left",
  },
  {
    title: "物资编号",
    dataIndex: "itemNum",
    width: 150,
  },
  {
    title: "物资唯一编码",
    dataIndex: "matUniqueCode",
    width: 180,
  },
  {
    title: "规格型号",
    dataIndex: "modspec",
    width: 120,
  },
  {
    title: "计量单位",
    dataIndex: "meterUnitName",
    width: 100,
  },
  {
    title: "库房",
    dataIndex: "wrhsAddrName",
    width: 120,
  },
  {
    title: "物资类型",
    dataIndex: "asetTypeName",
    width: 120,
  },
  {
    title: "参考单价",
    dataIndex: "price",
    width: 120,
    align: "right",
  },
  {
    title: "实际库存",
    children: [
      {
        title: "数量",
        dataIndex: "actNum",
        width: 120,
        align: "right",
      },
      {
        title: "金额",
        dataIndex: "amt",
        width: 120,
        align: "right",
      },
    ],
  },
  {
    title: "理论库存",
    children: [
      {
        title: "初始数量",
        dataIndex: "initialNum",
        width: 120,
        align: "right",
      },
      {
        title: "初始金额",
        dataIndex: "initialAmt",
        width: 120,
        align: "right",
      },
      {
        title: "入库数量",
        dataIndex: "storageNum",
        width: 120,
        align: "right",
      },
      {
        title: "入库金额",
        dataIndex: "storageAmt",
        width: 120,
        align: "right",
      },
      {
        title: "出库数量",
        dataIndex: "outboundNum",
        width: 120,
        align: "right",
      },
      {
        title: "出库金额",
        dataIndex: "outboundAmt",
        width: 120,
        align: "right",
      },
      {
        title: "理论数量",
        dataIndex: "targetNum",
        width: 120,
        align: "right",
      },
      {
        title: "理论金额",
        dataIndex: "targetAmt",
        width: 120,
        align: "right",
      },
    ],
  },
  {
    title: "差异",
    children: [
      {
        title: "数量差异",
        dataIndex: "numDiff",
        width: 120,
        align: "right",
      },
      {
        title: "金额差异",
        dataIndex: "amtDiff",
        width: 120,
        align: "right",
      },
    ],
  },
  {
    title: "助记码",
    dataIndex: "easyCode",
    width: 100,
  },
];
```

### 列配置说明

1. 固定列

   - 序号和物资名称列固定在左侧
   - 其他列可以横向滚动

2. 分组列

   - 实际库存：包含数量和金额
   - 理论库存：包含初始、入库、出库和理论值
   - 差异：包含数量差异和金额差异

3. 对齐方式

   - 数字类型（金额、数量）右对齐
   - 文本类型默认左对齐

4. 列宽建议
   - 序号：80px
   - 物资名称：200px
   - 编码类：150-180px
   - 数量金额类：120px
   - 其他文本：100-120px

### 数据格式化建议

1. 金额类数据

   ```typescript
   // 保留2位小数，千分位分隔
   const formatAmount = (value: number) => {
     return value?.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
   };
   ```

2. 数量类数据

   ```typescript
   // 保留2位小数，去除末尾0
   const formatNumber = (value: number) => {
     return value?.toFixed(2).replace(/\.?0+$/, "");
   };
   ```

3. 差异数据着色
   ```typescript
   // 差异数据样式
   const getDiffStyle = (value: number) => {
     if (value > 0) return { color: "#ff4d4f" }; // 红色：实际小于理论
     if (value < 0) return { color: "#52c41a" }; // 绿色：实际大于理论
     return {}; // 默认颜色：无差异
   };
   ```

## 字段对应关系表

| 字段名(key)   | 中文标题(title) | 说明                           |
| ------------- | --------------- | ------------------------------ |
| key           | 序号            | 自动生成的序号                 |
| name          | 物资名称        |                                |
| itemNum       | 物资编号        |                                |
| matUniqueCode | 物资唯一编码    |                                |
| modspec       | 规格型号        |                                |
| meterUnitName | 计量单位        |                                |
| wrhsAddrName  | 库房            |                                |
| asetTypeName  | 物资类型        |                                |
| price         | 参考单价        | 需要右对齐                     |
| actNum        | 实际数量        | 需要右对齐                     |
| amt           | 实际金额        | 需要右对齐                     |
| initialNum    | 初始数量        | 需要右对齐                     |
| initialAmt    | 初始金额        | 需要右对齐                     |
| storageNum    | 入库数量        | 需要右对齐                     |
| storageAmt    | 入库金额        | 需要右对齐                     |
| outboundNum   | 出库数量        | 需要右对齐                     |
| outboundAmt   | 出库金额        | 需要右对齐                     |
| targetNum     | 理论数量        | 需要右对齐                     |
| targetAmt     | 理论金额        | 需要右对齐                     |
| numDiff       | 数量差异        | 需要右对齐，正负值显示不同颜色 |
| amtDiff       | 金额差异        | 需要右对齐，正负值显示不同颜色 |
| easyCode      | 助记码          |                                |

### 分组关系

1. 实际库存

   - actNum (实际数量)
   - amt (实际金额)

2. 理论库存

   - initialNum, initialAmt (初始)
   - storageNum, storageAmt (入库)
   - outboundNum, outboundAmt (出库)
   - targetNum, targetAmt (理论)

3. 差异
   - numDiff (数量差异)
   - amtDiff (金额差异)

## 物资对账差异详情接口

### 接口基本信息

- **接口 URL**: `/mmisMaterialSum/queryStockCheckDetail`
- **请求方式**: POST
- **接口描述**: 查询特定物资的对账差异详情，包括更详细的入库、出库记录统计和库存差异分析

### 请求参数

请求体为 JSON 格式，包含以下字段：

| 参数名        | 类型   | 必填 | 说明         |
| ------------- | ------ | ---- | ------------ |
| matUniqueCode | String | 是   | 物资唯一编码 |

### 响应参数

响应数据为 JSON 格式，包含以下字段：

#### 物资基本信息

| 字段名        | 类型       | 说明         | 前端展示字段 |
| ------------- | ---------- | ------------ | ------------ |
| itemNum       | String     | 物资编号     | 物资编号     |
| materialName  | String     | 物资名称     | 物资名称     |
| specification | String     | 规格型号     | 规格型号     |
| unitName      | String     | 计量单位名称 | 计量单位     |
| typeName      | String     | 物资类型名称 | 物资类型     |
| warehouseName | String     | 库房名称     | 库房         |
| unitPrice     | BigDecimal | 参考单价     | 单价         |
| matUniqueCode | String     | 物资唯一编码 | 物资唯一编码 |
| hospitalId    | String     | 医院 ID      | 医院 ID      |

#### 库存数据

| 字段名         | 类型       | 说明                         | 前端展示字段 |
| -------------- | ---------- | ---------------------------- | ------------ |
| initialNum     | BigDecimal | 初始库存数量（基础数量）     | 初始数量     |
| initialAmt     | BigDecimal | 初始库存金额（基础金额）     | 初始金额     |
| storageNum     | BigDecimal | 累计入库数量                 | 入库数量     |
| storageAmt     | BigDecimal | 累计入库金额                 | 入库金额     |
| storageCount   | BigDecimal | 入库记录数量（笔数）         | 入库笔数     |
| outboundNum    | BigDecimal | 累计出库数量                 | 出库数量     |
| outboundAmt    | BigDecimal | 累计出库金额                 | 出库金额     |
| outboundCount  | BigDecimal | 出库记录数量（笔数）         | 出库笔数     |
| theoreticalNum | BigDecimal | 理论库存数量                 | 理论数量     |
| theoreticalAmt | BigDecimal | 理论库存金额                 | 理论金额     |
| actualNum      | BigDecimal | 实际库存数量                 | 实际数量     |
| actualAmt      | BigDecimal | 实际库存金额（数量 \* 单价） | 实际金额     |

#### 差异数据

| 字段名     | 类型       | 说明                            | 前端展示字段 |
| ---------- | ---------- | ------------------------------- | ------------ |
| diffNum    | BigDecimal | 数量差异（实际数量 - 理论数量） | 数量差异     |
| diffAmt    | BigDecimal | 金额差异（实际金额 - 理论金额） | 金额差异     |
| diffStatus | String     | 差异状态                        | 差异状态     |

### 差异状态说明

差异状态字段(diffStatus)可能的值有：

- `NoDeviation`: 无差异，实际库存等于理论库存
- `ActualGreater`: 实际库存大于理论库存（可能存在未记录入库）
- `TheoreticalGreater`: 理论库存大于实际库存（可能存在未记录出库）

### 计算逻辑说明

1. **理论库存计算**:

   - 理论数量 = 初始数量 + 入库数量 - 出库数量
   - 理论金额 = 初始金额 + 入库金额 - 出库金额

2. **差异计算**:
   - 数量差异 = 实际数量 - 理论数量
   - 金额差异 = 实际金额 - 理论金额
   - 差异状态根据数量差异判断：
     - 等于 0: 无差异
     - 大于 0: 实际库存大于理论库存
     - 小于 0: 理论库存大于实际库存

### 接口使用场景

1. 当用户点击物资对账列表中的某一行物资时，调用此接口获取该物资的详细对账信息
2. 用于深入分析单个物资的库存差异原因
3. 查看物资的详细入库和出库记录统计

### 前端表格列配置示例

```typescript
const detailColumns = [
  {
    title: "物资基本信息",
    children: [
      {
        title: "物资编号",
        dataIndex: "itemNum",
        width: 150,
      },
      {
        title: "物资名称",
        dataIndex: "materialName",
        width: 200,
      },
      {
        title: "规格型号",
        dataIndex: "specification",
        width: 120,
      },
      {
        title: "计量单位",
        dataIndex: "unitName",
        width: 100,
      },
      {
        title: "物资类型",
        dataIndex: "typeName",
        width: 120,
      },
      {
        title: "库房",
        dataIndex: "warehouseName",
        width: 120,
      },
      {
        title: "单价",
        dataIndex: "unitPrice",
        width: 120,
        align: "right",
      },
    ],
  },
  {
    title: "库存记录",
    children: [
      {
        title: "初始库存",
        children: [
          {
            title: "数量",
            dataIndex: "initialNum",
            width: 100,
            align: "right",
          },
          {
            title: "金额",
            dataIndex: "initialAmt",
            width: 100,
            align: "right",
          },
        ],
      },
      {
        title: "入库记录",
        children: [
          {
            title: "数量",
            dataIndex: "storageNum",
            width: 100,
            align: "right",
          },
          {
            title: "金额",
            dataIndex: "storageAmt",
            width: 100,
            align: "right",
          },
          {
            title: "笔数",
            dataIndex: "storageCount",
            width: 80,
            align: "right",
          },
        ],
      },
      {
        title: "出库记录",
        children: [
          {
            title: "数量",
            dataIndex: "outboundNum",
            width: 100,
            align: "right",
          },
          {
            title: "金额",
            dataIndex: "outboundAmt",
            width: 100,
            align: "right",
          },
          {
            title: "笔数",
            dataIndex: "outboundCount",
            width: 80,
            align: "right",
          },
        ],
      },
    ],
  },
  {
    title: "对账信息",
    children: [
      {
        title: "理论库存",
        children: [
          {
            title: "数量",
            dataIndex: "theoreticalNum",
            width: 100,
            align: "right",
          },
          {
            title: "金额",
            dataIndex: "theoreticalAmt",
            width: 100,
            align: "right",
          },
        ],
      },
      {
        title: "实际库存",
        children: [
          {
            title: "数量",
            dataIndex: "actualNum",
            width: 100,
            align: "right",
          },
          {
            title: "金额",
            dataIndex: "actualAmt",
            width: 100,
            align: "right",
          },
        ],
      },
      {
        title: "差异",
        children: [
          {
            title: "数量",
            dataIndex: "diffNum",
            width: 100,
            align: "right",
            render: (text) => ({
              props: {
                style: {
                  color:
                    text > 0 ? "#52c41a" : text < 0 ? "#f5222d" : "inherit",
                },
              },
              children: text,
            }),
          },
          {
            title: "金额",
            dataIndex: "diffAmt",
            width: 100,
            align: "right",
            render: (text) => ({
              props: {
                style: {
                  color:
                    text > 0 ? "#52c41a" : text < 0 ? "#f5222d" : "inherit",
                },
              },
              children: text,
            }),
          },
          {
            title: "状态",
            dataIndex: "diffStatus",
            width: 150,
            render: (text) => {
              const statusMap = {
                NoDeviation: { text: "无差异", color: "green" },
                ActualGreater: { text: "实际大于理论", color: "blue" },
                TheoreticalGreater: { text: "理论大于实际", color: "red" },
              };
              const status = statusMap[text] || { text: "未知", color: "gray" };
              return <Tag color={status.color}>{status.text}</Tag>;
            },
          },
        ],
      },
    ],
  },
];
```

### 字段映射表

前后端字段对照表：

| 后端字段名称   | 前端展示名称 | 分组              |
| -------------- | ------------ | ----------------- |
| itemNum        | 物资编号     | 物资基本信息      |
| materialName   | 物资名称     | 物资基本信息      |
| specification  | 规格型号     | 物资基本信息      |
| unitName       | 计量单位     | 物资基本信息      |
| typeName       | 物资类型     | 物资基本信息      |
| warehouseName  | 库房         | 物资基本信息      |
| unitPrice      | 单价         | 物资基本信息      |
| initialNum     | 初始数量     | 库存记录-初始库存 |
| initialAmt     | 初始金额     | 库存记录-初始库存 |
| storageNum     | 入库数量     | 库存记录-入库记录 |
| storageAmt     | 入库金额     | 库存记录-入库记录 |
| storageCount   | 入库笔数     | 库存记录-入库记录 |
| outboundNum    | 出库数量     | 库存记录-出库记录 |
| outboundAmt    | 出库金额     | 库存记录-出库记录 |
| outboundCount  | 出库笔数     | 库存记录-出库记录 |
| theoreticalNum | 理论数量     | 对账信息-理论库存 |
| theoreticalAmt | 理论金额     | 对账信息-理论库存 |
| actualNum      | 实际数量     | 对账信息-实际库存 |
| actualAmt      | 实际金额     | 对账信息-实际库存 |
| diffNum        | 数量差异     | 对账信息-差异     |
| diffAmt        | 金额差异     | 对账信息-差异     |
| diffStatus     | 差异状态     | 对账信息-差异     |

## 物资库存差异分析接口

### 接口基本信息

- **接口 URL**: `/mmisMaterialSum/queryStockAnalysis`
- **请求方式**: POST
- **接口描述**: 查询单个物资的库存差异分析，以表格形式展示期初、入库、出库、理论和实际库存数据

### 请求参数

请求体为 JSON 格式，包含以下字段：

| 参数名        | 类型   | 必填 | 说明         |
| ------------- | ------ | ---- | ------------ |
| matUniqueCode | String | 是   | 物资唯一编码 |

### 响应参数

响应数据为 JSON 格式，包含以下字段：

| 字段名         | 类型       | 说明                | 前端展示字段 |
| -------------- | ---------- | ------------------- | ------------ |
| initialNum     | BigDecimal | 期初库存数量        | 期初         |
| initialAmt     | BigDecimal | 期初库存金额        | 期初         |
| storageNum     | BigDecimal | 入库数量            | 入库         |
| storageAmt     | BigDecimal | 入库金额            | 入库         |
| storageCount   | Integer    | 入库记录笔数        | 入库         |
| outboundNum    | BigDecimal | 出库数量            | 出库         |
| outboundAmt    | BigDecimal | 出库金额            | 出库         |
| outboundCount  | Integer    | 出库记录笔数        | 出库         |
| theoreticalNum | BigDecimal | 理论库存数量        | 理论         |
| theoreticalAmt | BigDecimal | 理论库存金额        | 理论         |
| actualNum      | BigDecimal | 实际库存数量        | 实际         |
| actualAmt      | BigDecimal | 实际库存金额        | 实际         |
| diffNum        | BigDecimal | 数量差异(实际-理论) | 差异         |
| diffAmt        | BigDecimal | 金额差异(实际-理论) | 差异         |
| diffStatus     | String     | 差异状态            | 差异         |

### 差异状态说明

差异状态字段(diffStatus)可能的值有：

- `NoDeviation`: 无差异，实际库存等于理论库存
- `ActualGreater`: 实际库存大于理论库存（可能存在未记录入库）
- `TheoreticalGreater`: 理论库存大于实际库存（可能存在未记录出库）

### 接口使用场景

此接口主要用于物资详情页面中展示该物资的库存差异分析数据，以表格形式展示各种库存状态的数量、金额和笔数信息。

### 前端渲染示例

```jsx
const renderAnalysisTable = (data) => {
  const tableData = [
    {
      type: "期初",
      amount: data.initialAmt || 0,
      quantity: data.initialNum || 0,
      count: "-",
    },
    {
      type: "入库",
      amount: data.storageAmt || 0,
      quantity: data.storageNum || 0,
      count: data.storageCount || 0,
    },
    {
      type: "出库",
      amount: data.outboundAmt || 0,
      quantity: data.outboundNum || 0,
      count: data.outboundCount || 0,
    },
    {
      type: "理论",
      amount: data.theoreticalAmt || 0,
      quantity: data.theoreticalNum || 0,
      count: "-",
    },
    {
      type: "实际",
      amount: data.actualAmt || 0,
      quantity: data.actualNum || 0,
      count: "-",
    },
    {
      type: "差异",
      amount: data.diffAmt || 0,
      quantity: data.diffNum || 0,
      count: "-",
    },
  ];

  return (
    <Table
      dataSource={tableData}
      columns={[
        {
          title: "库存类型",
          dataIndex: "type",
          width: 100,
        },
        {
          title: "数量",
          dataIndex: "quantity",
          width: 120,
          align: "right",
        },
        {
          title: "金额",
          dataIndex: "amount",
          width: 120,
          align: "right",
        },
        {
          title: "单据数",
          dataIndex: "count",
          width: 100,
          align: "right",
        },
      ]}
      pagination={false}
      size="small"
      bordered
    />
  );
};
```
