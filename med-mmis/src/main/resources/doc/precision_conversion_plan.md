# 物资价格精度转换功能实现方案

## 1. 功能概述

实现物资价格精度的批量转换功能，将物资价格从当前精度转换为指定精度，并同步更新相关金额数据。

## 2. 数据库表分析

### 2.1 主要涉及表

1. `mmis_aset_info`（物资主表）

   - 字段：`ref_price`（参考价格，numeric(20,6)）
   - 说明：存储物资基本信息，不同规格的物资会有不同记录
   - 特点：同规格不同价格的物资会共用相同的 `code`

2. `mmis_aset_info_assist`（物资辅助表）

   - 字段：`ref_price`（参考价格，numeric(20,6)）
   - 说明：存储同规格不同价格的物资信息
   - 特点：通过 `code` 与主表关联，实现"一加一码"（唯一编码）

3. `mmis_material_sum`（物资库存汇总表）

   - 字段：`num`（数量）、`amt`（金额）
   - 说明：存储物资库存信息
   - 特点：通过 `mat_unique_code` 与辅助表关联

4. `mmis_price_history`（价格变更历史表）

   ```sql
   CREATE TABLE "public"."mmis_price_history" (
     "id" int4 NOT NULL DEFAULT nextval('mmis_price_history_id_seq'::regclass),
     "aset_id" int4 NOT NULL,                    -- 物资主表ID
     "aset_code" varchar(50) COLLATE "pg_catalog"."default",  -- 物资主表编码
     "mat_unique_code" varchar(50) COLLATE "pg_catalog"."default",  -- 物资唯一编码
     "old_price" numeric(20,6),                  -- 原价格
     "new_price" numeric(20,6),                  -- 新价格
     "precision" int4,                           -- 转换精度
     "old_stock_quantity" numeric(20,6),         -- 原库存数量
     "old_stock_amount" numeric(20,6),           -- 原库存金额
     "new_stock_amount" numeric(20,6),           -- 新库存金额
     "amount_diff" numeric(20,6),                -- 金额差异
     "operation_type" varchar(20),               -- 操作类型（PRECISION_CONVERT/PRICE_MODIFY/RESTORE）
     "batch_no" varchar(50),                     -- 批次号，用于标识同一次操作
     "operator" varchar(50) COLLATE "pg_catalog"."default",  -- 操作人
     "operate_time" timestamp(6),                -- 操作时间
     "remark" text COLLATE "pg_catalog"."default",  -- 备注
     "hospital_id" varchar(50) COLLATE "pg_catalog"."default",  -- 医院ID
     "status" varchar(20),                       -- 状态（SUCCESS/FAILED）
     "error_message" text,                       -- 错误信息
     CONSTRAINT "pk_mmis_price_history" PRIMARY KEY ("id")
   );

   -- 创建索引
   CREATE INDEX idx_mmis_price_history_batch_no ON mmis_price_history(batch_no);
   CREATE INDEX idx_mmis_price_history_mat_unique_code ON mmis_price_history(mat_unique_code);
   CREATE INDEX idx_mmis_price_history_operate_time ON mmis_price_history(operate_time);

   COMMENT ON TABLE "public"."mmis_price_history" IS '物资价格变更历史表';
   COMMENT ON COLUMN "public"."mmis_price_history"."aset_id" IS '物资主表ID';
   COMMENT ON COLUMN "public"."mmis_price_history"."aset_code" IS '物资主表编码';
   COMMENT ON COLUMN "public"."mmis_price_history"."mat_unique_code" IS '物资唯一编码';
   COMMENT ON COLUMN "public"."mmis_price_history"."old_price" IS '原价格';
   COMMENT ON COLUMN "public"."mmis_price_history"."new_price" IS '新价格';
   COMMENT ON COLUMN "public"."mmis_price_history"."precision" IS '转换精度';
   COMMENT ON COLUMN "public"."mmis_price_history"."old_stock_quantity" IS '原库存数量';
   COMMENT ON COLUMN "public"."mmis_price_history"."old_stock_amount" IS '原库存金额';
   COMMENT ON COLUMN "public"."mmis_price_history"."new_stock_amount" IS '新库存金额';
   COMMENT ON COLUMN "public"."mmis_price_history"."amount_diff" IS '金额差异';
   COMMENT ON COLUMN "public"."mmis_price_history"."operation_type" IS '操作类型（PRECISION_CONVERT/PRICE_MODIFY/RESTORE）';
   COMMENT ON COLUMN "public"."mmis_price_history"."batch_no" IS '批次号，用于标识同一次操作';
   COMMENT ON COLUMN "public"."mmis_price_history"."operator" IS '操作人';
   COMMENT ON COLUMN "public"."mmis_price_history"."operate_time" IS '操作时间';
   COMMENT ON COLUMN "public"."mmis_price_history"."remark" IS '备注';
   COMMENT ON COLUMN "public"."mmis_price_history"."hospital_id" IS '医院ID';
   COMMENT ON COLUMN "public"."mmis_price_history"."status" IS '状态（SUCCESS/FAILED）';
   COMMENT ON COLUMN "public"."mmis_price_history"."error_message" IS '错误信息';
   ```

### 2.2 表关系说明

1. 主表与辅助表关系：

   - 一对多关系
   - 通过 `code` 字段关联
   - 主表存储基础信息，辅助表存储价格变体

2. 辅助表与库存表关系：

   - 一对一关系
   - 通过 `mat_unique_code` 关联
   - 库存表记录具体库存信息

3. 价格历史表关系：
   - 记录所有价格变更历史
   - 通过 `aset_id` 关联主表
   - 通过 `aset_code` 关联主表编码
   - 通过 `mat_unique_code` 关联辅助表
   - 记录变更前后的价格和操作信息
   - 通过 `batch_no` 关联同批次操作
   - 支持数据恢复和回滚操作

### 2.3 价格精度说明

1. 当前精度：

   - 主表和辅助表的 `ref_price` 字段均为 numeric(20,6)
   - 支持最多 6 位小数

2. 目标精度：
   - 支持从 1 位到 6 位小数的转换
   - 默认转换为 2 位小数
   - 转换方式：四舍五入

## 3. 功能设计

### 3.1 接口设计

```java
@PostMapping("/convertPrecision")
public Result convertPrecision(@RequestBody PrecisionConversionDto dto) {
    // 1. 参数校验
    // 2. 执行转换
    // 3. 返回结果
}
```

### 3.2 请求参数

```java
public class PrecisionConversionDto {
    private Integer precision;        // 目标精度（1-6）
    private List<String> materialIds; // 需要转换的物资ID列表（可选）
    private Boolean isAll;           // 是否转换所有物资
    private String remark;           // 转换说明
}
```

### 3.3 实现步骤

1. **数据准备阶段**

   - 根据条件筛选需要转换的物资
   - 计算转换前后的价格差异
   - 生成转换预览数据

2. **数据转换阶段**

   - 开启事务
   - 记录原价格到历史表
   - 更新主表价格（mmis_aset_info）
   - 更新辅助表价格（mmis_aset_info_assist）
   - 同步更新库存金额（mmis_material_sum）
   - 提交事务

3. **结果验证阶段**
   - 验证转换结果
   - 生成转换报告
   - 发送通知（如需要）

## 4. 技术实现要点

### 4.1 数据一致性保证

1. 使用数据库事务确保数据一致性
2. 采用乐观锁防止并发修改
3. 实现数据回滚机制
4. 确保主表和辅助表价格同步更新
5. 价格变更前先备份到历史表

### 4.2 性能优化

1. 分批处理大量数据
2. 使用批量更新代替单条更新
3. 建立合适的索引
4. 优化表关联查询

### 4.3 异常处理

1. 定义业务异常类型
2. 实现异常恢复机制
3. 记录详细的操作日志

## 5. 安全考虑

### 5.1 权限控制

1. 接口访问权限控制（仅管理员可操作）
2. 数据操作权限验证
3. 敏感操作审计日志

### 5.2 数据验证

1. 输入参数合法性验证
2. 业务规则验证
3. 数据完整性验证
4. 主表和辅助表数据一致性验证

## 6. 后续优化方向

### 6.1 功能扩展

1. 支持多种精度转换规则
2. 实现价格变更历史追溯
3. 添加价格变更影响分析

### 6.2 性能优化

1. 实现异步处理机制
2. 优化大数据量处理
3. 添加缓存机制

## 7. 待确认事项

1. 数据库表结构确认

   - 是否需要添加其他索引
   - 是否需要添加其他字段

2. 业务规则确认

   - 价格转换的具体规则
   - 是否需要通知相关人员
   - 主表和辅助表价格同步的具体规则

3. 性能要求确认
   - 单次处理的最大数据量
   - 期望的处理时间
   - 并发处理要求
