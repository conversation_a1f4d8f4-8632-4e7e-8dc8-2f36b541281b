package com.jp.med.cms.modules.contractManage.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 关键要素
 * <AUTHOR>
 * @email -
 * @date 2024-06-20 11:31:27
 */
@Data
@TableName("cms_key_elements")
public class CmsKeyElementsEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 关键要素 */
	@TableField("key_element")
	private String keyElement;

	/** 节点控制要求 */
	@TableField("node_require")
	private String nodeRequire;

	/** 时间节点 */
	@TableField("time_node")
	private String timeNode;

	/** 顺序 */
	@TableField("seq")
	private Integer seq;

	/** 组织id */
	@TableField("hospital_id")
	private String hospitalId;

	/** 历史节点控制要求 */
	@TableField("his_require")
	private String hisRequire;

	/** 修改人 */
	@TableField("updtr")
	private String updtr;

	/** 修改时间 */
	@TableField("update_time")
	private String updateTime;


	/** 合同id */
	@TableField("contract_id")
	private Integer contractId;
}
