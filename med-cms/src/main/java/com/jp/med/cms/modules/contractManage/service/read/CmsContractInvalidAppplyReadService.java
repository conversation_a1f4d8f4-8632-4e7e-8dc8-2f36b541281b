package com.jp.med.cms.modules.contractManage.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cms.modules.contractManage.dto.CmsContractInvalidAppplyDto;
import com.jp.med.cms.modules.contractManage.vo.CmsContractInvalidAppplyVo;

import java.util.List;

/**
 * 合同作废申请
 * <AUTHOR>
 * @email -
 * @date 2024-08-14 16:29:12
 */
public interface CmsContractInvalidAppplyReadService extends IService<CmsContractInvalidAppplyDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsContractInvalidAppplyVo> queryList(CmsContractInvalidAppplyDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<CmsContractInvalidAppplyVo> queryPageList(CmsContractInvalidAppplyDto dto);
}

