package com.jp.med.cms.modules.templateConfig.mapper.read;

import com.jp.med.cms.modules.templateConfig.dto.CmsElementCurstomRequireDto;
import com.jp.med.cms.modules.templateConfig.vo.CmsElementCurstomRequireVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 元素定制需求
 * <AUTHOR>
 * @email -
 * @date 2024-11-26 10:49:02
 */
@Mapper
public interface CmsElementCurstomRequireReadMapper extends BaseMapper<CmsElementCurstomRequireDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsElementCurstomRequireVo> queryList(CmsElementCurstomRequireDto dto);
}
