package com.jp.med.cms.modules.opposite.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.cms.modules.opposite.dto.CmsOppositeCertificatesDto;
import com.jp.med.cms.modules.opposite.dto.CmsOppositeContactsDto;
import com.jp.med.cms.modules.opposite.dto.CmsOppositeFinanceDto;
import com.jp.med.cms.modules.opposite.mapper.read.CmsOppositeCertificatesReadMapper;
import com.jp.med.cms.modules.opposite.mapper.read.CmsOppositeContactsReadMapper;
import com.jp.med.cms.modules.opposite.mapper.read.CmsOppositeFinanceReadMapper;
import com.jp.med.cms.modules.opposite.service.read.CmsOppositeCertificatesReadService;
import com.jp.med.cms.modules.opposite.service.read.CmsOppositeContactsReadService;
import com.jp.med.cms.modules.opposite.service.read.CmsOppositeFinanceReadService;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.OSSUtil;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.cms.modules.opposite.mapper.read.CmsContractOppositeReadMapper;
import com.jp.med.cms.modules.opposite.dto.CmsContractOppositeDto;
import com.jp.med.cms.modules.opposite.vo.CmsContractOppositeVo;
import com.jp.med.cms.modules.opposite.service.read.CmsContractOppositeReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class CmsContractOppositeReadServiceImpl extends ServiceImpl<CmsContractOppositeReadMapper, CmsContractOppositeDto> implements CmsContractOppositeReadService {

    @Autowired
    private CmsContractOppositeReadMapper cmsContractOppositeReadMapper;

    @Autowired
    private CmsOppositeCertificatesReadMapper cmsOppositeCertificatesReadMapper;

    @Autowired
    private CmsOppositeCertificatesReadService cmsOppositeCertificatesReadService;

    @Autowired
    private CmsOppositeContactsReadMapper cmsOppositeContactsReadMapper;

    @Autowired
    private CmsOppositeFinanceReadMapper cmsOppositeFinanceReadMapper;

    @Autowired
    private CmsOppositeContactsReadService cmsOppositeContactsReadService;

    @Autowired
    private CmsOppositeFinanceReadService cmsOppositeFinanceReadService;





    @Override
    public List<CmsContractOppositeVo> queryList(CmsContractOppositeDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return cmsContractOppositeReadMapper.queryList(dto);
    }

    @Override
    public CmsContractOppositeVo queryOppositeDetail(CmsContractOppositeDto dto) {
        List<CmsContractOppositeVo> cmsContractOppositeVos = cmsContractOppositeReadMapper.queryList(dto);

        CmsOppositeCertificatesDto oppositeCertificatesDto = new CmsOppositeCertificatesDto();
        oppositeCertificatesDto.setOppositeId(dto.getId());

        CmsOppositeContactsDto cmsOppositeContactsDto = new CmsOppositeContactsDto();
        cmsOppositeContactsDto.setOppositeId(dto.getId());

        CmsOppositeFinanceDto cmsOppositeFinanceDto = new CmsOppositeFinanceDto();
        cmsOppositeFinanceDto.setOppositeId(dto.getId());

        if (cmsContractOppositeVos.size() > 0) {
            cmsContractOppositeVos.get(0).setOppositeCertificatesList(cmsOppositeCertificatesReadService.queryListNoPage(oppositeCertificatesDto));
            cmsContractOppositeVos.get(0).setOppositeContactsList(cmsOppositeContactsReadService.queryListNoPage(cmsOppositeContactsDto));
            cmsContractOppositeVos.get(0).setOppositeFinanceList(cmsOppositeFinanceReadService.queryListNoPage(cmsOppositeFinanceDto));
        }

        return cmsContractOppositeVos.get(0);
    }

    //获取文件外连接，根据filePath
    @Override
    public String getUrl(CmsContractOppositeDto dto) {
        if (dto.getFilePath() != null) {
            String url = OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_CMS, dto.getFilePath());
            return url;
        }else {
            throw new AppException("文件路径为空");
        }
    }


    /**
     * 查询相对方列表
     * @param dto
     * @return
     */
    @Override
    public List<CmsContractOppositeVo> oppositeList(CmsContractOppositeDto dto) {
        return cmsContractOppositeReadMapper.queryOppositeList(dto);
    }
}
