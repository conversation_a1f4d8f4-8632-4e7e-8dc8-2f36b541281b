package com.jp.med.cms.modules.templateConfig.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cms.modules.templateConfig.dto.CmsFormItemJsonDto;
import com.jp.med.cms.modules.templateConfig.entity.cmsCrudInterface;
import com.jp.med.cms.modules.templateConfig.vo.CmsFormItemJsonVo;

import java.util.List;

/**
 * 创建的表单元素json
 * <AUTHOR>
 * @email -
 * @date 2024-11-20 09:35:01
 */
public interface CmsFormItemJsonReadService extends IService<CmsFormItemJsonDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<cmsCrudInterface> queryList(CmsFormItemJsonDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<cmsCrudInterface> queryPageList(CmsFormItemJsonDto dto);
}

