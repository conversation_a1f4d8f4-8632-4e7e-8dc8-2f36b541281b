package com.jp.med.cms.modules.contractManage.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 合同终止详情表
 * <AUTHOR>
 * @email -
 * @date 2024-08-16 11:33:28
 */
@Data
@TableName("cms_contract_termination_detail" )
public class CmsContractTerminationDetailDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 组织id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 申请id */
    @TableField("apply_id")
    private Integer applyId;

    /** 附件 */
    @TableField("att")
    private String att;

    /** 附件名称 */
    @TableField("att_name")
    private String attName;

    /** 是否删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 操作人 */
    @TableField("updtr")
    private String updtr;

    /** 更新时间 */
    @TableField("update_time")
    private String updateTime;

}
