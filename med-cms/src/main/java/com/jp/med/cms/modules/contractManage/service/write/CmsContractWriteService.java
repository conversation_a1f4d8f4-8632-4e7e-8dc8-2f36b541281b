package com.jp.med.cms.modules.contractManage.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cms.modules.contractManage.dto.CmsContractDto;
import com.jp.med.cms.modules.contractManage.vo.CmsContractVo;

/**
 * 合同
 * <AUTHOR>
 * @email -
 * @date 2024-06-20 11:31:27
 */
public interface CmsContractWriteService extends IService<CmsContractDto> {
	CmsContractDto saveDto(CmsContractDto dto);

	String generateTempLate(CmsContractDto dto);

	void revokeDraftApply(CmsContractDto dto);

	void saveApply2(CmsContractDto dto);

	CmsContractDto addCtDraftDirect(CmsContractDto dto);

	CmsContractDto addCtSignedDirect(CmsContractDto dto);
}

