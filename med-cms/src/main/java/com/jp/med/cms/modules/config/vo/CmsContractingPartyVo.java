package com.jp.med.cms.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 签约主体表
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 09:40:11
 */
@Data
public class CmsContractingPartyVo {

	/** id */
	private Integer id;

	/** 主体名称 */
	private String partyName;

	/** 主体类型(个人、企业、非盈利组织) */
	private String partyType;

	/** 法人 */
	private String legalPerson;

	/** 主体指定人姓名 */
	private String contractName;

	/** 联系人电话 */
	private String phone;

	/** 邮箱 */
	private String email;

	/** 地址编码 */
	private String addr;

	/** 是否启用 */
	private String activeFlag;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 修改人 */
	private String updtr;

	/** 修改时间 */
	private String updateTime;

}
