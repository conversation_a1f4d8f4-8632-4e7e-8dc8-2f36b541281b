package com.jp.med.cms.modules.contractManage.service.write.impl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.DateUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.cms.modules.contractManage.mapper.write.CmsMilestonePlanWriteMapper;
import com.jp.med.cms.modules.contractManage.dto.CmsMilestonePlanDto;
import com.jp.med.cms.modules.contractManage.service.write.CmsMilestonePlanWriteService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 里程碑计划表
 * <AUTHOR>
 * @email -
 * @date 2024-07-31 17:39:04
 */
@Service
@Transactional(readOnly = false)
public class CmsMilestonePlanWriteServiceImpl extends ServiceImpl<CmsMilestonePlanWriteMapper, CmsMilestonePlanDto> implements CmsMilestonePlanWriteService {

	@Override
	public void saveDto(List<CmsMilestonePlanDto> dtoList) {
		String admin = "admin";
		String crter = StringUtils.isNotEmpty(dtoList.get(0).getCrter())?dtoList.get(0).getCrter(): admin;
		String ecexuteOrg = StringUtils.isNotEmpty(dtoList.get(0).getExecuteOrg())?dtoList.get(0).getExecuteOrg(): "";

		String currentTime = DateUtil.getCurrentTime(null);
		dtoList.forEach(dto -> {
			dto.setCrter(crter);
			dto.setCreateTime(currentTime);
			dto.setUpdateTime(currentTime);
			dto.setUpdtr(currentTime);
			dto.setHospitalId(MedConst.HOSPITAL_ID);
			dto.setHasGeneratedCalendar(MedConst.TYPE_0);
			dto.setExecuteOrg(ecexuteOrg);
			dto.setExecutePerson(crter);
		});
		BatchUtil.batch("insert", dtoList, CmsMilestonePlanWriteMapper.class);
	}
}
