package com.jp.med.cms.modules.contractManage.mapper.read;

import com.jp.med.cms.modules.contractManage.dto.CmsLiquidatedDamagesDto;
import com.jp.med.cms.modules.contractManage.vo.CmsLiquidatedDamagesVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 违约金赔付计划表
 * <AUTHOR>
 * @email -
 * @date 2024-08-16 11:33:28
 */
@Mapper
public interface CmsLiquidatedDamagesReadMapper extends BaseMapper<CmsLiquidatedDamagesDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsLiquidatedDamagesVo> queryList(CmsLiquidatedDamagesDto dto);
}
