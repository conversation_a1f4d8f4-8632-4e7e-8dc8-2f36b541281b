package com.jp.med.cms.modules.contractManage.mapper.read;

import com.jp.med.cms.modules.contractManage.dto.CmsMilestonePlanDto;
import com.jp.med.cms.modules.contractManage.vo.CmsMilestonePlanVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 里程碑计划表
 * <AUTHOR>
 * @email -
 * @date 2024-07-31 17:39:04
 */
@Mapper
public interface CmsMilestonePlanReadMapper extends BaseMapper<CmsMilestonePlanDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsMilestonePlanVo> queryList(CmsMilestonePlanDto dto);
}
