package com.jp.med.cms.modules.templateConfig.service.write.impl;

import com.alibaba.fastjson.JSONObject;
import com.jp.med.cms.modules.templateConfig.controller.CmsFormItemCustomTemplateController;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.DateUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.cms.modules.templateConfig.mapper.write.CmsFormItemCustomTemplateWriteMapper;
import com.jp.med.cms.modules.templateConfig.dto.CmsFormItemCustomTemplateDto;
import com.jp.med.cms.modules.templateConfig.service.write.CmsFormItemCustomTemplateWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户自定义模板
 * <AUTHOR>
 * @email -
 * @date 2024-11-20 09:35:01
 */
@Service
@Transactional(readOnly = false)
public class CmsFormItemCustomTemplateWriteServiceImpl extends ServiceImpl<CmsFormItemCustomTemplateWriteMapper, CmsFormItemCustomTemplateDto> implements CmsFormItemCustomTemplateWriteService {

	@Autowired
	private CmsFormItemCustomTemplateWriteMapper cmsFormItemCustomTemplateWriteMapper;
	
	@Override
	public void saveDto(CmsFormItemCustomTemplateDto dto) {
		// 如果id不为空,将原记录标记为已删除
		CmsFormItemCustomTemplateDto oldDto = null;
		if (dto.getId() != null) {
			oldDto = cmsFormItemCustomTemplateWriteMapper.selectById(dto.getId());
			if (oldDto != null) {
				oldDto.setIsDeleted(MedConst.IS_DELETED);
				cmsFormItemCustomTemplateWriteMapper.updateById(oldDto);
			}
		}
		
		JSONObject templateJson = JSONObject.parseObject(dto.getTemplate());
		String ttl = templateJson.getString("ttl");
		String typeCode = templateJson.getString("typeCode"); 
		String templateRemark = templateJson.getString("templateRemark");
		String useOrg = templateJson.getString("useOrg");

		dto.setTtl(ttl);
		dto.setUseOrg(useOrg);
		dto.setTypeCode(typeCode);
		dto.setTemplateRemark(templateRemark);
		
		String time = DateUtil.getCurrentTime(null);
		String empCode = org.apache.commons.lang.StringUtils.isNotEmpty(dto.getSysUser().getHrmUser().getEmpCode()) ? 
			dto.getSysUser().getHrmUser().getEmpCode() : dto.getSysUser().getNickname();
		
		if (dto.getId() != null && oldDto != null) {
			dto.setCrter(oldDto.getCrter());
			dto.setCreateTime(oldDto.getCreateTime());
			dto.setUpdtr(empCode);
			dto.setUpdateTime(time);
		} else {
			dto.setCrter(empCode);
			dto.setCreateTime(time);
			dto.setUpdtr(empCode);
			dto.setUpdateTime(time);
		}
		
		dto.setHospitalId(dto.getHospitalId());
		dto.setIsDeleted(MedConst.NOT_DELETED);

		String timestamp = String.valueOf(System.currentTimeMillis());
		dto.setCheckCode(timestamp);
		dto.setHisCheckCode(timestamp);
		dto.setUseTimes(0);
		dto.setId(null);
		cmsFormItemCustomTemplateWriteMapper.insert(dto);
	}
}
