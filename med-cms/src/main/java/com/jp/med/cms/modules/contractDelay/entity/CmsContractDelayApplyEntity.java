package com.jp.med.cms.modules.contractDelay.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 合同延期申请
 * <AUTHOR>
 * @email -
 * @date 2024-08-22 11:39:23
 */
@Data
@TableName("cms_contract_delay_apply")
public class CmsContractDelayApplyEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 合同id */
	@TableField("contract_id")
	private Integer contractId;

	/** 延期原因 */
	@TableField("reason")
	private String reason;

	/** 生效日期 */
	@TableField("effective_date")
	private String effectiveDate;

	/** 申请人 */
	@TableField("appyer")
	private String appyer;

	/** 申请科室 */
	@TableField("appery_org")
	private String apperyOrg;

	/** 申请人电话 */
	@TableField("appyer_phone")
	private String appyerPhone;

	/** 审核状态 */
	@TableField("chk_state")
	private String chkState;

	/** 审核批次号 */
	@TableField("audit_bchno")
	private String auditBchno;

	/** 创建人 */
	@TableField("crter")
	private String crter;

	/** 创建时间 */
	@TableField("create_time")
	private String createTime;

	/** 操作人 */
	@TableField("updtr")
	private String updtr;

	/** 操作时间 */
	@TableField("update_time")
	private String updateTime;

	/** 组织id */
	@TableField("hospital_id")
	private String hospitalId;

}
