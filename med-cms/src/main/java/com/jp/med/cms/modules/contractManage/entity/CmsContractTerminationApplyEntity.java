package com.jp.med.cms.modules.contractManage.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 合同终止申请
 * <AUTHOR>
 * @email -
 * @date 2024-08-16 11:33:28
 */
@Data
@TableName("cms_contract_termination_apply")
public class CmsContractTerminationApplyEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 合同id */
	@TableField("contract_id")
	private Integer contractId;

	/** 计划终止日期 */
	@TableField("plan_termination_date")
	private String planTerminationDate;

	/** 终止类型 */
	@TableField("termination_type")
	private String terminationType;

	/** 终止原因 */
	@TableField("reason")
	private String reason;

	/** 终止原因描述 */
	@TableField("termin_descr")
	private String terminDescr;

	/** 终止提出方 */
	@TableField("termination_party")
	private String terminationParty;

	/** 是否支付违约金 */
	@TableField("pay_liquidated_damages")
	private String payLiquidatedDamages;

	/** 组织id */
	@TableField("hospital_id")
	private String hospitalId;

	/** 创建人 */
	@TableField("crter")
	private String crter;

	/** 创建时间 */
	@TableField("create_time")
	private String createTime;

	/** 修改人 */
	@TableField("updtr")
	private String updtr;

	/** 修改时间 */
	@TableField("update_time")
	private String updateTime;

	/** 赔付方 */
	@TableField("a_or_b")
	private String aOrB;

	/** 赔偿金额 */
	@TableField("damages_amt")
	private BigDecimal damagesAmt;

	/** 赔偿期数 */
	@TableField("damages_periods")
	private Integer damagesPeriods;

	/** 赔偿开始时间 */
	@TableField("pay_start_date")
	private String payStartDate;

	/** 赔偿结束时间 */
	@TableField("pay_end_date")
	private String payEndDate;

	/** 审核批次号 */
	@TableField("audit_bchno")
	private String auditBchno;

	/** 审批状态 */
	@TableField("chk_state")
	private String chkState;

	/** 申请人 */
	@TableField("appyer")
	private String appyer;

	/** 申请科室 */
	@TableField("appyer_org_id")
	private String appyerOrgId;

	/** 申请人类型 */
	@TableField("appyer_type")
	private String appyerType;

	/** 申请时间 */
	@TableField("apply_time")
	private String applyTime;
}
