package com.jp.med.cms.modules.contractManage.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import com.jp.med.common.entity.audit.AuditDetail;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 合同作废申请
 * <AUTHOR>
 * @email -
 * @date 2024-08-14 16:29:12
 */
@Data
@TableName("cms_contract_invalid_appply" )
public class CmsContractInvalidAppplyDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 合同id */
    @TableField("contract_id")
    private Integer contractId;

    /** 合同主体id */
    @TableField("main_id")
    private Integer mainId;

    /** 签约科室 */
    @TableField("use_org")
    private String useOrg;

    /** 合同主体名称 */
    @TableField("main_name")
    private String mainName;

    /** 相对方id */
    @TableField("opposite_id")
    private Integer oppositeId;

    /** 相对方名称 */
    @TableField("opposite_name")
    private String oppositeName;

    /** 申请时间 */
    @TableField("apply_time")
    private String applyTime;

    /** 作废缘由(选?) */
    @TableField("reason")
    private String reason;

    /** 作废缘由详细说明 */
    @TableField("descr")
    private String descr;

    /** 申请人 */
    @TableField("appyer")
    private String appyer;

    /** 申请人科室id */
    @TableField("appy_org_id")
    private String appyOrgId;

    /** 申请人类型(院内院外) */
    @TableField("appyer_type")
    private String appyerType;

    /** 审核批次号 */
    @TableField("audit_bchno")
    private String auditBchno;

    /** 审核状态 */
    @TableField("chk_state")
    private String chkState;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建 时间 */
    @TableField("create_time")
    private String createTime;

    /** 更改时间 */
    @TableField("update_time")
    private String updateTime;

    /** 修改人 */
    @TableField("updtr")
    private String updtr;

    /** 组织id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 申请人电话 */
    @TableField("appyer_phone")
    private String appyerPhone;

    /** 计划作废时间 */
    @TableField("plan_invalid_time")
    private String planInvalidTime;


    /** 上传文件说明 */
    @TableField("att_descr")
    private String attDescr;

    /**
     * 审核页面
     */
    @TableField(exist = false)
    private String audit;

    /** 审核流程详情 */
    @TableField(exist = false)
    private List<AuditDetail> auditDetails;


    /**
     * 附件
     */
    @TableField(exist = false)
    private List<MultipartFile> attFiles;


}
