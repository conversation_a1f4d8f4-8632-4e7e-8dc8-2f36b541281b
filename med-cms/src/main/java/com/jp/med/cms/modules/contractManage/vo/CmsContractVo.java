package com.jp.med.cms.modules.contractManage.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import java.util.List;

import com.jp.med.cms.modules.common.vo.CmsAuditRcdfmVo;
import lombok.Data;

/**
 * 合同
 * <AUTHOR>
 * @email -
 * @date 2024-06-20 11:31:27
 */
@Data
public class CmsContractVo {

	/** 自增id */
	private Integer id;

	/** 合同编号 */
	private String ctCode;

	/** 合同编号 */
	private String ctUnifiedCode;

	/** 合同名称 */
	private String ctName;

	/** 招标编号 */
	private String tenderNum;

	/** 招标名称 */
	private String tenderName;

	/** 中标时间 */
	private String bidWinningTime;

	/** 签约时间 */
	private String signTime;

	/** 使用科室 */
	private String useOrg;

	/** 管理科室 */
	private String manageOrg;

	/** 相对方名称 */
	private String oppositeName;

	/** 相对方id */
	private Integer oppositeId;

	/** 院方信息 */
	private Integer mainId;

	/** 合同类型编码 */
	private String ctTypeCode;

	/** 合同状态 */
	private String ctStatus;

	/** 负责人 */
	private String responsiblePerson;

  private String	responsiblePersonName;

	/** 负责人联系电话 */
	private String responsiblePhone;

	/** 相对方联系人id */
	private Integer oppositePersonId;



	private Integer oppositeBankId;

	/** 相对方开户行 */
	private String oppositeBank;

	/** 相对方银行账户 */
	private String oppositeAccount;

	/** 合同总额 */
	private BigDecimal totalAmt;

	/** 付款方式(字典) */
	private String paymentType;

	/** 付款期数(数字) */
	private Integer paymentTerms;

	/** 审核批次号 */
	private String auditBchno;

	/** 申请人（录入人） */
	private String appyer;

	private String appyerName;

	/** 申请时间 */
	private String applyTime;

	/** 科室id */
	private String appyOrgId;

	/** 草拟审核状态 */
	private String chkState;

	/** 签订审核状态 */
	private String chkState1;

	/** 创建人(录入人/备用) */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 组织id */
	private String hospitalId;

	/** 逻辑删除标识 */
	private Integer isDeleted;

	/** 合同备注说明 */
	private String contractRemark;




	/**
	 * 合同草拟阶段审核流程实例编码
	 */
	private String draftProcessInstanceCode;

	/**
	 * 合同签订流程审核实例编码
	 */
	private String signedProcessInstanceCode;


//	关联的信息

	private String oppositePerson;
	private String oppositePhone;

	/** 合同数据 */
	private String dataStr;

	/** 合同模板数据 */
	private String tempStr;

	/** 合同原件 */
	private String att;

	/** 合同原件名称 */
	private String attName;


	/**
	 * 是否在审核名单标志位
	 */
	private String auditFlag;


	/**
	 * 合同文件详情
	 */
	private List<CmsContractDetailVo> tableDetails;

	/**
	 * 审核流程详情
	 */
	private List<CmsAuditRcdfmVo> auditDetails;

	/** 显示关键要素 */
	private String showKeyElement;

	/** 显示付款期数 */
	private String showPayTerms;


	/** 合同签订审批批次号 */
	private String auditBchno1;

	/** 合同签订审批申请人 */
	private String appyer1;

	/** 合同签订审批申请时间 */
	private String applyTime1;


	/** 合同生效开始时间 */
	private String validityStartDate;

	/** 合同生效结束时间 */
	private String validityEndDate;


	/** 是否续签 */
	private String renewal;

	/** 上一个合同id */
	private Integer lastContractId;

	/** 是否作废 */
	private String invalidFlag;

	/**草拟类型
	 *
	 */
	private String draftType;

	private String totalAmtInWord;

	/** 联系人电话 */
	private String contactPhone;

	/** 联系人姓名 */
	private String contactName;

	/** 开户行 */
	private String openingBank;

	/** 银行账号 */
	private String bankAccount;


	/**
	 * 合同份数
	 */
	private Integer ctCopies;


	/**
	 * 合同附件名称，逗号分隔，相当于只是给他留空备注一下合同附件的名称，可以多个，
	 * 有时候附件又是在合同原件里面的所以没法绑定具体的文件，就让他们自己写一个
	 */
	private String attachmentNames;

	/** 合同分类名称 */
	private String typeName;

	/** 合同数量 */
	private Integer contractCount;
}
