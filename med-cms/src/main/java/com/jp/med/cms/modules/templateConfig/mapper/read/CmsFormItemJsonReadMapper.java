package com.jp.med.cms.modules.templateConfig.mapper.read;

import com.jp.med.cms.modules.templateConfig.dto.CmsFormItemJsonDto;
import com.jp.med.cms.modules.templateConfig.vo.CmsFormItemJsonVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 创建的表单元素json
 * <AUTHOR>
 * @email -
 * @date 2024-11-20 09:35:01
 */
@Mapper
public interface CmsFormItemJsonReadMapper extends BaseMapper<CmsFormItemJsonDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsFormItemJsonVo> queryList(CmsFormItemJsonDto dto);
}
