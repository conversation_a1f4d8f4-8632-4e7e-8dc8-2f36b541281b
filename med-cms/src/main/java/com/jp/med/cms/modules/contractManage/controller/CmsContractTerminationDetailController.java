package com.jp.med.cms.modules.contractManage.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.cms.modules.contractManage.dto.CmsContractTerminationDetailDto;
import com.jp.med.cms.modules.contractManage.service.read.CmsContractTerminationDetailReadService;
import com.jp.med.cms.modules.contractManage.service.write.CmsContractTerminationDetailWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 合同终止详情表
 * <AUTHOR>
 * @email -
 * @date 2024-08-16 11:33:28
 */
@Api(value = "合同终止详情表", tags = "合同终止详情表")
@RestController
@RequestMapping("cmsContractTerminationDetail")
public class CmsContractTerminationDetailController {

    @Autowired
    private CmsContractTerminationDetailReadService cmsContractTerminationDetailReadService;

    @Autowired
    private CmsContractTerminationDetailWriteService cmsContractTerminationDetailWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询合同终止详情表")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody CmsContractTerminationDetailDto dto){
        return CommonResult.paging(cmsContractTerminationDetailReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询合同终止详情表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody CmsContractTerminationDetailDto dto){
        return CommonResult.success(cmsContractTerminationDetailReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增合同终止详情表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody CmsContractTerminationDetailDto dto){
        cmsContractTerminationDetailWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改合同终止详情表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody CmsContractTerminationDetailDto dto){
        cmsContractTerminationDetailWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除合同终止详情表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody CmsContractTerminationDetailDto dto){
        cmsContractTerminationDetailWriteService.removeById(dto);
        return CommonResult.success();
    }

}
