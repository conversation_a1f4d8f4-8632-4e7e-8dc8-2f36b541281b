package com.jp.med.cms.modules.contractMain.mapper.read;

import com.jp.med.cms.modules.contractMain.dto.CmsMainFinanceDto;
import com.jp.med.cms.modules.contractMain.vo.CmsMainFinanceVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 主体财务信息
 * <AUTHOR>
 * @email -
 * @date 2024-07-10 10:44:06
 */
@Mapper
public interface CmsMainFinanceReadMapper extends BaseMapper<CmsMainFinanceDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsMainFinanceVo> queryList(CmsMainFinanceDto dto);
}
