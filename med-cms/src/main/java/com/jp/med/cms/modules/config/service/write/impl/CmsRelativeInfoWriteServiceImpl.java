package com.jp.med.cms.modules.config.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.cms.modules.config.mapper.write.CmsRelativeInfoWriteMapper;
import com.jp.med.cms.modules.config.dto.CmsRelativeInfoDto;
import com.jp.med.cms.modules.config.service.write.CmsRelativeInfoWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 关联相对方信息
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 09:40:11
 */
@Service
@Transactional(readOnly = false)
public class CmsRelativeInfoWriteServiceImpl extends ServiceImpl<CmsRelativeInfoWriteMapper, CmsRelativeInfoDto> implements CmsRelativeInfoWriteService {
}
