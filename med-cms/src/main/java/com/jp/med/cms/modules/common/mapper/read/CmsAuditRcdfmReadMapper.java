package com.jp.med.cms.modules.common.mapper.read;

import com.jp.med.cms.modules.common.dto.CmsAuditRcdfmDto;
import com.jp.med.cms.modules.common.vo.CmsAuditRcdfmVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 审核明细表
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 09:33:58
 */
@Mapper
public interface CmsAuditRcdfmReadMapper extends BaseMapper<CmsAuditRcdfmDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsAuditRcdfmVo> queryList(CmsAuditRcdfmDto dto);
}
