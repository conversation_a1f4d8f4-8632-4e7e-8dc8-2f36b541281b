package com.jp.med.cms.modules.common.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cms.modules.common.dto.CmsAuditRcdfmDto;
import com.jp.med.cms.modules.common.vo.CmsAuditRcdfmVo;

import java.util.List;

/**
 * 审核明细表
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 09:33:58
 */
public interface CmsAuditRcdfmReadService extends IService<CmsAuditRcdfmDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsAuditRcdfmVo> queryList(CmsAuditRcdfmDto dto);
}

