package com.jp.med.cms.modules.contractManage.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 付款条件
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-06-20 11:31:27
 */
@Data
@TableName("cms_payment_terms")
public class CmsPaymentTermsEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 付款节点 */
	@TableField("stage")
	private String stage;

	/** 付款要求 */
	@TableField("requirements")
	private String requirements;

	/** 比例 */
	@TableField("proportion")
	private String proportion;

	/** 付款总额 */
	@TableField("total_amt")
	private BigDecimal totalAmt;

	/** 付款时间 */
	@TableField("payment_time")
	private String paymentTime;

	/** 组织id */
	@TableField("hospital_id")
	private String hospitalId;

	/** 顺序 */
	@TableField("seq")
	private Integer seq;

	/** 历史付款要求 */
	@TableField("his_requirements")
	private String hisRequirements;

	/** 修改时间 */
	@TableField("update_time")
	private String updateTime;

	/** 业务状态 */
	@TableField("business_status")
	private String businessStatus;

	/** 备注 */
	@TableField("remark")
	private String remark;

	/** 操作人 */
	@TableField("operator")
	private String operator;

	/** 操作时间 */
	@TableField("opt_time")
	private String optTime;

	/** 合同id */
	@TableField("contract_id")
	private Integer contractId;

	/** 付款期数 */
	@TableField("payment_terms")
	private Integer paymentTerms;

	/** 付款类型 字典 */
	@TableField("payment_type")
	private String paymentType;

	/** 当前阶段待付款 */
	@TableField("current_pay_amt")
	private BigDecimal currentPayAmt;

	/**
	 * 报销Id
	 */
	@TableField("reim_id")
	private String reimId;

	/**
	 * 报销状态标识
	 */
	@TableField("reim_status_flag")
	private String reimStatusFlag;

	/**
	 * 实际付款时间
	 */
	@TableField("actual_payment_time")
	private String actualPaymentTime;

	/**
	 * 报销人
	 */
	@TableField("reimburse_person")
	private String reimbursePerson;

	/**
	 * 报销部门
	 */
	@TableField("reimburse_dept")
	private String reimburseDept;

	/**
	 * 报销时间
	 */
	@TableField("reimburse_time")
	private String reimburseTime;

	/**
	 * 报销单号
	 */
	@TableField("reimburse_no")
	private String reimburseNo;

	/**
	 * 推送任务时间
	 */
	@TableField("push_task_time")
	private String pushTaskTime;
}
