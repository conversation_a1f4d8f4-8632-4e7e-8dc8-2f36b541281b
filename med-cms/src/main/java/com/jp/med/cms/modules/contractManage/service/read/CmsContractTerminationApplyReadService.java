package com.jp.med.cms.modules.contractManage.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cms.modules.contractManage.dto.CmsContractTerminationApplyDto;
import com.jp.med.cms.modules.contractManage.vo.CmsContractTerminationApplyVo;

import java.util.List;

/**
 * 合同终止申请
 * <AUTHOR>
 * @email -
 * @date 2024-08-16 11:33:28
 */
public interface CmsContractTerminationApplyReadService extends IService<CmsContractTerminationApplyDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsContractTerminationApplyVo> queryList(CmsContractTerminationApplyDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<CmsContractTerminationApplyVo> queryPageList(CmsContractTerminationApplyDto dto);
}

