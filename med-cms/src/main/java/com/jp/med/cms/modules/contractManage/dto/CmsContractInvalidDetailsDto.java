package com.jp.med.cms.modules.contractManage.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 合同作废详情
 * <AUTHOR>
 * @email -
 * @date 2024-08-14 19:17:09
 */
@Data
@TableName("cms_contract_invalid_details" )
public class CmsContractInvalidDetailsDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;


    /** 申请sid */
    @TableField("apply_id")
    private Integer applyId;

    /** 组织id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 附件 */
    @TableField("att")
    private String att;

    /** 附件名称 */
    @TableField("att_name")
    private String attName;

    /** 逻辑删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 操作人 */
    @TableField("updtr")
    private String updtr;

    /** 修改时间 */
    @TableField("update_time")
    private String updateTime;

}
