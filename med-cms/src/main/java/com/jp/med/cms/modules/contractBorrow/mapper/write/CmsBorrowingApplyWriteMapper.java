package com.jp.med.cms.modules.contractBorrow.mapper.write;

import com.jp.med.cms.modules.contractBorrow.dto.CmsBorrowingApplyDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 合同借阅申请表
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 21:10:47
 */
@Mapper
public interface CmsBorrowingApplyWriteMapper extends BaseMapper<CmsBorrowingApplyDto> {
	void updateChkState(@Param("auditBchno") String auditBchno, @Param("chkState") String chkState);

	void updateTransferredByBchno(CmsBorrowingApplyDto cmsBorrowingApplyDto);
}
