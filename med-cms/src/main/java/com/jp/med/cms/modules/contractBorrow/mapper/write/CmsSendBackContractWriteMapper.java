package com.jp.med.cms.modules.contractBorrow.mapper.write;

import com.jp.med.cms.modules.contractBorrow.dto.CmsBorrowingApplyDto;
import com.jp.med.cms.modules.contractBorrow.dto.CmsSendBackContractDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 合同借阅归还申请表
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 21:10:47
 */
@Mapper
public interface CmsSendBackContractWriteMapper extends BaseMapper<CmsSendBackContractDto> {
	void updateChkState(@Param("auditBchno") String auditBchno, @Param("chkState") String chkState);

	void updateBackedByBchno(CmsSendBackContractDto cmsSendBackContractDto);
}
