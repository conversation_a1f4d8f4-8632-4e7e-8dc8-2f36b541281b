package com.jp.med.cms.modules.contractManage.service.read.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.deepoove.poi.policy.HackLoopTableRenderPolicy;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.bpm.BpmMessageLogDto;
import com.jp.med.common.dto.bpm.BpmMessageLogPageReqVO;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.BpmProcessInstanceFeignApi;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.DateUtils;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.common.util.PdfUtil;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.cms.modules.contractManage.mapper.read.CmsContractReadMapper;
import com.jp.med.cms.modules.contractManage.dto.CmsContractDto;
import com.jp.med.cms.modules.contractManage.vo.CmsContractVo;
import com.jp.med.cms.modules.contractManage.service.read.CmsContractReadService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class CmsContractReadServiceImpl extends ServiceImpl<CmsContractReadMapper, CmsContractDto> implements CmsContractReadService {

	@Autowired
	private CmsContractReadMapper cmsContractReadMapper;

	@Autowired
	private BpmProcessInstanceFeignApi bpmProcessInstanceFeignApi;

	@Override
	public List<CmsContractVo> queryList(CmsContractDto dto) {

		return cmsContractReadMapper.queryList(dto);
	}

	@Override
	public List<CmsContractVo> queryPageList(CmsContractDto dto) {
		PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
		HrmUser hrmUser = dto.getSysUser().getHrmUser();
		String user = org.apache.commons.lang.StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();

		//草拟合同审批
		if (StringUtils.isNotEmpty(dto.getAudit())) {
			// 审核页面
			dto.setChker(user);
		} else {
			if (MedConst.DEV_NAME.equals(dto.getSysUser().getUsername())) {
				//如果是管理员新增提出需求，则empid设为null
				dto.setCrter(null);
			}else {

				dto.setCrter(user);
			}

		}
		//合同文件审核
		if (StringUtils.isNotEmpty(dto.getAudit1())) {
			// 审核页面
			dto.setChker(user);
		} else {
			if (MedConst.DEV_NAME.equals(dto.getSysUser().getUsername())) {
				//如果是管理员新增提出需求，则empid设为null
				dto.setCrter(null);
			}else {
				String collRoleIdsStr = dto.getSysUser().getRoleDataIdStr();
				List<String> roleIds = Arrays.asList(collRoleIdsStr.split(","));
				if (roleIds.contains("18")){
					dto.setCrter(null); 
				}else {
					dto.setCrter(user);
				}

			}

			}
		List<CmsContractVo> list = cmsContractReadMapper.queryList(dto);

		return list;
	}


	/**
	 * 查询所有的合同，角色就是默认管理员权限
	 * @param dto
	 * @return
	 */
	@Override
	public List<CmsContractVo> queryAllContract(CmsContractDto dto) {
		PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
		// 不设置crter和chker，表示查询所有合同
		// 直接调用mapper的查询方法
		List<CmsContractVo> list = cmsContractReadMapper.queryList(dto);
		return list;
	}

	/**
 * 获取合同规格编码
 * ZJ-科室-2024-0001
 */
	@Override
	public String getCtSpecCode(CmsContractDto dto) {
		// 获取当前年份
		String currentYear = DateUtil.getCurrentTime("yyyy");
		// 查询今年的合同数量
		Long count = cmsContractReadMapper.selectCount(
			new QueryWrapper<CmsContractDto>()
				.apply("to_char(create_time::timestamp,'yyyy') = {0}", currentYear)
		);
		// 生成编号 ZJ-科室-2024-0001
		String orgId = dto.getSysUser().getSysOrgId();
		if(StringUtils.isEmpty(orgId)) {
			throw new AppException("当前账号无科室，无法生成统一编码");
		}
		String orgName = cmsContractReadMapper.deptName(orgId);
		// 将科室名称转换为拼音首字母
		HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
		format.setCaseType(HanyuPinyinCaseType.UPPERCASE);
		format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
		format.setVCharType(HanyuPinyinVCharType.WITH_V);
		
		String pinyinOrgName = "";
		try {
			char[] chars = orgName.toCharArray();
			for (char c : chars) {
				if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
					String[] pinyin = PinyinHelper.toHanyuPinyinStringArray(c, format);
					if (pinyin != null && pinyin.length > 0) {
						pinyinOrgName += pinyin[0].charAt(0);
					}
				}
			}
		} catch (BadHanyuPinyinOutputFormatCombination e) {
			e.printStackTrace();
		}
		orgId = pinyinOrgName;

		String seq = String.format("%04d", count + 1);
		return "JY-" + orgId + "-" + currentYear + "-" + seq;

	}

	@Override
	public String generateAuditPdf(CmsContractDto dto) {
		try {
			List<CmsContractVo> cmsContractVos = cmsContractReadMapper.queryList(dto);
			CmsContractVo contractDto = cmsContractVos.get(0);
//			LambdaQueryWrapper<CmsContractDto> cmsContractDtoLambdaQueryWrapper = new LambdaQueryWrapper<>();
//			cmsContractDtoLambdaQueryWrapper.eq(CmsContractDto::getId, dto.getId());
//			CmsContractDto contractDto = baseMapper.selectOne(cmsContractDtoLambdaQueryWrapper);
			HashMap<String, Object> fillData = new HashMap<>();
			fillData.put("ctCode", StringUtils.isNotBlank(contractDto.getCtCode()) ? contractDto.getCtCode() : 
				(contractDto.getCtUnifiedCode() != null ? contractDto.getCtUnifiedCode() : ""));
			fillData.put("ctName", contractDto.getCtName() != null ? contractDto.getCtName() : "");
			fillData.put("attName", "");
			fillData.put("totalAmt", contractDto.getTotalAmt() != null ? (contractDto.getTotalAmt().compareTo(BigDecimal.ZERO) <= 0 ? "     " : contractDto.getTotalAmt().stripTrailingZeros().toPlainString()) : "");
			fillData.put("validityStartDate", StringUtils.isNotBlank(contractDto.getValidityStartDate()) && !"null".equals(contractDto.getValidityStartDate()) ? contractDto.getValidityStartDate() : "          ");
			fillData.put("validityEndDate", StringUtils.isNotBlank(contractDto.getValidityEndDate()) && !"null".equals(contractDto.getValidityEndDate()) ? contractDto.getValidityEndDate() : "          ");
			fillData.put("oppositeName", contractDto.getOppositeName() != null ? contractDto.getOppositeName() : "");
			fillData.put("contactName", contractDto.getContactName() != null ? contractDto.getContactName() : "");
			fillData.put("oppositePhone", contractDto.getOppositePhone() != null ? contractDto.getOppositePhone() : "");
			fillData.put("signTime", StringUtils.isNotBlank(contractDto.getSignTime()) && !"null".equals(contractDto.getSignTime()) ? contractDto.getSignTime() : "          ");
			fillData.put("nowDate", DateUtil.getCurrentDate("yyyy/MM/dd"));
			fillData.put("nowTime", DateUtil.getCurrentTime(null));
			fillData.put("ctCopies", contractDto.getCtCopies() != null ? contractDto.getCtCopies() : "     ");
			fillData.put("attachmentNames", contractDto.getAttachmentNames() != null ? contractDto.getAttachmentNames() : "     ");

			// 存放审批流程信息
			CommonFeignResult resultMap = bpmProcessInstanceFeignApi.getMessageLog(BpmMessageLogPageReqVO.builder()
					.processDefinitionKeys(
							new ArrayList<>(Arrays.asList("CMS_CONTRACT_UPLOAD_DRAFT")))
					.businessKey(contractDto.getId().toString()).build());
			// 先获取原始数据
			Object data = resultMap.get("data");
			List<Map<String, Object>> rawList;
			if (data instanceof List<?>) {
				rawList = ((List<?>) data).stream().filter(item -> item instanceof Map)
						.map(item -> (Map<String, Object>) item).collect(Collectors.toList());
			} else {
				throw new AppException("获取审批流程数据格式错误");
			}
			// 转换为正确的DTO对象，并在stream中过滤
			List<BpmMessageLogDto> auditDto = rawList.stream()
					.map(item -> BeanUtil.toBean(item, BpmMessageLogDto.class))
					.filter(item -> StrUtil.isNotBlank(item.getTaskId())).peek(item -> {
						// 设置更新日期
						item.setUpdateDate(Objects.nonNull(item.getUpdateTime())
								? DateUtils.format(item.getUpdateTime(), "yyyy-MM-dd")
								: null);
						item.setTaskName(
								StrUtil.isNotBlank(item.getTaskName()) ? item.getTaskName().replace("审批意见",
										item.getTaskName().endsWith("审批意见") ? "\r\n审批意见"
												: "\r\n审批意见\r\n")
										: null);

						try {
							// 解析JSON并设置签名图片
							String variablesLocal = item.getVariablesLocal();
							ObjectMapper objectMapper = new ObjectMapper();
							JsonNode jsonNode = objectMapper.readTree(variablesLocal);
							String taskSignUrl = jsonNode.path("TASK_SIGN_URL").asText();
							String taskReason = jsonNode.path("TASK_REASON").asText();

							item.setPictureRenderData(
									StrUtil.isNotBlank(taskSignUrl)
											? Pictures
											.ofUrl(OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_BPM,
													taskSignUrl), PictureType.suggestFileType(taskSignUrl))
											.size(40, 40).create()
											: null);
							item.setTaskReason(StrUtil.isNotBlank(taskReason) ? taskReason : null);
						} catch (Exception e) {
							log.error("处理签名图片失败", e);
						}
					}).collect(Collectors.toList());
			fillData.put("auditDto", auditDto);
			HackLoopTableRenderPolicy policy = new HackLoopTableRenderPolicy(true);
			Configure config = Configure.builder().bind("auditDto", policy).build();

			return PdfUtil.readOssRender(OSSConst.BUCKET_CMS, "documentTemplate/contractAuditTemplate.docx", fillData, config);

		} catch (Exception e) {
			log.error("pdf获取失败", e);
			throw new AppException("pdf获取失败");
		}
	}

	@Override
	public List<CmsContractVo> queryContractTypeCount(CmsContractDto dto) {
		return cmsContractReadMapper.queryContractTypeCount(dto);
	}
}
