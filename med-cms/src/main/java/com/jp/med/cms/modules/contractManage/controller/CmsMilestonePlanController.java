package com.jp.med.cms.modules.contractManage.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.cms.modules.contractManage.dto.CmsMilestonePlanDto;
import com.jp.med.cms.modules.contractManage.service.read.CmsMilestonePlanReadService;
import com.jp.med.cms.modules.contractManage.service.write.CmsMilestonePlanWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;


/**
 * 里程碑计划表
 * <AUTHOR>
 * @email -
 * @date 2024-07-31 17:39:04
 */
@Api(value = "里程碑计划表", tags = "里程碑计划表")
@RestController
@RequestMapping("cmsMilestonePlan")
public class CmsMilestonePlanController {

    @Autowired
    private CmsMilestonePlanReadService cmsMilestonePlanReadService;

    @Autowired
    private CmsMilestonePlanWriteService cmsMilestonePlanWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询里程碑计划表")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody CmsMilestonePlanDto dto){
        return CommonResult.paging(cmsMilestonePlanReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询里程碑计划表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody CmsMilestonePlanDto dto){
        return CommonResult.success(cmsMilestonePlanReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增里程碑计划表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody List<CmsMilestonePlanDto> dto){
        System.out.println(dto);
        CmsMilestonePlanDto cmsMilestonePlanDto = dto.get(0);
        cmsMilestonePlanWriteService.saveDto(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改里程碑计划表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody CmsMilestonePlanDto dto){
        cmsMilestonePlanWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除里程碑计划表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody CmsMilestonePlanDto dto){
        cmsMilestonePlanWriteService.removeById(dto);
        return CommonResult.success();
    }

}
