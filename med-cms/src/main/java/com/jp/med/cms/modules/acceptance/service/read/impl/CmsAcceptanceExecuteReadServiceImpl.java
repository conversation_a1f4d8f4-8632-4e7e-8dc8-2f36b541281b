package com.jp.med.cms.modules.acceptance.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.cms.modules.acceptance.mapper.read.CmsAcceptanceExecuteReadMapper;
import com.jp.med.cms.modules.acceptance.dto.CmsAcceptanceExecuteDto;
import com.jp.med.cms.modules.acceptance.vo.CmsAcceptanceExecuteVo;
import com.jp.med.cms.modules.acceptance.service.read.CmsAcceptanceExecuteReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class CmsAcceptanceExecuteReadServiceImpl extends ServiceImpl<CmsAcceptanceExecuteReadMapper, CmsAcceptanceExecuteDto> implements CmsAcceptanceExecuteReadService {

    @Autowired
    private CmsAcceptanceExecuteReadMapper cmsAcceptanceExecuteReadMapper;

    @Override
    public List<CmsAcceptanceExecuteVo> queryList(CmsAcceptanceExecuteDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return cmsAcceptanceExecuteReadMapper.queryList(dto);
    }

}
