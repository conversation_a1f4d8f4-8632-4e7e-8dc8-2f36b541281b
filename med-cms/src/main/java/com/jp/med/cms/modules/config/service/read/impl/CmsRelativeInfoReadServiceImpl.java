package com.jp.med.cms.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.cms.modules.config.mapper.read.CmsRelativeInfoReadMapper;
import com.jp.med.cms.modules.config.dto.CmsRelativeInfoDto;
import com.jp.med.cms.modules.config.vo.CmsRelativeInfoVo;
import com.jp.med.cms.modules.config.service.read.CmsRelativeInfoReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class CmsRelativeInfoReadServiceImpl extends ServiceImpl<CmsRelativeInfoReadMapper, CmsRelativeInfoDto> implements CmsRelativeInfoReadService {

    @Autowired
    private CmsRelativeInfoReadMapper cmsRelativeInfoReadMapper;

    @Override
    public List<CmsRelativeInfoVo> queryList(CmsRelativeInfoDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return cmsRelativeInfoReadMapper.queryList(dto);
    }

}
