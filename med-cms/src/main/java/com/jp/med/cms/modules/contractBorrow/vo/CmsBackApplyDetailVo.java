package com.jp.med.cms.modules.contractBorrow.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 合同借阅归还详情
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 21:10:47
 */
@Data
public class CmsBackApplyDetailVo {

	/** id */
	private Integer id;

	/** 借阅文件详情id */
	private Integer contractDetailId;

	/** 申请id */
	private Integer applyId;

	/** $column.comments */
	private String hospitalId;

	private String att;

	private String attName;

	private String backRemark;

	private Integer contractId;

	private String backType;
}
