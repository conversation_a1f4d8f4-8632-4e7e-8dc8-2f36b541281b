package com.jp.med.cms.modules.templateConfig.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cms.modules.templateConfig.dto.CmsSpecialElementDto;
import com.jp.med.cms.modules.templateConfig.vo.CmsSpecialElementVo;

import java.util.List;

/**
 * 定制特殊元素模板
 * <AUTHOR>
 * @email -
 * @date 2024-07-15 15:11:24
 */
public interface CmsSpecialElementReadService extends IService<CmsSpecialElementDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsSpecialElementVo> queryList(CmsSpecialElementDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<CmsSpecialElementVo> queryPageList(CmsSpecialElementDto dto);

	List<CmsSpecialElementVo> queryListByIds(CmsSpecialElementDto dto);
}

