package com.jp.med.cms.modules.common.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 审核明细表
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 09:33:58
 */
@Data
public class CmsAuditRcdfmVo {

	/** id */
	private Integer id;

	/** 批次号 */
	private String bchno;

	/** 审核人 */
	private String chker;

	/** 审核科室 */
	private String chkDept;

	/** 审核时间 */
	private String chkTime;

	/** $column.comments */
	private String chkRemarks;

	/** 审核签名 */
	private String chkSign;

	/** 审核附件 */
	private String chkAtt;

	/** 审核顺序 */
	private Integer chkSeq;

	/** 审核状态 */
	private String chkState;

	/** 审核签名地址 */
	private String chkSignPath;

	/** 审核附件地址 */
	private String chkAttPath;

	/** 实际审核人 */
	private String actChker;

	/** 说明 */
	private String dscr;

	/** APP消息ID */
	private Integer messageId;

	/** APP消息补充信息 */
	private String messageSup;

	/** APP消息部分Payload */
	private String messagePayload;

	/** 上级审核批次号 */
	private String parentBchno;

}
