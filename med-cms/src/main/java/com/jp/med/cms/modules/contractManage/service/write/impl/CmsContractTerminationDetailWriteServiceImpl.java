package com.jp.med.cms.modules.contractManage.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.cms.modules.contractManage.mapper.write.CmsContractTerminationDetailWriteMapper;
import com.jp.med.cms.modules.contractManage.dto.CmsContractTerminationDetailDto;
import com.jp.med.cms.modules.contractManage.service.write.CmsContractTerminationDetailWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合同终止详情表
 * <AUTHOR>
 * @email -
 * @date 2024-08-16 11:33:28
 */
@Service
@Transactional(readOnly = false)
public class CmsContractTerminationDetailWriteServiceImpl extends ServiceImpl<CmsContractTerminationDetailWriteMapper, CmsContractTerminationDetailDto> implements CmsContractTerminationDetailWriteService {
}
