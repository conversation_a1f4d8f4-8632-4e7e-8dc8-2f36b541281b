package com.jp.med.cms.modules.opposite.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 相对方联系人
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 20:21:53
 */
@Data
public class CmsOppositeContactsVo {

	/** id */
	private Integer id;

	/** 联系人姓名 */
	private String contactName;

	/** 联系电话 */
	private String phone;

	/** 邮箱 */
	private String email;

	/** 联系人备注 */
	private String contactRemark;

	/** 相对方id */
	private Integer oppositeId;

	/** 组织id */
	private String hospitalId;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 最近更新人 */
	private String updtr;

	/** 更新时间 */
	private String updateTime;

}
