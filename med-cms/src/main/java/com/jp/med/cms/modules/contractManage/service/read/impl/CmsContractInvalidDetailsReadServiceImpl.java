package com.jp.med.cms.modules.contractManage.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.cms.modules.contractManage.mapper.read.CmsContractInvalidDetailsReadMapper;
import com.jp.med.cms.modules.contractManage.dto.CmsContractInvalidDetailsDto;
import com.jp.med.cms.modules.contractManage.vo.CmsContractInvalidDetailsVo;
import com.jp.med.cms.modules.contractManage.service.read.CmsContractInvalidDetailsReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class CmsContractInvalidDetailsReadServiceImpl extends ServiceImpl<CmsContractInvalidDetailsReadMapper, CmsContractInvalidDetailsDto> implements CmsContractInvalidDetailsReadService {

    @Autowired
    private CmsContractInvalidDetailsReadMapper cmsContractInvalidDetailsReadMapper;

    @Override
    public List<CmsContractInvalidDetailsVo> queryList(CmsContractInvalidDetailsDto dto) {
        return cmsContractInvalidDetailsReadMapper.queryList(dto);
    }

    @Override
    public List<CmsContractInvalidDetailsVo> queryPageList(CmsContractInvalidDetailsDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return cmsContractInvalidDetailsReadMapper.queryList(dto);
    }

}
