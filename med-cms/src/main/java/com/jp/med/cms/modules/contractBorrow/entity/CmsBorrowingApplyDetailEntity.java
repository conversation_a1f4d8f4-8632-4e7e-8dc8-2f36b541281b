package com.jp.med.cms.modules.contractBorrow.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 合同借阅详情
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 21:10:47
 */
@Data
@TableName("cms_borrowing_apply_detail")
public class CmsBorrowingApplyDetailEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 借阅文件详情id */
	@TableField("contract_detail_id")
	private Integer contractDetailId;

	/** 申请id */
	@TableField("apply_id")
	private Integer applyId;

	/** $column.comments */
	@TableField("hospital_id")
	private String hospitalId;

	@TableField("borrow_type")
	private String borrowType;

	@TableField("contract_id")
	private Integer contractlId;
}
