package com.jp.med.cms.modules.contractManage.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.cms.modules.contractManage.mapper.read.CmsKeyElementsReadMapper;
import com.jp.med.cms.modules.contractManage.dto.CmsKeyElementsDto;
import com.jp.med.cms.modules.contractManage.vo.CmsKeyElementsVo;
import com.jp.med.cms.modules.contractManage.service.read.CmsKeyElementsReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class CmsKeyElementsReadServiceImpl extends ServiceImpl<CmsKeyElementsReadMapper, CmsKeyElementsDto> implements CmsKeyElementsReadService {

    @Autowired
    private CmsKeyElementsReadMapper cmsKeyElementsReadMapper;

    @Override
    public List<CmsKeyElementsVo> queryList(CmsKeyElementsDto dto) {
        return cmsKeyElementsReadMapper.queryList(dto);
    }

    @Override
    public List<CmsKeyElementsVo> queryPageList(CmsKeyElementsDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return cmsKeyElementsReadMapper.queryList(dto);
    }

}
