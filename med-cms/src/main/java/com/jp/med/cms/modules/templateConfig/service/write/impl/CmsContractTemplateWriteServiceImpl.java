package com.jp.med.cms.modules.templateConfig.service.write.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jp.med.cms.modules.templateConfig.dto.CmsSpecialElementDto;
import com.jp.med.cms.modules.templateConfig.dto.CmsSpecialTextElementDto;
import com.jp.med.cms.modules.templateConfig.mapper.read.CmsContractTemplateReadMapper;
import com.jp.med.cms.modules.templateConfig.mapper.write.CmsSpecialElementWriteMapper;
import com.jp.med.cms.modules.templateConfig.mapper.write.CmsSpecialTextElementWriteMapper;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.DateUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.cms.modules.templateConfig.mapper.write.CmsContractTemplateWriteMapper;
import com.jp.med.cms.modules.templateConfig.dto.CmsContractTemplateDto;
import com.jp.med.cms.modules.templateConfig.service.write.CmsContractTemplateWriteService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 合同模板
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-03 17:57:37
 */
@Service
@Transactional(readOnly = false)
public class CmsContractTemplateWriteServiceImpl extends ServiceImpl<CmsContractTemplateWriteMapper, CmsContractTemplateDto> implements CmsContractTemplateWriteService {

	@Autowired
	private CmsContractTemplateWriteMapper templateWriteMapper;
	@Autowired
	private CmsContractTemplateReadMapper templateReadMapper;

	@Autowired
	private CmsSpecialElementWriteMapper specialElementWriteMapper;

	@Autowired
	private CmsSpecialTextElementWriteMapper specialTextElementWriteMapper;

	@Override
	public void saveDto(CmsContractTemplateDto dto) {
		String time = DateUtil.getCurrentTime(null);
		String empCode = org.apache.commons.lang.StringUtils.isNotEmpty(dto.getSysUser().getHrmUser().getEmpCode()) ? dto.getSysUser().getHrmUser().getEmpCode() : dto.getSysUser().getNickname();
		dto.setCreateTime(time);
		dto.setCrter(empCode);
		dto.setUpdateTime(time);
		dto.setUpdtr(empCode);
		dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
		// 使用次数
		dto.setUseTimes(0);
		//保存时间戳备用
		long currentTimeMillis = System.currentTimeMillis();
		String stringValue = String.valueOf(currentTimeMillis);
		dto.setCheckCode(stringValue);
		dto.setHisCheckCode(stringValue);


		// TODO 将其他的该分类下的合同模板的状态修改为无效,后面有需要再说
		templateWriteMapper.insert(dto);
		// 创建ObjectMapper实例
		ObjectMapper objectMapper = new ObjectMapper();
		try {
			// 将JSON字符串解析为JsonNode
			JsonNode rootNode = objectMapper.readTree(dto.getTemplate());
			// 获取名为"specialIds"的元素
			JsonNode nameNode = rootNode.get("specialIds");
			List<Integer> specialIds = objectMapper.convertValue(nameNode, new TypeReference<List<Integer>>() {});
			specialIds.forEach(specialId -> {
				CmsSpecialElementDto specialElementDto = new CmsSpecialElementDto();
				specialElementDto.setId(specialId);
				specialElementDto.setActiveFlag(MedConst.TYPE_1);
				specialElementDto.setTemplateId(dto.getId());
				specialElementDto.setTypeCode(dto.getTypeCode());
				specialElementWriteMapper.updateById(specialElementDto);
			});

//			// 获取名为"specialTextIds"的元素
//			JsonNode textIds = rootNode.get("specialTextIds");
//			List<Integer> specialTextIds = objectMapper.convertValue(textIds, new TypeReference<List<Integer>>() {});
//			specialTextIds.forEach(textId -> {
//				CmsSpecialTextElementDto specialTextDto = new CmsSpecialTextElementDto();
//				specialTextDto.setId(textId);
//				specialTextDto.setActiveFlag(MedConst.TYPE_1);
//				specialTextElementWriteMapper.updateById(specialTextDto);
//			});
		}catch (Exception e) {
			throw new AppException("模板数据格式错误");
		}
	}
}
