package com.jp.med.cms.modules.opposite.mapper.read;

import com.jp.med.cms.modules.opposite.dto.CmsOppositeContactsDto;
import com.jp.med.cms.modules.opposite.vo.CmsOppositeContactsVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 相对方联系人
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 20:21:53
 */
@Mapper
public interface CmsOppositeContactsReadMapper extends BaseMapper<CmsOppositeContactsDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsOppositeContactsVo> queryList(CmsOppositeContactsDto dto);

}
