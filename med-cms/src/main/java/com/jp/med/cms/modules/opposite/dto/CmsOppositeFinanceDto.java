package com.jp.med.cms.modules.opposite.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 财务信息
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 20:21:53
 */
@Data
@TableName("cms_opposite_finance" )
public class CmsOppositeFinanceDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 开户行 */
    @TableField("opening_bank")
    private String openingBank;

    /** 银行账户 */
    @TableField("bank_account")
    private String bankAccount;

    /** 相对方id */
    @TableField("opposite_id")
    private Integer oppositeId;

    /** 组织id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 最近更新人 */
    @TableField("updtr")
    private String updtr;

    /** 更新时间 */
    @TableField("update_time")
    private String updateTime;

}
