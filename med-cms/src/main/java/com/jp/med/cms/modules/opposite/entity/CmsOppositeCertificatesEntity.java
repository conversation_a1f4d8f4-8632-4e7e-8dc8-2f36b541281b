package com.jp.med.cms.modules.opposite.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 相对方资质信息
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 20:21:53
 */
@Data
@TableName("cms_opposite_certificates")
public class CmsOppositeCertificatesEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 资质名称 */
	@TableField("certificates_name")
	private String certificatesName;

	/** 文件路径 */
	@TableField("file_path")
	private String filePath;

	/** 文件名称 */
	@TableField("file_name")
	private String fileName;

	/** 文件 */
	@TableField("file")
	private String file;

	/** 资质说明 */
	@TableField("certificates_remark")
	private String certificatesRemark;

	/** 相对方id */
	@TableField("opposite_id")
	private Integer oppositeId;

	/** 组织id */
	@TableField("hospital_id")
	private String hospitalId;

}
