package com.jp.med.cms.modules.contractManage.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.cms.modules.contractManage.dto.CmsProgressTrackingDto;
import com.jp.med.cms.modules.contractManage.service.read.CmsProgressTrackingReadService;
import com.jp.med.cms.modules.contractManage.service.write.CmsProgressTrackingWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 合同进度追踪时间线
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 11:01:44
 */
@Api(value = "合同进度追踪时间线", tags = "合同进度追踪时间线")
@RestController
@RequestMapping("cmsProgressTracking")
public class CmsProgressTrackingController {

    @Autowired
    private CmsProgressTrackingReadService cmsProgressTrackingReadService;

    @Autowired
    private CmsProgressTrackingWriteService cmsProgressTrackingWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询合同进度追踪时间线")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody CmsProgressTrackingDto dto){
        return CommonResult.paging(cmsProgressTrackingReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询合同进度追踪时间线")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody CmsProgressTrackingDto dto){
        return CommonResult.success(cmsProgressTrackingReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增合同进度追踪时间线")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody CmsProgressTrackingDto dto){
        cmsProgressTrackingWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改合同进度追踪时间线")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody CmsProgressTrackingDto dto){
        cmsProgressTrackingWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除合同进度追踪时间线")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody CmsProgressTrackingDto dto){
        cmsProgressTrackingWriteService.removeById(dto);
        return CommonResult.success();
    }

}
