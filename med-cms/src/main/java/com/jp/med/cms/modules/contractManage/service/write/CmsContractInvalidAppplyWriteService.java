package com.jp.med.cms.modules.contractManage.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cms.modules.contractManage.dto.CmsContractInvalidAppplyDto;

/**
 * 合同作废申请
 * <AUTHOR>
 * @email -
 * @date 2024-08-14 16:29:12
 */
public interface CmsContractInvalidAppplyWriteService extends IService<CmsContractInvalidAppplyDto> {
	void saveDto(CmsContractInvalidAppplyDto dto);
}

