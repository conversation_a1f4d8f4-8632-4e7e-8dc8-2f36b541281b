package com.jp.med.cms.modules.contractManage.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.cms.modules.contractManage.dto.CmsLiquidatedDamagesDto;
import com.jp.med.cms.modules.contractManage.service.read.CmsLiquidatedDamagesReadService;
import com.jp.med.cms.modules.contractManage.service.write.CmsLiquidatedDamagesWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 违约金赔付计划表
 * <AUTHOR>
 * @email -
 * @date 2024-08-16 11:33:28
 */
@Api(value = "违约金赔付计划表", tags = "违约金赔付计划表")
@RestController
@RequestMapping("cmsLiquidatedDamages")
public class CmsLiquidatedDamagesController {

    @Autowired
    private CmsLiquidatedDamagesReadService cmsLiquidatedDamagesReadService;

    @Autowired
    private CmsLiquidatedDamagesWriteService cmsLiquidatedDamagesWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询违约金赔付计划表")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody CmsLiquidatedDamagesDto dto){
        return CommonResult.paging(cmsLiquidatedDamagesReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询违约金赔付计划表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody CmsLiquidatedDamagesDto dto){
        return CommonResult.success(cmsLiquidatedDamagesReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增违约金赔付计划表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody CmsLiquidatedDamagesDto dto){
        cmsLiquidatedDamagesWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改违约金赔付计划表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody CmsLiquidatedDamagesDto dto){
        cmsLiquidatedDamagesWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除违约金赔付计划表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody CmsLiquidatedDamagesDto dto){
        cmsLiquidatedDamagesWriteService.removeById(dto);
        return CommonResult.success();
    }

}
