package com.jp.med.cms.modules.opposite.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.cms.modules.opposite.dto.CmsOppositeCertificatesDto;
import com.jp.med.cms.modules.opposite.service.read.CmsOppositeCertificatesReadService;
import com.jp.med.cms.modules.opposite.service.write.CmsOppositeCertificatesWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 相对方资质信息
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 20:21:53
 */
@Api(value = "相对方资质信息", tags = "相对方资质信息")
@RestController
@RequestMapping("cmsOppositeCertificates")
public class CmsOppositeCertificatesController {

    @Autowired
    private CmsOppositeCertificatesReadService cmsOppositeCertificatesReadService;

    @Autowired
    private CmsOppositeCertificatesWriteService cmsOppositeCertificatesWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询相对方资质信息")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody CmsOppositeCertificatesDto dto){
        return CommonResult.paging(cmsOppositeCertificatesReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增相对方资质信息")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody CmsOppositeCertificatesDto dto){
        cmsOppositeCertificatesWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改相对方资质信息")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody CmsOppositeCertificatesDto dto){
        cmsOppositeCertificatesWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除相对方资质信息")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody CmsOppositeCertificatesDto dto){
        cmsOppositeCertificatesWriteService.removeById(dto);
        return CommonResult.success();
    }

}
