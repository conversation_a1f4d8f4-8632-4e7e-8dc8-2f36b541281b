package com.jp.med.cms.modules.contractBorrow.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.common.entity.user.HrmUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.cms.modules.contractBorrow.mapper.read.CmsSendBackContractReadMapper;
import com.jp.med.cms.modules.contractBorrow.dto.CmsSendBackContractDto;
import com.jp.med.cms.modules.contractBorrow.vo.CmsSendBackContractVo;
import com.jp.med.cms.modules.contractBorrow.service.read.CmsSendBackContractReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class CmsSendBackContractReadServiceImpl extends ServiceImpl<CmsSendBackContractReadMapper, CmsSendBackContractDto> implements CmsSendBackContractReadService {

    @Autowired
    private CmsSendBackContractReadMapper cmsSendBackContractReadMapper;

    @Override
    public List<CmsSendBackContractVo> queryList(CmsSendBackContractDto dto) {
        return cmsSendBackContractReadMapper.queryList(dto);
    }

    @Override
    public List<CmsSendBackContractVo> queryPageList(CmsSendBackContractDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String user = org.apache.commons.lang.StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
        //草拟合同审批
        if (StringUtils.isNotEmpty(dto.getAudit())) {
            // 审核页面
            dto.setChker(user);
        } else {
            dto.setCrter(user);
        }
        return cmsSendBackContractReadMapper.queryList(dto);
    }

}
