package com.jp.med.cms.modules.opposite.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 合同相对方表
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 20:21:53
 */
@Data
public class CmsContractOppositeVo {

	/** id
 */
	private Integer id;

	/** 相对方名称 */
	private String oppositeName;

	/** 统一社会信用代码 */
	private String unifiedSocialCreditIdentifier;
	/** 是否关联 */
	private String relevanceOrNot;

	/** 经营状态 */
	private String managementForms;

	/** 企业类型 */
	private String businessType;

	/** 注册日期 */
	private String registrationDate;

	/** 注册资本 */
	private BigDecimal registeredAssets;

	/** 法人姓名 */
	private String corporateName;

	/** 法人手机号 */
	private String corporatePhone;

	/** 法人邮箱 */
	private String corporateEmail;

	/** 营业期限开始时间 */
	private String businessStartTime;

	/** 营业期限结束时间 */
	private String businessEndtime;

	/** 住所 */
	private String domicile;

	/** 经营范围 */
	private String businessScope;

	/** 备注 */
	private String remark;

	/** 组织id */
	private String hospitalId;

	/** 组织id */
	private String businessLicense;

	private List<CmsOppositeCertificatesVo> oppositeCertificatesList;

	private List<CmsOppositeContactsVo> oppositeContactsList;

	private List<CmsOppositeFinanceVo> oppositeFinanceList;
}
