package com.jp.med.cms.modules.opposite.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 相对方资质信息
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 20:21:53
 */
@Data
public class CmsOppositeCertificatesVo {

	/** id */
	private Integer id;

	/** 资质名称 */
	private String certificatesName;

	/** 文件路径 */
	private String filePath;

	/** 文件名称 */
	private String fileName;

	/** 文件 */
	private String file;

	/** 资质说明 */
	private String certificatesRemark;

	/** 相对方id */
	private Integer oppositeId;

	/** 组织id */
	private String hospitalId;

}
