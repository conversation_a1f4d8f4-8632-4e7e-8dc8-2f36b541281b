package com.jp.med.cms.modules.contractManage.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.cms.modules.contractManage.dto.CmsContractAddAttDto;
import com.jp.med.cms.modules.contractManage.service.read.CmsContractAddAttReadService;
import com.jp.med.cms.modules.contractManage.service.write.CmsContractAddAttWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2025-02-08 10:28:15
 */
@Api(value = "${comments}", tags = "${comments}")
@RestController
@RequestMapping("cmsContractAddAtt")
public class CmsContractAddAttController {

    @Autowired
    private CmsContractAddAttReadService cmsContractAddAttReadService;

    @Autowired
    private CmsContractAddAttWriteService cmsContractAddAttWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询${comments}")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody CmsContractAddAttDto dto){
        return CommonResult.paging(cmsContractAddAttReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询${comments}")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody CmsContractAddAttDto dto){
        return CommonResult.success(cmsContractAddAttReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增${comments}")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody CmsContractAddAttDto dto){
        cmsContractAddAttWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改${comments}")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody CmsContractAddAttDto dto){
        cmsContractAddAttWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除${comments}")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody CmsContractAddAttDto dto){
        cmsContractAddAttWriteService.removeById(dto);
        return CommonResult.success();
    }

}
