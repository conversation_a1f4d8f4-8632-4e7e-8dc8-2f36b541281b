package com.jp.med.cms.modules.templateConfig.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 创建的表单元素json
 * <AUTHOR>
 * @email -
 * @date 2024-11-20 09:35:01
 */
@Data
public class CmsFormItemJsonVo {

	/** id */
	private Integer id;

	/** curd的属性json */
	private String crudInterface;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 修改人 */
	private String updtr;

	/** 修改时间 */
	private String updateTime;

	/** 组织id */
	private String hospitalId;

	/**
	 * 删除标识
	 */
    private Integer isDeleted;
}
