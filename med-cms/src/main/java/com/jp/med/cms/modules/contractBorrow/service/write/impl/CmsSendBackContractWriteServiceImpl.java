package com.jp.med.cms.modules.contractBorrow.service.write.impl;
import cn.hutool.core.collection.CollectionUtil;
import com.jp.med.cms.modules.contractBorrow.dto.CmsBackApplyDetailDto;
import com.jp.med.cms.modules.contractBorrow.dto.CmsBorrowingApplyDetailDto;
import com.jp.med.cms.modules.contractBorrow.mapper.write.CmsBackApplyDetailWriteMapper;
import com.jp.med.cms.modules.contractBorrow.mapper.write.CmsBorrowingApplyDetailWriteMapper;
import com.jp.med.cms.modules.contractManage.dto.CmsProgressTrackingDto;
import com.jp.med.cms.modules.contractManage.mapper.write.CmsProgressTrackingWriteMapper;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.app.AppMsgSup;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.payload.AuditPayload;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.feign.AuditFeignService;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.FeignExecuteUtil;
import com.jp.med.common.util.ULIDUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.cms.modules.contractBorrow.mapper.write.CmsSendBackContractWriteMapper;
import com.jp.med.cms.modules.contractBorrow.dto.CmsSendBackContractDto;
import com.jp.med.cms.modules.contractBorrow.service.write.CmsSendBackContractWriteService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 合同借阅归还申请表
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 21:10:47
 */
@Service
@Transactional(readOnly = false)
public class CmsSendBackContractWriteServiceImpl extends ServiceImpl<CmsSendBackContractWriteMapper, CmsSendBackContractDto> implements CmsSendBackContractWriteService {

	@Autowired
	private CmsSendBackContractWriteMapper cmsSendBackContractWriteMapper;
	//进度追踪写入
	@Autowired
	private CmsProgressTrackingWriteMapper progressTrackingWriteMapper;

	@Autowired
	private AuditFeignService auditFeignService;
	@Override
	public void saveDto(CmsSendBackContractDto dto) {
		HrmUser hrmUser = dto.getSysUser().getHrmUser();
		String appyer = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
		//创建时间
		String currentTime = DateUtil.getCurrentTime(null);
		dto.setHospitalId(dto.getHospitalId());
		dto.setCrter(appyer);
		dto.setUpdtr(appyer);
		dto.setCreateTime(currentTime);
		dto.setUpdateTime(currentTime);

		//审核状态 0 未审核
		dto.setChkState(MedConst.TYPE_0);

		//审核批次号
		String batchNum = ULIDUtil.generate();
		dto.setAuditBchno(batchNum);
		dto.setContractId(dto.getContractId());
		// 新增
		baseMapper.insert(dto);

		//写一下进度
		CmsProgressTrackingDto cmsProgressTrackingDto = new CmsProgressTrackingDto();
		cmsProgressTrackingDto.setContractId(dto.getContractId());
		cmsProgressTrackingDto.setTime(currentTime);
		cmsProgressTrackingDto.setHospitalId(dto.getHospitalId());
		cmsProgressTrackingDto.setTitle("已有申请查阅记录待审批");
		cmsProgressTrackingDto.setContent("已有申请查阅记录待审批,请决定是否同意");
		cmsProgressTrackingDto.setType("warning");
		cmsProgressTrackingDto.setPushUrl("cms/borrowing/backContractAudit");
		progressTrackingWriteMapper.insert(cmsProgressTrackingDto);

		ArrayList<CmsBackApplyDetailDto> detailDtoList = new ArrayList<CmsBackApplyDetailDto>();
		if (CollectionUtil.isNotEmpty(dto.getCmsBorrowingApplyDetailsDtos())){
			dto.getCmsBorrowingApplyDetailsDtos().forEach(cmsBorrowingApplyDetailDto -> {
				CmsBackApplyDetailDto cmsBackApplyDetailDto = new CmsBackApplyDetailDto();
				cmsBackApplyDetailDto.setApplyId(dto.getId());
				cmsBackApplyDetailDto.setContractId(cmsBorrowingApplyDetailDto.getContractlId());
				cmsBackApplyDetailDto.setContractDetailId(cmsBorrowingApplyDetailDto.getContractDetailId());
				cmsBackApplyDetailDto.setBackType(cmsBorrowingApplyDetailDto.getBorrowType());
				detailDtoList.add(cmsBackApplyDetailDto);
			});
			BatchUtil.batch("insert",detailDtoList, CmsBackApplyDetailWriteMapper.class);
		}
		//审核流程
		if (CollectionUtil.isNotEmpty(dto.getAuditDetails())) {
			AppMsgSup appMsgSup = new AppMsgSup();
			appMsgSup.setTitle("合同归还申请");
			String appyerName = StringUtils.isNotEmpty(hrmUser.getEmpName()) ? hrmUser.getEmpName() : dto.getSysUser().getNickname();
			appMsgSup.setAppyer(appyer);
			appMsgSup.setAppyerName(appyerName);
			appMsgSup.setContent("合同归还申请:[" + dto.getBorrowTextArea() + "],申请人:[" + appyerName + "]");

			AuditPayload auditPayload = new AuditPayload();
			auditPayload.setAppyer(appyerName);
			auditPayload.setAppyerDept(dto.getSysUser().getHrmUser().getHrmOrgName());
			auditPayload.setAuditBchno(batchNum);

			auditPayload.setDetailUrl("/cms/cmsBackApplyDetail/appAuditDetail");
			Map<String, Object> map = new LinkedHashMap<>();
			map.put("ctName", "合同名称");
			map.put("appyerName", "申请人");

			auditPayload.setDisplayItem(map);

			auditPayload.setTableTitle("合同新建");
			auditPayload.setTableHeader("合同名称,申请人");
			auditPayload.setTableContent("ctName,appyerName");

			FeignExecuteUtil.execute(auditFeignService.saveAuditDetail(new AuditDetail(batchNum, dto.getAuditDetails(), appMsgSup, auditPayload, OSSConst.BUCKET_CMS)));
		}

	}
}
