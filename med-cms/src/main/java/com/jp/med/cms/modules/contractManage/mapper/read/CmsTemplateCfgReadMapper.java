package com.jp.med.cms.modules.contractManage.mapper.read;

import com.jp.med.cms.modules.contractManage.dto.CmsTemplateCfgDto;
import com.jp.med.cms.modules.contractManage.vo.CmsTemplateCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 合同模版配置
 * <AUTHOR>
 * @email -
 * @date 2024-06-20 11:31:27
 */
@Mapper
public interface CmsTemplateCfgReadMapper extends BaseMapper<CmsTemplateCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsTemplateCfgVo> queryList(CmsTemplateCfgDto dto);
}
