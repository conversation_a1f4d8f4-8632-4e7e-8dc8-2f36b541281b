package com.jp.med.cms.modules.templateConfig.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-11-15 11:18:36
 */
@Data
public class CmsSpecialTextElementVo {

	/** id */
	private Integer id;

	/** 标题 */
	private String ttl;

	/** 文本模板唯一标识 */
	private String ttlKey;

	/** 创建人 */
	private String crter;

	/** 组织id */
	private String hospitalId;

	/** 是否启动标识 */
	private String activeFlag;


	/** 创建时间 */
	private String createTime;

	/**
	 * 文本提示
	 */
	private String placeholder;
}
