package com.jp.med.cms.modules.templateConfig.mapper.read;

import com.jp.med.cms.modules.templateConfig.dto.CmsSpecialElementDto;
import com.jp.med.cms.modules.templateConfig.vo.CmsSpecialElementVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 定制特殊元素模板
 * <AUTHOR>
 * @email -
 * @date 2024-07-15 15:11:24
 */
@Mapper
public interface CmsSpecialElementReadMapper extends BaseMapper<CmsSpecialElementDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsSpecialElementVo> queryList(CmsSpecialElementDto dto);
}
