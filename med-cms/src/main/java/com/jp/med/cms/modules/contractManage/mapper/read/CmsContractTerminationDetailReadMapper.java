package com.jp.med.cms.modules.contractManage.mapper.read;

import com.jp.med.cms.modules.contractManage.dto.CmsContractTerminationDetailDto;
import com.jp.med.cms.modules.contractManage.vo.CmsContractTerminationDetailVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 合同终止详情表
 * <AUTHOR>
 * @email -
 * @date 2024-08-16 11:33:28
 */
@Mapper
public interface CmsContractTerminationDetailReadMapper extends BaseMapper<CmsContractTerminationDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsContractTerminationDetailVo> queryList(CmsContractTerminationDetailDto dto);
}
