package com.jp.med.cms.modules.contractType.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.jp.med.cms.modules.contractType.mapper.read.CmsContractTypeReadMapper;
import com.jp.med.cms.modules.contractType.vo.CmsContractTypeVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.common.util.ULIDUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.cms.modules.contractType.mapper.write.CmsContractTypeWriteMapper;
import com.jp.med.cms.modules.contractType.dto.CmsContractTypeDto;
import com.jp.med.cms.modules.contractType.service.write.CmsContractTypeWriteService;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * 合同分类表
 *
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 16:26:07
 */
@Service
@Transactional(readOnly = false)
public class CmsContractTypeWriteServiceImpl extends ServiceImpl<CmsContractTypeWriteMapper, CmsContractTypeDto> implements CmsContractTypeWriteService {

	@Autowired
	private CmsContractTypeReadMapper cmsContractTypeReadMapper;
	@Autowired
	private CmsContractTypeWriteMapper cmsContractTypeWriteMapper;

	/**
	 * 新增合同分类表
	 *
	 * @param dto 规则：父编码下如果有子编码，就最大+1
	 *            如果没有就父编码+001
	 */
	@Override
	public void saveDto(CmsContractTypeDto dto) {
		if (org.apache.commons.lang3.StringUtils.isNotEmpty(dto.getDtoJson())) {
			CmsContractTypeDto cmsContractTypeDto = JSONObject.parseObject(dto.getDtoJson(), CmsContractTypeDto.class);
			cmsContractTypeDto.setAttFiles(dto.getAttFiles());
			cmsContractTypeDto.setSysUser(dto.getSysUser());
			dto = cmsContractTypeDto;
		}
		//文件上传
		if (dto.getAttFiles() != null && CollectionUtil.isNotEmpty(dto.getAttFiles())) {
			StringBuilder attStr = new StringBuilder();
			StringBuilder attStr2 = new StringBuilder();
			dto.getAttFiles().forEach(a -> {
				String attFilePath = OSSUtil.uploadFile(OSSConst.BUCKET_CMS, "contractTemplate/", a);
				attStr.append(attFilePath);


			});
			dto.setAtt(attStr.toString());
		}
		/**
		 * 编码生成示例：
		 * 		-01
		 * 		 	-01001
		 * 		 	-01002
		 * 		-02
		 * 			-02001
		 */
//		if (MedConst.TYPE_1.equals(dto.getType())) {
//			dto.setSqlAutowiredHospitalCondition(true);
//			String maxCode = cmsContractTypeReadMapper.queryMaxCode(dto);
//			if (StringUtils.isNotEmpty(maxCode)) {
//				int number = Integer.parseInt(maxCode);
//				number++;
//				String incrementedStr = String.format("%02d", number);
//				dto.setCode(incrementedStr);
//			} else {
//				dto.setCode("01");
//			}
//		} else {
//			if (StringUtils.isEmpty(dto.getParentCode())) {
//				throw new AppException("父编码不能为空");
//			} else {
//                // 拿父编码去查code
//				dto.setSqlAutowiredHospitalCondition(true);
//				String maxCode = cmsContractTypeReadMapper.queryMaxCode(dto);
//				if (StringUtils.isNotEmpty(maxCode)) {
//					int number = Integer.parseInt(maxCode);
//					number++;
//					String incrementedStr = String.format("%05d", number);
//
//					dto.setCode(incrementedStr);
//				} else {
//					dto.setCode(dto.getParentCode() + "001");
//				}
//			}
//		}
		if (MedConst.TYPE_1.equals(dto.getType())) {
			dto.setCode(ULIDUtil.generate());
		}else {
			if (StringUtils.isEmpty(dto.getParentCode())) {
				throw new AppException("父编码不能为空");
			}
			dto.setCode(ULIDUtil.generate());
		}

//        if (StringUtils.isEmpty(dto.getParentCode()))dto.setCode("001");
		String empCode = StringUtils.isNotEmpty(dto.getSysUser().getHrmUser().getEmpCode()) ? dto.getSysUser().getHrmUser().getEmpCode() : dto.getSysUser().getNickname();
		String format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

//		String managerOrg1 = String.join(",", dto.getManagerOrg1());
//		String usingOrg1 = String.join(",", dto.getUsingOrg1());

//		dto.setManagerOrg(managerOrg1);
//		dto.setUsingOrg(usingOrg1);
		dto.setCrter(empCode);
		dto.setCreateTime(format);
		dto.setUpdtr(empCode);
		dto.setUpdteTime(format);
		dto.setIsDeleted(MedConst.NOT_DELETED);
		dto.setHospitalId(dto.getSysUser().getHospitalId());

		cmsContractTypeWriteMapper.insert(dto);
	}
}
