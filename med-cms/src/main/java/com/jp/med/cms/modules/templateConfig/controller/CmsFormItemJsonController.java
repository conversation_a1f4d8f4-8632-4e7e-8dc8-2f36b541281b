package com.jp.med.cms.modules.templateConfig.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.cms.modules.templateConfig.dto.CmsFormItemJsonDto;
import com.jp.med.cms.modules.templateConfig.service.read.CmsFormItemJsonReadService;
import com.jp.med.cms.modules.templateConfig.service.write.CmsFormItemJsonWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 创建的表单元素json
 * <AUTHOR>
 * @email -
 * @date 2024-11-20 09:35:01
 */
@Api(value = "创建的表单元素json", tags = "创建的表单元素json")
@RestController
@RequestMapping("cmsFormItemJson")
public class CmsFormItemJsonController {

    @Autowired
    private CmsFormItemJsonReadService cmsFormItemJsonReadService;

    @Autowired
    private CmsFormItemJsonWriteService cmsFormItemJsonWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询创建的表单元素json")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody CmsFormItemJsonDto dto){
        return CommonResult.paging(cmsFormItemJsonReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询创建的表单元素json")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody CmsFormItemJsonDto dto){
        return CommonResult.success(cmsFormItemJsonReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增创建的表单元素json")
    @PostMapping("/save")
    public CommonResult<?> save( CmsFormItemJsonDto dto){
        cmsFormItemJsonWriteService.saveDto(dto);
        return CommonResult.success();
    }

    @ApiOperation("定制元素")
    @PostMapping("/customElement")
        public CommonResult<?> customElement( @RequestBody CmsFormItemJsonDto dto){
        cmsFormItemJsonWriteService.customElement(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改创建的表单元素json")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody CmsFormItemJsonDto dto){
        cmsFormItemJsonWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除创建的表单元素json")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody CmsFormItemJsonDto dto){
        cmsFormItemJsonWriteService.removeDtoById(dto);
        return CommonResult.success();
    }

}
