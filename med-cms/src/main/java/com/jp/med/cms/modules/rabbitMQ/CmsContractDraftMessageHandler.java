package com.jp.med.cms.modules.rabbitMQ;

import com.jp.med.cms.enums.CmsProcessModel;
import com.jp.med.cms.modules.contractManage.dto.CmsProgressTrackingDto;
import com.jp.med.cms.modules.contractManage.mapper.write.CmsContractWriteMapper;
import com.jp.med.cms.modules.contractManage.mapper.write.CmsProgressTrackingWriteMapper;
import com.jp.med.cms.modules.contractManage.service.write.CmsContractWriteService;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.bpm.BpmProcessInstanceStatus;
import com.jp.med.common.messsage.AbstractBpmApproveMessageHandle;
import com.jp.med.common.util.DateUtil;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.jp.med.cms.modules.contractManage.dto.CmsContractDto;

@Slf4j
@RequiredArgsConstructor
@Component
public class CmsContractDraftMessageHandler  extends AbstractBpmApproveMessageHandle {

	private final CmsContractWriteMapper cmsContractWriteMapper;

	//进度追踪写入
	private final CmsProgressTrackingWriteMapper progressTrackingWriteMapper;
	@Override
	public String[] getProcessIdentifier() {
		return new String[] {CmsProcessModel.CT_UPLOAD_DRAFT.getProcessId()};
	}


	@RabbitListener(queues = "CMS_CONTRACT_UPLOAD_DRAFT")
	@Override
	public void onMessage(BpmProcessInstanceStatus msg, Message message, Channel channel) throws Exception {
		receiveMessage0(msg);
	}

	@Override
	protected void handleCreate(BpmProcessInstanceStatus message) {
		log.info("Process {} created!", message.getProcessDefinitionKey());
		log.debug(message.toString());
	}

	@Override
	protected void handleApproved(BpmProcessInstanceStatus message) {
		log.info("Process {} approved!", message.getProcessDefinitionKey());
		log.debug(message.toString());
		cmsContractWriteMapper.updateById(
				CmsContractDto.builder()
						.id(Integer.valueOf(message.getBusinessKey()))
						.chkState(AuditConst.RES_SUCCESS)
						.build()
		);
		//创建时间
		String currentTime = DateUtil.getCurrentTime(null);
		//写一下进度
		CmsProgressTrackingDto cmsProgressTrackingDto = new CmsProgressTrackingDto();
		cmsProgressTrackingDto.setContractId(Integer.parseInt(message.getBusinessKey()));
		cmsProgressTrackingDto.setTime(currentTime);
		cmsProgressTrackingDto.setHospitalId(MedConst.HOSPITAL_ID);
		cmsProgressTrackingDto.setTitle("合同新增审批通过");
		cmsProgressTrackingDto.setContent("草拟合同审批通过,待签订");
		cmsProgressTrackingDto.setType("success");
		cmsProgressTrackingDto.setPushUrl("cms/contractManager/contractControl");
		progressTrackingWriteMapper.insert(cmsProgressTrackingDto);
	}

	@Override
	protected void handleRejected(BpmProcessInstanceStatus message) {
		log.info("Process {} rejected!", message.getProcessDefinitionKey());
		log.debug(message.toString());
		cmsContractWriteMapper.updateById(
				CmsContractDto.builder()
						.id(Integer.valueOf(message.getBusinessKey()))
						.chkState(AuditConst.RES_FAIL)
						.build()
		);
		//创建时间
		String currentTime = DateUtil.getCurrentTime(null);
		//写一下进度
		CmsProgressTrackingDto cmsProgressTrackingDto = new CmsProgressTrackingDto();
		cmsProgressTrackingDto.setContractId(Integer.parseInt(message.getBusinessKey()));
		cmsProgressTrackingDto.setTime(currentTime);
		cmsProgressTrackingDto.setHospitalId(MedConst.HOSPITAL_ID);
		cmsProgressTrackingDto.setTitle("合同新增审批未通过");
		cmsProgressTrackingDto.setContent("草拟合同审批未通过,请重新拟定");
		cmsProgressTrackingDto.setType("error");
		cmsProgressTrackingDto.setPushUrl("cms/contractManager/contractDraft");
		progressTrackingWriteMapper.insert(cmsProgressTrackingDto);
	}

	@Override
	protected void handleRunning(BpmProcessInstanceStatus message) {
		log.info("Process {} is running!", message.getProcessDefinitionKey());
		log.debug(message.toString());
	}

	@Override
	protected void handleCancelled(BpmProcessInstanceStatus message) {
		log.info("Process {} is cancelled!", message.getProcessDefinitionKey());
		log.debug(message.toString());
		cmsContractWriteMapper.updateById(
				CmsContractDto.builder()
						.id(Integer.valueOf(message.getBusinessKey()))
						.chkState(MedConst.TYPE_4)
						.build()
		);
		//创建时间
		String currentTime = DateUtil.getCurrentTime(null);
		//写一下进度
		CmsProgressTrackingDto cmsProgressTrackingDto = new CmsProgressTrackingDto();
		cmsProgressTrackingDto.setContractId(Integer.parseInt(message.getBusinessKey()));
		cmsProgressTrackingDto.setTime(currentTime);
		cmsProgressTrackingDto.setHospitalId(MedConst.HOSPITAL_ID);
		cmsProgressTrackingDto.setTitle("合同新增审批已撤销");
		cmsProgressTrackingDto.setContent("草拟合同审批已撤销,请重新提交");
		cmsProgressTrackingDto.setType("warning");
		cmsProgressTrackingDto.setPushUrl("cms/contractManager/contractDraft");
		progressTrackingWriteMapper.insert(cmsProgressTrackingDto);
	}

}
