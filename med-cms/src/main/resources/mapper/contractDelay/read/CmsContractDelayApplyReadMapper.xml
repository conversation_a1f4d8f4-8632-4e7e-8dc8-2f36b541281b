<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.contractDelay.mapper.read.CmsContractDelayApplyReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.cms.modules.contractDelay.vo.CmsContractDelayApplyVo" id="contractDelayApplyMap">
        <result property="id" column="id"/>
        <result property="contractId" column="contract_id"/>
        <result property="reason" column="reason"/>
        <result property="effectiveDate" column="effective_date"/>
        <result property="appyer" column="appyer"/>
        <result property="apperyOrg" column="appery_org"/>
        <result property="appyerPhone" column="appyer_phone"/>
        <result property="chkState" column="chk_state"/>
        <result property="auditBchno" column="audit_bchno"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="updtr" column="updtr"/>
        <result property="updateTime" column="update_time"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.cms.modules.contractDelay.vo.CmsContractDelayApplyVo">
        select
            id as id,
            contract_id as contractId,
            reason as reason,
            effective_date as effectiveDate,
            appyer as appyer,
            appery_org as apperyOrg,
            appyer_phone as appyerPhone,
            chk_state as chkState,
            audit_bchno as auditBchno,
            crter as crter,
            create_time as createTime,
            updtr as updtr,
            update_time as updateTime,
            hospital_id as hospitalId
        from cms_contract_delay_apply
    </select>

</mapper>
