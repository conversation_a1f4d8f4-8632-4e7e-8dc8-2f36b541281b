<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.contractDelay.mapper.read.CmsContractDelayDetailsReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.cms.modules.contractDelay.vo.CmsContractDelayDetailsVo" id="contractDelayDetailsMap">
        <result property="id" column="id"/>
        <result property="keyElementId" column="key_element_id"/>
        <result property="keyElement" column="key_element"/>
        <result property="nodeRequire" column="node_require"/>
        <result property="timeNode" column="time_node"/>
        <result property="seq" column="seq"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="updtr" column="updtr"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.cms.modules.contractDelay.vo.CmsContractDelayDetailsVo">
        select
            id as id,
            key_element_id as keyElementId,
            key_element as keyElement,
            node_require as nodeRequire,
            time_node as timeNode,
            seq as seq,
            hospital_id as hospitalId,
            updtr as updtr,
            update_time as updateTime
        from cms_contract_delay_details
    </select>

</mapper>
