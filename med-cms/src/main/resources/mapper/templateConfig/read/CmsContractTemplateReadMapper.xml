<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.templateConfig.mapper.read.CmsContractTemplateReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.cms.modules.templateConfig.vo.CmsContractTemplateVo" id="contractTemplateMap">
        <result property="id" column="id"/>
        <result property="ttl" column="ttl"/>
        <result property="templateRemark" column="template_remark"/>
        <result property="template" column="template"/>
        <result property="createTime" column="create_time"/>
        <result property="crter" column="crter"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="activeFlag" column="active_flag"/>
        <result property="typeCode" column="type_code"/>
        <result property="updateTime" column="update_time"/>
        <result property="updtr" column="updtr"/>
        <result property="checkCode" column="check_code"/>
        <result property="hisCheckCode" column="his_check_code"/>
        <result property="useTimes" column="use_times"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.cms.modules.templateConfig.vo.CmsContractTemplateVo">
        select
            id as id,
            ttl as ttl,
            template_remark as templateRemark,
            template as template,
            create_time as createTime,
            crter as crter,
            hospital_id as hospitalId,
            active_flag as activeFlag,
            type_code as typeCode,
            type_code as contractType,
            update_time as updateTime,
            updtr as updtr,
            check_code as checkCode,
            his_check_code as hisCheckCode,
            use_times as useTimes,
          draft_branch_code as  draftBranchCode
        from cms_contract_template
        <where>
            <if test="id != null and id != ''">
                AND id = #{id,jdbcType=INTEGER}
            </if>
            <if test="ttl != null and ttl != ''">
                AND ttl LIKE CONCAT('%',#{ttl,jdbcType=VARCHAR},'%')
            </if>
            <if test="activeFlag != null and activeFlag != ''">
                AND active_flag = #{activeFlag,jdbcType=VARCHAR}
            </if>
            <if test="typeCode != null and typeCode != ''">
                AND type_code = #{typeCode,jdbcType=VARCHAR}
            </if>
            <if test="checkCode != null and checkCode != ''">
                AND check_code = #{checkCode,jdbcType=VARCHAR}
            </if>

            <if test="hisCheckCode != null and hisCheckCode != ''">
                AND his_check_code = #{hisCheckCode,jdbcType=VARCHAR}
            </if>
            <if test="useTimes != null and useTimes != ''">
                AND use_times = #{useTimes,jdbcType=INTEGER}
            </if>
        </where>
    </select>

</mapper>
