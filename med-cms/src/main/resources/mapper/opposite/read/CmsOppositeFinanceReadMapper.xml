<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.opposite.mapper.read.CmsOppositeFinanceReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.cms.modules.opposite.vo.CmsOppositeFinanceVo" id="oppositeFinanceMap">
        <result property="id" column="id"/>
        <result property="openingBank" column="opening_bank"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="oppositeId" column="opposite_id"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="updtr" column="updtr"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.cms.modules.opposite.vo.CmsOppositeFinanceVo">
        select
            id as id,
            opening_bank as openingBank,
            bank_account as bankAccount,
            opposite_id as oppositeId,
            hospital_id as hospitalId,
            crter as crter,
            create_time as createTime,
            updtr as updtr,
            update_time as updateTime
        from cms_opposite_finance
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="openingBank != null and openingBank !=''">
                and opening_bank = #{openingBank,jdbcType=VARCHAR}
            </if>
            <if test="bankAccount != null and bankAccount !=''">
                and bank_account = #{bankAccount,jdbcType=VARCHAR}
            </if>
            <if test="oppositeId != null">
                and opposite_id = #{oppositeId,jdbcType=INTEGER}
            </if>
            <if test="hospitalId != null">
                and hospital_id = #{hospitalId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

</mapper>
