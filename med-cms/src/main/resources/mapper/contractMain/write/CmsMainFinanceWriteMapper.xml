<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.contractMain.mapper.write.CmsMainFinanceWriteMapper">


    <insert id="insertDto" parameterType="com.jp.med.cms.modules.contractMain.vo.CmsMainFinanceVo">
        INSERT INTO cms_main_finance
        (
            hospital_id,
            opening_bank,
            bank_account,
            create_time,
            update_time,
            main_id,
            crter,
            updtr
        )
        VALUES (#{hospitalId},
                #{openingBank},
                #{bankAccount},
                #{createTime},
                #{updateTime},
                #{mainId},
                #{crter},
                #{updtr}
               )
    </insert>

</mapper>
