<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.contractMain.mapper.read.CmsMainFinanceReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.cms.modules.contractMain.vo.CmsMainFinanceVo" id="mainFinanceMap">
        <result property="id" column="id"/>
        <result property="openingBank" column="opening_bank"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="mainId" column="main_id"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="updtr" column="updtr"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.cms.modules.contractMain.vo.CmsMainFinanceVo">
        select
            id as id,
            opening_bank as openingBank,
            bank_account as bankAccount,
            main_id as mainId,
            hospital_id as hospitalId,
            crter as crter,
            create_time as createTime,
            updtr as updtr,
            update_time as updateTime
        from cms_main_finance
        <where>
            <if test="id != null">
                id = #{id,jdbcType=INTEGER}
            </if>
        <if test="mainId != null">
            and  main_id = #{mainId,jdbcType=INTEGER}
        </if>
        </where>
    </select>

</mapper>
