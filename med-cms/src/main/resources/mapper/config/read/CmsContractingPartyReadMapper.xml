<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.config.mapper.read.CmsContractingPartyReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.cms.modules.config.vo.CmsContractingPartyVo" id="contractingPartyMap">
        <result property="id" column="id"/>
        <result property="partyName" column="party_name"/>
        <result property="partyType" column="party_type"/>
        <result property="legalPerson" column="legal_person"/>
        <result property="contractName" column="contract_name"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="addr" column="addr"/>
        <result property="activeFlag" column="active_flag"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="updtr" column="updtr"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.cms.modules.config.vo.CmsContractingPartyVo">
        select
            id as id,
            party_name as partyName,
            party_type as partyType,
            legal_person as legalPerson,
            contract_name as contractName,
            phone as phone,
            email as email,
            addr as addr,
            active_flag as activeFlag,
            crter as crter,
            create_time as createTime,
            updtr as updtr,
            update_time as updateTime
        from cms_contracting_party
    </select>

</mapper>
