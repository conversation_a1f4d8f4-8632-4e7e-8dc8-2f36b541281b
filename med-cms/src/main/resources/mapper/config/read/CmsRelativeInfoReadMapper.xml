<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.config.mapper.read.CmsRelativeInfoReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.cms.modules.config.vo.CmsRelativeInfoVo" id="relativeInfoMap">
        <result property="id" column="id"/>
        <result property="relativeName" column="relative_name"/>
        <result property="phone" column="phone"/>
        <result property="addr" column="addr"/>
        <result property="addrTetails" column="addr_tetails"/>
        <result property="role" column="role"/>
        <result property="unitName" column="unit_name"/>
        <result property="unitContractName" column="unit_contract_name"/>
        <result property="unitPhone" column="unit_phone"/>
        <result property="unitEmail" column="unit_email"/>
        <result property="activeFlag" column="active_flag"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="updtr" column="updtr"/>
        <result property="updateTime" column="update_time"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.cms.modules.config.vo.CmsRelativeInfoVo">
        select
            id as id,
            relative_name as relativeName,
            phone as phone,
            addr as addr,
            addr_tetails as addrTetails,
            role as role,
            unit_name as unitName,
            unit_contract_name as unitContractName,
            unit_phone as unitPhone,
            unit_email as unitEmail,
            active_flag as activeFlag,
            crter as crter,
            create_time as createTime,
            updtr as updtr,
            update_time as updateTime,
            hospital_id as hospitalId
        from cms_relative_info
    </select>

</mapper>
