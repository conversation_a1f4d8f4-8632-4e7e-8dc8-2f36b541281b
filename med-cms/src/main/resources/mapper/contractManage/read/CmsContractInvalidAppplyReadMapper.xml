<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.contractManage.mapper.read.CmsContractInvalidAppplyReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.cms.modules.contractManage.vo.CmsContractInvalidAppplyVo" id="contractInvalidAppplyMap">
        <result property="id" column="id"/>
        <result property="contractId" column="contract_id"/>
        <result property="mainId" column="main_id"/>
        <result property="mainName" column="main_name"/>
        <result property="oppositeId" column="opposite_id"/>
        <result property="oppositeName" column="opposite_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="reason" column="reason"/>
        <result property="descr" column="descr"/>
        <result property="appyer" column="appyer"/>
        <result property="appyOrgId" column="appy_org_id"/>
        <result property="appyerType" column="appyer_type"/>
        <result property="auditBchno" column="audit_bchno"/>
        <result property="chkState" column="chk_state"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updtr" column="updtr"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="appyerPhone" column="appyer_phone"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.cms.modules.contractManage.vo.CmsContractInvalidAppplyVo">
        select cia.id as id,
        cia.contract_id as contractId,
        cia.main_id as mainId,
        cia.main_name as mainName,
        cia.opposite_id as oppositeId,

        cia.apply_time as applyTime,
        cia.reason as reason,
        cia.descr as descr,
        cia.appyer as appyer,
        case when position(#{chker,jdbcType=VARCHAR} in f.chker) > 0 then '1' else '0' end as auditFlag,
        cia.appy_org_id as appyOrgId,
        cia.appyer_type as appyerType,
        cia.audit_bchno as auditBchno,
        cia.chk_state as chkState,
        cia.crter as crter,
        cia.create_time as createTime,
        cia.update_time as updateTime,
        cia.updtr as updtr,
        cia.hospital_id as hospitalId,
        cia.appyer_phone as appyerPhone,
        cia.plan_invalid_time as planInvalidTime,
        cia.att_descr as attDescr,
        cia.use_org as useOrg,
        co.create_time as createTime,
        co.sign_time as signTime,
        co.opposite_name as oppositeName,
        co.att as att,
        co.att_name as attName,
        co.ct_code as ctCode,
        co.tender_num as tenderNum,
        co.bid_winning_time as bidWinningTime,
        co.tender_name as tenderName,
        co.use_org as useOrg,
        co.payment_type as paymentType,
        co.total_amt as totalAmt,
        co.ct_status as ctStatus,
        co.opposite_person as oppositePerson,
        co.opposite_phone as oppositePhone,
        co.responsible_person as responsiblePerson,
        co.responsible_phone as responsiblePhone


        from cms_contract_invalid_appply cia
        left join (select a.bchno,
        a.chker
        from cms_audit_rcdfm a
        inner join (select e.bchno,
        min(e.chk_seq) as seq
        from cms_audit_rcdfm e
        where e.chk_state = '0'
        and e.chk_time is null
        group by e.bchno) b
        on a.bchno = b.bchno
        and a.chk_seq = b.seq) f
        on cia.audit_bchno = f.bchno
        left join cms_contract co
        on cia.contract_id = co.id
        <where>
            <if test="chkState != null and chkState != '' ">
                and cia.chk_state = #{chkState,jdbcType=VARCHAR}
            </if>
            <if test="audit != null and audit != ''">
                AND exists (
                select e.bchno
                from cms_audit_rcdfm e
                where position(#{chker,jdbcType=VARCHAR} in e.chker) > 0
                and cia.audit_bchno = e.bchno
                <if test="chkState != null and chkState != '' and chkState == '0'">
                    and e.chk_state = '0'
                    and e.chk_time is null
                </if>
                )
            </if>
            <if test="id != null and id != ''">
                and cia.id = #{id,jdbcType=INTEGER}
            </if>
            <if test="contractId != null and contractId != ''">
                and cia.contract_id = #{contractId,jdbcType=INTEGER}
            </if>
            <if test="mainId != null and mainId != ''">
                and cia.main_id = #{mainId,jdbcType=INTEGER}
            </if>
            <if test="oppositeId != null and oppositeId != ''">
                and cia.opposite_id = #{oppositeId,jdbcType=INTEGER}
            </if>
            <if test="applyTime != null and applyTime != ''">
                and cia.apply_time = #{applyTime,jdbcType=VARCHAR}
            </if>
            <if test="reason != null and reason != ''">
                and cia.reason = #{reason,jdbcType=VARCHAR}
            </if>
            <if test="descr != null and descr != ''">
                and cia.descr = #{descr,jdbcType=VARCHAR}
            </if>
            <if test="appyer != null and appyer != ''">
                and cia.appyer = #{appyer,jdbcType=VARCHAR}
            </if>
            <if test="appyOrgId != null and appyOrgId != ''">
                and cia.appy_org_id = #{appyOrgId,jdbcType=VARCHAR}
            </if>
            <if test="appyerType != null and appyerType != ''">
                and cia.appyer_type = #{appyerType,jdbcType=VARCHAR}
            </if>
            <if test="auditBchno != null and auditBchno != ''">
                and cia.audit_bchno = #{auditBchno,jdbcType=VARCHAR}
            </if>
            <if test="chkState != null and chkState != ''">
                and cia.chk_state = #{chkState,jdbcType=VARCHAR}
            </if>
            <if test="crter != null and crter != ''">
                and cia.crter = #{crter,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null and createTime != ''">
                and cia.create_time = #{createTime,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null and updateTime != ''">
                and cia.update_time = #{updateTime,jdbcType=VARCHAR}
            </if>
            <if test="updtr != null and updtr != ''">
                and cia.updtr = #{updtr,jdbcType=VARCHAR}
            </if>

            <if test="appyerPhone != null and appyerPhone != ''">
                and cia.appyer_phone = #{appyerPhone,jdbcType=VARCHAR}
            </if>
            <if test="planInvalidTime != null and planInvalidTime != ''">
                and cia.plan_invalid_time = #{planInvalidTime,jdbcType=VARCHAR}
            </if>
            <if test="attDescr != null and attDescr != ''">
                and cia.att_descr = #{attDescr,jdbcType=VARCHAR}
            </if>
            <if test="mainName != null and mainName != ''">
                and cia.main_name = #{mainName,jdbcType=VARCHAR}
            </if>
            <if test="oppositeName != null and oppositeName != ''">
                and cia.opposite_name = #{oppositeName,jdbcType=VARCHAR}
            </if>
        </where>

    </select>

</mapper>
