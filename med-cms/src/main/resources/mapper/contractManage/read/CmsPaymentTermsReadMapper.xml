<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.contractManage.mapper.read.CmsPaymentTermsReadMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.cms.modules.contractManage.vo.CmsPaymentTermsVo"
        id="paymentTermsMap">
        <result property="id" column="id" />
        <result property="stage" column="stage" />
        <result property="requirements" column="requirements" />
        <result property="proportion" column="proportion" />
        <result property="totalAmt" column="total_amt" />
        <result property="paymentTime" column="payment_time" />
        <result property="hospitalId" column="hospital_id" />
        <result property="seq" column="seq" />
        <result property="hisRequirements" column="his_requirements" />
        <result property="updateTime" column="update_time" />
        <result property="businessStatus" column="business_status" />
        <result property="remark" column="remark" />
        <result property="operator" column="operator" />
        <result property="optTime" column="opt_time" />
        <result property="actualPaymentTime" column="actual_payment_time" />
        <result property="reimbursePerson" column="reimburse_person" />
        <result property="reimburseDept" column="reimburse_dept" />
        <result property="reimburseTime" column="reimburse_time" />
        <result property="reimburseNo" column="reimburse_no" />
        <result property="pushTaskTime" column="push_task_time" />
    </resultMap>
    <select id="queryList" resultType="com.jp.med.cms.modules.contractManage.vo.CmsPaymentTermsVo">
        select id as id, stage as stage, requirements as requirements, proportion as proportion,
        total_amt as totalAmt, payment_time as paymentTime, hospital_id as hospitalId, seq as seq,
        his_requirements as hisRequirements, update_time as updateTime, business_status as
        businessStatus, remark as remark, operator as operator, opt_time as optTime, contract_id as
        contractId, payment_terms as paymentTerms, payment_type as paymentType, current_pay_amt as
        currentPayAmt, reim_id as reimId, reim_status_flag as reimStatusFlag, actual_payment_time as
        actualPaymentTime, reimburse_person as reimbursePerson, reimburse_dept as reimburseDept,
        reimburse_time as reimburseTime, reimburse_no as reimburseNo, push_task_time as pushTaskTime
        from cms_payment_terms <where>
            <if test="id != null"> and id = #{id,jdbcType=INTEGER} </if>
            <if
                test="contractId != null"> and contract_id = #{contractId,jdbcType=INTEGER} </if>
            <if
                test="actualPaymentTimeStart != null and actualPaymentTimeStart != ''"> and
        actual_payment_time &gt;= #{actualPaymentTimeStart} </if>
            <if
                test="actualPaymentTimeEnd != null and actualPaymentTimeEnd != ''"> and
        actual_payment_time &lt;= #{actualPaymentTimeEnd} </if>
            <if
                test="reimburseTimeStart != null and reimburseTimeStart != ''"> and reimburse_time
        &gt;= #{reimburseTimeStart} </if>
            <if
                test="reimburseTimeEnd != null and reimburseTimeEnd != ''"> and reimburse_time &lt;=
        #{reimburseTimeEnd} </if>
            <if test="pushTaskTimeStart != null and pushTaskTimeStart != ''">
        and push_task_time &gt;= #{pushTaskTimeStart} </if>
            <if
                test="pushTaskTimeEnd != null and pushTaskTimeEnd != ''"> and push_task_time &lt;=
        #{pushTaskTimeEnd} </if>
            <if test="reimbursePerson != null and reimbursePerson != ''"> and
        reimburse_person like CONCAT('%', #{reimbursePerson}, '%') </if>
            <if
                test="reimburseDept != null and reimburseDept != ''"> and reimburse_dept like
        CONCAT('%', #{reimburseDept}, '%') </if>
        </where> ORDER BY seq ASC </select>

</mapper>