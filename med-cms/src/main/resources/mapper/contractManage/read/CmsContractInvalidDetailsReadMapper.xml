<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.contractManage.mapper.read.CmsContractInvalidDetailsReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.cms.modules.contractManage.vo.CmsContractInvalidDetailsVo" id="contractInvalidDetailsMap">
        <result property="id" column="id"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="att" column="att"/>
        <result property="attName" column="att_name"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="updtr" column="updtr"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.cms.modules.contractManage.vo.CmsContractInvalidDetailsVo">
        select
            cid.id as id,
            cid.apply_id as applyId,
            cid.hospital_id as hospitalId,
            cid.att as att,
            cid.att_name as attName,
            cid.is_deleted as isDeleted,
            cid.updtr as updtr,
            cid.update_time as updateTime
        from cms_contract_invalid_details cid
    </select>

</mapper>
