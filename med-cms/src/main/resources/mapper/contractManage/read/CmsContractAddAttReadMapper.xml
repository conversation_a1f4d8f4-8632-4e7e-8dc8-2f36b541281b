<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.contractManage.mapper.read.CmsContractAddAttReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.cms.modules.contractManage.vo.CmsContractAddAttVo" id="contractAddAttMap">
        <result property="id" column="id"/>
        <result property="att" column="att"/>
        <result property="attName" column="att_name"/>
        <result property="idDeleted" column="id_deleted"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="borrowedFlag" column="borrowed_flag"/>
        <result property="contractId" column="contract_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.cms.modules.contractManage.vo.CmsContractAddAttVo">
        select
            id as id,
            att as att,
            att_name as attName,
            id_deleted as idDeleted,
            hospital_id as hospitalId,
            borrowed_flag as borrowedFlag,
            contract_id as contractId
        from cms_contract_add_att
    </select>

</mapper>
