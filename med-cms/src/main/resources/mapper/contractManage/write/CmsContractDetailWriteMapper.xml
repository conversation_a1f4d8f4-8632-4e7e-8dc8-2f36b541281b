<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.contractManage.mapper.write.CmsContractDetailWriteMapper">

    <update id="updateBorrowedFlagByApplyId" parameterType="com.jp.med.cms.modules.contractManage.dto.CmsContractDetailDto">
        UPDATE cms_contract_detail
        <set>
            <if test="borrowedFlag != null">
                borrowed_flag = #{borrowedFlag,jdbcType=VARCHAR},
            </if>
        </set>
        where apply_id = #{applyId,jdbcType=INTEGER}
    </update>
</mapper>
