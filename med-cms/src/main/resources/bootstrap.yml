spring:
  application:
    name: med-cms
  cloud:
    nacos:
      config:
        server-addr: 10.2.233.11:8848
        namespace: dev
        extension-configs:
          - data-id: core-db.yml
            group: core
            refresh: true

          - data-id: cms-others.yml
            group: cms
            refresh: true

        shared-configs:
          - data-id: shared-redis.yml
            group: shared
            refresh: true

          - data-id: shared-feign.yml
            group: shared
            refresh: true

          - data-id: shared-mybatis.yml
            group: shared
            refresh: true

          - data-id: shared-others.yml
            group: shared
            refresh: true

          - data-id: shared-oss.yml
            group: shared
            refresh: true

          - data-id: shared-nacos.yml
            group: shared
            refresh: true

          - data-id: shared-sleuth.yml
            group: shared
            refresh: true

          - data-id: shared-logback.yml
            group: shared
            refresh: true

          - data-id: shared-activiti.yml
            group: shared
            refresh: true

          - data-id: shared-seata.yml
            group: shared
            refresh: true

server:
  port: 10702

