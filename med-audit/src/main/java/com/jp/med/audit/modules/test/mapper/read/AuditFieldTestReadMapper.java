package com.jp.med.audit.modules.test.mapper.read;

import com.jp.med.audit.modules.test.dto.AuditFieldTestDto;
import com.jp.med.audit.modules.test.vo.AuditFieldTestVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**

 * <AUTHOR>
 * @email -
 * @date 2023-10-15 16:37:59
 */
@Mapper
public interface AuditFieldTestReadMapper extends BaseMapper<AuditFieldTestDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AuditFieldTestVo> queryList(AuditFieldTestDto dto);
}
