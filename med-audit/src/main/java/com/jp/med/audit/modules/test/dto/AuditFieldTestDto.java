package com.jp.med.audit.modules.test.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**

 * <AUTHOR>
 * @email -
 * @date 2023-10-15 16:37:59
 */
@Data
@TableName("audit_field_test" )
public class AuditFieldTestDto extends CommonQueryDto {

    
    @TableId("field_1")
    private String field1;

    
    @TableField("field_2")
    private String field2;

}
