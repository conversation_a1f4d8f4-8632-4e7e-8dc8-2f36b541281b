<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.rms.modules.projectInfo.mapper.read.RmsCooperativeUnitsReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.rms.modules.projectInfo.vo.RmsCooperativeUnitsVo" id="cooperativeUnitsMap">
        <result property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="orgStructureD" column="org_structure_d"/>
        <result property="unitId" column="unit_id"/>
        <result property="seq" column="seq"/>
        <result property="unitName" column="unit_name"/>
        <result property="divide" column="divide"/>
        <result property="attachments" column="attachments" jdbcType="OTHER" typeHandler="com.jp.med.rms.typeHandler.JsonbArrayTypeHandler" />
        <result property="hospitalId" column="hospitalId"/>
        <result property="crter" column="crter"/>
        <result property="updtr" column="updtr"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="queryList" resultMap="cooperativeUnitsMap">
        select
            id,
            project_id,
            org_structure_d,
            unit_id,
            seq,
            unit_name,
            divide,
            attachments,
            crter,
            updtr,
            update_time,
            hospital_id as hospitalId,
        contact_person as contactPerson,

        contact_phone as contactPhone

        from rms_cooperative_units
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="projectId != null">
                and project_id = #{projectId}
            </if>
        </where>
    </select>

</mapper>
