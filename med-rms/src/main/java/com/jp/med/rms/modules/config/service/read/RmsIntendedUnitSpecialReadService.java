package com.jp.med.rms.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.rms.modules.config.dto.RmsIntendedUnitSpecialDto;
import com.jp.med.rms.modules.config.vo.RmsIntendedUnitSpecialVo;

import java.util.List;

/**
 * 意向单位科研专项表
 * <AUTHOR>
 * @email 
 * @date 2024-12-15 17:14:43
 */
public interface RmsIntendedUnitSpecialReadService extends IService<RmsIntendedUnitSpecialDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<RmsIntendedUnitSpecialVo> queryList(RmsIntendedUnitSpecialDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<RmsIntendedUnitSpecialVo> queryPageList(RmsIntendedUnitSpecialDto dto);
}

