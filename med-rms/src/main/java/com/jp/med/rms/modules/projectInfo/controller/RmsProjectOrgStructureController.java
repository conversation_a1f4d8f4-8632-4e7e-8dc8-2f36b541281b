package com.jp.med.rms.modules.projectInfo.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.rms.modules.projectInfo.dto.RmsProjectOrgStructureDto;
import com.jp.med.rms.modules.projectInfo.service.read.RmsProjectOrgStructureReadService;
import com.jp.med.rms.modules.projectInfo.service.write.RmsProjectOrgStructureWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 项目组织结构
 * <AUTHOR>
 * @email -
 * @date 2024-09-14 11:40:12
 */
@Api(value = "项目组织结构", tags = "项目组织结构")
@RestController
@RequestMapping("rmsProjectOrgStructure")
public class RmsProjectOrgStructureController {

    @Autowired
    private RmsProjectOrgStructureReadService rmsProjectOrgStructureReadService;

    @Autowired
    private RmsProjectOrgStructureWriteService rmsProjectOrgStructureWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询项目组织结构")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody RmsProjectOrgStructureDto dto){
        return CommonResult.paging(rmsProjectOrgStructureReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询项目组织结构")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody RmsProjectOrgStructureDto dto){
        return CommonResult.success(rmsProjectOrgStructureReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增项目组织结构")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody RmsProjectOrgStructureDto dto){
        rmsProjectOrgStructureWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改项目组织结构")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody RmsProjectOrgStructureDto dto){
        rmsProjectOrgStructureWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除项目组织结构")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody RmsProjectOrgStructureDto dto){
        rmsProjectOrgStructureWriteService.removeById(dto);
        return CommonResult.success();
    }

}
