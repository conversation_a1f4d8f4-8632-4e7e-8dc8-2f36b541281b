package com.jp.med.rms.modules.projectApplication.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.rms.enums.RmsBaseAuditStatus;
import com.jp.med.rms.enums.RmsQueryRange;
import com.jp.med.rms.modules.common.dto.RmsAuditRcdfmDto;
import com.jp.med.rms.modules.projectInfo.dto.*;
import com.jp.med.rms.typeHandler.GenericJsonbTypeHandler;
import com.jp.med.rms.typeHandler.JsonbArrayTypeHandler;
import lombok.*;
import org.apache.ibatis.type.JdbcType;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 项目信息
 *
 * <AUTHOR>
 * @email -
 * @date 2024-09-10 16:37:08
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("rms_project_main_info")
public class RmsProjectMainInfoDto extends CommonQueryDto {

	/**
	 * id
	 */
	@TableId("id")
	private Integer id;

	/**
	 * 查询范围
	 */
	@TableField(exist = false)
	private RmsQueryRange queryRange;

	/**
	 * 查询用户名
	 */
	@TableField(exist = false)
	private String queryUsername;

	/**
	 * 筛选条件：申报状态
	 * 1 筹备项目：未完成筹备流程的项目
	 * 2 筹备通过项目以及未完成立项流程的项目
	 * 3 筹备未通过项目
	 * 4 立项通过项目
	 * 5 立项未通过项目
	 */
	@TableField(exist = false)
	private Integer declareStatus;

	/**
	 * 是否是可报销项目
	 * 已经立项通过且尚未结题的项目
	 */
	@TableField(exist = false)
	private Boolean reimbursable;

	/**
	 * 是否阶段任务已全部完成
	 */
	@TableField(exist = false)
	private Boolean stageCompleted;

	/**
	 * 项目申报（储备申报、立项申报）：流程定义Key
	 */
	@TableField(exist = false)
	private String processDefinitionKey;

	/**
	 * 立项申报意向单位ID
	 */
	@TableField("intended_unit_id")
	private Integer intendedUnitId;

	/**
	 * 立项申报意向单位 #其他
	 */
	@TableField("intended_unit_other")
	private String intendedUnitOther;

	/**
	 * 立项申报意向单位科研专项id
	 */
	@TableField("intended_unit_special_id")
	private Integer intendedUnitSpecialId;

	/**
	 * 立项申报意向单位科研专项 #其他
	 */
	@TableField("intended_unit_special_other")
	private String intendedUnitSpecialOther;

	/**
	 * 筹备流程实例编码
	 */
	@TableField("pre_process_instance_code")
	private String preProcessInstanceCode;

	/**
	 * 立项流程实例编码
	 */
	@TableField("prj_process_instance_code")
	private String prjProcessInstanceCode;

	/**
	 * 项目编号
	 */
	@TableField("project_code")
	private String projectCode;

	/**
	 * 项目名称
	 */
	@TableField("project_name")
	private String projectName;

	/**
	 * 项目负责人
	 */
	@TableField("project_leader")
	private String projectLeader;

	/**
	 * 项目负责人用户名
	 */
	@TableField("project_leader_username")
	private String projectLeaderUsername;

	/**
	 * 联系电话
	 */
	@TableField("telephone")
	private String telephone;

	/**
	 * 申报级别
	 */
	@TableField("project_level")
	private String projectLevel;

	/**
	 * 课题类别
	 */
	@TableField("topic_category")
	private String topicCategory;

	/**
	 * 研究类型
	 */
	@TableField("research_type")
	private String researchType;

	/**
	 * 与外单位合作
	 */
	@TableField("external_cooperation")
	private String externalCooperation;

	/**
	 * 项目起始日期
	 */
	@TableField("project_start_date")
	private String projectStartDate;

	/**
	 * 项目结束日期
	 */
	@TableField("project_end_date")
	private String projectEndDate;

	/**
	 * 报送日期
	 */
	@TableField("submit_date")
	private String submitDate;

	/**
	 * 申报日期范围 开始日期
	 */
	@TableField(exist = false)
	private String startTime;

	/**
	 * 申报日期范围 结束日期
	 */
	@TableField(exist = false)
	private String endTime;

	/**
	 * 申报状态
	 * 0 筹备申报前
	 * 1 筹备申报科教科选择评审专家
	 * 2 筹备申报专家评审
	 * 3 筹备申报科教科评审
	 * 4 立项申报前（暂时未用）
	 * 5 立项评审科教科选择评审专家
	 * 6 立项评审专家评审
	 * 7 立项评审科教科评审
	 * 8 立项申报意向单位评审（暂时由科教科代审）
	 */
	@TableField("apply_status")
	private String applyStatus;

	/**
	 * 申请人
	 */
	@TableField("appyer")
	private String appyer;

	/**
	 * 申请科室
	 */
	@TableField(exist = false)
	private String apperyOrg;


	/** 科室id */
	@TableField("appy_org_id")
	private String appyOrgId;
	/**
	 * 申请人电话
	 */
	@TableField("appyer_phone")
	private String appyerPhone;

	/**
	 * 审核状态
	 * 1 通过
	 * 2 未通过
	 * 3 待审核
	 */
	@TableField("chk_state")
	private String chkState;

	/**
	 * 审核批次号
	 */
	@TableField("audit_bchno")
	private String auditBchno;

	/**
	 * 创建人
	 */
	@TableField("crter")
	private String crter;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private String createTime;

	/**
	 * 操作人
	 */
	@TableField("updtr")
	private String updtr;

	/**
	 * 操作时间
	 */
	@TableField("update_time")
	private String updateTime;

	/**
	 * 组织id
	 */
	@TableField("hospital_id")
	private String hospitalId;

	/**
	 * 立项拒绝原因
	 */
	@TableField("prj_reject_reason")
	private String prjRejectReason;

	/**
	 * 经费报销标记 0未生成计划，1：已生成计划待申请，2：已申请待报销，3：已完成报销
	 */
	@TableField("reim_flag")
	private String reimFlag;

	/**
	 * 六、已有研究基础、承担优势和项目实施的风险及应对策略的相关数据或成果佐证资料
	 * json字符串数组：json中包含ID, 源文件名、文件大小、文件类型、文件路径
	 */
	@TableField(value = "swot_attachments", jdbcType = JdbcType.OTHER, typeHandler = JsonbArrayTypeHandler.class)
	private List<Map<String, Object>> swotAttachments;

	/**
	 * 课题组成员签字附件
	 */
	@TableField(value = "researchers_sign_attachments", jdbcType = JdbcType.OTHER, typeHandler = JsonbArrayTypeHandler.class)
	private List<Map<String, Object>> researchersSignAttachments;

	/**
	 * 立项附件 #立项确认时上传
	 */
	@TableField(value = "prj_attachments", jdbcType = JdbcType.OTHER, typeHandler = JsonbArrayTypeHandler.class)
	private List<Map<String, Object>> prjAttachments;

	/**
	 * 是否立项通过
	 */
	@TableField("prj_approved")
	private Boolean prjApproved;

	/**
	 * 立项通过时间
	 */
	@TableField("prj_approved_time")
	private LocalDateTime prjApprovedTime;

	/**
	 * 结题总结
	 */
	@TableField("conclusion_summary")
	private String conclusionSummary;

	/**
	 * 结题附件材料
	 */
	@TableField(value = "conclusion_attachments", jdbcType = JdbcType.OTHER, typeHandler = JsonbArrayTypeHandler.class)
	private List<Map<String, Object>> conclusionAttachments;

	/**
	 * 结题其他附件
	 */
	@TableField(value = "conclusion_other_attachments", jdbcType = JdbcType.OTHER, typeHandler = GenericJsonbTypeHandler.class)
	private JsonNode conclusionOtherAttachments;

	/**
	 * 结题提交时间
	 */
	@TableField("conclusion_submit_time")
	private LocalDateTime conclusionSubmitTime;


	/**
	 * 结题状态 #0未提交 1已通过 2被驳回 3待审核
	 */
	@TableField("conclusion_status")
	private RmsBaseAuditStatus conclusionStatus;

	/**
	 * 结题审批时间
	 */
	@TableField("conclusion_audit_time")
	private LocalDateTime conclusionAuditTime;

	/**
	 * 结题流程实例编码
	 */
	@TableField("conclusion_process_instance_code")
	private String conclusionProcessInstanceCode;

	/**
	 * 伦理审查批件
	 */
	@TableField(value = "ethics_attachments", jdbcType = JdbcType.OTHER, typeHandler = GenericJsonbTypeHandler.class)
	private JsonNode ethicsAttachments;

	/**
	 * 伦理审批状态 #0未提交 1已通过 2被驳回 3待审核 4已取消
	 */
	@TableField("ethics_audit_status")
	private RmsBaseAuditStatus ethicsAuditStatus;

	/**
	 * 伦理审批时间
	 */
	@TableField("ethics_audit_time")
	private LocalDateTime ethicsAuditTime;

	/**
	 * 伦理审批流程实例编码
	 */
	@TableField("ethics_process_instance_code")
	private String ethicsProcessInstanceCode;

	/**
	 * 承诺书附件 #任务书
	 */
	@TableField(value = "promise_attachments", jdbcType = JdbcType.OTHER, typeHandler = JsonbArrayTypeHandler.class)
	private List<Map<String, Object>> promiseAttachments;

	/**
	 * 任务书审核状态 #0未提交 1已通过 2被驳回 3待审核 4已取消
	 */
	@TableField(value = "brief_audit_status")
	private RmsBaseAuditStatus briefAuditStatus;

	/**
	 * 任务书审核时间
	 */
	@TableField("brief_audit_time")
	private LocalDateTime briefAuditTime;

	/**
	 * 任务书流程实例编码
	 */
	@TableField("brief_process_instance_code")
	private String briefProcessInstanceCode;

	/**
	 * 验收总结
	 */
	@TableField("acceptance_summary")
	private String acceptanceSummary;

	/**
	 * 验收附件
	 */
	@TableField(value = "acceptance_attachments", jdbcType = JdbcType.OTHER, typeHandler = JsonbArrayTypeHandler.class)
	private List<Map<String, Object>> acceptanceAttachments;

	/**
	 * 验收其他附件
	 */
	@TableField(value = "acceptance_other_attachments", jdbcType = JdbcType.OTHER, typeHandler = GenericJsonbTypeHandler.class)
	private JsonNode acceptanceOtherAttachments;

	/**
	 * 验收审核状态 #0未提交 1已通过 2被驳回 3待审核 4已取消
	 */
	@TableField(value = "acceptance_audit_status")
	private RmsBaseAuditStatus acceptanceAuditStatus;

	/**
	 * 验收审核时间
	 */
	@TableField("acceptance_audit_time")
	private LocalDateTime acceptanceAuditTime;

	/**
	 * 验收流程实例编码
	 */
	@TableField("acceptance_process_instance_code")
	private String acceptanceProcessInstanceCode;

	/**
	 * 是否需要验收结题其他附件
	 */
	@TableField("require_acc_con_other_att")
	private Boolean requireAccConOtherAtt;

	/**
	 * 是否可延期
	 */
	@TableField("defer_allowed")
	private Boolean deferAllowed;

	/**
	 * 项目申报类型
	 */
	@TableField("project_apply_type")
	private String projectApplyType;

	/** 项目信息 */
	@TableField(exist = false)
	private RmsProjectInfoDto projectInfo;

	/**
	 * 创新类型
	 */
	@TableField(exist = false)
	private String innovationType;
//
//	/**
//	 * 成果形式
//	 */
//	@TableField(exist = false)
//	private String[] resultFormat;
//
//
//	/**
//	 * 产学研联合
//	 */
//	@TableField(exist = false)
//	private String industryAcademiaCollab;
//	/**
//	 * 知识产权状况
//	 */
//	@TableField(exist = false)
//	private String ipStatus;
//	/**
//	 * 成果水平
//	 */
//	@TableField(exist = false)
//	private String resultLevel;
//	/**
//	 * 技术标准制定
//	 */
//	@TableField(exist = false)
//	private String techStdDev;
//	/**
//	 * 成果形式
//	 */
//	@TableField(exist = false)
//	private String resultForm;
//
//	/**
//	 * 项目概览--知识产权概览
//	 */
//	@TableField(exist = false)
//	private RmsProjectInte rmsProjectInfo;
//
//	@TableField(exist = false)
//	private BigDecimal budget;
//
//	/**
//	 * 项目概述
//	 */
//	@TableField(exist = false)
//	private String projectDescr;

	@TableField(exist = false)
	private List<RmsProjectPerformanceGoalDto> rmsProjectPerformanceGoalData;

	/**
	 * 项目绩效目标
	 */
	@TableField(exist = false)
	private RmsProjectPerformanceGoalsDto rmsProjectPerformanceGoals;

	/**
	 * 分年度研究内容与考核指标（按每半年度写）
	 * 目前是都写在一起的，后端写的时候，根据时间自动判断是第几个阶段半年度计划
	 */
	@TableField(exist = false)
	private List<RmsProjectContentIndexDto> researchIndexData;

	/**
	 * 项目研究内容与考核指标 的补充说明与预期目标
	 */
	@TableField(exist = false)
	private RmsResearchIndexSupplyDto researchIndexSupply;

	/**
	 * 实验研究设备/仪器登记
	 */
	@TableField(exist = false)
	private List<RmsEquipmentRegistrationDto> equipmentRegData;

	/**
	 * 项目收入经费：申请项目经费、自筹项目经费、项目总经费
	 */
	@TableField(exist = false)
	private RmsProjectFundingIncomeDto rmsProjectFundingIncomeDto;

	/**
	 * 项目支出经费，虽然是List ，但支出项目是固定的
	 */
	@TableField(exist = false)
	private List<RmsProjectFundsExpenseDto> fundsExpenseData;

	/**
	 * 项目申报单位、合作单位及主要研究人员情况
	 */
	@TableField(exist = false)
	private RmsProjectOrgStructureDto projectOrgStructure;

	/**
	 * 项目申报单位、合作单位及主要研究人员情况一栏 --->合作单位
	 */
	@TableField(exist = false)
	private List<RmsCooperativeUnitsDto> cooperativeUnitsData;

	/**
	 * 课题组主要人员情况（含课题负责人）
	 */
	@TableField(exist = false)
	private List<RmsResearchersDto> researchersData;

	/**
	 * 项目申报书的富文本保存
	 */
	@TableField(exist = false)
	private RmsProjectRichTextDto richText;

	/**
	 * 立项材料
	 */
	@TableField(exist = false)
	private List<RmsProposalFileDto> proposalFilesData;


	// 以下与提交审批相关
	/**
	 * 审核页面
	 */
	@TableField(exist = false)
	private String audit;


	/** 审核流程详情 */
	@TableField(exist = false)
	private List<AuditDetail> auditDetails;


	/** 审核流程详情 */
	@TableField(exist = false)
	private List<RmsAuditRcdfmDto> rmsAuditDetails;

	/**
	 * 立项意向标志符
	 */
	@TableField(exist = false)
	private String intentionFlag;

	/**
	 * 申请文件
	 */
	@TableField(exist = false)
	private List<RmsFileUploadRecordDto> applicationFiles;


	/**
	 * 技术查新附件
	 */
	@TableField(value = "tech_query_attachment", jdbcType = JdbcType.OTHER, typeHandler = JsonbArrayTypeHandler.class)
	private List<Map<String, Object>> techQueryAttachment;

	/**
	 * 立项申报中的伦理审批附件
	 */
	@TableField(value = "ethics_approval_attachment",jdbcType = JdbcType.OTHER, typeHandler = JsonbArrayTypeHandler.class)
	private List<Map<String,Object>> ethicsApprovalAttachment;

	/**
	 * 申报书备案附件
	 */
	@TableField(value = "application_declaration_attachment",jdbcType = JdbcType.OTHER, typeHandler = JsonbArrayTypeHandler.class)
	private List<Map<String,Object>> applicationDeclarationAttachment;

	/**
	 * 承诺书附件
	 */
	@TableField(value = "promise_attachment",jdbcType = JdbcType.OTHER, typeHandler = JsonbArrayTypeHandler.class)
	private List<Map<String,Object>> promiseAttachment;

}
