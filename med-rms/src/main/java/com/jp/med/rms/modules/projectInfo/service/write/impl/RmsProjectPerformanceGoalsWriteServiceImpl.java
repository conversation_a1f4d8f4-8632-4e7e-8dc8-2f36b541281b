package com.jp.med.rms.modules.projectInfo.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.rms.modules.projectInfo.mapper.write.RmsProjectPerformanceGoalsWriteMapper;
import com.jp.med.rms.modules.projectInfo.dto.RmsProjectPerformanceGoalsDto;
import com.jp.med.rms.modules.projectInfo.service.write.RmsProjectPerformanceGoalsWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 项目绩效目标
 * <AUTHOR>
 * @email -
 * @date 2024-09-14 11:40:08
 */
@Service
@Transactional(readOnly = false)
public class RmsProjectPerformanceGoalsWriteServiceImpl extends ServiceImpl<RmsProjectPerformanceGoalsWriteMapper, RmsProjectPerformanceGoalsDto> implements RmsProjectPerformanceGoalsWriteService {
}
