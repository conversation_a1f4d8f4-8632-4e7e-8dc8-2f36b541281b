package com.jp.med.rms.modules.config.service.write.impl;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.util.DateUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.rms.modules.config.mapper.write.RmsFundingBudgetCfgWriteMapper;
import com.jp.med.rms.modules.config.dto.RmsFundingBudgetCfgDto;
import com.jp.med.rms.modules.config.service.write.RmsFundingBudgetCfgWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 经费科目预算科目对照配置
 * <AUTHOR>
 * @email -
 * @date 2024-09-13 16:45:40
 */
@Service
@Transactional(readOnly = false)
public class RmsFundingBudgetCfgWriteServiceImpl extends ServiceImpl<RmsFundingBudgetCfgWriteMapper, RmsFundingBudgetCfgDto> implements RmsFundingBudgetCfgWriteService {


	@Autowired
	private RmsFundingBudgetCfgWriteMapper rmsFundingBudgetCfgWriteMapper;

	@Override
	public void saveDto(RmsFundingBudgetCfgDto dto) {
		HrmUser hrmUser = dto.getSysUser().getHrmUser();
		String opter = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
		//创建时间
		String currentTime = DateUtil.getCurrentTime(null);

		dto.setUpdtr(opter);
		dto.setUpdateTime(currentTime);
		dto.setHospitalId(dto.getHospitalId());
		rmsFundingBudgetCfgWriteMapper.insert(dto);
	}
}
