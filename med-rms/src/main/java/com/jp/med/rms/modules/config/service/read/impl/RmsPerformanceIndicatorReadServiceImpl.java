package com.jp.med.rms.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.rms.modules.config.mapper.read.RmsPerformanceIndicatorReadMapper;
import com.jp.med.rms.modules.config.dto.RmsPerformanceIndicatorDto;
import com.jp.med.rms.modules.config.vo.RmsPerformanceIndicatorVo;
import com.jp.med.rms.modules.config.service.read.RmsPerformanceIndicatorReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class RmsPerformanceIndicatorReadServiceImpl extends ServiceImpl<RmsPerformanceIndicatorReadMapper, RmsPerformanceIndicatorDto> implements RmsPerformanceIndicatorReadService {

    @Autowired
    private RmsPerformanceIndicatorReadMapper rmsPerformanceIndicatorReadMapper;

    @Override
    public List<RmsPerformanceIndicatorVo> queryList(RmsPerformanceIndicatorDto dto) {
        return rmsPerformanceIndicatorReadMapper.queryList(dto);
    }

    @Override
    public List<RmsPerformanceIndicatorVo> queryPageList(RmsPerformanceIndicatorDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return rmsPerformanceIndicatorReadMapper.queryList(dto);
    }

}
