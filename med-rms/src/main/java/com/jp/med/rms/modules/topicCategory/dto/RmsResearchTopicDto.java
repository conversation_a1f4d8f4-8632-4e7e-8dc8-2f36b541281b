package com.jp.med.rms.modules.topicCategory.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 研究方向表
 * <AUTHOR>
 * @email -
 * @date 2024-09-10 16:25:56
 */
@Data
@TableName("rms_research_topic" )
public class RmsResearchTopicDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 课题类型 */
    @TableField("name")
    private String name;

    /** 实施内容 */
    @TableField("remark")
    private String remark;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 更新人 */
    @TableField("updtr")
    private String updtr;

    /** 更新时间 */
    @TableField("update_time")
    private String updateTime;

    /** 组织id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 逻辑删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

}
