package com.jp.med.rms.modules.projectApplyNotice.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 项目申报通知附件视图对象
 * 
 * <AUTHOR>
 * @email -
 * @date 2025-03-07 12:27:16
 */
@Data
public class RmsProjectApplyNoticeAttachmentVo {

	/** 主键ID */
	private Integer id;

	/** 通知ID */
	private Integer noticeId;

	/** 文件ID */
	private Integer fileId;

	/** 附件路径 */
	private String att;

	/** 附件名称 */
	private String attName;

	/** 创建者 */
	private String createBy;

	/** 创建时间 */
	private String createTime;

	/** 更新者 */
	private String updateBy;

	/** 更新时间 */
	private String updateTime;

	/** 删除标志：0-未删除，1-已删除 */
	private String delFlag;

	/** 组织id */
	private String hospitalId;

}
