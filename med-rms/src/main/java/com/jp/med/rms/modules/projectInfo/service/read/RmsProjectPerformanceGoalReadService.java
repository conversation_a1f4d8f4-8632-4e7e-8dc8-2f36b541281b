package com.jp.med.rms.modules.projectInfo.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.rms.modules.projectInfo.dto.RmsProjectPerformanceGoalDto;
import com.jp.med.rms.modules.projectInfo.vo.RmsProjectPerformanceGoalVo;

import java.util.List;

/**
 * 项目绩效目标
 * <AUTHOR>
 * @email 
 * @date 2024-12-19 10:38:45
 */
public interface RmsProjectPerformanceGoalReadService extends IService<RmsProjectPerformanceGoalDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<RmsProjectPerformanceGoalVo> queryList(RmsProjectPerformanceGoalDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<RmsProjectPerformanceGoalVo> queryPageList(RmsProjectPerformanceGoalDto dto);

    List<RmsProjectPerformanceGoalVo> nullable(RmsProjectPerformanceGoalDto dto);
}

