package com.jp.med.rms.modules.config.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 绩效指标
 * <AUTHOR>
 * @email 
 * @date 2024-12-13 15:54:30
 */
@Data
public class RmsPerformanceIndicatorVo {

	/** ID */
	private Integer id;

	/** 标签 */
	private String label;

	/** 备注 #将以括号形式追加在label后，如：社会效益目标（必填） */
	private String remark;

	/** 数据类型 #1:number 2:array 3:text 4:textarea 5:rich_text */
	private Integer dataType;

	/** 数据最大长度 */
	private Integer maxLength;

	/** 父ID */
	private Integer parentId;

	/** 字典类型 #数据类型为数组时，多选项的字典类型 */
	private String dictType;

	/** 字段名称 */
	private String fieldName;

	/** 单位 */
	private String unit;

	/** 是否显示前缀 */
	private Boolean showPrefix;

	/** 列数 */
	private Short cols;

	/** 是否需要附件 */
	private Boolean requiredAttachments;

	/** 排序号 #数字越小排名越靠前 */
	private Integer sort;

	/** 是否删除 */
	private Boolean deleted;

	/** 创建人 */
	private String creator;

	/** 创建时间 */
	private LocalDateTime createTime;

	/** 更新人 */
	private String updater;

	/** 更新时间 */
	private LocalDateTime updateTime;

	/** 组织ID */
	private String hospitalId;
}
