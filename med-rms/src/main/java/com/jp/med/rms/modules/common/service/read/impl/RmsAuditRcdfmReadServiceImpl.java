package com.jp.med.rms.modules.common.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.rms.modules.common.mapper.read.RmsAuditRcdfmReadMapper;
import com.jp.med.rms.modules.common.dto.RmsAuditRcdfmDto;
import com.jp.med.rms.modules.common.vo.RmsAuditRcdfmVo;
import com.jp.med.rms.modules.common.service.read.RmsAuditRcdfmReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class RmsAuditRcdfmReadServiceImpl extends ServiceImpl<RmsAuditRcdfmReadMapper, RmsAuditRcdfmDto> implements RmsAuditRcdfmReadService {

    @Autowired
    private RmsAuditRcdfmReadMapper rmsAuditRcdfmReadMapper;

    @Override
    public List<RmsAuditRcdfmVo> queryList(RmsAuditRcdfmDto dto) {
        return rmsAuditRcdfmReadMapper.queryList(dto);
    }

    @Override
    public List<RmsAuditRcdfmVo> queryPageList(RmsAuditRcdfmDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return rmsAuditRcdfmReadMapper.queryList(dto);
    }

}
