package com.jp.med.rms.modules.projectInfo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.rms.typeHandler.GenericJsonbTypeHandler;
import lombok.*;
import org.apache.ibatis.type.JdbcType;

import java.time.LocalDateTime;

/**
 * 项目绩效目标
 * <AUTHOR>
 * @email 
 * @date 2024-12-19 10:38:45
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("rms_project_performance_goal" )
public class RmsProjectPerformanceGoalDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Long id;

    /** 项目ID */
    @TableField("project_id")
    private Integer projectId;

    /** 指标ID */
    @TableField("indicator_id")
    private Integer indicatorId;

    /** 指标值 */
    @TableField(value = "indicator_value", jdbcType = JdbcType.OTHER, typeHandler = GenericJsonbTypeHandler.class)
    private JsonNode indicatorValue;

    /** 结题附件 */
    @TableField(value = "conclusion_attachments", jdbcType = JdbcType.OTHER, typeHandler = GenericJsonbTypeHandler.class)
    private JsonNode conclusionAttachments;

    /** 验收附件 */
    @TableField(value = "acceptance_attachments", jdbcType = JdbcType.OTHER, typeHandler = GenericJsonbTypeHandler.class)
    private JsonNode acceptanceAttachments;

    /** 创建人 */
    @TableField("creator")
    private String creator;

    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;

    /** 更新人 */
    @TableField("updater")
    private String updater;

    /** 更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /** 组织ID */
    @TableField("hospital_id")
    private String hospitalId;

}
