package com.jp.med.rms.modules.projectInfo.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 经费科目预算科目对照配置
 * <AUTHOR>
 * @email -
 * @date 2024-09-14 11:40:30
 */
@Data
@TableName("rms_funding_budget_cfg")
public class RmsFundingBudgetCfgEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 概算科目名称 */
	@TableField("funding_name")
	private String fundingName;

	/** 上级id */
	@TableField("parent_id")
	private Integer parentId;

	/** 预算项目id */
	@TableField("budget_proj_id")
	private Integer budgetProjId;

	/** 预算项目编码 */
	@TableField("budget_code")
	private String budgetCode;

	/** 组织id */
	@TableField("hospital_id")
	private String hospitalId;

	/** 操作人 */
	@TableField("updtr")
	private String updtr;

	/** 更新时间 */
	@TableField("update_time")
	private String updateTime;

}
