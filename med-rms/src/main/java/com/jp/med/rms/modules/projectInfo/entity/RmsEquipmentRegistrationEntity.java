package com.jp.med.rms.modules.projectInfo.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 仪器设备登记
 * <AUTHOR>
 * @email -
 * @date 2024-09-14 11:40:34
 */
@Data
@TableName("rms_equipment_registration")
public class RmsEquipmentRegistrationEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 项目id */
	@TableField("project_id")
	private Integer projectId;

	/** 序号 */
	@TableField("seq")
	private Integer seq;

	/** 仪器/设备名称 */
	@TableField("equ_name")
	private String equName;

	/** 仪器/设备型号 */
	@TableField("equ_model")
	private String equModel;

	/** 第一年仪器预计使用例数 */
	@TableField("fy_instr_usage_cnt")
	private Integer fyInstrUsageCnt;

	/** 第二年仪器预计使用例数 */
	@TableField("sec_instr_usage_cnt")
	private Integer secInstrUsageCnt;

	/** 组织id */
	@TableField("hospital_id")
	private String hospitalId;

	/** 操作人 */
	@TableField("updtr")
	private String updtr;

	/** 操作时间 */
	@TableField("update_time")
	private String updateTime;

}
