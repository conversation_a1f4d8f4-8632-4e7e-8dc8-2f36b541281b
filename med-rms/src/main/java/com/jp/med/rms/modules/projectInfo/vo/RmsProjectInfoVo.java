package com.jp.med.rms.modules.projectInfo.vo;

import com.jp.med.rms.modules.projectApplication.entity.RmsProjectInte;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 项目详情信息表
 * <AUTHOR>
 * @email -
 * @date 2024-09-14 11:40:18
 */
@Data
public class RmsProjectInfoVo {

	/** id */
	private Integer id;

	/** 项目id */
	private Integer projectId;

	/** 项目名称 */
	private String projectName;

	/** 项目开始日期 */
	private String projectStartDate;

	/** 项目结束日期 */
	private String projectEndDate;

	/** 创新类型 */
	private String innovationType;

	/** 产学研联合  */
	private String industryAcademiaCollab;

	/** 知识产权状况 */
	private String ipStatus;

	/** 成果水平 */
	private String resultLevel;

	/** 成果形式 */
	private String[] resultFormat;

	/**
	 * 知识产权（发明专利项数、实用型新型专利项数、其他专利项数）
	 */
	private RmsProjectInte intellectualProperty;

	/** 发明专利项数 */
	private Integer inventPatentCnt;

	/** 实用新型专利项数 */
	private Integer utilPatentCnt;

	/** 其他专利项数 */
	private Integer otherPatentNum;

	/** 技术标准制定 */
	private String techStdDev;

	/** 经费预算 */
	private BigDecimal budget;

	/** 项目概述 */
	private String projectDescr;

	/** 组织id */
	private String hospitalId;

	/** 更新人  */
	private String updtr;

	/** 更新时间 */
	private String updateTime;

}
