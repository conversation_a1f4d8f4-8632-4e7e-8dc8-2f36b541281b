package com.jp.med.rms.modules.projectInfo.service.read.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.rms.enums.RmsBaseAuditStatus;
import com.jp.med.rms.enums.RmsProjectChangeType;
import com.jp.med.rms.modules.projectApplication.dto.RmsProjectMainInfoDto;
import com.jp.med.rms.modules.projectApplication.mapper.read.RmsProjectMainInfoReadMapper;
import com.jp.med.rms.modules.projectInfo.dto.RmsProjectChangeDto;
import com.jp.med.rms.modules.projectInfo.mapper.read.RmsProjectChangeReadMapper;
import com.jp.med.rms.modules.projectInfo.service.read.RmsProjectChangeReadService;
import com.jp.med.rms.modules.projectInfo.vo.RmsProjectChangeVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Transactional(readOnly = true)
@Service
public class RmsProjectChangeReadServiceImpl extends ServiceImpl<RmsProjectChangeReadMapper, RmsProjectChangeDto> implements RmsProjectChangeReadService {
    private final RmsProjectMainInfoReadMapper rmsProjectMainInfoReadMapper;

    @Override
    public List<RmsProjectChangeVo> queryList(RmsProjectChangeDto dto) {
        return super.baseMapper.queryList(dto);
    }

    @Override
    public List<RmsProjectChangeVo> queryPageList(RmsProjectChangeDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return super.baseMapper.queryList(dto);
    }

    @Override
    public RmsProjectChangeVo querySingle(RmsProjectChangeDto dto) {
        return super.baseMapper.querySingle(dto);
    }

    @Override
    public Map<String, Boolean> display(RmsProjectChangeDto dto) {
        Long leader = super.baseMapper.selectCount(
                new LambdaQueryWrapper<RmsProjectChangeDto>()
                        .eq(RmsProjectChangeDto::getProjectId, dto.getProjectId())
                        .eq(RmsProjectChangeDto::getChangeType, RmsProjectChangeType.LEADER_CHANGE)
                        .eq(RmsProjectChangeDto::getAuditStatus, RmsBaseAuditStatus.PENDING)
                        .eq(RmsProjectChangeDto::getHospitalId, dto.getHospitalId())
        );
        Long member = super.baseMapper.selectCount(
                new LambdaQueryWrapper<RmsProjectChangeDto>()
                        .eq(RmsProjectChangeDto::getProjectId, dto.getProjectId())
                        .eq(RmsProjectChangeDto::getChangeType, RmsProjectChangeType.MEMBER_CHANGE)
                        .eq(RmsProjectChangeDto::getAuditStatus, RmsBaseAuditStatus.PENDING)
                        .eq(RmsProjectChangeDto::getHospitalId, dto.getHospitalId())
        );
        Long defer = super.baseMapper.selectCount(
                new LambdaQueryWrapper<RmsProjectChangeDto>()
                        .eq(RmsProjectChangeDto::getProjectId, dto.getProjectId())
                        .eq(RmsProjectChangeDto::getChangeType, RmsProjectChangeType.DEFER)
                        .eq(RmsProjectChangeDto::getHospitalId, dto.getHospitalId())
        );
        Long deferrable = rmsProjectMainInfoReadMapper.selectCount(
                new LambdaQueryWrapper<RmsProjectMainInfoDto>()
                        .eq(RmsProjectMainInfoDto::getId, dto.getProjectId())
                        .eq(RmsProjectMainInfoDto::getDeferAllowed, Boolean.TRUE)
                        .eq(RmsProjectMainInfoDto::getHospitalId, dto.getHospitalId())
        );
        return Map.of("leader", leader <= 0, "member", member <= 0, "defer", deferrable > 0 && defer <= 0);
    }

}
