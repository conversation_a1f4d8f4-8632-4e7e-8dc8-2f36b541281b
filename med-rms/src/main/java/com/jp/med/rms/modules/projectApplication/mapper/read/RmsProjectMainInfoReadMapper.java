package com.jp.med.rms.modules.projectApplication.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.rms.modules.projectApplication.dto.RmsProjectMainInfoDto;
import com.jp.med.rms.modules.projectApplication.vo.RmsProjectMainInfoVo;
import com.jp.med.rms.modules.projectInfo.dto.change.RmsProjectLeaderChangeInfo;
import com.jp.med.rms.modules.projectInfo.dto.change.RmsProjectMembersChangeInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目信息
 * <AUTHOR>
 * @email -
 * @date 2024-09-10 16:37:08
 */
@Mapper
public interface RmsProjectMainInfoReadMapper extends BaseMapper<RmsProjectMainInfoDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<RmsProjectMainInfoVo> queryList(RmsProjectMainInfoDto dto);

    RmsProjectMainInfoVo getById(Integer id);

	RmsProjectMainInfoVo queryProjectAllDetails(RmsProjectMainInfoDto dto);

    RmsProjectLeaderChangeInfo queryOriginalLeaderInfo(@Param("id") Integer id);

    /**
     * 查询项目原始成员信息
     * @param id 项目ID
     * @return 项目原始成员信息
     */
    RmsProjectMembersChangeInfo queryOriginalMembersInfo(@Param("id") Integer id);
}
