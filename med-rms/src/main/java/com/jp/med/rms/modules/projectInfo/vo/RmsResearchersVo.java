package com.jp.med.rms.modules.projectInfo.vo;

import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * 项目研究者
 * <AUTHOR>
 * @email -
 * @date 2024-09-14 11:39:58
 */
@Data
public class RmsResearchersVo {

	/** id */
	private Integer id;

	/** 研究人员类型 */
	private String researcherType;

	private Short seq;

	/** 研究人员姓名 */
	private String name;

	/** 研究人员用户名 */
	private String username;

	/** 研究人性别 */
	private String sex;

	/** 出生年月 */
	private String birthday;

	/** 工作单位 */
	private String workUnit;

	/** 身份证号 */
	private String idCard;

	/** 学位 */
	private String academicDegree;

	/** 职称 */
	private String professionalTitle;

	/** 现从事专业 */
	private String currentProfessional;

	/** 课题中的分工 */
	private String division;

	/** 研究时间(月/年) */
	private String studyTime;

	/** 是否档案管理员 */
	private Boolean manageable;

	/** 签名 */
	private String signature;

	/** 项目id */
	private Integer projectId;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 操作人  */
	private String updtr;

	/** 更新时间 */
	private String updateTime;

	/** 组织id */
	private String hospitalId;

	/** 逻辑删除 */
	private Integer isDeleted;

	/**
	 * 是否为内部人员
	 * 内部人员的 <code>username</code> 不为空
	 * @return boolean
	 */
	public Boolean getInternal() {
		return StringUtils.hasText(this.username);
	}
}
