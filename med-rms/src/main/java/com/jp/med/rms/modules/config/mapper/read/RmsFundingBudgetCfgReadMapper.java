package com.jp.med.rms.modules.config.mapper.read;

import com.jp.med.rms.modules.config.dto.RmsFundingBudgetCfgDto;
import com.jp.med.rms.modules.config.vo.RmsFundingBudgetCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 经费科目预算科目对照配置
 * <AUTHOR>
 * @email -
 * @date 2024-09-13 16:45:40
 */
@Mapper
public interface RmsFundingBudgetCfgReadMapper extends BaseMapper<RmsFundingBudgetCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<RmsFundingBudgetCfgVo> queryList(RmsFundingBudgetCfgDto dto);

    RmsFundingBudgetCfgVo getById(Integer id);
}
