package com.jp.med.rms.modules.projectInfo.dto.change;

import com.jp.med.rms.modules.projectInfo.dto.RmsResearchersDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date: 2024/12/30
 * @time: 16:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RmsProjectLeaderChangeInfo extends RmsProjectBaseChangeInfo {
    private String projectLeader;
    private String projectLeaderUsername;
    private RmsResearchersDto researcher;
    private String projectResp;
    private String prSex;
    private String prBirthday;
    private String prAcademic;
    private String prTitle;
    private String prPhone;
    private String prProfession;
}
