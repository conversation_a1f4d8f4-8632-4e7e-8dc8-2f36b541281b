package com.jp.med.rms.modules.topicCategory.service.write.impl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.util.DateUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.rms.modules.topicCategory.mapper.write.RmsResearchTopicWriteMapper;
import com.jp.med.rms.modules.topicCategory.dto.RmsResearchTopicDto;
import com.jp.med.rms.modules.topicCategory.service.write.RmsResearchTopicWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 研究方向表
 * <AUTHOR>
 * @email -
 * @date 2024-09-10 16:25:56
 */
@Service
@Transactional(readOnly = false)
public class RmsResearchTopicWriteServiceImpl extends ServiceImpl<RmsResearchTopicWriteMapper, RmsResearchTopicDto> implements RmsResearchTopicWriteService {


	//  保存课题类型（目前就只有：自然学科、社会学科）
	@Override
	public void saveDto(RmsResearchTopicDto dto) {
		HrmUser hrmUser = dto.getSysUser().getHrmUser();
		String opter = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
		//创建时间
		String currentTime = DateUtil.getCurrentTime(null);
		dto.setHospitalId(dto.getHospitalId());
		dto.setCrter(opter);
		dto.setUpdtr(opter);
		dto.setCreateTime(currentTime);
		dto.setUpdateTime(currentTime);
		dto.setIsDeleted(MedConst.NOT_DELETED);
		dto.setName(dto.getName());
		dto.setRemark(dto.getRemark());
		 baseMapper.insert(dto);
	}

	@Override
	public void updateDtoById(RmsResearchTopicDto dto) {
		HrmUser hrmUser = dto.getSysUser().getHrmUser();
		String opter = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
		//创建时间
		String currentTime = DateUtil.getCurrentTime(null);
		dto.setUpdateTime(currentTime);
		dto.setUpdtr(opter);
		baseMapper.updateById(dto);
	}

	@Override
	public void removeDtoById(RmsResearchTopicDto dto) {
		HrmUser hrmUser = dto.getSysUser().getHrmUser();
		String opter = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
		//创建时间
		String currentTime = DateUtil.getCurrentTime(null);
		dto.setUpdateTime(currentTime);
		dto.setUpdtr(opter);
		dto.setIsDeleted(MedConst.IS_DELETED);
		baseMapper.updateById(dto);
	}
}
