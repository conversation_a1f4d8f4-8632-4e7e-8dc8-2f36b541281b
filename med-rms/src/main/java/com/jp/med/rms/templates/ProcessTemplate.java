package com.jp.med.rms.templates;

import com.jp.med.common.dto.bpm.BpmProcessInstanceCreateReqDTO;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.BpmProcessInstanceFeignApi;
import com.jp.med.common.util.FeignExecuteUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 * 流程模板工具
 * <AUTHOR>
 * @date: 2024/12/31
 * @time: 9:09
 */
@Component
@RequiredArgsConstructor
public class ProcessTemplate {
    private final BpmProcessInstanceFeignApi bpmProcessInstanceFeignApi;

    /**
     * 发起BPM流程
     * @param processDefinitionKey 流程定义标识
     * @param businessKey 业务主键
     * @param userId 用户ID
     * @param processInstanceVariables 流程变量
     * @return 流程实例编码
     */
    public String initiateBpmProcessInstance(String processDefinitionKey, String businessKey, String userId, Map<String, Object> processInstanceVariables) {
        if (StringUtils.isBlank(businessKey)) {
            throw new AppException("业务主键不能为空");
        }
        CommonFeignResult processInstance = FeignExecuteUtil.execute(
                bpmProcessInstanceFeignApi.createProcessInstance(
                        new BpmProcessInstanceCreateReqDTO()
                                .setUserId(userId)
                                .setVariables(processInstanceVariables)
                                .setProcessDefinitionKey(processDefinitionKey)
                                .setBusinessKey(businessKey)
                )
        );
        if (!StringUtils.equals(processInstance.get("code").toString(), "200")) {
            throw new AppException("生成BPM流程异常");
        }
        return processInstance.get("data").toString();
    }

    /**
     * 发起BPM流程
     * @param processDefinitionKey 流程定义标识
     * @param businessKey 业务主键
     * @param userId 用户ID
     * @param processInstanceVariables 流程变量
     * @return 流程实例编码
     */
    public String initiateBpmProcessInstance(String processDefinitionKey, Integer businessKey, String userId, Map<String, Object> processInstanceVariables) {
        if (businessKey == null) {
            throw new AppException("业务主键不能为空");
        }
        return initiateBpmProcessInstance(processDefinitionKey, String.valueOf(businessKey), userId, processInstanceVariables);
    }

    /**
     * 发起BPM流程
     * @param processDefinitionKey 流程定义标识
     * @param businessKey 业务主键
     * @param userId 用户ID
     * @return 流程实例编码
     */
    public String initiateBpmProcessInstance(String processDefinitionKey, Integer businessKey, String userId) {
        if (businessKey == null) {
            throw new AppException("业务主键不能为空");
        }
        return initiateBpmProcessInstance(processDefinitionKey, String.valueOf(businessKey), userId, null);
    }

    /**
     * 发起BPM流程
     * @param processDefinitionKey 流程定义标识
     * @param businessKey 业务主键
     * @param userId 用户ID
     * @return 流程实例编码
     */
    public String initiateBpmProcessInstance(String processDefinitionKey, String businessKey, String userId) {
        return initiateBpmProcessInstance(processDefinitionKey, businessKey, userId, null);
    }
}
