package com.jp.med.rms.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 科研系统勾选框
 * <AUTHOR>
 * @email -
 * @date 2024-09-11 11:45:42
 */
@Data
public class RmsCheckBoxSelectionVo {

	/** id */
	private Integer id;

	/** 辅助项名称 */
	private String selectedLabel;


	/** 辅助项值 */
	private String selectedValue;

	/** 辅助项类型 */
	private String dictType;

	/** 备注 */
	private String remark;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 组织id */
	private String hospitalId;

	/** 逻辑删除 */
	private Integer isDeleted;


	/** 排序 */
	private Integer seq;

}
