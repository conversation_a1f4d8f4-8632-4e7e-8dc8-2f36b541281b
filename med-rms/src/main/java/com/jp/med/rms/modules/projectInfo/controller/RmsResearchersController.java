package com.jp.med.rms.modules.projectInfo.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.rms.modules.projectInfo.dto.RmsResearchersDto;
import com.jp.med.rms.modules.projectInfo.service.read.RmsResearchersReadService;
import com.jp.med.rms.modules.projectInfo.service.write.RmsResearchersWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 项目研究者
 * <AUTHOR>
 * @email -
 * @date 2024-09-14 11:39:58
 */
@Api(value = "项目研究者", tags = "项目研究者")
@RestController
@RequestMapping("rmsResearchers")
public class RmsResearchersController {

    @Autowired
    private RmsResearchersReadService rmsResearchersReadService;

    @Autowired
    private RmsResearchersWriteService rmsResearchersWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询项目研究者")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody RmsResearchersDto dto){
        return CommonResult.paging(rmsResearchersReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询项目研究者")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody RmsResearchersDto dto){
        return CommonResult.success(rmsResearchersReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增项目研究者")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody RmsResearchersDto dto){
        rmsResearchersWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改项目研究者")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody RmsResearchersDto dto){
        rmsResearchersWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除项目研究者")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody RmsResearchersDto dto){
        rmsResearchersWriteService.removeById(dto);
        return CommonResult.success();
    }

}
