package com.jp.med.rms.modules.projectApplication.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.rms.modules.projectApplication.dto.RmsProjectMainInfoDto;
import com.jp.med.rms.modules.projectApplication.vo.RmsProjectMainInfoVo;
import com.jp.med.rms.modules.projectInfo.dto.change.RmsProjectLeaderChangeInfo;
import com.jp.med.rms.modules.projectInfo.dto.change.RmsProjectMembersChangeInfo;

import java.util.List;

/**
 * 项目信息
 * <AUTHOR>
 * @email -
 * @date 2024-09-10 16:37:08
 */
public interface RmsProjectMainInfoReadService extends IService<RmsProjectMainInfoDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<RmsProjectMainInfoVo> queryList(RmsProjectMainInfoDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<RmsProjectMainInfoVo> queryPageList(RmsProjectMainInfoDto dto);

	RmsProjectMainInfoVo queryProjectDetails(RmsProjectMainInfoDto dto);

    /**
     * 生成申报书
     * @param dto
     * @return 申报书url
     */
    String generateDeclaration(RmsProjectMainInfoDto dto);

    RmsProjectLeaderChangeInfo originalLeader(RmsProjectMainInfoDto dto);

    RmsProjectMembersChangeInfo originalMembers(RmsProjectMainInfoDto dto);
}

