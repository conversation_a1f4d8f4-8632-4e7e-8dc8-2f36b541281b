package com.jp.med.rms.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.rms.modules.config.mapper.read.RmsIntendedUnitSpecialReadMapper;
import com.jp.med.rms.modules.config.dto.RmsIntendedUnitSpecialDto;
import com.jp.med.rms.modules.config.vo.RmsIntendedUnitSpecialVo;
import com.jp.med.rms.modules.config.service.read.RmsIntendedUnitSpecialReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class RmsIntendedUnitSpecialReadServiceImpl extends ServiceImpl<RmsIntendedUnitSpecialReadMapper, RmsIntendedUnitSpecialDto> implements RmsIntendedUnitSpecialReadService {

    @Autowired
    private RmsIntendedUnitSpecialReadMapper rmsIntendedUnitSpecialReadMapper;

    @Override
    public List<RmsIntendedUnitSpecialVo> queryList(RmsIntendedUnitSpecialDto dto) {
        return rmsIntendedUnitSpecialReadMapper.queryList(dto);
    }

    @Override
    public List<RmsIntendedUnitSpecialVo> queryPageList(RmsIntendedUnitSpecialDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return rmsIntendedUnitSpecialReadMapper.queryList(dto);
    }

}
