package com.jp.med.rms.modules.topicCategory.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.rms.modules.topicCategory.dto.RmsResearchTopicDto;

/**
 * 研究方向表
 * <AUTHOR>
 * @email -
 * @date 2024-09-10 16:25:56
 */
public interface RmsResearchTopicWriteService extends IService<RmsResearchTopicDto> {
	void saveDto(RmsResearchTopicDto dto);

	void updateDtoById(RmsResearchTopicDto dto);

	void removeDtoById(RmsResearchTopicDto dto);
}

