package com.jp.med.rms.modules.projectInfo.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.rms.modules.projectInfo.mapper.write.RmsCooperativeUnitsWriteMapper;
import com.jp.med.rms.modules.projectInfo.dto.RmsCooperativeUnitsDto;
import com.jp.med.rms.modules.projectInfo.service.write.RmsCooperativeUnitsWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合作单位
 * <AUTHOR>
 * @email -
 * @date 2024-09-23 15:00:14
 */
@Service
@Transactional(readOnly = false)
public class RmsCooperativeUnitsWriteServiceImpl extends ServiceImpl<RmsCooperativeUnitsWriteMapper, RmsCooperativeUnitsDto> implements RmsCooperativeUnitsWriteService {
}
