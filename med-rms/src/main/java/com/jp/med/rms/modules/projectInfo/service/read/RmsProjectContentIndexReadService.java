package com.jp.med.rms.modules.projectInfo.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.rms.modules.projectInfo.dto.RmsProjectContentIndexDto;
import com.jp.med.rms.modules.projectInfo.vo.RmsProjectContentIndexVo;

import java.util.List;

/**
 * 项目研究内容与研究指标
 * <AUTHOR>
 * @email -
 * @date 2024-09-23 11:42:57
 */
public interface RmsProjectContentIndexReadService extends IService<RmsProjectContentIndexDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<RmsProjectContentIndexVo> queryList(RmsProjectContentIndexDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<RmsProjectContentIndexVo> queryPageList(RmsProjectContentIndexDto dto);

    RmsProjectContentIndexVo info(Integer id);
}

