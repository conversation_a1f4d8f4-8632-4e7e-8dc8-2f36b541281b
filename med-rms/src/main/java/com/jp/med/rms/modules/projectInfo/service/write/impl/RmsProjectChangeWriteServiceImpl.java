package com.jp.med.rms.modules.projectInfo.service.write.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.DateUtil;
import com.jp.med.rms.enums.RmsBaseAuditStatus;
import com.jp.med.rms.enums.RmsProcessModel;
import com.jp.med.rms.enums.RmsProjectChangeType;
import com.jp.med.rms.modules.projectApplication.mapper.read.RmsProjectMainInfoReadMapper;
import com.jp.med.rms.modules.projectInfo.dto.RmsProjectChangeDto;
import com.jp.med.rms.modules.projectInfo.dto.change.RmsProjectDeferChangeInfo;
import com.jp.med.rms.modules.projectInfo.dto.change.RmsProjectLeaderChangeInfo;
import com.jp.med.rms.modules.projectInfo.dto.change.RmsProjectMembersChangeInfo;
import com.jp.med.rms.modules.projectInfo.mapper.read.RmsProjectChangeReadMapper;
import com.jp.med.rms.modules.projectInfo.mapper.write.RmsProjectChangeWriteMapper;
import com.jp.med.rms.modules.projectInfo.service.write.RmsProjectChangeWriteService;
import com.jp.med.rms.templates.ProcessTemplate;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 项目变更
 * <AUTHOR>
 * @email 
 * @date 2024-12-30 09:41:49
 */
@RequiredArgsConstructor
@Service
@Transactional(readOnly = false)
public class RmsProjectChangeWriteServiceImpl extends ServiceImpl<RmsProjectChangeWriteMapper, RmsProjectChangeDto> implements RmsProjectChangeWriteService {
    private final ObjectMapper objectMapper;

    private final RmsProjectMainInfoReadMapper rmsProjectMainInfoReadMapper;

    private final RmsProjectChangeReadMapper rmsProjectChangeReadMapper;

    private final ProcessTemplate processTemplate;

    private static final String DEFAULT_ERROR_MSG = "操作失败";

    @Override
    public void changeLeader(RmsProjectLeaderChangeInfo dto) {
        if (rmsProjectChangeReadMapper.selectCount(
                new LambdaQueryWrapper<RmsProjectChangeDto>()
                        .eq(RmsProjectChangeDto::getProjectId, dto.getProjectId())
                        .eq(RmsProjectChangeDto::getChangeType, RmsProjectChangeType.LEADER_CHANGE)
                        .eq(RmsProjectChangeDto::getAuditStatus, RmsBaseAuditStatus.PENDING)
                        .eq(RmsProjectChangeDto::getHospitalId, dto.getHospitalId())
        ) > 0) {
            throw new AppException("当前项目还有未完成的负责人变更流程");
        }
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String curUsr = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
        RmsProjectLeaderChangeInfo original = rmsProjectMainInfoReadMapper.queryOriginalLeaderInfo(dto.getProjectId());
        this.changeProject(
                RmsProjectChangeDto.builder()
                        .projectId(dto.getProjectId())
                        .changeType(RmsProjectChangeType.LEADER_CHANGE)
                        .origin(objectMapper.valueToTree(original))
                        .target(objectMapper.valueToTree(dto))
                        .reason(dto.getReason())
                        .membersSignAttachments(dto.getMembersSignAttachments())
                        .creator(curUsr)
                        .createTime(LocalDateTime.now())
                        .hospitalId(dto.getHospitalId())
                        .build()
        );
    }

    @Override
    public void changeMembers(RmsProjectMembersChangeInfo dto) {
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String curUsr = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
        RmsProjectMembersChangeInfo original = rmsProjectMainInfoReadMapper.queryOriginalMembersInfo(dto.getProjectId());
        dto.getResearchers().forEach(researcher -> {
            researcher.setProjectId(dto.getProjectId());
            researcher.setUpdtr(curUsr);
            researcher.setUpdateTime(DateUtil.getCurrentTime(null));
            researcher.setHospitalId(dto.getHospitalId());
        });
        this.changeProject(
                RmsProjectChangeDto.builder()
                        .projectId(dto.getProjectId())
                        .changeType(RmsProjectChangeType.MEMBER_CHANGE)
                        .origin(objectMapper.valueToTree(original))
                        .target(objectMapper.valueToTree(dto))
                        .reason(dto.getReason())
                        .membersSignAttachments(dto.getMembersSignAttachments())
                        .creator(curUsr)
                        .createTime(LocalDateTime.now())
                        .hospitalId(dto.getHospitalId())
                        .build()
        );
    }

    @Override
    public void defer(RmsProjectDeferChangeInfo dto) {
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String curUsr = StringUtils.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
        String projectEndDate = rmsProjectMainInfoReadMapper.getById(dto.getProjectId()).getProjectEndDate();
        Map<String, Object> origin = new HashMap<>();
        origin.put("projectEndDate", projectEndDate);
        Map<String, Object> target = new HashMap<>();
        BeanUtil.beanToMap(dto, target, false, false);
        target.put("projectEndDate", LocalDate.parse(projectEndDate, DateTimeFormatter.ISO_LOCAL_DATE).plusMonths(dto.getDeferMonths()));
        this.changeProject(
                RmsProjectChangeDto.builder()
                        .projectId(dto.getProjectId())
                        .changeType(RmsProjectChangeType.DEFER)
                        .origin(objectMapper.valueToTree(origin))
                        .target(objectMapper.valueToTree(target))
                        .reason(dto.getReason())
                        .membersSignAttachments(dto.getMembersSignAttachments())
                        .creator(curUsr)
                        .createTime(LocalDateTime.now())
                        .hospitalId(dto.getHospitalId())
                        .build()
        );
    }

    private void changeProject(RmsProjectChangeDto dto) {
        if (super.baseMapper.insert(dto) <= 0) {
            throw new AppException(DEFAULT_ERROR_MSG);
        }
        Map<String, Object> processInstanceVariables = new HashMap<>();
        processInstanceVariables.put("projectId", dto.getProjectId());
        processInstanceVariables.put("changeType", dto.getChangeType().getCode());
        String processInstanceCode = processTemplate.initiateBpmProcessInstance(RmsProcessModel.PROJECT_CHANGE_DECLARE.getProcessId(), dto.getId(), dto.getCreator(), processInstanceVariables);
        if (super.baseMapper.updateById(
                RmsProjectChangeDto.builder()
                        .id(dto.getId())
                        .auditStatus(RmsBaseAuditStatus.PENDING)
                        .processInstanceCode(processInstanceCode)
                        .build()
        ) <= 0) {
            throw new AppException(DEFAULT_ERROR_MSG);
        }
    }
}
