package com.jp.med.rms.modules.projectInfo.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.rms.modules.projectInfo.dto.RmsProjectFundingIncomeDto;
import com.jp.med.rms.modules.projectInfo.vo.RmsProjectFundingIncomeVo;

import java.util.List;

/**
 * 项目经费来源
 * <AUTHOR>
 * @email -
 * @date 2024-09-14 11:40:24
 */
public interface RmsProjectFundingIncomeReadService extends IService<RmsProjectFundingIncomeDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<RmsProjectFundingIncomeVo> queryList(RmsProjectFundingIncomeDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<RmsProjectFundingIncomeVo> queryPageList(RmsProjectFundingIncomeDto dto);
}

