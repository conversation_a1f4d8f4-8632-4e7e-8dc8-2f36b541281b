<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.app.modules.user.mapper.write.AppUserWriteMapper">


    <!-- 修改历史数据 -->
    <update id="updateHistory">
        UPDATE app_user
        SET active_flag = '0'
        WHERE username = #{username,jdbcType=VARCHAR}
    </update>

</mapper>
