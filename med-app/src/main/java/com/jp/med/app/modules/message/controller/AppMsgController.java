package com.jp.med.app.modules.message.controller;

import com.jp.med.common.dto.app.AppMsgDto;
import com.jp.med.common.entity.common.CommonFeignResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.app.modules.message.service.read.AppMsgReadService;
import com.jp.med.app.modules.message.service.write.AppMsgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * APP消息
 * <AUTHOR>
 * @email -
 * @date 2023-12-19 17:13:58
 */
@Api(value = "APP消息", tags = "APP消息")
@RestController
@RequestMapping("appMsg")
public class AppMsgController {

    @Autowired
    private AppMsgReadService appMsgReadService;

    @Autowired
    private AppMsgWriteService appMsgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询APP消息")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AppMsgDto dto){
        return CommonResult.paging(appMsgReadService.queryList(dto));
    }

    @ApiOperation("查询用户消息")
    @PostMapping("/userMessage")
    public CommonResult<?> userMessage(@RequestBody AppMsgDto dto){
        return CommonResult.success(appMsgReadService.queryUserMessage(dto));
    }

    @ApiOperation("查询消息数量")
    @PostMapping("/queryMessageNum")
    public CommonResult<?> queryMessageNum(@RequestBody AppMsgDto dto){
        return CommonResult.success(appMsgReadService.queryMessageNum(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增APP消息")
    @PostMapping("/save")
    public CommonFeignResult save(@RequestBody AppMsgDto dto){
        return CommonFeignResult.build().put(CommonFeignResult.DATA_KEY, appMsgWriteService.addMessage(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增验证码")
    @PostMapping("/addVerifyCode")
    public CommonResult<?> addVerifyCode(@RequestBody AppMsgDto dto){
        appMsgWriteService.addVerifyCode(dto);
        return CommonResult.success();
    }

    @ApiOperation("新增APP消息")
    @PostMapping("/modify")
    public CommonFeignResult modify(@RequestBody AppMsgDto dto){
        appMsgWriteService.updateById(dto);
        return CommonFeignResult.build();
    }

    /**
     * 修改
     */
    @ApiOperation("修改APP消息")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AppMsgDto dto){
        appMsgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改APP消息读取状态")
    @PostMapping("/updateReadState")
    public CommonResult<?> updateReadState(@RequestBody AppMsgDto dto){
        appMsgWriteService.updateReadState(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除APP消息")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AppMsgDto dto){
        appMsgWriteService.removeById(dto);
        return CommonResult.success();
    }

}
