package com.jp.med.app.modules.message.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * APP用户消息设置
 * <AUTHOR>
 * @email -
 * @date 2023-12-19 17:13:58
 */
@Data
@TableName("app_user_msg_set" )
public class AppUserMsgSetDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 消息类型编码 */
    @TableField("msg_type_code")
    private String msgTypeCode;

    /** 用户名 */
    @TableField("username")
    private String username;

    /** 是否显示 */
    @TableField("show")
    private String show;

    /** 是否置顶 */
    @TableField("top")
    private String top;

    /** 修改时间 */
    @TableField("modi_time")
    private String modiTime;

}
