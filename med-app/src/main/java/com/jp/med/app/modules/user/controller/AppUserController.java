package com.jp.med.app.modules.user.controller;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.ComputerUtil;
import com.jp.med.common.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.app.modules.user.dto.AppUserDto;
import com.jp.med.app.modules.user.service.read.AppUserReadService;
import com.jp.med.app.modules.user.service.write.AppUserWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletRequest;


/**
 * APP用户
 * <AUTHOR>
 * @email -
 * @date 2023-12-20 14:04:01
 */
@Api(value = "APP用户", tags = "APP用户")
@RestController
@RequestMapping("appUser")
public class AppUserController {

    @Autowired
    private AppUserReadService appUserReadService;

    @Autowired
    private AppUserWriteService appUserWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询APP用户")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AppUserDto dto){
        return CommonResult.paging(appUserReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增APP用户")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AppUserDto dto, HttpServletRequest request){
        // 更改历史记录状态
        appUserWriteService.updateHistory(dto);
        dto.setLoginTime(DateUtil.getCurrentTime(null));
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        dto.setIp(ComputerUtil.getClientIp(request));
        appUserWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改APP用户")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AppUserDto dto){
        appUserWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除APP用户")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AppUserDto dto){
        appUserWriteService.removeById(dto);
        return CommonResult.success();
    }

}
