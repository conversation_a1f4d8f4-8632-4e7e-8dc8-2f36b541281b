package com.jp.med.app.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.app.modules.config.mapper.read.AppMsgTypeCfgReadMapper;
import com.jp.med.app.modules.config.dto.AppMsgTypeCfgDto;
import com.jp.med.app.modules.config.vo.AppMsgTypeCfgVo;
import com.jp.med.app.modules.config.service.read.AppMsgTypeCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AppMsgTypeCfgReadServiceImpl extends ServiceImpl<AppMsgTypeCfgReadMapper, AppMsgTypeCfgDto> implements AppMsgTypeCfgReadService {

    @Autowired
    private AppMsgTypeCfgReadMapper appMsgTypeCfgReadMapper;

    @Override
    public List<AppMsgTypeCfgVo> queryList(AppMsgTypeCfgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return appMsgTypeCfgReadMapper.queryList(dto);
    }

}
