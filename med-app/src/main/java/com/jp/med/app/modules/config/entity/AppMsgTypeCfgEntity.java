package com.jp.med.app.modules.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 消息类型配置
 * <AUTHOR>
 * @email -
 * @date 2023-12-19 15:21:06
 */
@Data
@TableName("app_msg_type_cfg")
public class AppMsgTypeCfgEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 消息类型编码 */
	@TableField("msg_type_code")
	private String msgTypeCode;

	/** 消息类型名称 */
	@TableField("msg_type_name")
	private String msgTypeName;

	/** 描述 */
	@TableField("dscr")
	private String dscr;

	/** 图标 */
	@TableField("icon")
	private String icon;

	/** 创建人 */
	@TableField("crter")
	private String crter;

	/** 创建时间 */
	@TableField("create_time")
	private String createTime;

}
