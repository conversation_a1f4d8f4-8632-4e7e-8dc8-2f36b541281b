package com.jp.med.app.modules.message.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.jp.med.app.modules.config.dto.AppMsgTypeCfgDto;
import com.jp.med.app.modules.config.service.read.AppMsgTypeCfgReadService;
import com.jp.med.app.modules.config.vo.AppMsgTypeCfgVo;
import com.jp.med.app.modules.message.dto.AppUserMsgSetDto;
import com.jp.med.app.modules.message.mapper.read.AppUserMsgSetReadMapper;
import com.jp.med.app.modules.message.vo.AppUserMsgSetVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.app.AppMsgDto;
import com.jp.med.common.util.OSSUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.app.modules.message.mapper.read.AppMsgReadMapper;
import com.jp.med.app.modules.message.vo.AppMsgVo;
import com.jp.med.app.modules.message.service.read.AppMsgReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class AppMsgReadServiceImpl extends ServiceImpl<AppMsgReadMapper, AppMsgDto> implements AppMsgReadService {

    @Autowired
    private AppMsgReadMapper appMsgReadMapper;

    @Autowired
    private AppMsgTypeCfgReadService appMsgTypeCfgReadService;

    @Autowired
    private AppUserMsgSetReadMapper appUserMsgSetReadMapper;

    @Override
    public List<AppMsgVo> queryList(AppMsgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        // 用户消息
        dto.setRecer(dto.getSysUser().getHrmUser().getEmpCode());
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<AppMsgVo> appMsgVos = appMsgReadMapper.queryList(dto);
        appMsgVos.sort(Comparator.comparing(AppMsgVo::getCreateTime).reversed());
        return appMsgVos;
    }

    @Override
    public List<AppMsgTypeCfgVo> queryUserMessage(AppMsgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        // 系统配置
        List<AppMsgTypeCfgVo> appMsgTypeCfgVos = appMsgTypeCfgReadService.queryList(new AppMsgTypeCfgDto());
        if (CollectionUtil.isNotEmpty(appMsgTypeCfgVos)) {
            String username = dto.getSysUser().getHrmUser().getEmpCode();
            // 用户设置
            AppUserMsgSetDto appUserMsgSetDto = new AppUserMsgSetDto();
            appUserMsgSetDto.setUsername(username);
            List<AppUserMsgSetVo> appUserMsgSetVos = appUserMsgSetReadMapper.queryList(appUserMsgSetDto);
            // 用户消息
            dto.setRecer(username);
            List<AppMsgVo> appMsgVos = appMsgReadMapper.queryList(dto);
            appMsgTypeCfgVos.forEach(config -> {
                config.setLastMessageCreateTime("");
                if (StringUtils.isNotEmpty(config.getIcon())) {
                    config.setIconUrl(OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_APP, config.getIcon()));
                }
                // 配置
                if (CollectionUtil.isNotEmpty(appUserMsgSetVos)) {
                    AppUserMsgSetVo userSet = appUserMsgSetVos.stream().filter(v -> config.getMsgTypeCode().equals(v.getMsgTypeCode())).findFirst().get();
                    config.setUserSet(userSet);
                }
                // 消息
                if (CollectionUtil.isNotEmpty(appMsgVos)) {
                    AtomicInteger msgNum = new AtomicInteger();
                    List<AppMsgVo> userMessages = appMsgVos.stream().filter(v -> {
                        if(MedConst.TYPE_0.equals(v.getRead()) &&
                                config.getMsgTypeCode().equals(v.getMsgTypeCode())){
                           msgNum.addAndGet(1);
                        }
                        return config.getMsgTypeCode().equals(v.getMsgTypeCode());
                    }).collect(Collectors.toList());
                    if (userMessages.size() > 0) {
                        userMessages.sort(Comparator.comparing(AppMsgVo::getCreateTime).reversed());
                        config.setNotRedMessageNum(msgNum.intValue());
                        config.setLastMessageCreateTime(userMessages.get(0).getCreateTime());
                        config.setUserMessages(userMessages);
                        config.setNotReadTip(replacePlaceholder(config.getNotReadTip(),new HashMap<>(){
                            {
                                put(1, String.valueOf(config.getNotRedMessageNum()));
                            }
                        }));
                    }
                }
            });
            // 根据时间排序
            appMsgTypeCfgVos.sort(Comparator.comparing(AppMsgTypeCfgVo::getLastMessageCreateTime).reversed());
        }
        return appMsgTypeCfgVos;
    }

    private String replacePlaceholder(String input, Map<Integer, String> params){
        Pattern pattern = Pattern.compile("\\$(\\d+)");
        Matcher matcher = pattern.matcher(input);
        StringBuilder sb = new StringBuilder();
        while (matcher.find()) {
            int paramIndex = Integer.parseInt(matcher.group(1));
            String paramValue = params.get(paramIndex);
            matcher.appendReplacement(sb, paramValue);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    @Override
    public Integer queryMessageNum(AppMsgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        // 用户消息
        dto.setRecer(dto.getSysUser().getHrmUser().getEmpCode());
        dto.setRead(MedConst.TYPE_0);
        List<AppMsgVo> appMsgVos = appMsgReadMapper.queryList(dto);
        return appMsgVos.size();
    }
}
