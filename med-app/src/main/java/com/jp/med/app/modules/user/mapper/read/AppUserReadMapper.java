package com.jp.med.app.modules.user.mapper.read;

import com.jp.med.app.modules.user.dto.AppUserDto;
import com.jp.med.app.modules.user.vo.AppUserVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * APP用户
 * <AUTHOR>
 * @email -
 * @date 2023-12-20 14:04:01
 */
@Mapper
public interface AppUserReadMapper extends BaseMapper<AppUserDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AppUserVo> queryList(AppUserDto dto);
}
