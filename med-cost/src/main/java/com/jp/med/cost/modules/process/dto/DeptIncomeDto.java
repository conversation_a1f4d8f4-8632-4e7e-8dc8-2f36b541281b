package com.jp.med.cost.modules.process.dto;

import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.lang.reflect.Field;
import java.math.BigDecimal;

/**
 * <p>
 * 医院住院收入表
 * </p>
 *
 * <AUTHOR>
 * @since 2021/5/12
 */
@Getter
@Setter
@ToString
public class DeptIncomeDto extends CommonQueryDto {
    /**
     * 出院科室
     * VARCHAR(100)
     * isNullable true
     */
    private String dscg_dept;
    /**
     * 开单科室
     * VARCHAR(100)
     * isNullable true
     */
    private String bilg_dept;
    /**
     * 门诊医师
     * VARCHAR(100)
     * isNullable true
     */
    private String otp_dr;
    /**
     * 住院医师
     * VARCHAR(100)
     * isNullable true
     */
    private String ipdr;
    /**
     * 介绍人
     * VARCHAR(100)
     * isNullable true
     */
    private String itro_psn;
    /**
     * 姓名
     * VARCHAR(100)
     * isNullable true
     */
    private String name;
    /**
     * 性别
     * VARCHAR(2)
     * isNullable true
     */
    private String gend;
    /**
     * 住院号
     * VARCHAR(100)
     * isNullable true
     */
    private String ipt_no;
    /**
     * 入院日期
     * VARCHAR(100)
     * isNullable true
     */
    private String adm_date;
    /**
     * 出院日期
     * VARCHAR(100)
     * isNullable true
     */
    private String dscg_date;
    /**
     * 开单人
     * VARCHAR(100)
     * isNullable true
     */
    private String drawer;
    /**
     * A超费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double a_scan_fee;
    /**
     * B超费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double bmus_fee;
    /**
     * CT费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double ct_fee;
    /**
     * DR费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double dr_fee;
    /**
     * X线费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double x_ray_fee;
    /**
     * 病理费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double palg_fee;
    /**
     * 材料费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double matlfee;
    /**
     * 彩超费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double cdu_fee;
    /**
     * 肠镜费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double colosc_fee;
    /**
     * 床位费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double bedfee;
    /**
     * 磁共振费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double mri_fee;
    /**
     * 放射费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double rdat_fee;
    /**
     * 核医学费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double nuclear_med_fee;
    /**
     * 护理费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double nursfee;
    /**
     * 化验费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double asy_fee;
    /**
     * 会诊费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double consu_fee;
    /**
     * 监测费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double mnit_fee;
    /**
     * 监护费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double cutd_fee;
    /**
     * 检查费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double examfee;
    /**
     * 检验费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double inspect_fee;
    /**
     * 降温费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double lower_temp_fee;
    /**
     * 接生费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double midffee;
    /**
     * 菌检费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double bac_exa_fee;
    /**
     * 麻醉费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double anst_fee;
    /**
     * 脑地型图费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double brain_atlas_fee;
    /**
     * 抢救费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double resc_fee;
    /**
     * 尸体料理
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double corp_handle;
    /**
     * 手术费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double oper_fee;
    /**
     * 输血费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double bld_fee;
    /**
     * 输氧费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double oxy_fee;
    /**
     * 碎石费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double gravel_fee;
    /**
     * 胃镜费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double gas_fee;
    /**
     * 西药费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double west_fee;
    /**
     * 心电图费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double elec_fee;
    /**
     * 诊察费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double diag_fee;
    /**
     * 治疗费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double treat_fee;
    /**
     * 中草药
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double tcmherb;
    /**
     * 中成药
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double tcmpat;
    /**
     * 注射费
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double injfee;
    /**
     * 费用合计
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double fee_sum;

    /**
     * 获取所有表头
     *
     * @return 获取表头
     */
    public static String[] getHeaders() {
        String[] headers = {
                "出院科室",
                "开单科室",
                "门诊医师",
                "住院医师",
                "介绍人",
                "姓名",
                "性别",
                "住院号",
                "入院日期",
                "出院日期",
                "开单人",
                "A超",
                "B超",
                "CT费",
                "DR",
                "X线",
                "病理",
                "材料费",
                "彩超费",
                "肠镜费",
                "床位费",
                "磁共振",
                "放射费",
                "核医学",
                "护理费",
                "化验费",
                "会诊费",
                "监测费",
                "监护费",
                "检查费",
                "检验费",
                "降温费",
                "接生费",
                "菌检费",
                "麻醉费",
                "脑地型图",
                "抢救费",
                "尸体料理",
                "手术费",
                "输血费",
                "输氧费",
                "碎石费",
                "胃镜费",
                "西药费",
                "心电图",
                "诊察费",
                "治疗费",
                "中草药",
                "中成药",
                "注射费",
                "合计"
        };
        return headers;
    }

    /**
     * 获取所有字段名
     *
     * @return
     */
    public static String[] getFieldNames() {
        Field[] fields = DeptIncomeDto.class.getDeclaredFields();
        String[] names = new String[fields.length];
        for (int i = 0; i < fields.length; i++) {
            names[i] = fields[i].getName();
        }
        return names;
    }
}
