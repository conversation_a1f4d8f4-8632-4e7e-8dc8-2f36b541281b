package com.jp.med.cost.modules.analysis.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class DiseaseCostCalculationVo implements Serializable {
    private static final long serialVersionUID = -5129195046737576067L;

    /**
     * 主诊编码
     */
    private String ake325;

    /**
     * 主要诊断名称
     */
    private String bke740;

    /**
     * 主要手术编码
     */
    private String ake394;

    /**
     * 主要手术名称
     */
    private String bke7400;

    /**
     * 主治医师
     */
    private String ake384;

    /**
     * 病例数
     */
    private Integer num;

    /**
     * 病种收入
     */
    private BigDecimal akc264;

    /**
     * 病种成本
     */
    private BigDecimal kkh262;

    /**
     * 成本率
     */
    private BigDecimal radio;

    private List<BigDecimal> trend;
}
