package com.jp.med.cost.modules.analysis.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@ToString
public class GroupCostVo {
    /**
     * DRG编码
     */
    private String bke716;

    /**
     * DRG名称
     */
    private String bke717;

    /**
     * 分组数量
     */
    private Integer groupCount;

    /**
     * DRG组收入
     */
    private BigDecimal groupIncome;

    /**
     * DRG组收入
     */
    private BigDecimal groupCost;

    /**
     * 病种数量
     */
    private Integer diseaseCount;

    /**
     * 记录数
     */
    private  Integer recordCount;

    /**
     * 成本率
     */
    private BigDecimal costRatio;

    /**
     * 成本期号
     */
    private String issue;

    /**
     * 成本值
     */
    private BigDecimal costValue;

    /**
     * 成本趋势
     */
    List<GroupCostVo> costTrend;

    /**
     * 成本趋势
     */
    List<BigDecimal> costTrendValues;

    private BigDecimal costAll;

    private BigDecimal costThis;

    private BigDecimal costOther;

    private BigDecimal costThisRatio;

    private BigDecimal costOtherRatio;

    private BigDecimal constrolCost;

    private BigDecimal unconstrolCost;
}
