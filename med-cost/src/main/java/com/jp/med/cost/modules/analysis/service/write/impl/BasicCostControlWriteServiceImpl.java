package com.jp.med.cost.modules.analysis.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.exception.AppException;
import com.jp.med.cost.modules.analysis.dto.BasicCostControlDto;
import com.jp.med.cost.modules.analysis.mapper.read.BasicCostControlReadMapper;
import com.jp.med.cost.modules.analysis.mapper.write.BasicCostControlWriteMapper;
import com.jp.med.cost.modules.analysis.service.write.BasicCostControlWriteService;
import com.jp.med.cost.modules.analysis.vo.CostControllabilityVo;
import com.jp.med.cost.modules.analysis.vo.DeptMonCostTagtVo;
import com.jp.med.cost.modules.analysis.vo.HospMonCostTagtVo;
import com.jp.med.cost.modules.calculation.dto.CostCalculationDto;
import com.jp.med.cost.modules.calculation.mapper.write.CostCalculationWriteMapper;
import com.jp.med.cost.modules.common.constant.CostIConst;
import com.jp.med.cost.modules.common.mapper.CostBaseMapper;
import com.jp.med.cost.modules.common.util.DateUtil;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
@Transactional
public class BasicCostControlWriteServiceImpl extends ServiceImpl<BasicCostControlWriteMapper, BasicCostControlDto> implements BasicCostControlWriteService {

    @Resource
    private BasicCostControlWriteMapper basicCostControlWriteMapper;

    @Resource
    private BasicCostControlReadMapper basicCostControlReadMapper;

    @Resource
    private CostBaseMapper costBaseMapper;

//    @Resource(name = "ta404dsSqlSessionTemplate")
//    private SqlSessionTemplate ta404dsSqlSessionTemplate;
//
//    @Resource
//    private BatchServiceImpl batchService;

    @Override
    public void setHospitalCostGoal(BasicCostControlDto dto) {
        // 原始数据期号
        String issueStartOriginal = dto.getIssueStart();
        String issueEndOriginal = dto.getIssueEnd();
        int months = 1;
        // 查询不可控成本总额
        BigDecimal uncontrolledCost = basicCostControlReadMapper.queryUncontrolledCost(dto);
        if (ValidateUtil.isEmpty(uncontrolledCost)) {
            throw new AppException("未找到相应数据");
        }
        // 如果没有查询到当前期号的成本数据，则使用上一个月的成本数据
        String issue = dto.getIssueStart();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Calendar calendar = Calendar.getInstance();
        try {
            switch (dto.getDateType()) {
                case "1":
                    months = 1;
                    break;
                case "2":
                    months = 3;
                    break;
                case "3":
                    months = 12;
                    break;
            }
            Date date;
            if (uncontrolledCost == null) {
                calendar.setTime(sdf.parse(issue));
                calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - months);
                date = calendar.getTime();
                issue = sdf.format(date);
                dto.setIssueStart(issue);
                issue = dto.getIssueEnd();
                calendar.setTime(sdf.parse(issue));
                calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - months);
                date = calendar.getTime();
                issue = sdf.format(date);
                dto.setIssueEnd(issue);
            }
            uncontrolledCost = basicCostControlReadMapper.queryUncontrolledCost(dto);
            // 求可控成本目标总额
            BigDecimal controlledCostGoal = dto.getGoal().subtract(uncontrolledCost);
            // 查询全院成本目标记录
            BasicCostControlDto qo = new BasicCostControlDto();
            qo.setUsername(dto.getUsername());
            qo.setMedins_no(dto.getMedins_no());
            qo.setIssueStart(issueStartOriginal);
            qo.setIssueEnd(issueEndOriginal);
            qo.setDateType(dto.getDateType());
            HospMonCostTagtVo kkr1Vo = basicCostControlReadMapper.queryHospitalCostGoalHis(dto);
            if (ValidateUtil.isEmpty(kkr1Vo)) {
                kkr1Vo = new HospMonCostTagtVo();
                // 设置目标指定次数
                kkr1Vo.setTagt_cnt(1);
            }
            kkr1Vo.setYear(dto.getIssueStart().substring(0, 4));
            kkr1Vo.setMedins_no(dto.getMedins_no());
            kkr1Vo.setData_ym(dto.getIssueStart());
            kkr1Vo.setTime_type(dto.getDateType());
            // 设置历史不可控成本
            kkr1Vo.setHis_dyna_cost(uncontrolledCost);
            // 设置可控成本目标i
            kkr1Vo.setImmut_cost_tagt(controlledCostGoal);
            // 设置成本目标
            kkr1Vo.setCost_tagt(dto.getGoal());
            kkr1Vo.setVali_flag(CostIConst.AAE100_1);
            kkr1Vo.setOprt_time(DateUtil.getCurrTimestamp());
            kkr1Vo.setTagt_formul_time(DateUtil.getCurrTimestamp());
            //se_hosp_cost_tagt_id
            kkr1Vo.setHosp_cost_tagt_id(costBaseMapper.executeForSequence("se_hosp_cost_tagt_id"));
            kkr1Vo.setFormul_psn(dto.getUsername());
            // 求科室成本数据占比
            List<CostControllabilityVo> list = basicCostControlReadMapper.queryDeptCostControllability(dto);
            List<DeptMonCostTagtVo> listGoal = new ArrayList<>();
            // 计算科室成本总目标
            for (CostControllabilityVo item : list) {
                item.setControllableGoal(controlledCostGoal.multiply(item.getControllableCostRatio()));
                item.setCostGoal(item.getControllableGoal().add(item.getUncontrollableCost()));
                for (int i = 0; i < months; i++) {

                    calendar.setTime(sdf.parse(issueStartOriginal));
                    calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + i);
                    date = calendar.getTime();
                    issue = sdf.format(date);

                    DeptMonCostTagtVo kkr2Vo = new DeptMonCostTagtVo();
                    BeanUtils.copyProperties(item, kkr2Vo);
                    kkr2Vo.setData_ym(issue);
                    // 计算不可控成本
                    kkr2Vo.setUn_contrly_cost(item.getUncontrollableCost().divide(new BigDecimal(months)));
                    kkr2Vo.setCost_tagt(item.getCostGoal().divide(new BigDecimal(months)));
                    kkr2Vo.setVali_flag(CostIConst.AAE100_1);
                    kkr2Vo.setHosp_cost_tagt_id(kkr1Vo.getHosp_cost_tagt_id());
                    kkr2Vo.setDept_cost_tagt_id(costBaseMapper.executeForSequence("AAY020"));
                    kkr2Vo.setOprt_time(DateUtil.getCurrTimestamp());
                    kkr2Vo.setMedins_no(dto.getMedins_no());
                    listGoal.add(kkr2Vo);
                }
            }
            basicCostControlWriteMapper.removeRepeatHospitalCostGoal(qo);
            basicCostControlWriteMapper.saveHospitalCostGoal(kkr1Vo);
            if (ValidateUtil.isNotEmpty(listGoal)) {
                basicCostControlWriteMapper.removeRepeatDeptCostGoal(qo);
                basicCostControlWriteMapper.saveDeptCostGoal(listGoal);
            }
        } catch (ParseException e) {
            e.printStackTrace();
            throw new AppException("制定成本目标失败");
        }
    }
}
