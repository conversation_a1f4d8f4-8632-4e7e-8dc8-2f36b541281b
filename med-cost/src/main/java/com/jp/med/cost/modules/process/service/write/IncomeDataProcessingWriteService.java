package com.jp.med.cost.modules.process.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cost.modules.process.dto.DeptIncomeDto;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

/**
 * <p>
 * 医院收入数据处理类
 * </p>
 *
 * <AUTHOR>
 * @since 2021/5/11
 */
public interface IncomeDataProcessingWriteService extends IService<DeptIncomeDto> {
    Workbook processingData(Map<String, MultipartFile> files) throws IOException, Exception;
}
