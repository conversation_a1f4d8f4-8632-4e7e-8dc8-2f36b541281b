package com.jp.med.cost.modules.analysis.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.cost.modules.analysis.dto.BasicCostControlDto;
import com.jp.med.cost.modules.analysis.dto.ServiceProjectCostDto;
import com.jp.med.cost.modules.analysis.mapper.read.BasicCostControlReadMapper;

import com.jp.med.cost.modules.analysis.mapper.read.ClinicalCostCollectionReadMapper;
import com.jp.med.cost.modules.analysis.service.read.ClinicalCostCollectionReadService;
import com.jp.med.cost.modules.analysis.vo.ClinicalCostCollectionVo;
import com.jp.med.cost.modules.analysis.vo.DeptCostDetailsVo;
import com.jp.med.cost.modules.analysis.vo.DeptCostServiceVo;
import com.jp.med.cost.modules.analysis.vo.DeptCostTypeVo;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Transactional(readOnly = true)
public class ClinicalCostCollectionReadServiceImpl extends ServiceImpl<ClinicalCostCollectionReadMapper, ServiceProjectCostDto> implements ClinicalCostCollectionReadService {
    @Resource
    private ClinicalCostCollectionReadMapper clinicalCostCollectionReadMapper;

    @Override
    public ClinicalCostCollectionVo queryCostCollection(ServiceProjectCostDto dto) {
        ClinicalCostCollectionVo clinicalCostCollectionVo = clinicalCostCollectionReadMapper.queryCostSumary(dto);
        if (clinicalCostCollectionVo == null) {
            clinicalCostCollectionVo = new ClinicalCostCollectionVo();
        }

        clinicalCostCollectionVo.setDeptCostVos(clinicalCostCollectionReadMapper.queryDeptCost(dto));

        return clinicalCostCollectionVo;
    }

    @Override
    public DeptCostServiceVo queryDeptServiceCost(ServiceProjectCostDto dto) {
        if (ValidateUtil.isEmpty(dto.getDept_codg())) {
            return null;
        }

        List<DeptCostServiceVo> deptCostServiceVos = clinicalCostCollectionReadMapper.queryDeptServiceCost(dto);
        DeptCostServiceVo deptCostServiceVo = new DeptCostServiceVo();
        if (ValidateUtil.isNotEmpty(deptCostServiceVos)) {
            DeptCostServiceVo deptCostServiceVo1 = deptCostServiceVos.get(0);
            BeanUtils.copyProperties(deptCostServiceVo1, deptCostServiceVo);
        }

        DeptCostServiceVo serviceVo = clinicalCostCollectionReadMapper.queryDeptCostRank(dto);
        if (ValidateUtil.isNotEmpty(deptCostServiceVo.getAkf001()) && deptCostServiceVo.getAkf001().equals(serviceVo.getAkf001())) {
            deptCostServiceVo.setNum(serviceVo.getNum());
            deptCostServiceVo.setRank(serviceVo.getRank());
            deptCostServiceVo.setRaiseRank(serviceVo.getRaiseRank());
        }

        deptCostServiceVos = null;
        serviceVo = null;

        DeptCostServiceVo deptCostRadio = clinicalCostCollectionReadMapper.queryDeptCostRadio(dto);
        if (ValidateUtil.isNotEmpty(deptCostServiceVo.getAkf001()) && deptCostServiceVo.getAkf001().equals(deptCostRadio.getAkf001())) {
            deptCostServiceVo.setRadio(deptCostRadio.getRadio());
            deptCostServiceVo.setRadioNum(deptCostRadio.getRadioNum());
            deptCostServiceVo.setRadioRank(deptCostRadio.getRadioRank());
        }

        deptCostRadio = null;

        return deptCostServiceVo;

    }

    @Override
    public List<DeptCostServiceVo> queryDeptCostType(ServiceProjectCostDto dto) {
        if (ValidateUtil.isEmpty(dto.getDept_codg())) {
            return null;
        }
        return clinicalCostCollectionReadMapper.queryDeptCostType(dto);
    }

    @Override
    public List<DeptCostTypeVo> querySelectDeptType(ServiceProjectCostDto dto) {
        if (ValidateUtil.isEmpty(dto.getDept_codg())) {
            return null;
        }

        List<DeptCostTypeVo> deptCostTypeVos = clinicalCostCollectionReadMapper.querySelectDeptType(dto);
        List<DeptCostTypeVo> result = new ArrayList<>();
        result.add(new DeptCostTypeVo("不限", ""));
        if (ValidateUtil.isNotEmpty(deptCostTypeVos)) {
            result.addAll(deptCostTypeVos);
        }

        return result;
    }

    @Override
    public List<DeptCostDetailsVo> queryDeptCostDetail(ServiceProjectCostDto dto) {
        if (ValidateUtil.isEmpty(dto.getDept_codg())) {
            return null;
        }

        return clinicalCostCollectionReadMapper.queryDeptCostDetail(dto);
    }

    @Override
    public DeptCostServiceVo queryDeptRank(ServiceProjectCostDto dto) {
        if (ValidateUtil.isNotEmpty(dto.getDept_codg()) && ValidateUtil.isNotEmpty(dto.getKkh194())) {
            return clinicalCostCollectionReadMapper.queryDeptRank(dto);
        }

        return new DeptCostServiceVo();
    }
}
