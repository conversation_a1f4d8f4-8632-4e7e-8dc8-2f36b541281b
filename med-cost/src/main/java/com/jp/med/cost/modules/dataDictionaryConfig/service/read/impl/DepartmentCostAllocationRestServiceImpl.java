package com.jp.med.cost.modules.dataDictionaryConfig.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.cost.modules.common.constant.CostIConst;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import com.jp.med.cost.modules.common.vo.DeptCostCfgVo;
import com.jp.med.cost.modules.common.vo.DeptTypeAprtParaCfgVo;
import com.jp.med.cost.modules.common.vo.MedinsCostItemCfgVo;
import com.jp.med.cost.modules.dataDictionaryConfig.dto.DepartCostDto;
import com.jp.med.cost.modules.dataDictionaryConfig.dto.DepartTreeDto;
import com.jp.med.cost.modules.dataDictionaryConfig.mapper.read.DepartmentCostAllocationReadMapper;
import com.jp.med.cost.modules.dataDictionaryConfig.service.read.DepartmentCostAllocationReadService;
import com.jp.med.cost.modules.dataDictionaryConfig.vo.DepartTreeListVo;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional(readOnly = true)
public class DepartmentCostAllocationRestServiceImpl extends ServiceImpl<DepartmentCostAllocationReadMapper, DepartCostDto> implements DepartmentCostAllocationReadService {

    @Resource
    private DepartmentCostAllocationReadMapper departmentCostAllocationReadMapper;

    @Override
    public List<DepartTreeListVo> listDictionaryConfig(DepartTreeDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        dto.setMedins_no(dto.getHospitalId());
        //查询临床科室数据
        dto.setDept_obj_type(CostIConst.DEPT_OBJ_TYPE_CLINICAL);
        List<DepartTreeListVo> root = null;
        List<DepartTreeListVo> list = departmentCostAllocationReadMapper.selectAllTree(dto);
//        if (ValidateUtil.isNotEmpty(list)) {
//            root = list.stream().filter(item -> {
//                //设置树展示key、lable
//                item.setKey(item.getDept_codg());
//                item.setLabel(item.getDept_name());
//                return ValidateUtil.isEmpty(item.getPrnt_medins_dept_codg());
//            }).collect(Collectors.toList());
//            if (ValidateUtil.isNotEmpty(root)) {
//                root.stream().forEach(r -> {
//                    list.stream().forEach(l -> {
//                        if (ValidateUtil.isNotEmpty(l.getPrnt_medins_dept_codg()) && l.getPrnt_medins_dept_codg().equals(r.getDept_codg())) {
//                            List<DepartTreeListVo> children = r.getChildren();
//                            //设置树展示key、lable
//                            l.setKey(l.getDept_codg());
//                            l.setLabel(l.getDept_name());
//                            if (ValidateUtil.isEmpty(children)) {
//                                children = new ArrayList<>();
//                            }
//                            children.add(l);
//                            r.setChildren(children);
//                        }
//                    });
//                });
//            }
//        }
        return list;
    }

    @Override
    public List<MedinsCostItemCfgVo> queryCostNames(DepartCostDto dto) {
        /**
         * 全院通用或者顶级科室
         */
        String bkeb60 = null;
        List<DepartTreeListVo> topDeptment = departmentCostAllocationReadMapper.IsTopDeptment(dto);
        if (ValidateUtil.isNotEmpty(topDeptment)) {
            DepartTreeListVo departmentTreeResDto = topDeptment.get(0);
            if ("0".equals(departmentTreeResDto.getDept_type()) || "1".equals(departmentTreeResDto.getIs_hosp_toplv_dept())) {
                dto.setTopDeptment(Boolean.TRUE);
            }
            dto.setDept_type(departmentTreeResDto.getDept_type());
            bkeb60 = departmentTreeResDto.getDept_type();
        }
        List<MedinsCostItemCfgVo> kkp8Vos = departmentCostAllocationReadMapper.queryCostNames(dto);

        if (ValidateUtil.isNotEmpty(kkp8Vos)) {
            List<MedinsCostItemCfgVo> lowerDeptCostName = departmentCostAllocationReadMapper.queryLowerDeptCostName(dto);
            if (ValidateUtil.isNotEmpty(lowerDeptCostName) && ValidateUtil.isNotEmpty(bkeb60)) {
                String finalBkeb6 = bkeb60;
                List<MedinsCostItemCfgVo> collect = lowerDeptCostName.stream().filter(item -> {
                    String[] kkh209 = item.getDept_type_comm_cost().split(",");
                    return moreThanDeptLevel(Arrays.asList(kkh209), finalBkeb6);
                }).collect(Collectors.toList());
                if (ValidateUtil.isNotEmpty(collect)) {
                    List<String> names = kkp8Vos.stream().map(item -> item.getTitle()).collect(Collectors.toList());
                    collect.stream().forEach(c -> {
                        if (!names.contains(c.getTitle())) {
                            kkp8Vos.add(c);
                        }
                    });
                }
            }
        }
        return kkp8Vos;
    }

    @Override
    public List<DepartTreeListVo> queryDeptById(DepartCostDto dto) {
        return departmentCostAllocationReadMapper.queryDeptById(dto);
    }

    @Override
    public List<MedinsCostItemCfgVo> queryCostByCostCode(DepartCostDto dto) {
        return departmentCostAllocationReadMapper.queryCostByCostCode(dto);
    }

    @Override
    public List<DeptCostCfgVo> queryDeptCodt(DepartCostDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<DeptTypeAprtParaCfgVo> kkp2Vos = departmentCostAllocationReadMapper.queryALLCostParams(dto);
        PageHelper.startPage(dto);
        List<DeptCostCfgVo> kkp1Vos = departmentCostAllocationReadMapper.queryDeptCodt(dto);
        if (ValidateUtil.isNotEmpty(kkp1Vos)) {
            kkp1Vos.stream().forEach(item -> {
                String paramater = item.getCost_aprt_code();
                List<String> list = new ArrayList<>();
                if (paramater != null) {
                    long l = Long.parseLong(paramater);
                    if (l == 0L) {
                        list.add("未设置");
                    } else {
                        List<String> codes = setCostParams(paramater);
                        item.setCodes(codes);
                        if (ValidateUtil.isNotEmpty(kkp2Vos) && ValidateUtil.isNotEmpty(codes)) {
                            kkp2Vos.stream().forEach(kkp2Vo -> {
                                codes.stream().forEach(code -> {
                                    if (kkp2Vo.getAna_cof_codg().equals(code)) {
                                        list.add(kkp2Vo.getAprt_cof_name());
                                    }
                                });

                            });
                        }
                    }
                } else {
                    list.add("未设置");
                }
                item.setAprt_dept_psncnt_list(list);
            });
        }
        return kkp1Vos;
    }

    @Override
    @Cacheable(cacheNames = "CostParams", key = "#dto.hospitalId")
    public List<DeptTypeAprtParaCfgVo> queryCostParams(DepartCostDto dto) {
        return departmentCostAllocationReadMapper.queryCostParams(dto);
    }

    @Override
    public List<DeptTypeAprtParaCfgVo> queryALLCostParams(DepartCostDto dto) {
        return departmentCostAllocationReadMapper.queryALLCostParams(dto);
    }

    @Override
    public List<DeptCostCfgVo> queryDeptCostList(DepartCostDto dto) {
        return departmentCostAllocationReadMapper.queryDeptCodt(dto);
    }

    @Override
    public String queryLastBke930(DepartCostDto dto) {
        return departmentCostAllocationReadMapper.queryLastBke930(dto);
    }

    @Override
    public List<DeptCostCfgVo> queryDeptCosts(DepartCostDto dto) {
        return departmentCostAllocationReadMapper.queryDeptCosts(dto);
    }

    private List<String> setCostParams(String str1) {
        String str2 = "1";

        int index = str1.indexOf(str2);
        List<String> result = new ArrayList<>();

        while (index != -1) {
            index = str2.length() + index;
            String s = "";
            for (int i = 1; i < index; i++) {
                s += "0";
            }
            s += str2;
            for (int i = 0; i < (str1.length() - index); i++) {
                s += "0";
            }
            result.add(s);
            index = str1.indexOf(str2, index);
        }

        return result;
    }

    private boolean moreThanDeptLevel(List<String> level, String bkeb60) {
        if (ValidateUtil.isEmpty(level) || ValidateUtil.isEmpty(bkeb60)) {
            return false;
        }

        for (String item : level) {
            if (Integer.parseInt(item) <= Integer.parseInt(bkeb60)) {
                return true;
            }
        }

        return false;
    }
}
