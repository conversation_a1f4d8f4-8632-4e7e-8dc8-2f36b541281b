package com.jp.med.cost.modules.extract.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cost.modules.common.vo.MedinsCostItemCfgVo;
import com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto;
import com.jp.med.cost.modules.extract.vo.DeptAsetAsgnRcdVo;
import com.jp.med.cost.modules.extract.vo.OthCostDetlVo;
import org.springframework.web.multipart.MultipartFile;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * 基础数据传输写入服务
 * </p>
 *
 * <AUTHOR>
 * @since 2021/7/22
 */
public interface BasicDataTransmissionWriteService extends IService<BasicDataTransmissionDto> {

    /**
     * 设置药品耗材的溢价比例
     *
     * @param dto
     * @return
     */
    int setPremiumRatio(BasicDataTransmissionDto dto);

    /**
     * 从结算数据里抽取医疗保障金
     *
     * @param dto
     * @return
     */
    int extractMedicalFund(BasicDataTransmissionDto dto);

    /**
     * 从结算数据里抽取医疗保障金
     *
     * @param issue
     * @return
     */
    int extractMedicalFund(String issue);


    /**
     * 插入人员薪资信息
     *
     * @param list   表格数据
     * @param fields 表格字段
     * @param kkp8s  人员薪资成本项
     * @param dto
     * @return
     */
    int savePersonnelExpenses(List<HashMap> list, String[] fields, List<MedinsCostItemCfgVo> kkp8s, BasicDataTransmissionDto dto);

    /**
     * 抽取上一期人员薪资数据作为本期人员薪资数据
     *
     * @param dto
     * @return
     */
    int usePersonnelExpensesPreData(BasicDataTransmissionDto dto);

    /**
     * 保存其他成本
     *
     * @param kkp8s 人员薪资成本项
     * @param dto
     * @return
     */
    void saveOtherCost(List<OthCostDetlVo> kkq5VoList, List<MedinsCostItemCfgVo> kkp8s, BasicDataTransmissionDto dto);

    /**
     * 保存资产成本数据
     *
     * @param kkq4List
     * @param dto
     * @return
     */
    int saveAssets(List<DeptAsetAsgnRcdVo> kkq4List, BasicDataTransmissionDto dto) throws ParseException;

    /**
     * 计算设备折旧
     *
     * @param issue
     * @throws ParseException
     */
    void saveDepreciationExpense(String issue) throws ParseException;

    /**
     * 更新其他成本表里的成本代码
     *
     * @param kkq5Vo
     */
    void updateOtherCostName(List<OthCostDetlVo> kkq5Vo);

    /**
     * 插入导入文件记录
     *
     * @param file
     */
    void insertImportCostRecord(BasicDataTransmissionDto dto, MultipartFile file);

    /**
     * 查询收费明细
     *
     * @param dto
     * @return
     */
    int extractChargeDetailsByIssue(BasicDataTransmissionDto dto);

    /**
     * 查询耗材领用记录
     *
     * @param dto
     * @return
     */
    int extractConsumablesUseByIssue(BasicDataTransmissionDto dto);

    /**
     * 查询药品领用记录
     *
     * @param dto
     * @return
     */
    int extractDrugUseByIssue(BasicDataTransmissionDto dto);

    /**
     * 查询收费明细
     *
     * @param issue
     * @return
     */
    int extractChargeDetailsByIssue(String issue);

    /**
     * 查询耗材领用记录
     *
     * @param issue
     * @return
     */
    int extractConsumablesUseByIssue(String issue);

    /**
     * 查询药品领用记录
     *
     * @param issue
     * @return
     */
    int extractDrugUseByIssue(String issue);
}
