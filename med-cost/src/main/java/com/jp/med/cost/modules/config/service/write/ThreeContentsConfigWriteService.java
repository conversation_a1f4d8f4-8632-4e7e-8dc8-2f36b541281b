package com.jp.med.cost.modules.config.service.write;


import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cost.modules.config.dto.ThreeContentsConfigDto;

public interface ThreeContentsConfigWriteService extends IService<ThreeContentsConfigDto> {
    void fnChangeThreeContentsConfig(ThreeContentsConfigDto dto);

    void fnAddThreeContentsConfig(ThreeContentsConfigDto dto);
}
