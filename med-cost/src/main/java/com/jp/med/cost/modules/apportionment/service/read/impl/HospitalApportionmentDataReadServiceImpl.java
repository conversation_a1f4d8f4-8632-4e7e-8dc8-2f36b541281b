package com.jp.med.cost.modules.apportionment.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.cost.modules.apportionment.dto.ApportionmentDataDto;
import com.jp.med.cost.modules.apportionment.mapper.read.HospitalApportionmentDataReadMapper;
import com.jp.med.cost.modules.apportionment.service.read.HospitalApportionmentDataReadService;
import com.jp.med.cost.modules.apportionment.vo.ApportionmentDataVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
@Transactional
public class HospitalApportionmentDataReadServiceImpl extends ServiceImpl<HospitalApportionmentDataReadMapper, ApportionmentDataDto> implements HospitalApportionmentDataReadService {
    @Resource
    private HospitalApportionmentDataReadMapper hospitalApportionmentDataReadMapper;

    @Override
    public List<ApportionmentDataVo> queryDeptInitData(ApportionmentDataDto dto) {
        PageHelper.startPage(dto);
        return hospitalApportionmentDataReadMapper.queryDeptInitData(dto);
    }
}
