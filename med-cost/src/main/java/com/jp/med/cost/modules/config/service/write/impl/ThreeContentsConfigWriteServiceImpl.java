package com.jp.med.cost.modules.config.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.cost.modules.config.dto.ThreeContentsConfigDto;
import com.jp.med.cost.modules.config.mapper.write.ThreeContentsConfigWriteMapper;
import com.jp.med.cost.modules.config.service.write.ThreeContentsConfigWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service("threeContentsConfigWriteService")
@Transactional
public class ThreeContentsConfigWriteServiceImpl extends ServiceImpl<ThreeContentsConfigWriteMapper, ThreeContentsConfigDto> implements ThreeContentsConfigWriteService {
    @Resource
    private ThreeContentsConfigWriteMapper threeContentsConfigWriteMapper;

    @Override
    public void fnChangeThreeContentsConfig(ThreeContentsConfigDto dto) {
        threeContentsConfigWriteMapper.fnChangeThreeContentsConfig(dto);
    }

    @Override
    public void fnAddThreeContentsConfig(ThreeContentsConfigDto dto) {
        threeContentsConfigWriteMapper.fnAddThreeContentsConfig(dto);

    }
}
