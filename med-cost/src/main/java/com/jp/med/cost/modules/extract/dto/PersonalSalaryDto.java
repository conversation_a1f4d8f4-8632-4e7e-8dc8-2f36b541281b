package com.jp.med.cost.modules.extract.dto;

import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.cost.modules.extract.vo.PsnSalRcdVo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 人员薪资DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2021/4/13
 */
@Getter
@Setter
public class PersonalSalaryDto extends CommonQueryDto implements Serializable {
    private static final long serialVersionUID = 1909845608869611316L;

    /**
     * 人员薪资数据对象
     */
    private PsnSalRcdVo psnSalRcdVo;

    /**
     * 人员薪资ID
     */
    private String psn_sal_id;

    /**
     * 科室编码
     */
    private String dept_codg;

    /**
     * 人员编码
     */
    private String psn_no;

    /**
     * 姓名
     */
    private String name;

    /**
     * 薪资期号
     */
    private String sal_ym;
}
