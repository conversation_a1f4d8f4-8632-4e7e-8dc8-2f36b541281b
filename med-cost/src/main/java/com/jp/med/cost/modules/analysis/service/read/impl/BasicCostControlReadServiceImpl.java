package com.jp.med.cost.modules.analysis.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.cost.modules.analysis.dto.BasicCostControlDto;
import com.jp.med.cost.modules.analysis.mapper.read.BasicCostControlReadMapper;
import com.jp.med.cost.modules.analysis.service.read.BasicCostControlReadService;
import com.jp.med.cost.modules.analysis.vo.*;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import com.jp.med.cost.modules.dataDictionaryConfig.vo.DepartTreeListVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional(readOnly = true)
public class BasicCostControlReadServiceImpl extends ServiceImpl<BasicCostControlReadMapper, BasicCostControlDto> implements BasicCostControlReadService {
    @Resource
    private BasicCostControlReadMapper basicCostControlReadMapper;

    @Override
    public List<HospitalCostDataVo> queryHospitalCostTrend(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryHospitalCostTrend(dto);
    }

    @Override
    public HospitalCostDataVo queryHospitalCostSum(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryHospitalCostSum(dto);
    }

    @Override
    public List<HospitalCostTypeVo> queryHospitalCostType(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryHospitalCostType(dto);
    }

    @Override
    public List<HospitalCostTypeVo> queryHospitalCostTypeTrend(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryHospitalCostTypeTrend(dto);
    }

    @Override
    public HospitalCostTypeVo queryHospitalCostTypePie(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryHospitalCostTypePie(dto);
    }

    @Override
    public List<HospitalCostTypeVo> queryHospitalCostTypeOtherTrend(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryHospitalCostTypeOtherTrend(dto);
    }

    @Override
    public List<DeptTypeCostVo> queryDeptCostType(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryDeptCostType(dto);
    }

    @Override
    public List<DeptTypeCostVo> queryDeptCostTypeTrend(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryDeptCostTypeTrend(dto);
    }

    @Override
    public List<DepartTreeListVo> selectDeptList(BasicCostControlDto dto) {
        List<DepartTreeListVo> listDepartmentTreeResDtos = basicCostControlReadMapper.selectDeptList(dto);
        List<DepartTreeListVo> root = null;
        if (ValidateUtil.isNotEmpty(listDepartmentTreeResDtos)) {
            root = listDepartmentTreeResDtos.stream().filter(item -> {
                return ValidateUtil.isEmpty(item.getPrnt_medins_dept_codg());
            }).collect(Collectors.toList());

            if (ValidateUtil.isNotEmpty(root)) {
                root.stream().forEach(r -> {
                    listDepartmentTreeResDtos.stream().forEach(l -> {
                        if (ValidateUtil.isNotEmpty(l.getPrnt_medins_dept_codg()) && l.getPrnt_medins_dept_codg().equals(r.getDept_codg())) {
                            List<DepartTreeListVo> children = r.getChildren();
                            if (ValidateUtil.isEmpty(children)) {
                                children = new ArrayList<>();
                            }
                            children.add(getTreeNode(l, listDepartmentTreeResDtos));
                            r.setChildren(children);
                        }
                    });
                });
            }
        }
        return root;
    }

    @Override
    public DeptCostInfoVo queryDeptCostInfo(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryDeptCostInfo(dto);
    }

    @Override
    public List<DeptCostInfoVo> queryDeptCostInfoList(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryDeptCostInfoList(dto);
    }

    @Override
    public List<DeptCostInfoVo> queryDeptCostContrastList(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryDeptCostContrastList(dto);
    }

    private DepartTreeListVo getTreeNode(DepartTreeListVo node, List<DepartTreeListVo> data) {
        data.stream().forEach(item -> {
            if (ValidateUtil.isNotEmpty(item.getPrnt_medins_dept_codg()) && item.getPrnt_medins_dept_codg().equals(node.getDept_codg())) {
                List<DepartTreeListVo> children = node.getChildren();
                if (ValidateUtil.isEmpty(children)) {
                    children = new ArrayList<>();
                }
                children.add(getTreeNode(item, data));
                node.setChildren(children);
            }

        });

        if (!node.getIsSelectCostDict() && ValidateUtil.isEmpty(node.getChildren())) {
            node.setIsSelectCostDict(Boolean.TRUE);
            if ("1".equals(node.getIs_cost_aprt())) {
                node.setJoinCostNum(1);
            } else {
                node.setNotJoinCostNum(1);
            }
        } else if (!node.getIsSelectCostDict() && ValidateUtil.isNotEmpty(node.getChildren())) {
            Integer joinCostNum = node.getJoinCostNum();
            Integer notJoinCostNum = node.getNotJoinCostNum();
            List<DepartTreeListVo> children = node.getChildren();
            Optional<Integer> joinCostNumOption = children.stream().map(r -> r.getJoinCostNum()).reduce((a, b) -> a + b);
            joinCostNum = joinCostNumOption.orElse(0);
            Optional<Integer> notJoinCostNumOption = children.stream().map(r -> r.getNotJoinCostNum()).reduce((a, b) -> a + b);
            notJoinCostNum = notJoinCostNumOption.orElse(0);
            node.setJoinCostNum(joinCostNum);
            node.setNotJoinCostNum(notJoinCostNum);
        }
        return node;
    }

    @Override
    public List<CostGoalHistoryVo> queryCostGoalHistory(BasicCostControlDto dto) {
        // 查询基本的成本目标记录
        List<CostGoalHistoryVo> list = basicCostControlReadMapper.queryCostGoalHistory(dto);
        // 查询对应的成本目标对应时间段的实际成本
        for (CostGoalHistoryVo item : list) {
            BasicCostControlDto qo = new BasicCostControlDto();
            qo.setIssueStart(item.getIssueStart());
            qo.setIssueEnd(item.getIssueEnd());
            qo.setMedins_no(dto.getMedins_no());
            BigDecimal costSum = basicCostControlReadMapper.querySelfCostSum(qo);
            item.setCost(costSum);
        }
        return list;
    }

    @Override
    public DeptInfoVo queryDeptInfo(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryDeptInfo(dto);
    }

    @Override
    public List<DeptInfoVo> queryDeptCostTrend(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryDeptCostTrend(dto);
    }

    @Override
    public List<DeptInfoVo> queryPersonCostRatioTrend(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryPersonCostRatioTrend(dto);
    }

    @Override
    public List<CostDeptTypeVo> queryDeptTypeCost(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryDeptTypeCost(dto);
    }

    @Override
    public BigDecimal queryDeptTypeCostSum(BasicCostControlDto dto) {
        return basicCostControlReadMapper.queryDeptTypeCostSum(dto);
    }
}
