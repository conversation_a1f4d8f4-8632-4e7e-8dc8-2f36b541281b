package com.jp.med.cost.modules.dataDictionaryConfig.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.cost.modules.common.vo.MedinsCostItemCfgVo;
import com.jp.med.cost.modules.dataDictionaryConfig.dto.DictionaryDetailReqDto;
import com.jp.med.cost.modules.dataDictionaryConfig.dto.ListDictionaryReqDto;

import java.util.List;

public interface MedicalInstitutionCostLibraryReadMapper extends BaseMapper<ListDictionaryReqDto> {
    List<MedinsCostItemCfgVo> listDictionaryConfig(ListDictionaryReqDto dto);

    MedinsCostItemCfgVo getById(DictionaryDetailReqDto dto);

    /**
     * 查询成本名称是否已存在
     *
     * @param dto
     * @return
     */
    Integer queryCostNameIsExists(DictionaryDetailReqDto dto);
}
