package com.jp.med.cost.modules.calculation.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.exception.AppException;
import com.jp.med.cost.modules.calculation.dto.CostCalculationDto;
import com.jp.med.cost.modules.calculation.mapper.read.CostCalculationReadMapper;
import com.jp.med.cost.modules.calculation.service.read.CostCalculationReadService;
import com.jp.med.cost.modules.calculation.vo.*;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import com.jp.med.cost.modules.common.vo.ServiceLavelVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@Transactional(readOnly = true)
public class CostCalculationReadServiceImpl extends ServiceImpl<CostCalculationReadMapper, CostCalculationDto> implements CostCalculationReadService {
    @Resource
    private CostCalculationReadMapper costCalculationReadMapper;

    @Override
    public List<DeptMonSelfCostVo> queryDeptSelfCost(CostCalculationDto dto) {
        return costCalculationReadMapper.queryDeptSelfCost(dto);
    }

    @Override
    public List<DeptAllocationParamVo> queryDeptAllocationParam(CostCalculationDto dto) {
        return costCalculationReadMapper.queryDeptAllocationParam(dto);
    }

    @Override
    public List<ServiceUnitMedItemCostDetlVo> queryServiceProjectCost(CostCalculationDto dto) {
        PageHelper.startPage(dto);
        return costCalculationReadMapper.queryServiceProjectCost(dto);
    }


    @Override
    public List<CostVo> queryDeptAllocationCost(CostCalculationDto dto) {
        return costCalculationReadMapper.queryDeptAllocationCost(dto);
    }

    @Override
    public List<CostVo> queryDeptNotHaveAllocationCost(CostCalculationDto dto) {
        return costCalculationReadMapper.queryDeptNotHaveAllocationCost(dto);
    }

//    @Override
//    public List<Kkr3Vo> queryAllServiceProjectData(CostCalculationDto dto) {
//        return costCalculationReadMapper.queryAllServiceProjectData(dto);
//    }

    @Override
    public Integer queryIsExistData(CostCalculationDto dto) {
        return costCalculationReadMapper.queryIsExistData(dto);
    }

    @Override
    public List<HospPatnCostingRcdVo> queryCalcPatientCost(CostCalculationDto dto) {
        return costCalculationReadMapper.queryCalcPatientCost(dto);
    }

    @Override
    public Integer queryExistPatientData(CostCalculationDto dto) {
        if (ValidateUtil.isNotEmpty(dto.getIssue()) && ValidateUtil.isNotEmpty(dto.getDateType())) {
            return costCalculationReadMapper.queryExistPatientData(dto);
        }
        return Integer.valueOf(0);
    }

    @Override
    public Integer queryExistDiseaseGroupData(CostCalculationDto dto) {
        if (ValidateUtil.isNotEmpty(dto.getTableName())) {
            return costCalculationReadMapper.queryExistDiseaseGroupData(dto);
        }
        return Integer.valueOf(0);
    }

    @Override
    public List<DieaseGroupCostVo> queryDieaseGroup(CostCalculationDto dto) {
        return costCalculationReadMapper.queryDieaseGroup(dto);
    }

    @Override
    public String queryAke554(CostCalculationDto dto) {
        String ake554 = costCalculationReadMapper.queryAke554(dto);
        if (ValidateUtil.isEmpty(ake554)) {
            throw new AppException("病案首页类型不能为空!!!");
        }
        return ake554;
    }

    @Override
    public List<CostingServModIncRatVo> queryDieaseData(CostCalculationDto dto) {
        List<CostingServModIncRatVo> kbd1Vos = costCalculationReadMapper.queryDieaseData(dto);
        return kbd1Vos;
    }

    @Override
    public List<Map<String, Object>> queryKkr6Data(CostCalculationDto dto) {
        return costCalculationReadMapper.queryKkr6Data(dto);
    }

    @Override
    public List<HospDiseIcd10CostingRcdVo> queryKkr7Data(CostCalculationDto dto) {
        return costCalculationReadMapper.queryKkr7Data(dto);
    }

    @Override
    public List<ServiceLavelVo> queryServiceLabel(CostCalculationDto dto) {
        return costCalculationReadMapper.queryServiceLabel(dto);
    }

    @Override
    public List<ServiceLavelVo> queryServiceUnit() {
        return costCalculationReadMapper.queryServiceUnit();
    }
}
