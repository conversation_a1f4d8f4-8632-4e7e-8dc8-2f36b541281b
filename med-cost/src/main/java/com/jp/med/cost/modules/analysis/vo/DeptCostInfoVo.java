package com.jp.med.cost.modules.analysis.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 科室成本总额
 */
@Getter
@Setter
@ToString
public class DeptCostInfoVo {
    /**
     * 科室编码
     */
    private String akf001;

    /**
     * 科室名称
     */
    private String bkf001;

    /**
     * 成本类型
     */
    private String costType;

    /**
     * 成本目标
     */
    private BigDecimal costGoal;

    /**
     * 成本目标完成比例
     */
    private BigDecimal costGoalRatio;

    /**
     * 成本类型名称
     */
    private String costTypeName;

    /**
     * 科室自身成本
     */
    private BigDecimal selfCost;

    /**
     * 科室分摊成本
     */
    private BigDecimal shareCost;

    /**
     * 科室成本金额
     */
    private BigDecimal cost;

    /**
     * 成本占比
     */
    private BigDecimal costRatio;

    /**
     * 状态
     */
    private BigDecimal costGoalStatus;

    /**
     * 比较上一期成本
     */
    private BigDecimal costLast;

    /**
     * 人均成本
     */
    private BigDecimal personCost;

    /**
     * 同类科室人均成本
     */
    private BigDecimal personCostAvg;

    /**
     * 科室数目
     */
    private Integer personDeptCount;

    /**
     *同类科室人均成本排名
     */
    private Integer personCostRank;

    /**
     * 科室人数
     */
    private Integer personCount;

    /**
     * 住院科室排名
     */
    private Integer zyksRank;

    /**
     * 科室数量
     */
    private Integer zyksCount;

    /**
     * 住院科室排名
     */
    private Integer zyksRankLast;

    /**
     * 科室数量
     */
    private Integer zyksCountLast;

    /**
     * 同类科室排名
     */
    private Integer tlksRank;

    /**
     * 同类科室数量
     */
    private Integer tlksCount;


    /**
     * 人力成本
     */
    private BigDecimal ryCost;

    /**
     * 卫生材料成本
     */
    private BigDecimal wsclCost;

    /**
     * 药品成本
     */
    private BigDecimal ypCost;

    /**
     * 固定资产成本
     */
    private BigDecimal gdzcCost;

    /**
     * 无形资产成本
     */
    private BigDecimal wxzcCost;

    /**
     * 提取风险金成本
     */
    private BigDecimal fxjCost;

    /**
     * 其他成本
     */
    private BigDecimal qtCost;

    /**
     * 人均成本排行
     */
    private Integer personRank;

    /**
     * 科室成本排名
     */
    private Integer costRank;

    /**
     * 分摊成本占比
     */
    private String shareCostRatio;

    /**
     * 自身成本占比
     */
    private String selfCostRatio;
}

