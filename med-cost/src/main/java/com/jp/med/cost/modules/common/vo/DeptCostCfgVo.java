package com.jp.med.cost.modules.common.vo;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * class DeptCostCfgVo
 * desc   科室成本配置 - dept_cost_cfg
 *
 * <AUTHOR>
 */
public class DeptCostCfgVo implements Serializable {

    private static final long serialVersionUID = 8747815907266037154L;
    /**
     * 医疗机构编号
     * VARCHAR(30)
     * isNullable false
     */
    private String medins_no;
    /**
     * 数据期号
     * INTEGER
     * isNullable true
     */
    private String data_ym;
    /**
     * 科室编码
     * VARCHAR(30)
     * isNullable false
     */
    private String dept_codg;
    /**
     * 科室名称
     * VARCHAR(50)
     * isNullable false
     */
    private String dept_name;
    /**
     * 科室类别
     * VARCHAR(6)
     * isNullable false
     */
    private String dept_type;
    /**
     * 成本编码
     * VARCHAR(30)
     * isNullable false
     */
    private String cost_codg;
    /**
     * 科室成本编码
     * VARCHAR(30)
     * isNullable false
     */
    private String dept_cost_codg;
    /**
     * 成本名称
     * VARCHAR(100)
     * isNullable true
     */
    private String cost_name;
    /**
     * 成本类型
     * VARCHAR(6)
     * isNullable true
     */
    private String cost_type;
    /**
     * 成本形态
     * VARCHAR(6)
     * isNullable true
     */
    private String cost_shape;
    /**
     * 成本可控性
     * VARCHAR(6)
     * isNullable true
     */
    private String cost_contrly;
    /**
     * 成本分摊代码
     * VARCHAR(8)
     * isNullable true
     */
    private String cost_aprt_code;
    /**
     * 操作时间
     * TIMESTAMP
     * isNullable false
     */
    private Timestamp oprt_time;
    /**
     * 有效标志
     * VARCHAR(6)
     * isNullable false
     */
    private String vali_flag;
    /**
     * 科室成本配置ID
     * VARCHAR(30)
     * isNullable false
     */
    private String dept_cost_cfg_id;
    /**
     * 成本分摊配置codes
     */
    private List<String> codes;

    /**
     * 分摊系数-职工人数list
     */
    private List<String> aprt_dept_psncnt_list;

    /**
     * 科室类型共有成本
     * VARCHAR(12)
     * isNullable true
     */
    private String dept_type_comm_cost;

    /**
     * 设置 medins_no 医疗机构编号
     *
     * @param medins_no 医疗机构编号
     */
    public void setMedins_no(String medins_no) {
        this.medins_no = medins_no;
    }

    /**
     * 设置 data_ym 数据期号
     *
     * @param data_ym 数据期号
     */
    public void setData_ym(String data_ym) {
        this.data_ym = data_ym;
    }

    /**
     * 设置 dept_codg 科室编码
     *
     * @param dept_codg 科室编码
     */
    public void setDept_codg(String dept_codg) {
        this.dept_codg = dept_codg;
    }

    /**
     * 设置 dept_name 科室名称
     *
     * @param dept_name 科室名称
     */
    public void setDept_name(String dept_name) {
        this.dept_name = dept_name;
    }

    /**
     * 设置 dept_type 科室类别
     *
     * @param dept_type 科室类别
     */
    public void setDept_type(String dept_type) {
        this.dept_type = dept_type;
    }

    /**
     * 设置 cost_codg 成本编码
     *
     * @param cost_codg 成本编码
     */
    public void setCost_codg(String cost_codg) {
        this.cost_codg = cost_codg;
    }

    /**
     * 设置 dept_cost_codg 科室成本编码
     *
     * @param dept_cost_codg 科室成本编码
     */
    public void setDept_cost_codg(String dept_cost_codg) {
        this.dept_cost_codg = dept_cost_codg;
    }

    /**
     * 设置 cost_name 成本名称
     *
     * @param cost_name 成本名称
     */
    public void setCost_name(String cost_name) {
        this.cost_name = cost_name;
    }

    /**
     * 设置 cost_type 成本类型
     *
     * @param cost_type 成本类型
     */
    public void setCost_type(String cost_type) {
        this.cost_type = cost_type;
    }

    /**
     * 设置 cost_shape 成本形态
     *
     * @param cost_shape 成本形态
     */
    public void setCost_shape(String cost_shape) {
        this.cost_shape = cost_shape;
    }

    /**
     * 设置 cost_contrly 成本可控性
     *
     * @param cost_contrly 成本可控性
     */
    public void setCost_contrly(String cost_contrly) {
        this.cost_contrly = cost_contrly;
    }

    /**
     * 设置 cost_aprt_code 成本分摊代码
     *
     * @param cost_aprt_code 成本分摊代码
     */
    public void setCost_aprt_code(String cost_aprt_code) {
        this.cost_aprt_code = cost_aprt_code;
    }

    /**
     * 设置 oprt_time 操作时间
     *
     * @param oprt_time 操作时间
     */
    public void setOprt_time(Timestamp oprt_time) {
        this.oprt_time = oprt_time;
    }

    /**
     * 设置 vali_flag 有效标志
     *
     * @param vali_flag 有效标志
     */
    public void setVali_flag(String vali_flag) {
        this.vali_flag = vali_flag;
    }

    /**
     * 设置 dept_cost_cfg_id 科室成本配置ID
     *
     * @param dept_cost_cfg_id 科室成本配置ID
     */
    public void setDept_cost_cfg_id(String dept_cost_cfg_id) {
        this.dept_cost_cfg_id = dept_cost_cfg_id;
    }

    /**
     * 获取 medins_no 医疗机构编号
     *
     * @return medins_no
     */
    public String getMedins_no() {
        return this.medins_no;
    }

    /**
     * 获取 data_ym 数据期号
     *
     * @return data_ym
     */
    public String getData_ym() {
        return this.data_ym;
    }

    /**
     * 获取 dept_codg 科室编码
     *
     * @return dept_codg
     */
    public String getDept_codg() {
        return this.dept_codg;
    }

    /**
     * 获取 dept_name 科室名称
     *
     * @return dept_name
     */
    public String getDept_name() {
        return this.dept_name;
    }

    /**
     * 获取 dept_type 科室类别
     *
     * @return dept_type
     */
    public String getDept_type() {
        return this.dept_type;
    }

    /**
     * 获取 cost_codg 成本编码
     *
     * @return cost_codg
     */
    public String getCost_codg() {
        return this.cost_codg;
    }

    /**
     * 获取 dept_cost_codg 科室成本编码
     *
     * @return dept_cost_codg
     */
    public String getDept_cost_codg() {
        return this.dept_cost_codg;
    }

    /**
     * 获取 cost_name 成本名称
     *
     * @return cost_name
     */
    public String getCost_name() {
        return this.cost_name;
    }

    /**
     * 获取 cost_type 成本类型
     *
     * @return cost_type
     */
    public String getCost_type() {
        return this.cost_type;
    }

    /**
     * 获取 cost_shape 成本形态
     *
     * @return cost_shape
     */
    public String getCost_shape() {
        return this.cost_shape;
    }

    /**
     * 获取 cost_contrly 成本可控性
     *
     * @return cost_contrly
     */
    public String getCost_contrly() {
        return this.cost_contrly;
    }

    /**
     * 获取 cost_aprt_code 成本分摊代码
     *
     * @return cost_aprt_code
     */
    public String getCost_aprt_code() {
        return this.cost_aprt_code;
    }

    /**
     * 获取 oprt_time 操作时间
     *
     * @return oprt_time
     */
    public Timestamp getOprt_time() {
        return this.oprt_time;
    }

    /**
     * 获取 vali_flag 有效标志
     *
     * @return vali_flag
     */
    public String getVali_flag() {
        return this.vali_flag;
    }

    /**
     * 获取 dept_cost_cfg_id 科室成本配置ID
     *
     * @return dept_cost_cfg_id
     */
    public String getDept_cost_cfg_id() {
        return this.dept_cost_cfg_id;
    }

    /**
     * 获取成本分摊codes
     *
     * @return
     */
    public List<String> getCodes() {
        return codes;
    }

    /**
     * 设置成本分摊codes
     *
     * @param codes
     */
    public void setCodes(List<String> codes) {
        this.codes = codes;
    }

    public List<String> getAprt_dept_psncnt_list() {
        return aprt_dept_psncnt_list;
    }

    public void setAprt_dept_psncnt_list(List<String> aprt_dept_psncnt_list) {
        this.aprt_dept_psncnt_list = aprt_dept_psncnt_list;
    }

    public String getDept_type_comm_cost() {
        return dept_type_comm_cost;
    }

    public void setDept_type_comm_cost(String dept_type_comm_cost) {
        this.dept_type_comm_cost = dept_type_comm_cost;
    }

    /**
     * 转换为map对象
     *
     * @return Map
     */
    public java.util.Map toMap() {
        java.util.Map map = new java.util.HashMap();
        // medins_no 医疗机构编号
        map.put("medins_no", getMedins_no());
        // data_ym 数据期号
        map.put("data_ym", getData_ym());
        // dept_codg 科室编码
        map.put("dept_codg", getDept_codg());
        // dept_name 科室名称
        map.put("dept_name", getDept_name());
        // dept_type 科室类别
        map.put("dept_type", getDept_type());
        // cost_codg 成本编码
        map.put("cost_codg", getCost_codg());
        // dept_cost_codg 科室成本编码
        map.put("dept_cost_codg", getDept_cost_codg());
        // cost_name 成本名称
        map.put("cost_name", getCost_name());
        // cost_type 成本类型
        map.put("cost_type", getCost_type());
        // cost_shape 成本形态
        map.put("cost_shape", getCost_shape());
        // cost_contrly 成本可控性
        map.put("cost_contrly", getCost_contrly());
        // cost_aprt_code 成本分摊代码
        map.put("cost_aprt_code", getCost_aprt_code());
        // oprt_time 操作时间
        map.put("oprt_time", getOprt_time());
        // vali_flag 有效标志
        map.put("vali_flag", getVali_flag());
        // dept_cost_cfg_id 科室成本配置ID
        map.put("dept_cost_cfg_id", getDept_cost_cfg_id());
        // dept_cost_cfg_id 科室成本配置ID
        map.put("codes", getCodes());
        map.put("aprt_dept_psncnt_list", getAprt_dept_psncnt_list());
        return map;
    }

}