package com.jp.med.cost.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

/**
 * 不计价耗材列表
 * <AUTHOR>
 * @email -
 * @date 2024-03-20 11:03:21
 */
@Data
@TableName("hosp_consumables_name" )
public class CostConsumablesNameDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 不计价耗材名称 */
    @TableField("non_chargeable_consumables_name")
    private String nonChargeableConsumablesName;

    /** 备注 */
    @TableField("remark")
    private String remark;

    /** 医院id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 耗材编码 */
    @TableField("project_code")
    private String projectCode;

}
