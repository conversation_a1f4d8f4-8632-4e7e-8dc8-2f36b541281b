package com.jp.med.cost.modules.dataDictionaryConfig.service.write.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.exception.AppException;
import com.jp.med.cost.modules.common.util.StringUtil;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import com.jp.med.cost.modules.common.constant.CostIConst;
import com.jp.med.cost.modules.common.vo.DeptCostCfgVo;
import com.jp.med.cost.modules.common.vo.MedinsCostItemCfgVo;
import com.jp.med.cost.modules.dataDictionaryConfig.dto.ApportionParamReqDto;
import com.jp.med.cost.modules.dataDictionaryConfig.dto.DepartCostDto;
import com.jp.med.cost.modules.dataDictionaryConfig.mapper.write.DepartmentCostAllocationWriteMapper;
import com.jp.med.cost.modules.dataDictionaryConfig.service.read.DepartmentCostAllocationReadService;
import com.jp.med.cost.modules.dataDictionaryConfig.service.write.DepartmentCostAllocationWriteService;
import com.jp.med.cost.modules.dataDictionaryConfig.vo.DepartTreeListVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Transactional(readOnly = false)
@Service
public class DepartmentCostAllocationWriteServiceImpl extends ServiceImpl<DepartmentCostAllocationWriteMapper, DeptCostCfgVo> implements DepartmentCostAllocationWriteService {

    @Autowired
    private DepartmentCostAllocationReadService departmentCostAllocationReadService;

    @Resource
    private DepartmentCostAllocationWriteMapper departmentCostAllocationWriteMapper;

    @Override
    public void generateKkp1Data(DepartCostDto dto) {

        if (ValidateUtil.isEmpty(dto.getDept_codg())) {
            throw new AppException("科室编码不能为空!!");
        }


        if (ValidateUtil.isEmpty(dto.getCost_codg())) {
            throw new AppException("成本编码不能为空!!!");
        }

        List<String> strings = JSON.parseArray(dto.getCost_codg(), String.class);
        dto.setCost_codeList(strings);

        List<DepartTreeListVo> listDepartmentTreeResDtos = departmentCostAllocationReadService.queryDeptById(dto);

        if (ValidateUtil.isEmpty(listDepartmentTreeResDtos)) {
            throw new AppException("科室不存在!!");
        }

        List<MedinsCostItemCfgVo> costByCostCode = departmentCostAllocationReadService.queryCostByCostCode(dto);

        if (ValidateUtil.isEmpty(costByCostCode)) {
            throw new AppException("成本不存在!!");
        }

        String akb020 = ValidateUtil.isEmpty(dto.getHospitalId()) ? costByCostCode.get(0).getMedins_no() : dto.getHospitalId();

        List<DeptCostCfgVo> result = new ArrayList<>();
        DepartTreeListVo departmentTreeResDto = listDepartmentTreeResDtos.get(0);

        costByCostCode.stream().forEach(item -> {
            DeptCostCfgVo kkp1Vo = new DeptCostCfgVo();
            kkp1Vo.setData_ym(dto.getIssue());
            kkp1Vo.setMedins_no(akb020);
            kkp1Vo.setDept_codg(departmentTreeResDto.getDept_codg());
            kkp1Vo.setDept_name(departmentTreeResDto.getDept_name());
            kkp1Vo.setDept_type(departmentTreeResDto.getDept_type());
            kkp1Vo.setCost_codg(item.getCost_codg());
            kkp1Vo.setDept_cost_codg(departmentTreeResDto.getDept_codg() + "-" + item.getCost_codg());
            kkp1Vo.setCost_name(item.getCost_name());
            kkp1Vo.setCost_type(item.getCost_type());
            kkp1Vo.setCost_shape(item.getCost_shape());
            kkp1Vo.setCost_contrly(item.getCost_contrly());
            kkp1Vo.setCost_aprt_code(item.getCost_aprt_code());
            kkp1Vo.setVali_flag(CostIConst.VALI_FLAG_EFFECTIVE);
            result.add(kkp1Vo);
        });

        //todo 执行批量插入
        departmentCostAllocationWriteMapper.batchInsertdeptCostCfg(result);
    }

    @Override
    public void deleteDeptCost(DepartCostDto dto) {
        if (ValidateUtil.isEmpty(dto.getDept_cost_cfg_id())) {
            throw new AppException("科室成本配置id不能为空!!");
        }
        int num = departmentCostAllocationWriteMapper.deleteDeptCost(dto);
        if (num > 1) {
            throw new AppException("更新科室成本配置的数量大于1");
        }
    }

    @Override
    public void updateDeptJoinCost(DepartCostDto dto) {
        if (ValidateUtil.isEmpty(dto.getDept_codg())) {
            throw new AppException("科室编码不能为空!!");
        }
        int num = departmentCostAllocationWriteMapper.updateDeptJoinCost(dto);
        if (num > 1) {
            throw new AppException("更新科室成本配置的数量大于1");
        }
    }

    @Override
    public boolean operateDeptApportionParameter(ApportionParamReqDto dto) {
        if (ValidateUtil.isEmpty(dto.getIds())) {
            throw new AppException("更新的主键不能为空!");
        }
        if (ValidateUtil.isNotEmpty(dto.getCodes())) {
            // 将选择的所有分摊参数的编码与0二进制相与
            int result = CostIConst.ZERO;
            List<String> codes = dto.getCodes();
            for (String code : codes) {
                if (!ValidateUtil.isEmpty(code)) {
                    long avoidOverflows = Long.parseLong(code, 2);
                    int num = (int) avoidOverflows;
                    result = result | num;
                }
            }

            String binary = StringUtil.binaryToDecimal(result);

            if (ValidateUtil.isEmpty(binary) || binary.length() < 8) {
                throw new AppException("设置分摊参数出错");
            }

            dto.setCost_aprt_code(binary.substring(binary.length() - 8));
        }

        int res = departmentCostAllocationWriteMapper.operateDeptApportionParameter(dto);
        if (res > 0) {
            return true;
        }
        return false;
    }


}
