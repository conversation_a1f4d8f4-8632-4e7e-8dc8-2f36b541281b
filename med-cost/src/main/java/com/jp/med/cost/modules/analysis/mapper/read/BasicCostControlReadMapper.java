package com.jp.med.cost.modules.analysis.mapper.read;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.cost.modules.analysis.dto.BasicCostControlDto;
import com.jp.med.cost.modules.analysis.vo.*;
import com.jp.med.cost.modules.dataDictionaryConfig.vo.DepartTreeListVo;

import java.math.BigDecimal;
import java.util.List;

public interface BasicCostControlReadMapper extends BaseMapper<BasicCostControlDto> {
    /**
     * 查询医院成本趋势
     *
     * @param dto
     * @return
     */
    List<HospitalCostDataVo> queryHospitalCostTrend(BasicCostControlDto dto);

    /**
     * 查询科室成本汇总
     *
     * @param dto
     * @return
     */
    HospitalCostDataVo queryHospitalCostSum(BasicCostControlDto dto);

    /**
     * 查询成本类型汇总
     *
     * @param dto
     * @return
     */
    List<HospitalCostTypeVo> queryHospitalCostType(BasicCostControlDto dto);

    /**
     * 查询成本类型月度汇总
     *
     * @param dto
     * @return
     */
    List<HospitalCostTypeVo> queryHospitalCostTypeTrend(BasicCostControlDto dto);

    /**
     * 查询医院成本类型饼状图数据
     *
     * @param dto
     * @return
     */
    HospitalCostTypeVo queryHospitalCostTypePie(BasicCostControlDto dto);

    /**
     * 查询成本类型趋势图
     *
     * @param dto
     * @return
     */
    List<HospitalCostTypeVo> queryHospitalCostTypeOtherTrend(BasicCostControlDto dto);

    /**
     * 查询科室类型成本
     *
     * @param dto
     * @return
     */
    List<DeptTypeCostVo> queryDeptCostType(BasicCostControlDto dto);

    /**
     * 查询不可控成本
     *
     * @param dto
     * @return
     */
    BigDecimal queryUncontrolledCost(BasicCostControlDto dto);

    /**
     * 查询科室成本类型趋势
     *
     * @param dto
     * @return
     */
    List<DeptTypeCostVo> queryDeptCostTypeTrend(BasicCostControlDto dto);

    /**
     * 查询科室列表
     *
     * @param dto
     * @return
     */
    List<DepartTreeListVo> selectDeptList(BasicCostControlDto dto);

    /**
     * 查询科室成本数据
     *
     * @param dto
     * @return
     */
    DeptCostInfoVo queryDeptCostInfo(BasicCostControlDto dto);

    /**
     * 查询科室成本INFO
     *
     * @param dto
     * @return
     */
    List<DeptCostInfoVo> queryDeptCostInfoList(BasicCostControlDto dto);

    /**
     * 查询科室成本对比信息
     *
     * @param dto
     * @return
     */
    List<DeptCostInfoVo> queryDeptCostContrastList(BasicCostControlDto dto);

    /**
     * 查询科室成本可控性信息
     *
     * @param dto
     * @return
     */
    List<CostControllabilityVo> queryDeptCostControllability(BasicCostControlDto dto);

    /**
     * 查询历史的全院目标定制记录
     *
     * @param dto
     * @return
     */
    HospMonCostTagtVo queryHospitalCostGoalHis(BasicCostControlDto dto);

    /**
     * 查询成本
     *
     * @param dto
     * @return
     */
    List<CostGoalHistoryVo> queryCostGoalHistory(BasicCostControlDto dto);

    /**
     * 查询科室自生成本
     *
     * @param dto
     * @return
     */
    BigDecimal querySelfCostSum(BasicCostControlDto dto);

    /**
     * 查询科室成本
     *
     * @param dto
     * @return
     */
    DeptInfoVo queryDeptInfo(BasicCostControlDto dto);

    /**
     * 成本趋势
     *
     * @param dto
     * @return
     */
    List<DeptInfoVo> queryDeptCostTrend(BasicCostControlDto dto);

    /**
     * 查询人均成本率
     *
     * @param dto
     * @return
     */
    List<DeptInfoVo> queryPersonCostRatioTrend(BasicCostControlDto dto);

    /**
     * 查询科室成本
     *
     * @param dto
     * @return
     */
    List<CostDeptTypeVo> queryDeptTypeCost(BasicCostControlDto dto);

    /**
     * 查询科室成本汇总
     *
     * @param dto
     * @return
     */
    BigDecimal queryDeptTypeCostSum(BasicCostControlDto dto);
}
