package com.jp.med.cost.modules.extract.service.write.imp;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.cost.modules.common.util.ExcelUtil;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import com.jp.med.cost.modules.extract.dto.CollectionConsumablesDto;
import com.jp.med.cost.modules.extract.excelDto.CollectionConsumablesExcelDto;
import com.jp.med.cost.modules.extract.mapper.write.CollectionConsumablesWriteMapper;
import com.jp.med.cost.modules.extract.service.write.CollectionConsumablesWriteService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

@Service("collectionConsumablesWriteService")
@Transactional
public class CollectionConsumablesWriteServiceImpl extends ServiceImpl<CollectionConsumablesWriteMapper, CollectionConsumablesDto> implements CollectionConsumablesWriteService {
    private static final Logger logger = LoggerFactory.getLogger(CollectionConsumablesWriteServiceImpl.class);
    @Resource
    private CollectionConsumablesWriteMapper collectionConsumablesWriteMapper;

    @Override
    public void updateChangeCollection(CollectionConsumablesDto dto) {
        collectionConsumablesWriteMapper.fnChangeCollection(dto);
    }

    @Override
    public void addCollection(CollectionConsumablesDto dto) {
        collectionConsumablesWriteMapper.fnAddCollection(dto);
    }

    @Override
    public void importData(MultipartFile file, String fileType) {
        byte[] bytes = new byte[0];
        try {
            bytes = file.getBytes();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
        }
        InputStream in = new ByteArrayInputStream(bytes);
        String[] fieldArray = {"matl_name", "pric", "purcpric", "rece_cnt", "used_cnt", "emp", "rece_dept_codg", "recer_name", "memo", "rece_date"};
        Class<CollectionConsumablesExcelDto> T = CollectionConsumablesExcelDto.class;
        List<CollectionConsumablesExcelDto> list = ExcelUtil.getDataListFromExcelFileStream(in, fieldArray, 0, true, T);
        if (ValidateUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                collectionConsumablesWriteMapper.insert(item);
            });
        }
    }
}
