package com.jp.med.cost.modules.common.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Setter
@Getter
public class Kkp2Vo implements Serializable {
    private static final long serialVersionUID = -4074191815267370624L;

    private String aay002; // 成本分摊系数配置id
    private String akb020; // 医疗机构编号
    private String kkh198; // 科室类别(0顶级科室,1行政科室,2医辅科室,3医技科室,4临床科室)
    private String kkh199; // 分摊级别
    private String kkh200; // 分析系数编码
    private String kkh201; // 分摊系数名称
    private Date aae036; // 操作时间
    private String aae100; // 有效标志
}
