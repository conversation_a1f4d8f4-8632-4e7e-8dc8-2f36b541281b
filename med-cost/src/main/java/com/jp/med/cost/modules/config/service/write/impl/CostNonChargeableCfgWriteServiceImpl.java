package com.jp.med.cost.modules.config.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.cost.modules.config.dto.CostNonChargeableCfgDto;
import com.jp.med.cost.modules.config.mapper.write.CostNonChargeableCfgWriteMapper;
import com.jp.med.cost.modules.config.service.write.CostNonChargeableCfgWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 不计价耗材
 * <AUTHOR>
 * @email -
 * @date 2024-03-19 22:09:43
 */
@Service
@Transactional(readOnly = false)
public class CostNonChargeableCfgWriteServiceImpl extends ServiceImpl<CostNonChargeableCfgWriteMapper, CostNonChargeableCfgDto> implements CostNonChargeableCfgWriteService {
}
