package com.jp.med.cost.modules.analysis.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
public class HospitalCostTypeVo {

    private String issue;

    /**
     * 成本类型名称
     */
    private String costType;

    /**
     * 可控成本
     */
    private String controlledCost;

    /**
     * 不可控成本
     */
    private String uncontrolledCost;

    /**
     * 固定成本
     */
    private String fixedCost;

    /**
     * 变动成本
     */
    private String changeCost;

    /**
     * 人员薪资费
     */
    private BigDecimal ryFee;

    /**
     * 卫生材料费
     */
    private BigDecimal wsclFee;

    /**
     * 药品费
     */
    private BigDecimal ypFee;

    /**
     * 固定资产
     */
    private BigDecimal gdzcFee;


    /**
     * 无形资产摊销
     */
    private BigDecimal wxzcFee;

    /**
     * 风险金
     */
    private BigDecimal fxjFee;

    /**
     * 其他费
     */
    private BigDecimal qtFee;
}
