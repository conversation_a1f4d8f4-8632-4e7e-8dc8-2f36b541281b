package com.jp.med.cost.modules.analysis.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.cost.modules.analysis.dto.ServiceProjectCostDto;
import com.jp.med.cost.modules.analysis.service.read.ServiceProjectCostReadService;
import com.jp.med.cost.modules.analysis.vo.ServiceProjectCostVO;
import com.jp.med.cost.modules.common.constant.CostIConst;
import com.jp.med.cost.modules.common.controller.HospBaseController;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;

@Api(value = "成本数据分析详情", tags = "成本数据分析详情")
@RestController
@RequestMapping("ServiceProjectCost")
@Validated
public class ServiceProjectCostController extends HospBaseController {
    @Autowired
    private ServiceProjectCostReadService serviceProjectCostReadService;

    /**
     * @Description: 分页查询成本数据分析详情
     * @Param:
     * @return:
     * @Author: wanghw
     * @Date: 2021/4/14
     */
    @RequestMapping("queryServiceProjectCost")
    public CommonResult<?> queryServiceProjectCost(@RequestBody ServiceProjectCostDto dto, HttpServletRequest request) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        List<ServiceProjectCostVO> resultList = serviceProjectCostReadService.queryServiceProjectCost(dto);
        return CommonResult.paging(resultList);
    }

    /**
     * 查询服务项目明细
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryServiceProjectDetail")
    public CommonResult<?> queryServiceProjectDetail(@RequestBody ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }
        HashMap<String, Object> result = new HashMap<>();
        result.put("detail", serviceProjectCostReadService.queryServiceProjectDetail(dto));
        return CommonResult.success(result);
    }

    /**
     * 查询服务项目明细成本趋势
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryServiceDetailTrend")
    public CommonResult<?> queryServiceDetailTrend(@RequestBody ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }
        dto.setIssuess();
        HashMap<String, Object> result = new HashMap<>();
        result.put("list", serviceProjectCostReadService.queryServiceDetailTrend(dto));
        return CommonResult.success(result);
    }

    /**
     * 询服务项目明细成本占比情况
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryServiceProjectDetailRadio")
    public CommonResult<?> queryServiceProjectDetailRadio(@RequestBody ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }

        HashMap<String, Object> result = new HashMap<>();
        result.put("data", serviceProjectCostReadService.queryServiceProjectDetailRadio(dto));
        return CommonResult.success(result);
    }

    /**
     * 查询科室的服务项目详情
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryServiceProjectDept")
    public CommonResult<?> queryServiceProjectDept(@RequestBody ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }

        HashMap<String, Object> result = new HashMap<>();
        result.put("list", serviceProjectCostReadService.queryServiceProjectDept(dto));
        return CommonResult.success(result);
    }
}
