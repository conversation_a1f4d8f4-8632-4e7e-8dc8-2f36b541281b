package com.jp.med.cost.modules.analysis.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.cost.modules.analysis.dto.BasicCostControlDto;
import com.jp.med.cost.modules.analysis.mapper.write.BasicCostControlWriteMapper;
import com.jp.med.cost.modules.analysis.mapper.write.DiseaseGroupCostCalculationWriteMapper;
import com.jp.med.cost.modules.analysis.service.write.DiseaseGroupCostCalculationWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 医院病组分析写入实现类
 */
@Service
@Transactional
public class DiseaseGroupCostCalculationWriteServiceImpl extends ServiceImpl<DiseaseGroupCostCalculationWriteMapper, BasicCostControlDto> implements DiseaseGroupCostCalculationWriteService {

    @Resource
    private DiseaseGroupCostCalculationWriteMapper diseaseGroupCostCalculationWriteMapper;
}
