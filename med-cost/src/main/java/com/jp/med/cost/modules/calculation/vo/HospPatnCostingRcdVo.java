package com.jp.med.cost.modules.calculation.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * class HospPatnCostingRcdVo
 * desc   医院患者成本核算记录 - hosp_patn_costing_rcd 
 *
 * <AUTHOR>
 */
public class HospPatnCostingRcdVo implements Serializable {

	private static final long serialVersionUID = -1972048384158606549L;
	/**
		 * 医院患者成本核算记录ID
		 * VARCHAR(30)
		 * isNullable false
		 */
		private String hosp_patn_costing_rcd_id;
		/**
		 * 医疗机构编号
		 * VARCHAR(30)
		 * isNullable false
		 */
		private String medins_no;
		/**
		 * 数据期号
		 * VARCHAR(6)
		 * isNullable false
		 */
		private String data_ym;
		/**
		 * 病人ID
		 * VARCHAR(30)
		 * isNullable true
		 */
		private String patn_id;
		/**
		 * 主页ID
		 * VARCHAR(30)
		 * isNullable false
		 */
		private String home_id;
		/**
		 * 住院号
		 * VARCHAR(30)
		 * isNullable false
		 */
		private String ipt_no;
		/**
		 * 就诊登记号
		 * VARCHAR(50)
		 * isNullable true
		 */
		private String mdtrt_regno;
		/**
		 * 姓名
		 * VARCHAR(100)
		 * isNullable true
		 */
		private String name;
		/**
		 * 性别
		 * VARCHAR(10)
		 * isNullable true
		 */
		private String gend;
		/**
		 * 年龄
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal age;
		/**
		 * 入院时间
		 * TIMESTAMP
		 * isNullable true
		 */
		private Timestamp adm_time;
		/**
		 * 出院时间
		 * TIMESTAMP
		 * isNullable true
		 */
		private Timestamp dscg_time;
		/**
		 * 实际住院天数
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal act_ipt_days;
		/**
		 * 总收入
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal total_income;
		/**
		 * 入院科室
		 * VARCHAR(30)
		 * isNullable true
		 */
		private String adm_dept;
		/**
		 * 出院科室
		 * VARCHAR(30)
		 * isNullable true
		 */
		private String dscg_dept;
		/**
		 * 主要诊断疾病编码
		 * VARCHAR(50)
		 * isNullable true
		 */
		private String main_diag_dise_codg;
		/**
		 * 手术及操作编码1
		 * VARCHAR(50)
		 * isNullable true
		 */
		private String oprn_oprt_codg;
		/**
		 * 主任
		 * VARCHAR(30)
		 * isNullable true
		 */
		private String chf;
		/**
		 * 主治医师
		 * VARCHAR(30)
		 * isNullable true
		 */
		private String atddr;
		/**
		 * 住院医师
		 * VARCHAR(30)
		 * isNullable true
		 */
		private String ipdr;
		/**
		 * 人员经费
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal psn_fund;
		/**
		 * 卫生材料费
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal hc_matlfee;
		/**
		 * 药品费
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal drugfee;
		/**
		 * 成本金额
		 * DECIMAL
		 * isNullable false
		 */
		private BigDecimal cost_amt;
		/**
		 * 操作时间
		 * TIMESTAMP
		 * isNullable false
		 */
		private Timestamp oprt_time;
		/**
		 * 有效标志
		 * VARCHAR(6)
		 * isNullable false
		 */
		private String vali_flag;
		/**
		 * 可控成本金额
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal contr_cost_amt;
		/**
		 * 不可控成本金额
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal un_contrly_cost_amt;
		/**
		 * DRG编码
		 * VARCHAR(10)
		 * isNullable true
		 */
		private String drg_codg;
		/**
		 * DRG名称
		 * VARCHAR(100)
		 * isNullable true
		 */
		private String drg_name;
		/**
		 * 分组状态
		 * VARCHAR(2)
		 * isNullable true
		 */
		private String grp_stas;
		/**
		 * 类型--3年度，2--季度,1--月度
		 * VARCHAR(5)
		 * isNullable true
		 */
		private String cycle_type;

		/**
		 * 设置 hosp_patn_costing_rcd_id 医院患者成本核算记录ID
		 * @param hosp_patn_costing_rcd_id 医院患者成本核算记录ID
		 */
		public void setHosp_patn_costing_rcd_id(String hosp_patn_costing_rcd_id){
			this.hosp_patn_costing_rcd_id = hosp_patn_costing_rcd_id;
		}
		/**
		 * 设置 medins_no 医疗机构编号
		 * @param medins_no 医疗机构编号
		 */
		public void setMedins_no(String medins_no){
			this.medins_no = medins_no;
		}
		/**
		 * 设置 data_ym 数据期号
		 * @param data_ym 数据期号
		 */
		public void setData_ym(String data_ym){
			this.data_ym = data_ym;
		}
		/**
		 * 设置 patn_id 病人ID
		 * @param patn_id 病人ID
		 */
		public void setPatn_id(String patn_id){
			this.patn_id = patn_id;
		}
		/**
		 * 设置 home_id 主页ID
		 * @param home_id 主页ID
		 */
		public void setHome_id(String home_id){
			this.home_id = home_id;
		}
		/**
		 * 设置 ipt_no 住院号
		 * @param ipt_no 住院号
		 */
		public void setIpt_no(String ipt_no){
			this.ipt_no = ipt_no;
		}
		/**
		 * 设置 mdtrt_regno 就诊登记号
		 * @param mdtrt_regno 就诊登记号
		 */
		public void setMdtrt_regno(String mdtrt_regno){
			this.mdtrt_regno = mdtrt_regno;
		}
		/**
		 * 设置 name 姓名
		 * @param name 姓名
		 */
		public void setName(String name){
			this.name = name;
		}
		/**
		 * 设置 gend 性别
		 * @param gend 性别
		 */
		public void setGend(String gend){
			this.gend = gend;
		}
		/**
		 * 设置 age 年龄
		 * @param age 年龄
		 */
		public void setAge(BigDecimal age){
			this.age = age;
		}
		/**
		 * 设置 adm_time 入院时间
		 * @param adm_time 入院时间
		 */
		public void setAdm_time(Timestamp adm_time){
			this.adm_time = adm_time;
		}
		/**
		 * 设置 dscg_time 出院时间
		 * @param dscg_time 出院时间
		 */
		public void setDscg_time(Timestamp dscg_time){
			this.dscg_time = dscg_time;
		}
		/**
		 * 设置 act_ipt_days 实际住院天数
		 * @param act_ipt_days 实际住院天数
		 */
		public void setAct_ipt_days(BigDecimal act_ipt_days){
			this.act_ipt_days = act_ipt_days;
		}
		/**
		 * 设置 total_income 总收入
		 * @param total_income 总收入
		 */
		public void setTotal_income(BigDecimal total_income){
			this.total_income = total_income;
		}
		/**
		 * 设置 adm_dept 入院科室
		 * @param adm_dept 入院科室
		 */
		public void setAdm_dept(String adm_dept){
			this.adm_dept = adm_dept;
		}
		/**
		 * 设置 dscg_dept 出院科室
		 * @param dscg_dept 出院科室
		 */
		public void setDscg_dept(String dscg_dept){
			this.dscg_dept = dscg_dept;
		}
		/**
		 * 设置 main_diag_dise_codg 主要诊断疾病编码
		 * @param main_diag_dise_codg 主要诊断疾病编码
		 */
		public void setMain_diag_dise_codg(String main_diag_dise_codg){
			this.main_diag_dise_codg = main_diag_dise_codg;
		}
		/**
		 * 设置 oprn_oprt_codg 手术及操作编码1
		 * @param oprn_oprt_codg 手术及操作编码1
		 */
		public void setOprn_oprt_codg(String oprn_oprt_codg){
			this.oprn_oprt_codg = oprn_oprt_codg;
		}
		/**
		 * 设置 chf 主任
		 * @param chf 主任
		 */
		public void setChf(String chf){
			this.chf = chf;
		}
		/**
		 * 设置 atddr 主治医师
		 * @param atddr 主治医师
		 */
		public void setAtddr(String atddr){
			this.atddr = atddr;
		}
		/**
		 * 设置 ipdr 住院医师
		 * @param ipdr 住院医师
		 */
		public void setIpdr(String ipdr){
			this.ipdr = ipdr;
		}
		/**
		 * 设置 psn_fund 人员经费
		 * @param psn_fund 人员经费
		 */
		public void setPsn_fund(BigDecimal psn_fund){
			this.psn_fund = psn_fund;
		}
		/**
		 * 设置 hc_matlfee 卫生材料费
		 * @param hc_matlfee 卫生材料费
		 */
		public void setHc_matlfee(BigDecimal hc_matlfee){
			this.hc_matlfee = hc_matlfee;
		}
		/**
		 * 设置 drugfee 药品费
		 * @param drugfee 药品费
		 */
		public void setDrugfee(BigDecimal drugfee){
			this.drugfee = drugfee;
		}
		/**
		 * 设置 cost_amt 成本金额
		 * @param cost_amt 成本金额
		 */
		public void setCost_amt(BigDecimal cost_amt){
			this.cost_amt = cost_amt;
		}
		/**
		 * 设置 oprt_time 操作时间
		 * @param oprt_time 操作时间
		 */
		public void setOprt_time(Timestamp oprt_time){
			this.oprt_time = oprt_time;
		}
		/**
		 * 设置 vali_flag 有效标志
		 * @param vali_flag 有效标志
		 */
		public void setVali_flag(String vali_flag){
			this.vali_flag = vali_flag;
		}
		/**
		 * 设置 contr_cost_amt 可控成本金额
		 * @param contr_cost_amt 可控成本金额
		 */
		public void setContr_cost_amt(BigDecimal contr_cost_amt){
			this.contr_cost_amt = contr_cost_amt;
		}
		/**
		 * 设置 un_contrly_cost_amt 不可控成本金额
		 * @param un_contrly_cost_amt 不可控成本金额
		 */
		public void setUn_contrly_cost_amt(BigDecimal un_contrly_cost_amt){
			this.un_contrly_cost_amt = un_contrly_cost_amt;
		}
		/**
		 * 设置 drg_codg DRG编码
		 * @param drg_codg DRG编码
		 */
		public void setDrg_codg(String drg_codg){
			this.drg_codg = drg_codg;
		}
		/**
		 * 设置 drg_name DRG名称
		 * @param drg_name DRG名称
		 */
		public void setDrg_name(String drg_name){
			this.drg_name = drg_name;
		}
		/**
		 * 设置 grp_stas 分组状态
		 * @param grp_stas 分组状态
		 */
		public void setGrp_stas(String grp_stas){
			this.grp_stas = grp_stas;
		}
		/**
		 * 设置 cycle_type 类型--3年度，2--季度,1--月度
		 * @param cycle_type 类型--3年度，2--季度,1--月度
		 */
		public void setCycle_type(String cycle_type){
			this.cycle_type = cycle_type;
		}

		/**
		 * 获取 hosp_patn_costing_rcd_id 医院患者成本核算记录ID
		 * @return  hosp_patn_costing_rcd_id
		 */
	    public String getHosp_patn_costing_rcd_id(){
			return this.hosp_patn_costing_rcd_id;
		}
		/**
		 * 获取 medins_no 医疗机构编号
		 * @return  medins_no
		 */
	    public String getMedins_no(){
			return this.medins_no;
		}
		/**
		 * 获取 data_ym 数据期号
		 * @return  data_ym
		 */
	    public String getData_ym(){
			return this.data_ym;
		}
		/**
		 * 获取 patn_id 病人ID
		 * @return  patn_id
		 */
	    public String getPatn_id(){
			return this.patn_id;
		}
		/**
		 * 获取 home_id 主页ID
		 * @return  home_id
		 */
	    public String getHome_id(){
			return this.home_id;
		}
		/**
		 * 获取 ipt_no 住院号
		 * @return  ipt_no
		 */
	    public String getIpt_no(){
			return this.ipt_no;
		}
		/**
		 * 获取 mdtrt_regno 就诊登记号
		 * @return  mdtrt_regno
		 */
	    public String getMdtrt_regno(){
			return this.mdtrt_regno;
		}
		/**
		 * 获取 name 姓名
		 * @return  name
		 */
	    public String getName(){
			return this.name;
		}
		/**
		 * 获取 gend 性别
		 * @return  gend
		 */
	    public String getGend(){
			return this.gend;
		}
		/**
		 * 获取 age 年龄
		 * @return  age
		 */
	    public BigDecimal getAge(){
			return this.age;
		}
		/**
		 * 获取 adm_time 入院时间
		 * @return  adm_time
		 */
	    public Timestamp getAdm_time(){
			return this.adm_time;
		}
		/**
		 * 获取 dscg_time 出院时间
		 * @return  dscg_time
		 */
	    public Timestamp getDscg_time(){
			return this.dscg_time;
		}
		/**
		 * 获取 act_ipt_days 实际住院天数
		 * @return  act_ipt_days
		 */
	    public BigDecimal getAct_ipt_days(){
			return this.act_ipt_days;
		}
		/**
		 * 获取 total_income 总收入
		 * @return  total_income
		 */
	    public BigDecimal getTotal_income(){
			return this.total_income;
		}
		/**
		 * 获取 adm_dept 入院科室
		 * @return  adm_dept
		 */
	    public String getAdm_dept(){
			return this.adm_dept;
		}
		/**
		 * 获取 dscg_dept 出院科室
		 * @return  dscg_dept
		 */
	    public String getDscg_dept(){
			return this.dscg_dept;
		}
		/**
		 * 获取 main_diag_dise_codg 主要诊断疾病编码
		 * @return  main_diag_dise_codg
		 */
	    public String getMain_diag_dise_codg(){
			return this.main_diag_dise_codg;
		}
		/**
		 * 获取 oprn_oprt_codg 手术及操作编码1
		 * @return  oprn_oprt_codg
		 */
	    public String getOprn_oprt_codg(){
			return this.oprn_oprt_codg;
		}
		/**
		 * 获取 chf 主任
		 * @return  chf
		 */
	    public String getChf(){
			return this.chf;
		}
		/**
		 * 获取 atddr 主治医师
		 * @return  atddr
		 */
	    public String getAtddr(){
			return this.atddr;
		}
		/**
		 * 获取 ipdr 住院医师
		 * @return  ipdr
		 */
	    public String getIpdr(){
			return this.ipdr;
		}
		/**
		 * 获取 psn_fund 人员经费
		 * @return  psn_fund
		 */
	    public BigDecimal getPsn_fund(){
			return this.psn_fund;
		}
		/**
		 * 获取 hc_matlfee 卫生材料费
		 * @return  hc_matlfee
		 */
	    public BigDecimal getHc_matlfee(){
			return this.hc_matlfee;
		}
		/**
		 * 获取 drugfee 药品费
		 * @return  drugfee
		 */
	    public BigDecimal getDrugfee(){
			return this.drugfee;
		}
		/**
		 * 获取 cost_amt 成本金额
		 * @return  cost_amt
		 */
	    public BigDecimal getCost_amt(){
			return this.cost_amt;
		}
		/**
		 * 获取 oprt_time 操作时间
		 * @return  oprt_time
		 */
	    public Timestamp getOprt_time(){
			return this.oprt_time;
		}
		/**
		 * 获取 vali_flag 有效标志
		 * @return  vali_flag
		 */
	    public String getVali_flag(){
			return this.vali_flag;
		}
		/**
		 * 获取 contr_cost_amt 可控成本金额
		 * @return  contr_cost_amt
		 */
	    public BigDecimal getContr_cost_amt(){
			return this.contr_cost_amt;
		}
		/**
		 * 获取 un_contrly_cost_amt 不可控成本金额
		 * @return  un_contrly_cost_amt
		 */
	    public BigDecimal getUn_contrly_cost_amt(){
			return this.un_contrly_cost_amt;
		}
		/**
		 * 获取 drg_codg DRG编码
		 * @return  drg_codg
		 */
	    public String getDrg_codg(){
			return this.drg_codg;
		}
		/**
		 * 获取 drg_name DRG名称
		 * @return  drg_name
		 */
	    public String getDrg_name(){
			return this.drg_name;
		}
		/**
		 * 获取 grp_stas 分组状态
		 * @return  grp_stas
		 */
	    public String getGrp_stas(){
			return this.grp_stas;
		}
		/**
		 * 获取 cycle_type 类型--3年度，2--季度,1--月度
		 * @return  cycle_type
		 */
	    public String getCycle_type(){
			return this.cycle_type;
		}

		/**
		 * 转换为map对象
		 * @return Map
		 */
    	public java.util.Map toMap() {
			java.util.Map map = new java.util.HashMap();
				// hosp_patn_costing_rcd_id 医院患者成本核算记录ID
				map.put("hosp_patn_costing_rcd_id", getHosp_patn_costing_rcd_id());
				// medins_no 医疗机构编号
				map.put("medins_no", getMedins_no());
				// data_ym 数据期号
				map.put("data_ym", getData_ym());
				// patn_id 病人ID
				map.put("patn_id", getPatn_id());
				// home_id 主页ID
				map.put("home_id", getHome_id());
				// ipt_no 住院号
				map.put("ipt_no", getIpt_no());
				// mdtrt_regno 就诊登记号
				map.put("mdtrt_regno", getMdtrt_regno());
				// name 姓名
				map.put("name", getName());
				// gend 性别
				map.put("gend", getGend());
				// age 年龄
				map.put("age", getAge());
				// adm_time 入院时间
				map.put("adm_time", getAdm_time());
				// dscg_time 出院时间
				map.put("dscg_time", getDscg_time());
				// act_ipt_days 实际住院天数
				map.put("act_ipt_days", getAct_ipt_days());
				// total_income 总收入
				map.put("total_income", getTotal_income());
				// adm_dept 入院科室
				map.put("adm_dept", getAdm_dept());
				// dscg_dept 出院科室
				map.put("dscg_dept", getDscg_dept());
				// main_diag_dise_codg 主要诊断疾病编码
				map.put("main_diag_dise_codg", getMain_diag_dise_codg());
				// oprn_oprt_codg 手术及操作编码1
				map.put("oprn_oprt_codg", getOprn_oprt_codg());
				// chf 主任
				map.put("chf", getChf());
				// atddr 主治医师
				map.put("atddr", getAtddr());
				// ipdr 住院医师
				map.put("ipdr", getIpdr());
				// psn_fund 人员经费
				map.put("psn_fund", getPsn_fund());
				// hc_matlfee 卫生材料费
				map.put("hc_matlfee", getHc_matlfee());
				// drugfee 药品费
				map.put("drugfee", getDrugfee());
				// cost_amt 成本金额
				map.put("cost_amt", getCost_amt());
				// oprt_time 操作时间
				map.put("oprt_time", getOprt_time());
				// vali_flag 有效标志
				map.put("vali_flag", getVali_flag());
				// contr_cost_amt 可控成本金额
				map.put("contr_cost_amt", getContr_cost_amt());
				// un_contrly_cost_amt 不可控成本金额
				map.put("un_contrly_cost_amt", getUn_contrly_cost_amt());
				// drg_codg DRG编码
				map.put("drg_codg", getDrg_codg());
				// drg_name DRG名称
				map.put("drg_name", getDrg_name());
				// grp_stas 分组状态
				map.put("grp_stas", getGrp_stas());
				// cycle_type 类型--3年度，2--季度,1--月度
				map.put("cycle_type", getCycle_type());
				return map;
		}
	
}