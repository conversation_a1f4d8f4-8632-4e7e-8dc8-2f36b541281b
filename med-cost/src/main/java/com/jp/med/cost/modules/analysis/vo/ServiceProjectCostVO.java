package com.jp.med.cost.modules.analysis.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class ServiceProjectCostVO implements Serializable {
    private static final long serialVersionUID = -2396597260761012531L;
    /**
     * 服务项目编码
     */
    private String ake005;

    /**
     * 服务项目名称
     */
    private String ake006;

    /**
     * 成本
     */
    private BigDecimal kkh262;

    /**
     * 执行科室
     */
    private String bkf001;

    /**
     * 费用类型
     */
    private String kkh065;

    /**
     * 收入
     */
    private BigDecimal kkh066;

    /**
     * 成本率
     */
    private BigDecimal radio;



    List<BigDecimal> trend;

}
