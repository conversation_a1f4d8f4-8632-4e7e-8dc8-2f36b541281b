package com.jp.med.cost.modules.common.vo;

import java.io.Serializable;

/**
 * class DeptCchiResuCosmCfgVo
 * desc   科室CCHI资源消耗配置 - dept_cchi_resu_cosm_cfg 
 *
 * <AUTHOR>
 */
public class DeptCchiResuCosmCfgVo implements Serializable {

	private static final long serialVersionUID = 1902093219935298632L;
	/**
		 * 科室工作项目资源消耗配置表ID
		 * VARCHAR(30)
		 * isNullable false
		 */
		private String dept_job_item_resu_cosm_cfg_tab_id;
		/**
		 * 年度
		 * INTEGER
		 * isNullable false
		 */
		private Integer year;
		/**
		 * 医疗服务机构编号
		 * VARCHAR(30)
		 * isNullable true
		 */
		private String medins_no;
		/**
		 * 医院科室编码
		 * VARCHAR(50)
		 * isNullable true
		 */
		private String dept_codg;
		/**
		 * 医院科室名称
		 * VARCHAR(100)
		 * isNullable true
		 */
		private String dept_name;
		/**
		 * 医疗机构三目录编码
		 * VARCHAR(50)
		 * isNullable true
		 */
		private String medins_hilist_codg;
		/**
		 * 医疗机构三目录名称
		 * VARCHAR(100)
		 * isNullable true
		 */
		private String medins_hilist_name;
		/**
		 * CCHI项目编码
		 * VARCHAR(50)
		 * isNullable false
		 */
		private String cchi_item_codg;
		/**
		 * CCHI项目名称
		 * VARCHAR(100)
		 * isNullable false
		 */
		private String cchi_itemname;
		/**
		 * CCHI项目内涵
		 * TEXT
		 * isNullable true
		 */
		private String cchi_item_cont;
		/**
		 * 低值耗材
		 * NUMERIC(8, 2)
		 * isNullable true
		 */
		private Double lowval_mcs;
		/**
		 * 技术难度
		 * NUMERIC(8, 2)
		 * isNullable true
		 */
		private Double art_dif;
		/**
		 * 医生人数
		 * INTEGER
		 * isNullable true
		 */
		private Integer dr_psncnt;
		/**
		 * 医护人数
		 * INTEGER
		 * isNullable true
		 */
		private Integer medi_care_psncnt;
		/**
		 * 医技人数
		 * INTEGER
		 * isNullable true
		 */
		private Integer med_tech_psncnt;
		/**
		 * 工勤人数
		 * INTEGER
		 * isNullable true
		 */
		private Integer work_attd_psncnt;
		/**
		 * 平均耗时
		 * NUMERIC(8, 2)
		 * isNullable true
		 */
		private Double ave_timecost;
		/**
		 * 耗时单位
		 * VARCHAR(6)
		 * isNullable true
		 */
		private String timecost_emp;
		/**
		 * 统筹区编码
		 * VARCHAR(30)
		 * isNullable false
		 */
		private String pool_coty_codg;
		/**
		 * 有效标志
		 * VARCHAR(6)
		 * isNullable false
		 */
		private String vali_flag;

		/**
		 * 设置 dept_job_item_resu_cosm_cfg_tab_id 科室工作项目资源消耗配置表ID
		 * @param dept_job_item_resu_cosm_cfg_tab_id 科室工作项目资源消耗配置表ID
		 */
		public void setDept_job_item_resu_cosm_cfg_tab_id(String dept_job_item_resu_cosm_cfg_tab_id){
			this.dept_job_item_resu_cosm_cfg_tab_id = dept_job_item_resu_cosm_cfg_tab_id;
		}
		/**
		 * 设置 year 年度
		 * @param year 年度
		 */
		public void setYear(Integer year){
			this.year = year;
		}
		/**
		 * 设置 medins_no 医疗服务机构编号
		 * @param medins_no 医疗服务机构编号
		 */
		public void setMedins_no(String medins_no){
			this.medins_no = medins_no;
		}
		/**
		 * 设置 dept_codg 医院科室编码
		 * @param dept_codg 医院科室编码
		 */
		public void setDept_codg(String dept_codg){
			this.dept_codg = dept_codg;
		}
		/**
		 * 设置 dept_name 医院科室名称
		 * @param dept_name 医院科室名称
		 */
		public void setDept_name(String dept_name){
			this.dept_name = dept_name;
		}
		/**
		 * 设置 medins_hilist_codg 医疗机构三目录编码
		 * @param medins_hilist_codg 医疗机构三目录编码
		 */
		public void setMedins_hilist_codg(String medins_hilist_codg){
			this.medins_hilist_codg = medins_hilist_codg;
		}
		/**
		 * 设置 medins_hilist_name 医疗机构三目录名称
		 * @param medins_hilist_name 医疗机构三目录名称
		 */
		public void setMedins_hilist_name(String medins_hilist_name){
			this.medins_hilist_name = medins_hilist_name;
		}
		/**
		 * 设置 cchi_item_codg CCHI项目编码
		 * @param cchi_item_codg CCHI项目编码
		 */
		public void setCchi_item_codg(String cchi_item_codg){
			this.cchi_item_codg = cchi_item_codg;
		}
		/**
		 * 设置 cchi_itemname CCHI项目名称
		 * @param cchi_itemname CCHI项目名称
		 */
		public void setCchi_itemname(String cchi_itemname){
			this.cchi_itemname = cchi_itemname;
		}
		/**
		 * 设置 cchi_item_cont CCHI项目内涵
		 * @param cchi_item_cont CCHI项目内涵
		 */
		public void setCchi_item_cont(String cchi_item_cont){
			this.cchi_item_cont = cchi_item_cont;
		}
		/**
		 * 设置 lowval_mcs 低值耗材
		 * @param lowval_mcs 低值耗材
		 */
		public void setLowval_mcs(Double lowval_mcs){
			this.lowval_mcs = lowval_mcs;
		}
		/**
		 * 设置 art_dif 技术难度
		 * @param art_dif 技术难度
		 */
		public void setArt_dif(Double art_dif){
			this.art_dif = art_dif;
		}
		/**
		 * 设置 dr_psncnt 医生人数
		 * @param dr_psncnt 医生人数
		 */
		public void setDr_psncnt(Integer dr_psncnt){
			this.dr_psncnt = dr_psncnt;
		}
		/**
		 * 设置 medi_care_psncnt 医护人数
		 * @param medi_care_psncnt 医护人数
		 */
		public void setMedi_care_psncnt(Integer medi_care_psncnt){
			this.medi_care_psncnt = medi_care_psncnt;
		}
		/**
		 * 设置 med_tech_psncnt 医技人数
		 * @param med_tech_psncnt 医技人数
		 */
		public void setMed_tech_psncnt(Integer med_tech_psncnt){
			this.med_tech_psncnt = med_tech_psncnt;
		}
		/**
		 * 设置 work_attd_psncnt 工勤人数
		 * @param work_attd_psncnt 工勤人数
		 */
		public void setWork_attd_psncnt(Integer work_attd_psncnt){
			this.work_attd_psncnt = work_attd_psncnt;
		}
		/**
		 * 设置 ave_timecost 平均耗时
		 * @param ave_timecost 平均耗时
		 */
		public void setAve_timecost(Double ave_timecost){
			this.ave_timecost = ave_timecost;
		}
		/**
		 * 设置 timecost_emp 耗时单位
		 * @param timecost_emp 耗时单位
		 */
		public void setTimecost_emp(String timecost_emp){
			this.timecost_emp = timecost_emp;
		}
		/**
		 * 设置 pool_coty_codg 统筹区编码
		 * @param pool_coty_codg 统筹区编码
		 */
		public void setPool_coty_codg(String pool_coty_codg){
			this.pool_coty_codg = pool_coty_codg;
		}
		/**
		 * 设置 vali_flag 有效标志
		 * @param vali_flag 有效标志
		 */
		public void setVali_flag(String vali_flag){
			this.vali_flag = vali_flag;
		}

		/**
		 * 获取 dept_job_item_resu_cosm_cfg_tab_id 科室工作项目资源消耗配置表ID
		 * @return  dept_job_item_resu_cosm_cfg_tab_id
		 */
	    public String getDept_job_item_resu_cosm_cfg_tab_id(){
			return this.dept_job_item_resu_cosm_cfg_tab_id;
		}
		/**
		 * 获取 year 年度
		 * @return  year
		 */
	    public Integer getYear(){
			return this.year;
		}
		/**
		 * 获取 medins_no 医疗服务机构编号
		 * @return  medins_no
		 */
	    public String getMedins_no(){
			return this.medins_no;
		}
		/**
		 * 获取 dept_codg 医院科室编码
		 * @return  dept_codg
		 */
	    public String getDept_codg(){
			return this.dept_codg;
		}
		/**
		 * 获取 dept_name 医院科室名称
		 * @return  dept_name
		 */
	    public String getDept_name(){
			return this.dept_name;
		}
		/**
		 * 获取 medins_hilist_codg 医疗机构三目录编码
		 * @return  medins_hilist_codg
		 */
	    public String getMedins_hilist_codg(){
			return this.medins_hilist_codg;
		}
		/**
		 * 获取 medins_hilist_name 医疗机构三目录名称
		 * @return  medins_hilist_name
		 */
	    public String getMedins_hilist_name(){
			return this.medins_hilist_name;
		}
		/**
		 * 获取 cchi_item_codg CCHI项目编码
		 * @return  cchi_item_codg
		 */
	    public String getCchi_item_codg(){
			return this.cchi_item_codg;
		}
		/**
		 * 获取 cchi_itemname CCHI项目名称
		 * @return  cchi_itemname
		 */
	    public String getCchi_itemname(){
			return this.cchi_itemname;
		}
		/**
		 * 获取 cchi_item_cont CCHI项目内涵
		 * @return  cchi_item_cont
		 */
	    public String getCchi_item_cont(){
			return this.cchi_item_cont;
		}
		/**
		 * 获取 lowval_mcs 低值耗材
		 * @return  lowval_mcs
		 */
	    public Double getLowval_mcs(){
			return this.lowval_mcs;
		}
		/**
		 * 获取 art_dif 技术难度
		 * @return  art_dif
		 */
	    public Double getArt_dif(){
			return this.art_dif;
		}
		/**
		 * 获取 dr_psncnt 医生人数
		 * @return  dr_psncnt
		 */
	    public Integer getDr_psncnt(){
			return this.dr_psncnt;
		}
		/**
		 * 获取 medi_care_psncnt 医护人数
		 * @return  medi_care_psncnt
		 */
	    public Integer getMedi_care_psncnt(){
			return this.medi_care_psncnt;
		}
		/**
		 * 获取 med_tech_psncnt 医技人数
		 * @return  med_tech_psncnt
		 */
	    public Integer getMed_tech_psncnt(){
			return this.med_tech_psncnt;
		}
		/**
		 * 获取 work_attd_psncnt 工勤人数
		 * @return  work_attd_psncnt
		 */
	    public Integer getWork_attd_psncnt(){
			return this.work_attd_psncnt;
		}
		/**
		 * 获取 ave_timecost 平均耗时
		 * @return  ave_timecost
		 */
	    public Double getAve_timecost(){
			return this.ave_timecost;
		}
		/**
		 * 获取 timecost_emp 耗时单位
		 * @return  timecost_emp
		 */
	    public String getTimecost_emp(){
			return this.timecost_emp;
		}
		/**
		 * 获取 pool_coty_codg 统筹区编码
		 * @return  pool_coty_codg
		 */
	    public String getPool_coty_codg(){
			return this.pool_coty_codg;
		}
		/**
		 * 获取 vali_flag 有效标志
		 * @return  vali_flag
		 */
	    public String getVali_flag(){
			return this.vali_flag;
		}

		/**
		 * 转换为map对象
		 * @return Map
		 */
    	public java.util.Map toMap() {
			java.util.Map map = new java.util.HashMap();
				// dept_job_item_resu_cosm_cfg_tab_id 科室工作项目资源消耗配置表ID
				map.put("dept_job_item_resu_cosm_cfg_tab_id", getDept_job_item_resu_cosm_cfg_tab_id());
				// year 年度
				map.put("year", getYear());
				// medins_no 医疗服务机构编号
				map.put("medins_no", getMedins_no());
				// dept_codg 医院科室编码
				map.put("dept_codg", getDept_codg());
				// dept_name 医院科室名称
				map.put("dept_name", getDept_name());
				// medins_hilist_codg 医疗机构三目录编码
				map.put("medins_hilist_codg", getMedins_hilist_codg());
				// medins_hilist_name 医疗机构三目录名称
				map.put("medins_hilist_name", getMedins_hilist_name());
				// cchi_item_codg CCHI项目编码
				map.put("cchi_item_codg", getCchi_item_codg());
				// cchi_itemname CCHI项目名称
				map.put("cchi_itemname", getCchi_itemname());
				// cchi_item_cont CCHI项目内涵
				map.put("cchi_item_cont", getCchi_item_cont());
				// lowval_mcs 低值耗材
				map.put("lowval_mcs", getLowval_mcs());
				// art_dif 技术难度
				map.put("art_dif", getArt_dif());
				// dr_psncnt 医生人数
				map.put("dr_psncnt", getDr_psncnt());
				// medi_care_psncnt 医护人数
				map.put("medi_care_psncnt", getMedi_care_psncnt());
				// med_tech_psncnt 医技人数
				map.put("med_tech_psncnt", getMed_tech_psncnt());
				// work_attd_psncnt 工勤人数
				map.put("work_attd_psncnt", getWork_attd_psncnt());
				// ave_timecost 平均耗时
				map.put("ave_timecost", getAve_timecost());
				// timecost_emp 耗时单位
				map.put("timecost_emp", getTimecost_emp());
				// pool_coty_codg 统筹区编码
				map.put("pool_coty_codg", getPool_coty_codg());
				// vali_flag 有效标志
				map.put("vali_flag", getVali_flag());
				return map;
		}
	
}