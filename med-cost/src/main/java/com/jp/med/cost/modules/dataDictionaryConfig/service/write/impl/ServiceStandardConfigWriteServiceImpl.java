package com.jp.med.cost.modules.dataDictionaryConfig.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.exception.AppException;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import com.jp.med.cost.modules.dataDictionaryConfig.dto.ServiceStandardConfigDto;
import com.jp.med.cost.modules.dataDictionaryConfig.mapper.write.ServiceStandardConfigWriteMapper;
import com.jp.med.cost.modules.dataDictionaryConfig.service.write.ServiceStandardConfigWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
@Transactional
public class ServiceStandardConfigWriteServiceImpl extends ServiceImpl<ServiceStandardConfigWriteMapper, ServiceStandardConfigDto> implements ServiceStandardConfigWriteService {
    @Resource
    private ServiceStandardConfigWriteMapper serviceStandardConfigWriteMapper;

    @Override
    public void updateServiceStandardConfig(ServiceStandardConfigDto dto) {
        if (ValidateUtil.isEmpty(dto.getDept_job_item_resu_cosm_cfg_tab_id())) {
            throw new AppException("科室工作项目资源消耗配置表id不能为空");
        }

        int num = serviceStandardConfigWriteMapper.updateServiceStandardConfig(dto);

        if (num > 1) {
            throw new AppException("更新条数大于1");
        }
    }
}
