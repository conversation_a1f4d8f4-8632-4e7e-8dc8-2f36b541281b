package com.jp.med.cost.modules.common.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
/**
 * 医疗机构科室组织树
 */
public class DepartTreeListVo {
    /**
     * 医疗机构科室ID
     */
    private Long ids;

    /**
     * 定点医疗机构ID
     */
    private Long fix_medins_id;

    /**
     * 医疗服务机构编号
     */
    private String medins_no;

    /**
     * 科室分类
     */
    private String dept_obj_type;

    /**
     * 科室类别(0全院,1行政,2医辅,3医技,4临床)
     */
    private String dept_type;

    /**
     * 临床类别(1住院,2门诊)
     */
    private String clnc_type;

    /**
     * 科室编码
     */
    private String dept_codg;

    /**
     * 科室名称
     */
    private String dept_name;

    /**
     * 科室负责人
     */
    private String dept_resper;

    /**
     * 科室联系方式
     */
    private String dept_coninfo;

    /**
     * 科室使用面积
     */
    private String dept_used_area;

    /**
     * 床位数
     */
    private Integer bed_cnt;

    /**
     * 科室层次
     */
    private String dept_lv;

    /**
     * 开设日期
     */
    private Date open_date;

    /**
     * 上级医疗机构科室编码
     */
    private String prnt_medins_dept_codg;

    /**
     * 是否成本分摊(1是,0否)
     */
    private String is_cost_aprt;

    /**
     * 标准科室编码
     */
    private String std_dept_codg;

    /**
     * 标准科室名称
     */
    private String std_dept_name;

    /**
     * 是否全院顶级科室(1是,0否)
     */
    private String is_hosp_toplv_dept;

    /**
     * 当前有效标志
     */
    private String vali_flag;

    /**
     * 经办人
     */
    private String opter;

    /**
     * 经办时间
     */
    private Date opt_time;
    private List<DepartTreeListVo> children;
}
