package com.jp.med.cost.modules.extract.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.cost.modules.common.controller.HospBaseController;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import com.jp.med.cost.modules.extract.dto.CollectionConsumablesDto;
import com.jp.med.cost.modules.extract.service.read.CollectionConsumablesReadService;
import com.jp.med.cost.modules.extract.service.write.CollectionConsumablesWriteService;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(value = "科室耗材领取", tags = "科室耗材领取")
@RestController
@RequestMapping("CollectionConsumables")
@Validated
public class CollectionConsumablesController extends HospBaseController {
    private static final Logger logger = LoggerFactory.getLogger(CollectionConsumablesController.class);
    @Autowired
    private CollectionConsumablesReadService collectionConsumablesReadService;
    @Autowired
    private CollectionConsumablesWriteService collectionConsumablesWriteService;

    /**
     * 分页查询科室耗材领取记录
     *
     * @return
     */
    @PostMapping("queryCollectionConsumables")
    public CommonResult<?> queryCollectionConsumables(CollectionConsumablesDto dto, HttpServletRequest request) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        List page = collectionConsumablesReadService.queryCollectionConsumables(dto);
        return CommonResult.paging(page);
    }

    @RequestMapping("fnChangeCollection")
    public void fnChangeCollection(CollectionConsumablesDto dto, HttpServletRequest request) throws Exception {
        if (ValidateUtil.isEmpty(getOrgStatisticsData(request).getAkb020())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        collectionConsumablesWriteService.updateChangeCollection(dto);
    }

    /**
     * 增加收费项目
     *
     * @return
     */
    @RequestMapping("fnAddCollection")
    public void fnAddConfig(CollectionConsumablesDto dto, HttpServletRequest request) throws Exception {
        if (ValidateUtil.isEmpty(getOrgStatisticsData(request).getAkb020())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        collectionConsumablesWriteService.addCollection(dto);
    }

    /**
     * excl解析
     *
     * @param file
     */
    @RequestMapping("uploadFile")
    public void uploadFile(@RequestPart("files") MultipartFile file, @RequestParam("fileType") String fileType) {
        if (file.isEmpty()) {
            throw new AppException("上传的文件是空的");
        }
        try {
            collectionConsumablesWriteService.importData(file, fileType);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }
}
