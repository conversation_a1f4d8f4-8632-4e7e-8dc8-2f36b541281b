package com.jp.med.cost.modules.analysis.controller;


import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.cost.modules.analysis.dto.ServiceProjectCostDto;
import com.jp.med.cost.modules.analysis.service.read.DiseaseCostCalculationReadService;
import com.jp.med.cost.modules.analysis.vo.DiseaseCostCalculationVo;
import com.jp.med.cost.modules.analysis.vo.PatientCostServiceProjectVo;
import com.jp.med.cost.modules.common.constant.CostIConst;
import com.jp.med.cost.modules.common.controller.HospBaseController;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(value = "病种成本核算", tags = "病种成本核算")
@RestController
@RequestMapping("DiseaseCostCalculation")
@Validated
public class DiseaseCostCalculationRestService extends HospBaseController {
    @Autowired
    private DiseaseCostCalculationReadService diseaseCostCalculationReadService;

    /**
     * 查询病种成本信息
     *
     * @param dto
     */
    @PostMapping("/queryDiseaseCostCalculation")
    public CommonResult<?> queryDiseaseCostCalculation(@RequestBody ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }
        List<DiseaseCostCalculationVo> list = diseaseCostCalculationReadService.queryDiseaseCostCalculation(dto);
        return CommonResult.paging(list);
    }


    /**
     * 查询全病种数量及种均成本比例
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryAllDieaseCostInfo")
    public CommonResult<?> queryAllDieaseCostInfo(ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }
        dto.getStatisticsIssue();
        HashMap<String, Object> result = new HashMap<>();
        result.put("result", diseaseCostCalculationReadService.queryAllDieaseCostInfo(dto));
        return CommonResult.success(result);
    }

    /**
     * 查询详细病种成本和其他病种成本
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryDieaseDetail")
    public CommonResult<?> queryDieaseDetail(ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }
        dto.getStatisticsIssue();
        HashMap<String, Object> result = new HashMap<>();
        result.put("list", diseaseCostCalculationReadService.queryDieaseDetail(dto));
        return CommonResult.success(result);
    }

    /**
     * 查询病种可控成本和不可控成本
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryDieaseControllable")
    public CommonResult<?> queryDieaseControllable(ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }
        dto.getStatisticsIssue();
        HashMap<String, Object> result = new HashMap<>();
        result.put("list", diseaseCostCalculationReadService.queryDieaseControllable(dto));
        return CommonResult.success(result);
    }

    /**
     * 查询医生列表
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryDoctorList")
    public CommonResult<?> queryDoctorList(ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }

        HashMap<String, Object> result = new HashMap<>();
        result.put("list", diseaseCostCalculationReadService.queryDoctorList(dto));
        return CommonResult.success(result);

    }

    @PostMapping("/queryDieaseDetailInfo")
    public CommonResult<?> queryDieaseDetailInfo(ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }

        Map<String, Object> map = new HashMap<>(2);
        map.put("data", diseaseCostCalculationReadService.queryDieaseDetailInfo(dto));
        dto.setIssuess();
        map.put("list", diseaseCostCalculationReadService.queryDieaseCostTrend(dto));
        return CommonResult.success(map);
    }

    @PostMapping("/queryDieaseCostFateType")
    public CommonResult<?> queryDieaseCostFateType(ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }
        dto.setIssuess();
        List<Map<String, Object>> costFateType = diseaseCostCalculationReadService.queryDieaseCostFateType(dto);
        Map<String, List<Map<String, Object>>> map = new HashMap<>(12);
        if (ValidateUtil.isNotEmpty(costFateType)) {
            for (Map<String, Object> objectMap : costFateType) {
                for (Map.Entry<String, Object> entry : objectMap.entrySet()) {
                    if ("ake384".equalsIgnoreCase(entry.getKey())) {
                        List<Map<String, Object>> ake384s = map.get(entry.getValue());
                        if (ake384s == null) {
                            ake384s = new ArrayList<>();
                        }

                        ake384s.add(objectMap);
                        map.put(entry.getValue().toString(), ake384s);
                    }
                }
            }

        }
        HashMap<String, Object> result = new HashMap<>();
        result.put("list", map);
        return CommonResult.success(result);
    }

    /**
     * 查询主要诊断编码和主要手术编码的收费类型
     *
     * @param dto
     * @throws Exception
     */
    @PostMapping("/queryCostFateType")
    public CommonResult<?> queryCostFateType(ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }
        dto.getStatisticsIssue();

        HashMap<String, Object> result = new HashMap<>();
        result.put("list", diseaseCostCalculationReadService.queryCostFateType(dto));
        return CommonResult.success(result);
    }

    /**
     * 查询主要诊断和主要手术对应的服务项目
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryPatientProject")
    public CommonResult<?> queryPatientProject(ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }

        List<PatientCostServiceProjectVo> list = diseaseCostCalculationReadService.queryPatientProject(dto);
        return CommonResult.paging(list);

    }

    /**
     * 查询主要诊断和主要手术对应的标准服务项目
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryStandardService")
    public CommonResult<?> queryStandardService(ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }
        dto.getStatisticsIssue();

        List<PatientCostServiceProjectVo> list = diseaseCostCalculationReadService.queryStandardService(dto);
        return CommonResult.paging(list);

    }

    /**
     * 查询主要诊断和主要手术对应的标准服务项目汇总
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryStandardServiceSum")
    public CommonResult<?> queryStandardServiceSum(ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }
        dto.getStatisticsIssue();

        HashMap<String, Object> result = new HashMap<>();
        result.put("data", diseaseCostCalculationReadService.queryStandardServiceSum(dto));
        return CommonResult.success(result);

    }

    /**
     * 查询病种对应的各个服务单元成本
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryServiceUnitPart")
    public CommonResult<?> queryServiceUnitPart(ServiceProjectCostDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }

        HashMap<String, Object> result = new HashMap<>();
        result.put("data", diseaseCostCalculationReadService.queryServiceUnitPart(dto));
        return CommonResult.success(result);
    }
}
