package com.jp.med.cost.modules.config.dto;

import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.cost.modules.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class ThreeContentsConfigDto extends CommonQueryDto implements Serializable {
    private static final long serialVersionUID = 6662354608122133260L;
    /**
     * 社保三目录ID
     */
    private String si_hilist_id;
    /**
     * 社保三目录编码
     */
    private String si_hilist_codg;
    /**
     * 社保三目录名称
     */
    private String si_hilist_name;
    /**
     * 剂型
     */
    private String dosform;
    /**
     * 医疗发票项目类别
     */
    private String med_invo_item_type;
    /**
     * 收费项目等级
     */
    private String chrg_item_lv;
    /**
     * 三大目录类别
     */
    private String hilist_type;
    /**
     * 变更日期
     */
    private String chg_date;
    /**
     * 国家药品编码本位码
     */
    private String nat_drug_codg_stan_code;
    /**
     * 诊疗服务项目编码
     */
    private String trt_servitem_codg;
    /**
     * 备注
     */
    private String memo;
    /**
     * 是否生效
     */
    private String is_efft;
}
