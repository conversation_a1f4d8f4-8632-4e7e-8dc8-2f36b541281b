package com.jp.med.cost.modules.analysis.service.read;


import com.jp.med.cost.modules.analysis.dto.DiseaseGroupCostCalculationDto;
import com.jp.med.cost.modules.analysis.vo.DieaseDetailVo;
import com.jp.med.cost.modules.analysis.vo.GroupCostVo;
import com.jp.med.cost.modules.analysis.vo.ServiceProjectDetailVo;

import java.util.List;
import java.util.Map;

/**
 * 病组成本核算读取服务
 */
public interface DiseaseGroupCostCalculationReadService {

    /**
     * 查询病组成本
     * @param dto
     * @return
     */
    List<GroupCostVo> queryGroupCostPage(DiseaseGroupCostCalculationDto dto);

    /**
     * 查询成本可控性占比
     * @param dto
     * @return
     */
    GroupCostVo getGroupRatioInfo(DiseaseGroupCostCalculationDto dto);

    /**
     * 查询成本可控性占比信息
     * @param dto
     * @return
     */
    GroupCostVo getControlRatioInfo(DiseaseGroupCostCalculationDto dto);

    /**
     *
     * @param dto
     * @return
     */
    GroupCostVo getGroupInfo(DiseaseGroupCostCalculationDto dto);


    DieaseDetailVo queryDieaseGroupDetail(DiseaseGroupCostCalculationDto dto);

    List<ServiceProjectDetailVo> queryDieaseGroupDetailCount(DiseaseGroupCostCalculationDto dto);

    List<Map<String, Object>> queryDieaseGroupCostFateType(DiseaseGroupCostCalculationDto dto);
}
