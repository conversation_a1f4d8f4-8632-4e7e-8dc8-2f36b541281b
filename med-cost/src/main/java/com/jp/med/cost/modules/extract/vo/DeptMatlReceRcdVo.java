package com.jp.med.cost.modules.extract.vo;

import java.io.Serializable;

/**
 * class DeptMatlReceRcdVo
 * desc   科室材料领用记录 - dept_matl_rece_rcd  his 对接使用
 *
 * <AUTHOR>
 */
public class DeptMatlReceRcdVo implements Serializable {

    private static final long serialVersionUID = 917584511019908170L;
    /**
     * 材料领用ID
     * INTEGER
     * isNullable false
     */
    private Integer matl_rece_id;
    /**
     * 材料名称
     * VARCHAR(100)
     * isNullable true
     */
    private String matl_name;
    /**
     * 单价
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double pric;
    /**
     * 进价
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double purcpric;
    /**
     * 领用数量
     * INTEGER
     * isNullable true
     */
    private Integer rece_cnt;

    /**
     * 使用数量
     * INTEGER
     * isNullable true
     */
    private Integer used_cnt;
    /**
     * 单位
     * VARCHAR(16)
     * isNullable true
     */
    private String emp;
    /**
     * 领用科室编码
     * VARCHAR(20)
     * isNullable true
     */
    private String rece_dept_codg;
    /**
     * 领用科室名称
     * VARCHAR(50)
     * isNullable true
     */
    private String rece_dept_name;
    /**
     * 领用人姓名
     * VARCHAR(50)
     * isNullable true
     */
    private String recer_name;
    /**
     * 备注
     * VARCHAR(64)
     * isNullable true
     */
    private String memo;
    /**
     * 领用日期
     * VARCHAR(10)
     * isNullable true
     */
    private String rece_date;
    /**
     * 有效标志
     * VARCHAR(2)
     * isNullable true
     */
    private String vali_flag;


    /**
     * 设置 matl_rece_id 材料领用ID
     *
     * @param matl_rece_id 材料领用ID
     */
    public void setMatl_rece_id(Integer matl_rece_id) {
        this.matl_rece_id = matl_rece_id;
    }

    /**
     * 设置 matl_name 材料名称
     *
     * @param matl_name 材料名称
     */
    public void setMatl_name(String matl_name) {
        this.matl_name = matl_name;
    }

    /**
     * 设置 pric 单价
     *
     * @param pric 单价
     */
    public void setPric(Double pric) {
        this.pric = pric;
    }

    /**
     * 设置 purcpric 进价
     *
     * @param purcpric 进价
     */
    public void setPurcpric(Double purcpric) {
        this.purcpric = purcpric;
    }

    /**
     * 设置 rece_cnt 领用数量
     *
     * @param rece_cnt 领用数量
     */
    public void setRece_cnt(Integer rece_cnt) {
        this.rece_cnt = rece_cnt;
    }

    /**
     * 设置 used_cnt 使用数量
     *
     * @param used_cnt 使用数量
     */
    public void setUsed_cnt(Integer used_cnt) {
        this.used_cnt = used_cnt;
    }

    /**
     * 设置 emp 单位
     *
     * @param emp 单位
     */
    public void setEmp(String emp) {
        this.emp = emp;
    }

    /**
     * 设置 rece_dept_codg 领用科室编码
     *
     * @param rece_dept_codg 领用科室编码
     */
    public void setRece_dept_codg(String rece_dept_codg) {
        this.rece_dept_codg = rece_dept_codg;
    }

    /**
     * 设置 rece_dept_name 领用科室名称
     *
     * @param rece_dept_name 领用科室名称
     */
    public void setRece_dept_name(String rece_dept_name) {
        this.rece_dept_name = rece_dept_name;
    }

    /**
     * 设置 recer_name 领用人姓名
     *
     * @param recer_name 领用人姓名
     */
    public void setRecer_name(String recer_name) {
        this.recer_name = recer_name;
    }

    /**
     * 设置 memo 备注
     *
     * @param memo 备注
     */
    public void setMemo(String memo) {
        this.memo = memo;
    }

    /**
     * 设置 rece_date 领用日期
     *
     * @param rece_date 领用日期
     */
    public void setRece_date(String rece_date) {
        this.rece_date = rece_date;
    }

    /**
     * 设置 vali_flag 有效标志
     *
     * @param vali_flag 有效标志
     */
    public void setVali_flag(String vali_flag) {
        this.vali_flag = vali_flag;
    }

    /**
     * 获取 matl_rece_id 材料领用ID
     *
     * @return matl_rece_id
     */
    public Integer getMatl_rece_id() {
        return this.matl_rece_id;
    }

    /**
     * 获取 matl_name 材料名称
     *
     * @return matl_name
     */
    public String getMatl_name() {
        return this.matl_name;
    }

    /**
     * 获取 pric 单价
     *
     * @return pric
     */
    public Double getPric() {
        return this.pric;
    }

    /**
     * 获取 purcpric 进价
     *
     * @return purcpric
     */
    public Double getPurcpric() {
        return this.purcpric;
    }

    /**
     * 获取 rece_cnt 领用数量
     *
     * @return rece_cnt
     */
    public Integer getRece_cnt() {
        return this.rece_cnt;
    }

    /**
     * 获取 used_cnt 使用数量
     *
     * @return used_cnt
     */
    public Integer getUsed_cnt() {
        return this.used_cnt;
    }

    /**
     * 获取 emp 单位
     *
     * @return emp
     */
    public String getEmp() {
        return this.emp;
    }

    /**
     * 获取 rece_dept_codg 领用科室编码
     *
     * @return rece_dept_codg
     */
    public String getRece_dept_codg() {
        return this.rece_dept_codg;
    }

    /**
     * 获取 rece_dept_name 领用科室名称
     *
     * @return rece_dept_name
     */
    public String getRece_dept_name() {
        return this.rece_dept_name;
    }

    /**
     * 获取 recer_name 领用人姓名
     *
     * @return recer_name
     */
    public String getRecer_name() {
        return this.recer_name;
    }

    /**
     * 获取 memo 备注
     *
     * @return memo
     */
    public String getMemo() {
        return this.memo;
    }

    /**
     * 获取 rece_date 领用日期
     *
     * @return rece_date
     */
    public String getRece_date() {
        return this.rece_date;
    }

    /**
     * 获取 vali_flag 有效标志
     *
     * @return vali_flag
     */
    public String getVali_flag() {
        return this.vali_flag;
    }

    /**
     * 转换为map对象
     *
     * @return Map
     */
    public java.util.Map toMap() {
        java.util.Map map = new java.util.HashMap();
        // matl_rece_id 材料领用ID
        map.put("matl_rece_id", getMatl_rece_id());
        // matl_name 材料名称
        map.put("matl_name", getMatl_name());
        // pric 单价
        map.put("pric", getPric());
        // purcpric 进价
        map.put("purcpric", getPurcpric());
        // rece_cnt 领用数量
        map.put("rece_cnt", getRece_cnt());
        // used_cnt 使用数量
        map.put("used_cnt", getUsed_cnt());
        // emp 单位
        map.put("emp", getEmp());
        // rece_dept_codg 领用科室编码
        map.put("rece_dept_codg", getRece_dept_codg());
        // rece_dept_name 领用科室名称
        map.put("rece_dept_name", getRece_dept_name());
        // recer_name 领用人姓名
        map.put("recer_name", getRecer_name());
        // memo 备注
        map.put("memo", getMemo());
        // rece_date 领用日期
        map.put("rece_date", getRece_date());
        // vali_flag 有效标志
        map.put("vali_flag", getVali_flag());
        return map;
    }

}