package com.jp.med.cost.modules.extract.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.cost.modules.common.controller.HospBaseController;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import com.jp.med.cost.modules.extract.dto.DrugCollectionDto;
import com.jp.med.cost.modules.extract.service.read.DrugCollectionReadService;
import com.jp.med.cost.modules.extract.service.write.DrugCollectionWriteService;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(value = "科室药品领取", tags = "科室药品领取")
@RestController
@RequestMapping("DrugCollection")
@Validated
public class DrugCollectionController extends HospBaseController {
    private static final Logger logger = LoggerFactory.getLogger(DrugCollectionController.class);
    @Autowired
    private DrugCollectionReadService drugCollectionReadService;
    @Autowired
    private DrugCollectionWriteService drugCollectionWriteService;

    /**
     * 分页查询科室药品领取记录
     *
     * @return
     */
    @PostMapping("queryDrugCollection")
    public CommonResult queryDrugCollection(DrugCollectionDto dto, HttpServletRequest request) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        List page = drugCollectionReadService.queryDrugCollection(dto);
        return CommonResult.paging(page);
    }

    /**
     * 修改药品领取记录
     *
     * @param dto
     * @param request
     * @throws Exception
     */
    @RequestMapping("fnChangeDrugCollection")
    public void fnChangeDrugCollection(DrugCollectionDto dto, HttpServletRequest request) throws Exception {
        if (ValidateUtil.isEmpty(getOrgStatisticsData(request).getAkb020())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        drugCollectionWriteService.updateChangeDrugCollection(dto);
    }

    /**
     * 增加药品领取记录
     *
     * @return
     */
    @RequestMapping("fnAddDrugCollection")
    public void fnAddDrugCollection(DrugCollectionDto dto, HttpServletRequest request) throws Exception {
        if (ValidateUtil.isEmpty(getOrgStatisticsData(request).getAkb020())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        drugCollectionWriteService.addDrugCollection(dto);
    }

    /**
     * excl解析
     *
     * @param file
     */
    @RequestMapping("uploadFile")
    public void uploadFile(@RequestPart("files") MultipartFile file, @RequestParam("fileType") String fileType) {
        if (file.isEmpty()) {
            throw new AppException("上传的文件是空的");
        }
        try {
            drugCollectionWriteService.importData(file, fileType);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }
}
