package com.jp.med.cost.modules.analysis.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class DieaseDetailVo implements Serializable {

    private static final long serialVersionUID = -9022731977712314201L;

    /**
     * 病例数
     */
    private Integer num;

    /**
     * 病例数同比
     */
    private BigDecimal numlastradio;

    /**
     * 病例数环比
     */
    private BigDecimal numradio;

    /**
     * 例均费用
     */
    private BigDecimal akc264;

    /**
     * 例均费用同比
     */
    private BigDecimal akc264lastradio;

    /**
     * 例均费用环比
     */
    private BigDecimal akc264radio;

    /**
     * 单价成本
     */
    private BigDecimal kkh262;

    /**
     * 单价成本同比
     */
    private BigDecimal kkh262lastradio;

    /**
     * 单价成本环比
     */
    private BigDecimal kkh262radio;

    /**
     * 成本率
     */
    private BigDecimal costradio;

    /**
     * 成本率同比
     */
    private BigDecimal lastradio;

    /**
     * 成本率环比
     */
    private BigDecimal radio;
}
