package com.jp.med.cost.modules.analysis.controller;

import com.alibaba.fastjson.JSON;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.cost.modules.analysis.dto.BasicCostControlDto;
import com.jp.med.cost.modules.analysis.service.read.CostReportOutputReadService;
import com.jp.med.cost.modules.analysis.vo.CostingRepCfgVo;
import com.jp.med.cost.modules.common.constant.CostIConst;
import com.jp.med.cost.modules.common.controller.HospBaseController;
import com.jp.med.cost.modules.common.util.JsonFactory;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 成本报表输出
 */
@Api(value = "成本报表输出", tags = "成本报表输出")
@RestController
@RequestMapping("CostReportOutput")
@Validated
public class CostReportOutputRestService extends HospBaseController {

    private static final Logger logger = LoggerFactory.getLogger(CostReportOutputRestService.class);

    @Autowired
    private CostReportOutputReadService costReportOutputReadService;

    /**
     * 查询成本报表配置数据
     *
     * @return
     */
    @PostMapping("/queryCostReportTree")
    public CommonResult<?> queryCostReportTree(@RequestBody BasicCostControlDto dto, HttpServletRequest request) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException(CostIConst.HOSPITAL_ERROR_INFO);
        }
        Map<String, Object> map = new HashMap<>(3);
        map.put(CostIConst.HOSPITAL_CODE, dto.getMedins_no());
        map.put(CostIConst.HOSPITAL_NAME, dto.getMedins_name());
        List<CostingRepCfgVo> kah1Vos = costReportOutputReadService.queryCostReportTree();
        CostingRepCfgVo kah1Vo = null;
        if (ValidateUtil.isNotEmpty(kah1Vos)) {
            kah1Vo = kah1Vos.get(0);
            while (kah1Vo != null && ValidateUtil.isEmpty(kah1Vo.getPrnt_id()) && ValidateUtil.isNotEmpty(kah1Vo.getChildren())) {
                kah1Vo = kah1Vo.getChildren().get(0);
            }
            if (kah1Vo != null) {
                map.put("key", JSON.parseObject(JSON.toJSONString(kah1Vo), CostingRepCfgVo.class));
            }
        }
        map.put("data", kah1Vos);
        return CommonResult.success(map);
    }

    /**
     * 得到预览的pdf文件
     *
     * @param dto
     * @param request
     * @param response
     */
    @GetMapping({"/getPdf"})
    public void getPdf(@RequestBody BasicCostControlDto dto, HttpServletRequest request, HttpServletResponse response) {
        try {
            Map<String, Object> param = JsonFactory.json2bean(dto.getParamJson(), HashMap.class);
            String raqfilename = (String) param.get("raqfilename");

            if (StringUtils.isBlank(raqfilename)) {
                throw new AppException("报表标识不能为空");
            }

            if (ValidateUtil.isEmpty(param.get(CostIConst.HOSPITAL_CODE)) || ValidateUtil.isEmpty(param.get(CostIConst.HOSPITAL_NAME))) {
                param.put(CostIConst.HOSPITAL_CODE, dto.getMedins_no() == null ? "" : dto.getMedins_no());
                param.put(CostIConst.HOSPITAL_NAME, dto.getMedins_name() == null ? "" : dto.getMedins_name());
            }

            if (ValidateUtil.isEmpty(param.get("data_ym"))) {
                param.put("data_ym", "");
            }

            response.setHeader("Content-type", "application/pdf;charset=utf-8");
            //todo 输出报表
//            Context cxt = new Context();
//            ReportDefine reportDefine = RunqianReportUtil.getReportDefineWithChild(raqfilename);
//            cxt.setParamMap(param);
//            Engine engine = new Engine(reportDefine, cxt);
//            IReport iReport = engine.calc();
//            response.setContentType("application/pdf; charset=UTF-8");
//            ByteArrayOutputStream bos = new ByteArrayOutputStream();
//            ReportUtils.exportToPDF(bos, iReport, true, false);
//            response.getOutputStream().write(bos.toByteArray());
//            response.getOutputStream().flush();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } catch (Throwable throwable) {
            logger.error(throwable.getMessage(), throwable);
        }
    }

    /**
     * 导出所有报表
     *
     * @param dto
     * @param fileName
     * @param request
     * @param response
     */
    @PostMapping("exportAllFile")
    public void exportAllFile(@RequestBody BasicCostControlDto dto, String fileName, HttpServletRequest request, HttpServletResponse response) throws IOException {
        List<String> fileNames = costReportOutputReadService.queryCostReportFileName();

        if (ValidateUtil.isNotEmpty(fileNames)) {
            Map<String, Object> param = JsonFactory.json2bean(dto.getParamJson(), HashMap.class);

            fileNames = fileNames.stream().filter((item) -> !ValidateUtil.isEmpty(item)).collect(Collectors.toList());
//            todo 输出报表
//            Engine engine = null;
//            ReportDefine reportDefine = null;
//            IReport iReport = null;

            File file = null;
            OutputStream out = null;
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ZipOutputStream outputStream = null;
            try {
                outputStream = new ZipOutputStream(response.getOutputStream());
                String downloadFileName = URLEncoder.encode(fileName, "UTF-8");
                response.setHeader("content-type", "application/octet-stream");
                response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
                response.setContentType("application/pdf; charset=UTF-8");
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + new String((downloadFileName).getBytes(), "UTF-8"));

                for (String name : fileNames) {
                    try {
//                        todo 输出报表
//                        Context cxt = new Context();
//                        cxt.setParamMap(param);
//                        reportDefine = RunqianReportUtil.getReportDefineWithChild(name);
//                        engine = new Engine(reportDefine, cxt);
//                        iReport = engine.calc();
//                        ReportUtils.exportToPDF(bos, iReport, true, false);
//                        byte[] bytes = bos.toByteArray();
//                        outputStream.putNextEntry(new ZipEntry(name + DrgIConst.PDF_EXT));
//                        outputStream.write(bytes, 0, bytes.length);
                    } catch (Exception e) {
                        e.printStackTrace();
                        logger.error("{} 导出出错!!!", name + CostIConst.PDF_EXT);
                    }

                }
                outputStream.flush();
            } catch (Exception e) {
                e.printStackTrace();
            } catch (Throwable throwable) {
                throwable.printStackTrace();
            } finally {
                if (outputStream != null) {
                    outputStream.close();
                }
            }
        } else {
            logger.warn("没有要打印的报表");
        }
    }

    /**
     * 医院科室医疗成本表pdf
     *
     * @param paramJson
     * @param request
     * @param response
     */
    @RequestMapping({"/getHospitalDepartmentPdf"})
    public void getHospitalDepartmentPdf(String paramJson, HttpServletRequest request, HttpServletResponse response) {
        try {
            Map<String, Object> param = JsonFactory.json2bean(paramJson, HashMap.class);
            String raqfilename = (String) param.get("raqfilename");

            if (StringUtils.isBlank(raqfilename)) {
                throw new AppException("报表标识不能为空");
            }

            response.setHeader("Content-type", "application/pdf;charset=utf-8");

//            Context cxt = new Context();
//            ReportDefine reportDefine = RunqianReportUtil.getReportDefineWithChild(raqfilename);
//            cxt.setParamMap(param);
//            Engine engine = new Engine(reportDefine, cxt);
//            IReport iReport = engine.calc();
//            response.setContentType("application/pdf; charset=UTF-8");
//            ByteArrayOutputStream bos = new ByteArrayOutputStream();
//            ReportUtils.exportToPDF(bos, iReport, true, false);
//            response.getOutputStream().write(bos.toByteArray());
//            response.getOutputStream().flush();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } catch (Throwable throwable) {
            logger.error(throwable.getMessage(), throwable);
        }
    }
}
