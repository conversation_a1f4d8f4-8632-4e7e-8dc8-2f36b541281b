package com.jp.med.cost.modules.extract.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.cost.modules.common.constant.CostIConst;
import com.jp.med.cost.modules.common.controller.HospBaseController;
import com.jp.med.cost.modules.common.util.BeanMapUtil;
import com.jp.med.cost.modules.common.util.ExcelUtil;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import com.jp.med.cost.modules.common.vo.ErrorResponse;
import com.jp.med.cost.modules.common.vo.HospChrgItemDetlVo;
import com.jp.med.cost.modules.common.vo.MedinsCostItemCfgVo;
import com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto;
import com.jp.med.cost.modules.extract.service.read.BasicDataTransmissionReadService;
import com.jp.med.cost.modules.extract.service.write.BasicDataTransmissionWriteService;
import com.jp.med.cost.modules.extract.vo.*;
import io.swagger.annotations.Api;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 基础数据传输后台编写
 * </p>
 *
 * <AUTHOR>
 * @since 2021/7/22
 */
@Api(value = "基础数据传输", tags = "基础数据传输")
@RestController
@RequestMapping("BasicDataTransmission")
@Validated
public class BasicDataTransmissionController extends HospBaseController {
    private static final Logger logger = LoggerFactory.getLogger(BasicDataTransmissionController.class);
    @Resource
    private BasicDataTransmissionReadService basicDataTransmissionReadService;

    @Resource
    private BasicDataTransmissionWriteService basicDataTransmissionWriteService;

    /**
     * 模板文件名称
     */
    private static Map<String, String> templateNames = new HashMap<>();

    public BasicDataTransmissionController() {
        templateNames.put(CostIConst.COST_TEMPLATE_TYPE_1, "人力成本表");
        templateNames.put(CostIConst.COST_TEMPLATE_TYPE_2, "资产成本表");
        templateNames.put(CostIConst.COST_TEMPLATE_TYPE_3, "其他成本表");
    }

    /**
     * 分页查询收费项目明细
     *
     * @param dto
     */
    @PostMapping("queryChargeDetailsPage")
    public CommonResult<?> queryChargeDetailsPage(@RequestBody BasicDataTransmissionDto dto) {
        List<HospChrgItemDetlVo> page = basicDataTransmissionReadService.queryChargeDetailsPage(dto);
        return CommonResult.paging(page);
    }

    /**
     * 分页查询药品和耗材数据
     *
     * @param dto
     */
    @PostMapping("queryConsumableAndDrugsPage")
    public CommonResult<?> queryConsumableAndDrugsPage(@RequestBody BasicDataTransmissionDto dto) {
        List<DrugMcsReceRcdVo> page = basicDataTransmissionReadService.queryConsumableAndDrugsPage(dto);
        return CommonResult.paging(page);
    }

    /**
     * 查询医疗保障基金数据
     *
     * @param dto
     */
    @PostMapping("queryMedicalFundListPage")
    public CommonResult<?> queryMedicalFundListPage(@RequestBody BasicDataTransmissionDto dto) {
        List<MedRgoldExtaRcdVo> page = basicDataTransmissionReadService.queryMedicalFundListPage(dto);
        // 判断是否存在当前期号的数据，没有则从结算明细里面抽取
        if (ValidateUtil.isEmpty(page) || page.size() == 0) {
            int count = basicDataTransmissionWriteService.extractMedicalFund(dto);
            if (count > 0) {
                page = basicDataTransmissionReadService.queryMedicalFundListPage(dto);
            }
        }
        return CommonResult.paging(page);
    }

    /**
     * 查询医疗保障基金数据
     *
     * @param dto
     */
    @PostMapping("queryPersonnelExpensesPage")
    public CommonResult<?> queryPersonnelExpensesPage(@RequestBody BasicDataTransmissionDto dto) throws Exception {
        // 查询人力成本项
        dto.setCost_type(CostIConst.KKH206_1);
        List<MedinsCostItemCfgVo> kkp8s = basicDataTransmissionReadService.queryKkp8List(dto);
        String str = "";
        for (MedinsCostItemCfgVo kkp8 : kkp8s) {
            str += ("'F" + kkp8.getCost_codg() + "' " + "F" + kkp8.getCost_codg() + ",");
        }
        str = str.substring(0, str.length() - 1);
        dto.setLabr_cost_codg_fields(str);
        List<Map<String, Object>> page = basicDataTransmissionReadService.queryPersonnelExpensesPage(dto);
        return CommonResult.paging(page);
    }

    /**
     * 查询资产数据
     *
     * @param dto
     */
    @PostMapping("queryAssetsPage")
    public CommonResult<?> queryAssetsPage(@RequestBody BasicDataTransmissionDto dto) throws Exception {
        List<DeptAsetAsgnRcdVo> page = basicDataTransmissionReadService.queryAssetsPage(dto);
        return CommonResult.success(page);
    }

    /**
     * 检查指定月份的数据是否存在
     *
     * @param dto
     */
    @PostMapping("checkMonthData")
    public CommonResult<?> checkMonthData(@RequestBody BasicDataTransmissionDto dto) {
        BasicDataTransmissionVo basicDataTransmissionVo = basicDataTransmissionReadService.checkMonthData(dto);
        HashMap resultMap = new HashMap<>();
        resultMap.put("checkCount", basicDataTransmissionVo.getCheckCount());
        return CommonResult.success(resultMap);
    }

    /**
     * 设置材料药品溢价比例
     *
     * @param dto
     */
    @PostMapping("setPremiumRatio")
    public CommonResult<?> setPremiumRatio(@RequestBody BasicDataTransmissionDto dto) {
        int num = basicDataTransmissionWriteService.setPremiumRatio(dto);
        HashMap resultMap = new HashMap<>();
        resultMap.put("updateCount", num);
        return CommonResult.success(resultMap);
    }

    /**
     * 获取指定期号药品耗材溢价比例
     *
     * @param dto
     */
    @PostMapping("getPremiumRatio")
    public CommonResult<?> getPremiumRatio(@RequestBody BasicDataTransmissionDto dto) {
        BigDecimal ratio = basicDataTransmissionReadService.getPremiumRatio(dto);
        if (ValidateUtil.isEmpty(ratio)) {
            // 默认溢价比例设置为10%
            ratio = new BigDecimal(10);
        }
        HashMap resultMap = new HashMap<>();
        resultMap.put("premiumRatio", ratio);
        return CommonResult.success(resultMap);
    }

    /**
     * 提取指定期号的医疗风险金
     *
     * @param dto
     */
    @PostMapping("extractMedicalFund")
    public CommonResult<?> extractMedicalFund(@RequestBody BasicDataTransmissionDto dto) {
        int count = basicDataTransmissionWriteService.extractMedicalFund(dto);
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("updateCount", count);
        return CommonResult.success(result);
    }

    /**
     * 下载模板
     *
     * @param dto
     */
    @PostMapping("downloadTemplate")
    @ResponseBody
    public void downloadTemplate(@RequestBody BasicDataTransmissionDto dto, HttpServletResponse response) {
        // 获取模板文件
        System.out.print(dto);
        try {
            writeTemplate(dto, response);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("[模板加载失败]" + e.getMessage());
        }
    }

    @PostMapping("fileUpload")
    public CommonResult<?> fileUpload(@RequestBody BasicDataTransmissionDto dto, HttpServletRequest request) throws Exception {
        MultipartHttpServletRequest multiPartRequest = (MultipartHttpServletRequest) request;
        // 获取请求中的EXCEL文件
        MultipartFile file = multiPartRequest.getFile("file");
        try {
            parseExcelData(dto, file);
            Map<String, Object> result = new HashMap<String, Object>();
            result.put("status", 1);
            return CommonResult.success(result);
        } catch (Exception e) {
            logger.error(e.getMessage());
            Map<String, Object> result = new HashMap<String, Object>();
            result.put("status", 0);
            result.put("error", e.getMessage());
            return CommonResult.success(result);
        }

    }

    /**
     * 查询成本项
     *
     * @param dto
     * @throws Exception
     */
    @PostMapping("queryCostItem")
    public CommonResult<?> queryCostItem(@RequestBody BasicDataTransmissionDto dto) throws Exception {
        dto.setMedins_no(dto.getHospitalId());
        List<MedinsCostItemCfgVo> list = basicDataTransmissionReadService.queryKkp8List(dto);
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("costItem", list);
        return CommonResult.success(result);
    }


    /**
     * 查询上一期人员经费期号
     *
     * @param dto
     * @throws Exception
     */
    @PostMapping("checkPersonnelExpensesPreData")
    public CommonResult<?> checkPersonnelExpensesPreData(@RequestBody BasicDataTransmissionDto dto) throws Exception {
        dto.setMedins_no(dto.getHospitalId());
        String preIssue = basicDataTransmissionReadService.checkPersonnelExpensesPreData(dto);
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("issue", preIssue);
        return CommonResult.success(result);
    }

    /**
     * 查询上一期人员经费期号
     *
     * @param dto
     * @throws Exception
     */
    @PostMapping("usePersonnelExpensesPreData")
    public CommonResult<?> usePersonnelExpensesPreData(@RequestBody BasicDataTransmissionDto dto) throws Exception {
        int num = basicDataTransmissionWriteService.usePersonnelExpensesPreData(dto);
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("updateCount", num);
        return CommonResult.success(result);
    }

    /**
     * 其他成本文件上传
     *
     * @param dto
     * @param request
     * @throws Exception
     */
    @PostMapping("otherCostFileUpload")
    public CommonResult<?> otherCostFileUpload(@RequestBody BasicDataTransmissionDto dto, HttpServletRequest request) throws Exception {
        MultipartHttpServletRequest multiPartRequest = (MultipartHttpServletRequest) request;
        Map<String, Object> result = new HashMap<String, Object>();
        // 获取请求中的EXCEL文件
        MultipartFile file = multiPartRequest.getFile("file");
        try {
            String originalFilename = file.getOriginalFilename();

            boolean isExcelFile = ExcelUtil.isExcelFile(originalFilename);

            long size = file.getSize();
            int fileSizeEndWithMb = (int) Math.floor((double) size / (1024 * 1024));

            if (!isExcelFile || fileSizeEndWithMb > 10) {
                result.put("status", 0);
                result.put("error", "文件后缀不为Excel格式，或文件已大于10M");
            } else {
                List<ErrorResponse> errorResponses = parseExcelData(dto, file);
                if (errorResponses == null || errorResponses.isEmpty()) {
                    result.put("status", 1);
                } else {
                    result.put("status", 1);
                    result.put("errorResponses", errorResponses);
                }

            }
            return CommonResult.success(result);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("status", 0);
            result.put("error", "解析excel文件出错，请联系管理员或检查excel文件是否正确");
            return CommonResult.success(result);
        }

    }

    /**
     * 查询其他成本
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryOtherCost")
    public CommonResult<?> queryOtherCost(BasicDataTransmissionDto dto) throws Exception {
        return CommonResult.paging(basicDataTransmissionReadService.queryOtherCost(dto));
    }

    /**
     * 查询期号下是否有其他成本数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/queryOtherCostNumByIssue")
    public CommonResult<?> queryOtherCostNumByIssue(BasicDataTransmissionDto dto) throws Exception {
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("num", basicDataTransmissionReadService.queryOtherCostNumByIssue(dto));
        return CommonResult.success(result);
    }

    /**
     * 解析EXCEL表格数据，并保存数据
     */
    private List<ErrorResponse> parseExcelData(BasicDataTransmissionDto dto, MultipartFile file) throws Exception {
        List<ErrorResponse> errorResponses = new ArrayList<>();
        InputStream in = file.getInputStream();
        Workbook wb = WorkbookFactory.create(file.getInputStream());
        Sheet sheet = wb.getSheetAt(0);
        try {
            // 获取模板中的期号
            // ExcelUtil.getDataListFromExcelSheet();
            String title = sheet.getRow(0).getCell(0).getStringCellValue();
            if (ValidateUtil.isEmpty(title)) {
                errorResponses.add(new ErrorResponse("获取模板标题失败", "请检查上传的文件数据"));
                return errorResponses;
            }
            String issue = title.substring(title.length() - 7, title.length()).replace("-", "");
            if (!issue.equals(dto.getIssue())) {
                errorResponses.add(new ErrorResponse("上传文件期号与当前期号不一致", "请确保上传表格内容数据期号与当前期号一致"));
                return errorResponses;
            }
            List<String> fieldsList = null;
            switch (dto.getTemplate_type()) {
                case "1":
                    errorResponses.addAll(parsePersonnelExpenses(dto, sheet));
                    break;
                case "2":
                    errorResponses.addAll(parseAssets(dto, sheet));
                    break;
                case "3":
                    dto.setCost_type(CostIConst.KKH206_7);
                    List<MedinsCostItemCfgVo> kkp8s = basicDataTransmissionReadService.queryKkp8List(dto);
                    fieldsList = new ArrayList<>();
                    // 科室编码
                    fieldsList.add("akf001");
                    // 科室名称
                    fieldsList.add("bkf001");
                    // 其他成本名称
                    fieldsList.add("kkh255");
                    //  成本金额
                    fieldsList.add("kkh256Str");
                    String[] fieldsStr = new String[fieldsList.size()];
                    fieldsList.toArray(fieldsStr);
                    // List<HashMap> mapList = ExcelUtil.getDataListFromExcelSheet(sheet, fieldsStr, 2, 1, HashMap.class);
                    List<HashMap> mapList = ExcelUtil.getDataListFromExcelSheet(sheet, fieldsStr, 2, 0, HashMap.class);
                    Map map = ValidateOtherCostData(mapList, dto);
                    List<OthCostDetlVo> kkq5VoList = (List<OthCostDetlVo>) map.get("list");
                    errorResponses.addAll((List<ErrorResponse>) map.get("validate"));
                    if (errorResponses == null || errorResponses.isEmpty()) {
                        basicDataTransmissionWriteService.saveOtherCost(kkq5VoList, kkp8s, dto);
                    }
                    break;
            }
            if (errorResponses == null || errorResponses.isEmpty()) {
                dto.setFile_is_upld_succ(CostIConst.AAE100_1);
            } else {
                dto.setFile_is_upld_succ(CostIConst.AAE100_0);
            }
            basicDataTransmissionWriteService.insertImportCostRecord(dto, file);
        } catch (Exception e) {
            e.printStackTrace();
            dto.setFile_is_upld_succ(CostIConst.AAE100_0);
            /**
             * 保存文件上传记录及保存上传的文件
             */
            basicDataTransmissionWriteService.insertImportCostRecord(dto, file);

        } finally {
            in.close();
        }

        return errorResponses;
    }

    /**
     * 解析人员薪资数据
     *
     * @param dto
     * @param sheet
     * @throws Exception
     */
    private List<ErrorResponse> parsePersonnelExpenses(BasicDataTransmissionDto dto, Sheet sheet) {
        List<ErrorResponse> errorList = new ArrayList<>();
        // 查询人员薪资成本配置项
        dto.setCost_type(CostIConst.KKH206_1);
        List<MedinsCostItemCfgVo> kkp8s = basicDataTransmissionReadService.queryKkp8List(dto);
        List<String> fieldsList = new ArrayList<>();
        // 科室编码
        fieldsList.add("dept_codg");
        // 科室名称
        fieldsList.add("dept_name");
        // 科室类别
        fieldsList.add("dept_type");
        // 人员类别
        fieldsList.add("psn_type");
        // 人数
        fieldsList.add("psncnt");
        // 动态设置成本项
        for (MedinsCostItemCfgVo kkp8 : kkp8s) {
            fieldsList.add("F" + kkp8.getCost_codg());
        }
        String[] fields = new String[fieldsList.size()];
        fieldsList.toArray(fields);
        List<HashMap> list = ExcelUtil.getDataListFromExcelSheet(sheet, fields, 2, 0, HashMap.class);
        if (list.size() == 0) {
            errorList.add(new ErrorResponse("当前上传文件无数据", "请检查上传的文件数据"));
        }
        basicDataTransmissionWriteService.savePersonnelExpenses(list, fields, kkp8s, dto);
        return errorList;
    }

    /**
     * 解析资产数据上传
     *
     * @param dto
     * @param sheet
     * @throws Exception
     */
    private List<ErrorResponse> parseAssets(BasicDataTransmissionDto dto, Sheet sheet) throws Exception {
        List<ErrorResponse> errorList = new ArrayList<>();
        // 成本类型名称Map
        Map<String, String> kkh246Map = new HashMap<>();
        kkh246Map.put("固定资产", CostIConst.KKH206_4);
        kkh246Map.put("无形资产", CostIConst.KKH206_5);
        Map<String, String> kkh251Map = new HashMap<>();
        kkh251Map.put("直线折旧", CostIConst.KKH251_1);
        kkh251Map.put("加速折旧", CostIConst.KKH251_2);
        Map<String, MedinsDeptVo> deptNameMap = basicDataTransmissionReadService.getDeptNameMap(dto.getMedins_no());
        List<MedinsCostItemCfgVo> kkp8sAll = new ArrayList<>();
        dto.setCost_type(CostIConst.KKH206_4);
        List<MedinsCostItemCfgVo> kkp8s = basicDataTransmissionReadService.queryKkp8List(dto);
        kkp8sAll.addAll(kkp8s);
        dto.setCost_type(CostIConst.KKH206_5);
        kkp8s = basicDataTransmissionReadService.queryKkp8List(dto);
        kkp8sAll.addAll(kkp8s);
        Map<String, MedinsCostItemCfgVo> kkp8NameMap = new HashMap<>();
        kkp8sAll.forEach(item -> {
            kkp8NameMap.put(item.getCost_name(), item);
        });
        // 1. 校验数据是否为空
        if (sheet.getLastRowNum() == 1) {
            errorList.add(new ErrorResponse("未找到【" + dto.getIssue() + "】的数据", "请检查上传文件中的内容"));
            return errorList;
        }
        // 2. 检查上传模板字段是否存在
        String[] columns = {"资产编码", "资产名称", "成本名称", "成本类型", "使用科室编码", "使用科室", "资产入账时间", "单位原值（元）", "数量", "折旧年限（月）", "折旧方式"};
        Row headerRow = sheet.getRow(1);
        List<String> tableColumn = new ArrayList<>();
        List<String> notInColumn = new ArrayList<>();
        logger.info("headerRow.getPhysicalNumberOfCells() INFO ", headerRow.getPhysicalNumberOfCells());
        for (int i = 0; i < headerRow.getPhysicalNumberOfCells(); i++) {
            tableColumn.add(headerRow.getCell(i).getStringCellValue().replace(" ", ""));
        }
        for (String column : columns) {
            if (!tableColumn.contains(column)) {
                notInColumn.add(column);
            }
        }
        if (notInColumn.size() > 0) {
            errorList.add(new ErrorResponse("未找到【" + String.join("、", notInColumn) + "】列名", "使用模板文件进行导入"));
            return errorList;
        }
        // 解析表格数据
        List<HashMap> list = ExcelUtil.getDataListFromExcelSheet(sheet, columns, 2, 0, HashMap.class);
        List<DeptAsetAsgnRcdVo> kkq4List = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 3. 检查数据中的
        for (HashMap<String, Object> item : list) {
            DeptAsetAsgnRcdVo kkq4 = new DeptAsetAsgnRcdVo();
            // 3.判断成本名称是否存在
            if (!kkp8NameMap.keySet().contains(item.get(columns[2]))) {
                errorList.add(new ErrorResponse("成本名称【" + item.get(columns[2]) + "】不存在", "请在“数据字典配置 - 成本数据配置”中添加"));
                return errorList;
            }
            // 设置医疗机构编号
            kkq4.setMedins_no(dto.getMedins_no());
            // 设置文件上传期号
            kkq4.setData_ym(Integer.valueOf(dto.getIssue()));
            // 设置成本名称
            kkq4.setCost_name((String) item.get(columns[2]));
            // 设置成本编号
            kkq4.setAset_cost_codg(kkp8NameMap.get((String) item.get(columns[2])).getCost_codg());
            // 设置上传期号
            // 4. 判断成本类型是否存在
            if (!kkh246Map.keySet().contains(item.get(columns[3]))) {
                errorList.add(new ErrorResponse("成本类型【" + item.get(columns[3]) + "】不存在", "请检查成本类型数据"));
                return errorList;
            }
            kkq4.setCost_type(kkh246Map.get(item.get(columns[3])));
            // 5. 判断科室名称是否存在
            if (!deptNameMap.keySet().contains(item.get(columns[5]))) {
                errorList.add(new ErrorResponse("科室名称【" + item.get(columns[5]) + "】不存在", "请检查【" + item.get(columns[5]) + "】使用科室数据"));
                return errorList;
            }
            // 设置科室编码
            kkq4.setDept_codg(deptNameMap.get(item.get(columns[5])).getDept_codg());
            // 设置科室名称
            kkq4.setDept_name((String) item.get(columns[5]));
            // 设置科室类型
            kkq4.setDept_type(deptNameMap.get(item.get(columns[5])).getDept_type());
            // 6. 入账时间判断
            String dateStr = String.valueOf(item.get(columns[6]));
            dateStr = dateStr.replace("-", "");
            dateStr = dateStr.substring(0, 6);
            Integer issueData = Integer.valueOf(dateStr);
            Integer issue = Integer.valueOf(dto.getIssue());
            if (issueData > issue) {
                errorList.add(new ErrorResponse("资产入账时间【" + item.get(columns[6]) + "】错误", "资产入账时间仅能在当前选择的上传月份之前"));
                return errorList;
            }
            // 设置入帐时间
            kkq4.setAset_booked_in_time(Timestamp.valueOf((String) item.get(columns[6])));
            // 7. 检查单位原值
            try {
                BigDecimal kkh248 = new BigDecimal((String) item.get(columns[7]));
                if (kkh248.compareTo(new BigDecimal("0")) < 0) {
                    errorList.add(new ErrorResponse("单位原值【" + item.get(columns[7]) + "】错误", "请检查【" + item.get(columns[7]) + "】（返回校验失败的单位原值）单位原值数据"));
                    return errorList;
                }
                // 设置单位原值
                kkq4.setEmp_initval(kkh248);
            } catch (Exception e) {
                errorList.add(new ErrorResponse("单位原值【" + item.get(columns[7]) + "】错误", "请检查【" + item.get(columns[7]) + "】（返回校验失败的单位原值）单位原值数据"));
                return errorList;
            }
            // 8. 检查数量
            try {
                Integer kkh249 = new Integer((String) item.get(columns[8]));
                if (kkh249.compareTo(new Integer("0")) < 0) {
                    errorList.add(new ErrorResponse("数量【" + item.get(columns[8]) + "】错误", "请检查【" + item.get(columns[8]) + "】（返回校验失败的数量）数量数据"));
                    return errorList;
                }
                // 设置数量
                kkq4.setCnt(BigDecimal.valueOf(kkh249));
            } catch (Exception e) {
                errorList.add(new ErrorResponse("单位原值【" + item.get(columns[8]) + "】错误", "请检查【" + item.get(columns[8]) + "】（返回校验失败的单位原值）单位原值数据"));
                return errorList;
            }
            // 9. 检查折旧方式
            if (!kkh251Map.keySet().contains(item.get(columns[10]))) {
                errorList.add(new ErrorResponse("折旧方式【" + item.get(columns[10]) + "】不存在", "请检查折旧方式数据"));
                return errorList;
            }
            // 设置折旧期限
            kkq4.setDepr_ddln(new BigDecimal((String) item.get(columns[9])));
            // 设置折旧方式
            kkq4.setDepr_way(kkh251Map.get(item.get(columns[10])));
            // 设置有效方式
            kkq4.setVali_flag(CostIConst.AAE100_1);
            // 设置资产编码
            kkq4.setAset_codg((String) item.get(columns[0]));
            // 设置资产名称
            kkq4.setAset_name((String) item.get(columns[1]));
            kkq4List.add(kkq4);
        }
        List<String> kkh243List = kkq4List.stream().map(DeptAsetAsgnRcdVo::getAset_codg)
                .collect(Collectors.toList());
        long count = kkh243List.stream().distinct().count();
        if (count != kkh243List.size()) {
            errorList.add(new ErrorResponse("资产编码重复", "请检查资产编码数据"));
            return errorList;
        }
        int num = basicDataTransmissionWriteService.saveAssets(kkq4List, dto);
        return errorList;
    }

    /**
     * 写入文件导入模板
     *
     * @param dto
     * @param response
     * @throws IOException
     */
    private void writeTemplate(BasicDataTransmissionDto dto, HttpServletResponse response) throws IOException {
        String filepath = "template" + File.separator + templateNames.get(dto.getType()) + ".xlsx";
        InputStream in = this.getClass().getClassLoader().getResourceAsStream(filepath);
        XSSFWorkbook workbook = new XSSFWorkbook(in);
        in.close();
        makeTemplate(dto, workbook);
        workbook.write(response.getOutputStream());
        response.getOutputStream().flush();
        response.getOutputStream().close();
    }

    /**
     * 构建模板
     */
    private void makeTemplate(BasicDataTransmissionDto dto, XSSFWorkbook workbook) {
        XSSFSheet sheet = workbook.getSheetAt(0);
        // 表格第一行，第一列为模板标题，设根据传入参数设置模板标题
        XSSFCell cell = sheet.getRow(0).getCell(0);
        // 设置单元格的值
        cell.setCellValue(dto.getMedins_name() + templateNames.get(dto.getType()) + " " + dto.getIssue());
        if (CostIConst.COST_TEMPLATE_TYPE_1.equals(dto.getType())) {
            // 查询人力成本列表
            dto.setCost_type(CostIConst.KKH206_1);
            List<MedinsCostItemCfgVo> kkp8s = basicDataTransmissionReadService.queryKkp8List(dto);
            // 获取header
            XSSFRow header = sheet.getRow(1);
            // 动态添加成本列 获取第一行样式
            XSSFCellStyle style = header.getCell(0).getCellStyle();
            for (int i = 0; i < kkp8s.size(); i++) {
                XSSFCell c = header.createCell(i + 5);
                c.setCellStyle(style);
                c.setCellValue(kkp8s.get(i).getCost_name());
            }

        }
    }

    /**
     * 校验其他成本数据
     *
     * @param list
     * @return
     */
    private Map ValidateOtherCostData(List<HashMap> list, BasicDataTransmissionDto dto) {
        List<ErrorResponse> errorResponses = new ArrayList<>();
        Map<String, Object> result = new HashMap<>(2);
        if (ValidateUtil.isNotEmpty(list)) {
            List<MedinsCostItemCfgVo> allCost = basicDataTransmissionReadService.queryAllOtherCost(dto);

            List<String> notOtherCostName = null;
            List<String> otherCostName = null;

            if (ValidateUtil.isNotEmpty(allCost)) {
                notOtherCostName = allCost.stream().filter(kkp8Vo -> {
                    return "1".equals(kkp8Vo.getKey());
                }).map(kkp8Vo -> kkp8Vo.getCost_name()).collect(Collectors.toList());
                otherCostName = allCost.stream().filter(kkp8Vo -> {
                    return "2".equals(kkp8Vo.getKey());
                }).map(kkp8Vo -> kkp8Vo.getCost_name()).collect(Collectors.toList());
            }

            List<OthCostDetlVo> kkq5Vos = new ArrayList<>(list.size());
            List<OthCostDetlVo> finalKkq5Vos = kkq5Vos;
            list.stream().forEach(map -> {
                finalKkq5Vos.add(BeanMapUtil.mapToBean(map, OthCostDetlVo.class));
            });

            if (ValidateUtil.isNotEmpty(finalKkq5Vos)) {
                kkq5Vos = finalKkq5Vos.stream().filter(kkq5Vo -> {
                    return !ValidateUtil.isEmpty(kkq5Vo);
                }).collect(Collectors.toList());
            }

            result.put("list", kkq5Vos);

            if (ValidateUtil.isNotEmpty(kkq5Vos)) {
                List<OthCostDetlVo> collect = kkq5Vos.stream().filter(kkq5Vo -> {
                    return ValidateUtil.isEmpty(kkq5Vo.getDept_name());
                }).collect(Collectors.toList());

                if (!ValidateUtil.isEmpty(collect)) {
                    errorResponses.add(new ErrorResponse("科室名称缺失", "使用模板文件进行导入或者科室名称不能为空"));
                }

                collect = kkq5Vos.stream().filter(kkq5Vo -> {
                    return ValidateUtil.isEmpty(kkq5Vo.getOth_cost_name());
                }).collect(Collectors.toList());

                if (!ValidateUtil.isEmpty(collect)) {
                    errorResponses.add(new ErrorResponse("成本名称缺失", "使用模板文件进行导入或者成本名称不能为空"));
                }

                collect = kkq5Vos.stream().filter(kkq5Vo -> {
                    return ValidateUtil.isEmpty(kkq5Vo.getCost_amt());
                }).collect(Collectors.toList());

                if (!ValidateUtil.isEmpty(collect)) {
                    errorResponses.add(new ErrorResponse("成本金额缺失", "使用模板文件进行导入或者成本金额不能为空"));
                }

                Map<String, List<OthCostDetlVo>> collect1 = kkq5Vos.stream().collect(Collectors.groupingBy(Kkq5Vo -> Kkq5Vo.getDept_name() + "___" + Kkq5Vo.getOth_cost_name()));

                Set<Map.Entry<String, List<OthCostDetlVo>>> entries = collect1.entrySet();
                for (Map.Entry<String, List<OthCostDetlVo>> entry : entries) {
                    if (entry.getValue().size() > 1) {
                        String key = entry.getKey();
                        String[] s = key.split("___");
                        if (s != null && s.length == 2) {
                            String bkf001 = s[0];
                            String kkh255 = s[1];
                            errorResponses.add(new ErrorResponse("科室(" + bkf001 + ")和成本(" + kkh255 + ")不唯一", "请检查（科室，成本）是否唯一"));
                        }

                    }
                }

                for (OthCostDetlVo kkq5Vo : kkq5Vos) {
                    String kkh256Str = kkq5Vo.getCost_amt() == null ? null : kkq5Vo.getCost_amt().toString();
                    if (ValidateUtil.isNotEmpty(kkh256Str)) {
                        BigDecimal kkh256 = null;
                        try {
                            kkh256 = new BigDecimal(kkh256Str);
                        } catch (Exception e) {
                            errorResponses.add(new ErrorResponse("成本金额不是数字", "请检查成本金额是否是数字"));
                        }
                        kkq5Vo.setCost_amt(kkh256);

                    }

                    if (ValidateUtil.isNotEmpty(notOtherCostName)) {
                        if (notOtherCostName.contains(kkq5Vo.getOth_cost_name())) {
                            errorResponses.add(new ErrorResponse("成本【" + kkq5Vo.getOth_cost_name() + "】不是其他成本类型", "请检查成本名称对应的成本类型"));
                        }
                    }

                    if (ValidateUtil.isNotEmpty(otherCostName)) {
                        if (!otherCostName.contains(kkq5Vo.getOth_cost_name())) {
                            errorResponses.add(new ErrorResponse("成本【" + kkq5Vo.getOth_cost_name() + "】未在“数据字典配置 - 成本数据配置”中", "请先在“数据字典配置 - 成本数据配置”中添加"));
                        }
                    }
                }

            }

        }
        result.put("validate", errorResponses);
        return result;
    }

    /**
     * 查询期号下是否有其他成本数据
     *
     * @param dto
     * @return
     */
    @PostMapping("/extractDataFromHis")
    public void extractDataFromHis(BasicDataTransmissionDto dto) throws Exception {
        if (CostIConst.COST_EXTRACT_TYPE_1.equals(dto.getType())) {
            int num = basicDataTransmissionWriteService.extractChargeDetailsByIssue(dto);
        } else if (CostIConst.COST_EXTRACT_TYPE_2.equals(dto.getType())) {
            int num = basicDataTransmissionWriteService.extractConsumablesUseByIssue(dto);
        } else if (CostIConst.COST_EXTRACT_TYPE_3.equals(dto.getType())) {
            int num = basicDataTransmissionWriteService.extractDrugUseByIssue(dto);
        }
    }
}
