package com.jp.med.cost.modules.config.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.cost.modules.config.dto.CCHIConfigDto;
import com.jp.med.cost.modules.config.dto.ConfigurationChargingDto;
import com.jp.med.cost.modules.config.vo.HospChrgItemCfgVo;


import java.util.List;

public interface ConfigurationChargingItemsReadMapper extends BaseMapper<ConfigurationChargingDto> {
    List<HospChrgItemCfgVo> queryConfiguration(ConfigurationChargingDto dto);

    List<String> querychrgItemlv();
}
