package com.jp.med.cost.modules.dataDictionaryConfig.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.cost.modules.common.controller.HospBaseController;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import com.jp.med.cost.modules.common.vo.MedinsCostItemCfgVo;
import com.jp.med.cost.modules.dataDictionaryConfig.dto.*;
import com.jp.med.cost.modules.dataDictionaryConfig.service.read.MedicalInstitutionCostLibraryReadService;
import com.jp.med.cost.modules.dataDictionaryConfig.service.write.MedicalInstitutionCostLibraryWriteService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 医疗机构成本库
 */
@Api(value = "医疗机构成本库配置", tags = "医疗机构成本库配置")
@RestController
@RequestMapping("MedicalInstitutionCostLibrary")
@Validated
public class MedicalInstitutionCostLibraryController extends HospBaseController {
    @Autowired
    private MedicalInstitutionCostLibraryReadService medicalInstitutionCostLibraryReadService;

    @Autowired
    private MedicalInstitutionCostLibraryWriteService medicalInstitutionCostLibraryWriteService;

    /**
     * 分页查询数据字典配置（有效标志需要为0）
     */
    @PostMapping("listDictionaryConfig")
    public CommonResult<?> listDictionaryConfig(@RequestBody ListDictionaryReqDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        List<ListDictionaryResDto> page = medicalInstitutionCostLibraryReadService.listDictionaryConfig(dto);
        return CommonResult.paging(page);
    }

    /**
     * 新增数据字典配置
     */
    @PostMapping("addDictionaryConfig")
    public CommonResult<?> addDictionaryConfig(@RequestBody DictionaryAddReqDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        boolean res = medicalInstitutionCostLibraryWriteService.addDictionaryConfig(dto);
        return CommonResult.success(res);
    }

    /**
     * 更新数据字典配置
     */
    @PostMapping("updateDictionaryConfig")
    public CommonResult<?> updateDictionaryConfig(@RequestBody DictionaryAddReqDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        boolean res = medicalInstitutionCostLibraryWriteService.updateDictionaryConfig(dto);
        return CommonResult.success(res);
    }

    /**
     * 更新id查询数据字典配置
     */
    @PostMapping("getDictionaryConfigById")
    public CommonResult<?> getDictionaryConfigById(@RequestBody DictionaryDetailReqDto reqDto) throws Exception {
        if (ValidateUtil.isEmpty(reqDto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        MedinsCostItemCfgVo resDto = medicalInstitutionCostLibraryReadService.getById(reqDto);
        return CommonResult.success(resDto);
    }

    /**
     * 删除数据字典配置（逻辑删除 有效标志位1）
     */
    @PostMapping("deleteDictionaryConfig")
    public CommonResult<?> deleteDictionaryConfig(@RequestBody DictionaryDetailReqDto reqDto) throws Exception {
        if (ValidateUtil.isEmpty(reqDto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        if (!reqDto.getHosp_cost_item_cfg_id().isEmpty()) {
            boolean res = medicalInstitutionCostLibraryWriteService.logicDelete(reqDto);
            return CommonResult.success(res);
        } else {
            return CommonResult.failed("全院成本项配置ID不能为空!");
        }
    }

    /**
     * 设置分摊参数
     */
    @PostMapping("operateApportionParameter")
    public CommonResult<?> operateApportionParameter(@RequestBody ApportionParamReqDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        boolean res = medicalInstitutionCostLibraryWriteService.operateApportionParameter(dto);
        return CommonResult.success(res);
    }

    /**
     * 查询成本名称是否已存在
     *
     * @param dto
     * @return
     */
    @PostMapping("queryCostNameIsExists")
    public CommonResult<?> queryCostNameIsExists(@RequestBody DictionaryDetailReqDto dto) {
        return CommonResult.success(medicalInstitutionCostLibraryReadService.queryCostNameIsExists(dto));
    }
}
