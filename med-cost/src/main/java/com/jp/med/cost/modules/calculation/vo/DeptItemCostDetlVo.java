package com.jp.med.cost.modules.calculation.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * class DeptItemCostDetlVo
 * desc   科室项目成本明细 - dept_item_cost_detl 
 *
 * <AUTHOR>
 */
public class DeptItemCostDetlVo implements Serializable {

	private static final long serialVersionUID = -8973247185685404415L;
	/**
		 * 科室项目成本明细ID
		 * VARCHAR(30)
		 * isNullable false
		 */
		private String dept_item_cost_detl_id;
		/**
		 * 医疗机构编号
		 * VARCHAR(30)
		 * isNullable false
		 */
		private String medins_no;
		/**
		 * 数据期号
		 * INTEGER
		 * isNullable false
		 */
		private Integer data_ym;
		/**
		 * 科室编码
		 * VARCHAR(30)
		 * isNullable false
		 */
		private String dept_codg;
		/**
		 * 科室名称
		 * VARCHAR(50)
		 * isNullable false
		 */
		private String dept_name;
		/**
		 * 医疗机构三目录编码
		 * VARCHAR(50)
		 * isNullable false
		 */
		private String medins_hilist_codg;
		/**
		 * 医疗机构三目录名称
		 * VARCHAR(100)
		 * isNullable false
		 */
		private String medins_hilist_name;
		/**
		 * 低值耗材
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal lowval_mcs;
		/**
		 * 技术难度
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal art_dif;
		/**
		 * 医生人数
		 * INTEGER
		 * isNullable true
		 */
		private Integer dr_psncnt;
		/**
		 * 医护人数
		 * INTEGER
		 * isNullable true
		 */
		private Integer medi_care_psncnt;
		/**
		 * 医技人数
		 * INTEGER
		 * isNullable true
		 */
		private Integer med_tech_psncnt;
		/**
		 * 工勤人数
		 * INTEGER
		 * isNullable true
		 */
		private Integer work_attd_psncnt;
		/**
		 * 耗时单位
		 * VARCHAR(6)
		 * isNullable true
		 */
		private String timecost_emp;
		/**
		 * 平均耗时
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal ave_timecost;
		/**
		 * 人员经费
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal psn_fund;
		/**
		 * 卫生材料费
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal hc_matlfee;
		/**
		 * 药品费
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal drugfee;
		/**
		 * 固定资产折旧费
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal fix_ast_depre_charge;
		/**
		 * 无形资产摊销费
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal ing_ast_amor_fee;
		/**
		 * 提取医疗风险基金
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal exta_med_risk_fund;
		/**
		 * 其它运行费用
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal oth_run_fee;
		/**
		 * 成本金额
		 * DECIMAL
		 * isNullable false
		 */
		private BigDecimal cost_amt;
		/**
		 * 操作时间
		 * TIMESTAMP
		 * isNullable false
		 */
		private Timestamp oprt_time;
		/**
		 * 有效标志
		 * VARCHAR(6)
		 * isNullable false
		 */
		private String vali_flag;
		/**
		 * 成本大类
		 * VARCHAR(6)
		 * isNullable true
		 */
		private String cost_big_clss;
		/**
		 * 项目发生次数
		 * INTEGER
		 * isNullable true
		 */
		private Integer item_ocur_cnt;
		/**
		 * 项目总收入
		 * DECIMAL
		 * isNullable true
		 */
		private BigDecimal item_total_income;
		/**
		 * 类型--3年度，2--季度,1--月度
		 * VARCHAR(5)
		 * isNullable true
		 */
		private String type_3_year_2_quat_mon;
		/**
		 * 收费类型
		 * VARCHAR(30)
		 * isNullable true
		 */
		private String chrg_type;

		/**
		 * 设置 dept_item_cost_detl_id 科室项目成本明细ID
		 * @param dept_item_cost_detl_id 科室项目成本明细ID
		 */
		public void setDept_item_cost_detl_id(String dept_item_cost_detl_id){
			this.dept_item_cost_detl_id = dept_item_cost_detl_id;
		}
		/**
		 * 设置 medins_no 医疗机构编号
		 * @param medins_no 医疗机构编号
		 */
		public void setMedins_no(String medins_no){
			this.medins_no = medins_no;
		}
		/**
		 * 设置 data_ym 数据期号
		 * @param data_ym 数据期号
		 */
		public void setData_ym(Integer data_ym){
			this.data_ym = data_ym;
		}
		/**
		 * 设置 dept_codg 科室编码
		 * @param dept_codg 科室编码
		 */
		public void setDept_codg(String dept_codg){
			this.dept_codg = dept_codg;
		}
		/**
		 * 设置 dept_name 科室名称
		 * @param dept_name 科室名称
		 */
		public void setDept_name(String dept_name){
			this.dept_name = dept_name;
		}
		/**
		 * 设置 medins_hilist_codg 医疗机构三目录编码
		 * @param medins_hilist_codg 医疗机构三目录编码
		 */
		public void setMedins_hilist_codg(String medins_hilist_codg){
			this.medins_hilist_codg = medins_hilist_codg;
		}
		/**
		 * 设置 medins_hilist_name 医疗机构三目录名称
		 * @param medins_hilist_name 医疗机构三目录名称
		 */
		public void setMedins_hilist_name(String medins_hilist_name){
			this.medins_hilist_name = medins_hilist_name;
		}
		/**
		 * 设置 lowval_mcs 低值耗材
		 * @param lowval_mcs 低值耗材
		 */
		public void setLowval_mcs(BigDecimal lowval_mcs){
			this.lowval_mcs = lowval_mcs;
		}
		/**
		 * 设置 art_dif 技术难度
		 * @param art_dif 技术难度
		 */
		public void setArt_dif(BigDecimal art_dif){
			this.art_dif = art_dif;
		}
		/**
		 * 设置 dr_psncnt 医生人数
		 * @param dr_psncnt 医生人数
		 */
		public void setDr_psncnt(Integer dr_psncnt){
			this.dr_psncnt = dr_psncnt;
		}
		/**
		 * 设置 medi_care_psncnt 医护人数
		 * @param medi_care_psncnt 医护人数
		 */
		public void setMedi_care_psncnt(Integer medi_care_psncnt){
			this.medi_care_psncnt = medi_care_psncnt;
		}
		/**
		 * 设置 med_tech_psncnt 医技人数
		 * @param med_tech_psncnt 医技人数
		 */
		public void setMed_tech_psncnt(Integer med_tech_psncnt){
			this.med_tech_psncnt = med_tech_psncnt;
		}
		/**
		 * 设置 work_attd_psncnt 工勤人数
		 * @param work_attd_psncnt 工勤人数
		 */
		public void setWork_attd_psncnt(Integer work_attd_psncnt){
			this.work_attd_psncnt = work_attd_psncnt;
		}
		/**
		 * 设置 timecost_emp 耗时单位
		 * @param timecost_emp 耗时单位
		 */
		public void setTimecost_emp(String timecost_emp){
			this.timecost_emp = timecost_emp;
		}
		/**
		 * 设置 ave_timecost 平均耗时
		 * @param ave_timecost 平均耗时
		 */
		public void setAve_timecost(BigDecimal ave_timecost){
			this.ave_timecost = ave_timecost;
		}
		/**
		 * 设置 psn_fund 人员经费
		 * @param psn_fund 人员经费
		 */
		public void setPsn_fund(BigDecimal psn_fund){
			this.psn_fund = psn_fund;
		}
		/**
		 * 设置 hc_matlfee 卫生材料费
		 * @param hc_matlfee 卫生材料费
		 */
		public void setHc_matlfee(BigDecimal hc_matlfee){
			this.hc_matlfee = hc_matlfee;
		}
		/**
		 * 设置 drugfee 药品费
		 * @param drugfee 药品费
		 */
		public void setDrugfee(BigDecimal drugfee){
			this.drugfee = drugfee;
		}
		/**
		 * 设置 fix_ast_depre_charge 固定资产折旧费
		 * @param fix_ast_depre_charge 固定资产折旧费
		 */
		public void setFix_ast_depre_charge(BigDecimal fix_ast_depre_charge){
			this.fix_ast_depre_charge = fix_ast_depre_charge;
		}
		/**
		 * 设置 ing_ast_amor_fee 无形资产摊销费
		 * @param ing_ast_amor_fee 无形资产摊销费
		 */
		public void setIng_ast_amor_fee(BigDecimal ing_ast_amor_fee){
			this.ing_ast_amor_fee = ing_ast_amor_fee;
		}
		/**
		 * 设置 exta_med_risk_fund 提取医疗风险基金
		 * @param exta_med_risk_fund 提取医疗风险基金
		 */
		public void setExta_med_risk_fund(BigDecimal exta_med_risk_fund){
			this.exta_med_risk_fund = exta_med_risk_fund;
		}
		/**
		 * 设置 oth_run_fee 其它运行费用
		 * @param oth_run_fee 其它运行费用
		 */
		public void setOth_run_fee(BigDecimal oth_run_fee){
			this.oth_run_fee = oth_run_fee;
		}
		/**
		 * 设置 cost_amt 成本金额
		 * @param cost_amt 成本金额
		 */
		public void setCost_amt(BigDecimal cost_amt){
			this.cost_amt = cost_amt;
		}
		/**
		 * 设置 oprt_time 操作时间
		 * @param oprt_time 操作时间
		 */
		public void setOprt_time(Timestamp oprt_time){
			this.oprt_time = oprt_time;
		}
		/**
		 * 设置 vali_flag 有效标志
		 * @param vali_flag 有效标志
		 */
		public void setVali_flag(String vali_flag){
			this.vali_flag = vali_flag;
		}
		/**
		 * 设置 cost_big_clss 成本大类
		 * @param cost_big_clss 成本大类
		 */
		public void setCost_big_clss(String cost_big_clss){
			this.cost_big_clss = cost_big_clss;
		}
		/**
		 * 设置 item_ocur_cnt 项目发生次数
		 * @param item_ocur_cnt 项目发生次数
		 */
		public void setItem_ocur_cnt(Integer item_ocur_cnt){
			this.item_ocur_cnt = item_ocur_cnt;
		}
		/**
		 * 设置 item_total_income 项目总收入
		 * @param item_total_income 项目总收入
		 */
		public void setItem_total_income(BigDecimal item_total_income){
			this.item_total_income = item_total_income;
		}
		/**
		 * 设置 type_3_year_2_quat_mon 类型--3年度，2--季度,1--月度
		 * @param type_3_year_2_quat_mon 类型--3年度，2--季度,1--月度
		 */
		public void setType_3_year_2_quat_mon(String type_3_year_2_quat_mon){
			this.type_3_year_2_quat_mon = type_3_year_2_quat_mon;
		}
		/**
		 * 设置 chrg_type 收费类型
		 * @param chrg_type 收费类型
		 */
		public void setChrg_type(String chrg_type){
			this.chrg_type = chrg_type;
		}

		/**
		 * 获取 dept_item_cost_detl_id 科室项目成本明细ID
		 * @return  dept_item_cost_detl_id
		 */
	    public String getDept_item_cost_detl_id(){
			return this.dept_item_cost_detl_id;
		}
		/**
		 * 获取 medins_no 医疗机构编号
		 * @return  medins_no
		 */
	    public String getMedins_no(){
			return this.medins_no;
		}
		/**
		 * 获取 data_ym 数据期号
		 * @return  data_ym
		 */
	    public Integer getData_ym(){
			return this.data_ym;
		}
		/**
		 * 获取 dept_codg 科室编码
		 * @return  dept_codg
		 */
	    public String getDept_codg(){
			return this.dept_codg;
		}
		/**
		 * 获取 dept_name 科室名称
		 * @return  dept_name
		 */
	    public String getDept_name(){
			return this.dept_name;
		}
		/**
		 * 获取 medins_hilist_codg 医疗机构三目录编码
		 * @return  medins_hilist_codg
		 */
	    public String getMedins_hilist_codg(){
			return this.medins_hilist_codg;
		}
		/**
		 * 获取 medins_hilist_name 医疗机构三目录名称
		 * @return  medins_hilist_name
		 */
	    public String getMedins_hilist_name(){
			return this.medins_hilist_name;
		}
		/**
		 * 获取 lowval_mcs 低值耗材
		 * @return  lowval_mcs
		 */
	    public BigDecimal getLowval_mcs(){
			return this.lowval_mcs;
		}
		/**
		 * 获取 art_dif 技术难度
		 * @return  art_dif
		 */
	    public BigDecimal getArt_dif(){
			return this.art_dif;
		}
		/**
		 * 获取 dr_psncnt 医生人数
		 * @return  dr_psncnt
		 */
	    public Integer getDr_psncnt(){
			return this.dr_psncnt;
		}
		/**
		 * 获取 medi_care_psncnt 医护人数
		 * @return  medi_care_psncnt
		 */
	    public Integer getMedi_care_psncnt(){
			return this.medi_care_psncnt;
		}
		/**
		 * 获取 med_tech_psncnt 医技人数
		 * @return  med_tech_psncnt
		 */
	    public Integer getMed_tech_psncnt(){
			return this.med_tech_psncnt;
		}
		/**
		 * 获取 work_attd_psncnt 工勤人数
		 * @return  work_attd_psncnt
		 */
	    public Integer getWork_attd_psncnt(){
			return this.work_attd_psncnt;
		}
		/**
		 * 获取 timecost_emp 耗时单位
		 * @return  timecost_emp
		 */
	    public String getTimecost_emp(){
			return this.timecost_emp;
		}
		/**
		 * 获取 ave_timecost 平均耗时
		 * @return  ave_timecost
		 */
	    public BigDecimal getAve_timecost(){
			return this.ave_timecost;
		}
		/**
		 * 获取 psn_fund 人员经费
		 * @return  psn_fund
		 */
	    public BigDecimal getPsn_fund(){
			return this.psn_fund;
		}
		/**
		 * 获取 hc_matlfee 卫生材料费
		 * @return  hc_matlfee
		 */
	    public BigDecimal getHc_matlfee(){
			return this.hc_matlfee;
		}
		/**
		 * 获取 drugfee 药品费
		 * @return  drugfee
		 */
	    public BigDecimal getDrugfee(){
			return this.drugfee;
		}
		/**
		 * 获取 fix_ast_depre_charge 固定资产折旧费
		 * @return  fix_ast_depre_charge
		 */
	    public BigDecimal getFix_ast_depre_charge(){
			return this.fix_ast_depre_charge;
		}
		/**
		 * 获取 ing_ast_amor_fee 无形资产摊销费
		 * @return  ing_ast_amor_fee
		 */
	    public BigDecimal getIng_ast_amor_fee(){
			return this.ing_ast_amor_fee;
		}
		/**
		 * 获取 exta_med_risk_fund 提取医疗风险基金
		 * @return  exta_med_risk_fund
		 */
	    public BigDecimal getExta_med_risk_fund(){
			return this.exta_med_risk_fund;
		}
		/**
		 * 获取 oth_run_fee 其它运行费用
		 * @return  oth_run_fee
		 */
	    public BigDecimal getOth_run_fee(){
			return this.oth_run_fee;
		}
		/**
		 * 获取 cost_amt 成本金额
		 * @return  cost_amt
		 */
	    public BigDecimal getCost_amt(){
			return this.cost_amt;
		}
		/**
		 * 获取 oprt_time 操作时间
		 * @return  oprt_time
		 */
	    public Timestamp getOprt_time(){
			return this.oprt_time;
		}
		/**
		 * 获取 vali_flag 有效标志
		 * @return  vali_flag
		 */
	    public String getVali_flag(){
			return this.vali_flag;
		}
		/**
		 * 获取 cost_big_clss 成本大类
		 * @return  cost_big_clss
		 */
	    public String getCost_big_clss(){
			return this.cost_big_clss;
		}
		/**
		 * 获取 item_ocur_cnt 项目发生次数
		 * @return  item_ocur_cnt
		 */
	    public Integer getItem_ocur_cnt(){
			return this.item_ocur_cnt;
		}
		/**
		 * 获取 item_total_income 项目总收入
		 * @return  item_total_income
		 */
	    public BigDecimal getItem_total_income(){
			return this.item_total_income;
		}
		/**
		 * 获取 type_3_year_2_quat_mon 类型--3年度，2--季度,1--月度
		 * @return  type_3_year_2_quat_mon
		 */
	    public String getType_3_year_2_quat_mon(){
			return this.type_3_year_2_quat_mon;
		}
		/**
		 * 获取 chrg_type 收费类型
		 * @return  chrg_type
		 */
	    public String getChrg_type(){
			return this.chrg_type;
		}

		/**
		 * 转换为map对象
		 * @return Map
		 */
    	public java.util.Map toMap() {
			java.util.Map map = new java.util.HashMap();
				// dept_item_cost_detl_id 科室项目成本明细ID
				map.put("dept_item_cost_detl_id", getDept_item_cost_detl_id());
				// medins_no 医疗机构编号
				map.put("medins_no", getMedins_no());
				// data_ym 数据期号
				map.put("data_ym", getData_ym());
				// dept_codg 科室编码
				map.put("dept_codg", getDept_codg());
				// dept_name 科室名称
				map.put("dept_name", getDept_name());
				// medins_hilist_codg 医疗机构三目录编码
				map.put("medins_hilist_codg", getMedins_hilist_codg());
				// medins_hilist_name 医疗机构三目录名称
				map.put("medins_hilist_name", getMedins_hilist_name());
				// lowval_mcs 低值耗材
				map.put("lowval_mcs", getLowval_mcs());
				// art_dif 技术难度
				map.put("art_dif", getArt_dif());
				// dr_psncnt 医生人数
				map.put("dr_psncnt", getDr_psncnt());
				// medi_care_psncnt 医护人数
				map.put("medi_care_psncnt", getMedi_care_psncnt());
				// med_tech_psncnt 医技人数
				map.put("med_tech_psncnt", getMed_tech_psncnt());
				// work_attd_psncnt 工勤人数
				map.put("work_attd_psncnt", getWork_attd_psncnt());
				// timecost_emp 耗时单位
				map.put("timecost_emp", getTimecost_emp());
				// ave_timecost 平均耗时
				map.put("ave_timecost", getAve_timecost());
				// psn_fund 人员经费
				map.put("psn_fund", getPsn_fund());
				// hc_matlfee 卫生材料费
				map.put("hc_matlfee", getHc_matlfee());
				// drugfee 药品费
				map.put("drugfee", getDrugfee());
				// fix_ast_depre_charge 固定资产折旧费
				map.put("fix_ast_depre_charge", getFix_ast_depre_charge());
				// ing_ast_amor_fee 无形资产摊销费
				map.put("ing_ast_amor_fee", getIng_ast_amor_fee());
				// exta_med_risk_fund 提取医疗风险基金
				map.put("exta_med_risk_fund", getExta_med_risk_fund());
				// oth_run_fee 其它运行费用
				map.put("oth_run_fee", getOth_run_fee());
				// cost_amt 成本金额
				map.put("cost_amt", getCost_amt());
				// oprt_time 操作时间
				map.put("oprt_time", getOprt_time());
				// vali_flag 有效标志
				map.put("vali_flag", getVali_flag());
				// cost_big_clss 成本大类
				map.put("cost_big_clss", getCost_big_clss());
				// item_ocur_cnt 项目发生次数
				map.put("item_ocur_cnt", getItem_ocur_cnt());
				// item_total_income 项目总收入
				map.put("item_total_income", getItem_total_income());
				// type_3_year_2_quat_mon 类型--3年度，2--季度,1--月度
				map.put("type_3_year_2_quat_mon", getType_3_year_2_quat_mon());
				// chrg_type 收费类型
				map.put("chrg_type", getChrg_type());
				return map;
		}
	
}