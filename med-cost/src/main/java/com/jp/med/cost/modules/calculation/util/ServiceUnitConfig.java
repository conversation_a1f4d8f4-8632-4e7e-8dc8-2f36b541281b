package com.jp.med.cost.modules.calculation.util;


import com.jp.med.cost.modules.calculation.service.read.CostCalculationReadService;
import com.jp.med.cost.modules.common.vo.ServiceLavelVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

@Component
public class ServiceUnitConfig {
    private final Logger logger = LoggerFactory.getLogger(ServiceUnitConfig.class);

    @Resource
    private CostCalculationReadService costCalculationReadService;

    private static  List<ServiceLavelVo> serviceUnit;

    @PostConstruct
    public void initConfigM() {
        try {
            logger.info("---------------------初始化服务单元配置开始!-------------------------");
            serviceUnit  = costCalculationReadService.queryServiceUnit();
            logger.info("初始化服务单元配置成功");
        } catch (Exception e) {
            logger.error("初始化服务单元配置失败：" + e.getMessage());
        }
        logger.info("---------------------初始化服务单元配置结束!-------------------------");
    }

    public static List<ServiceLavelVo> getServiceUnit(){
        return serviceUnit;
    }
}
