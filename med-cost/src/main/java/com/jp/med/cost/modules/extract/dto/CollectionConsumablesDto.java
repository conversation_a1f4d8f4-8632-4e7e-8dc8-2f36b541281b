package com.jp.med.cost.modules.extract.dto;

import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class CollectionConsumablesDto extends CommonQueryDto implements Serializable {
    private static final long serialVersionUID = -6349335760798810840L;
    /**
     * 材料领用ID
     */
    private String matl_rece_id;
    /**
     * 材料名称
     */
    private String matl_name;
    /**
     * 单价
     */
    private String pric;
    /**
     * 进价
     */
    private String purcpric;
    /**
     * 领用数量
     */
    private String rece_cnt;
    /**
     * 使用数量
     */
    private String used_cnt;
    /**
     * 单位
     */
    private String emp;
    /**
     * 领用科室编码
     */
    private String rece_dept_codg;
    /**
     * 领用科室名称
     */
    private String rece_dept_name;
    /**
     * 领用人姓名
     */
    private String recer_name;
    /**
     * 备注
     */
    private String memo;
    /**
     * 领用日期
     */
    private String rece_date;
    /**
     * 有效标志
     */
    private String vali_flag;
}
