package com.jp.med.cost.modules.process.mapper.read;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.cost.modules.process.dto.DeptIncomeDto;
import com.jp.med.cost.modules.process.dto.DoctorIncomeSumDto;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021/5/13
 */
public interface IncomeDataProcessingReadMapper extends BaseMapper<T> {
    /**
     * 查询科室住院医师收入
     *
     * @return
     */
    List<DeptIncomeDto> queryDeptIncomeByZyys();

    /**
     * 查询医师收入数据汇总数据
     *
     * @return
     */
    List<DoctorIncomeSumDto> queryDoctorIncomeSum();

    /**
     * 查询科室汇总数据
     *
     * @return
     */
    List<DeptIncomeDto> queryDeptIncomeByCykb();
}
