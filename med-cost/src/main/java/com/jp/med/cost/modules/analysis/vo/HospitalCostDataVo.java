package com.jp.med.cost.modules.analysis.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 医院成本VO
 */
@Getter
@Setter
@ToString
public class HospitalCostDataVo {
    /**
     * 日期名称
     */
    String issue;

    /**
     * 成本费用
     */
    BigDecimal value;

    /**
     * 成本总额
     */
    BigDecimal costFee;

    /**
     * 上一期成本额
     */
    BigDecimal costLastFee;

    /**
     * 环比
     */
    BigDecimal costLastFeeRatio;

    /**
     * 去年同期成本额
     */
    BigDecimal costLastYearFee;

    /**
     * 环比
     */
    BigDecimal costLastYearFeeRatio;

    /**
     * 成本目标
     */
    BigDecimal costFeeGoal;

    /**
     * 成本目标完成率
     */
    BigDecimal goalOverRatio;

    /**
     * 全院成本趋势
     */
    private List<HospitalCostDataVo> hospitalCostTrend;
}
