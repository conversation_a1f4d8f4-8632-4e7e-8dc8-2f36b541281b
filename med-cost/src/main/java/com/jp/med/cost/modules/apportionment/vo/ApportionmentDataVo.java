package com.jp.med.cost.modules.apportionment.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 分摊数据Vo
 */
@Getter
@Setter
public class ApportionmentDataVo implements Serializable {
    /**
     * 科室编码
     */
    private String dept_codg;

    /**
     * 科室名称
     */
    private String dept_name;

    /**
     * 科室类别(0顶级科室,1行政科室,2医辅科室,3医技科室,4临床科室)
     */
    private String dept_type;

    /**
     * 人员经费直接成本
     */
    private BigDecimal directCostPersonnelFunds;

    /**
     * 人员经费间接成本
     */
    private BigDecimal indirectCostPersonnelExpenses = new BigDecimal(0);

    /**
     * 人员经费总成本
     */
    private BigDecimal sumCostPersonnelExpenses;

    /**
     * 卫生材料费直接成本
     */
    private BigDecimal directCostSanitaryMaterial;

    /**
     * 卫生材料费间接成本
     */
    private BigDecimal indirectCostSanitaryMaterial = new BigDecimal(0);


    /**
     * 卫生材料费总成本
     */
    private BigDecimal sumCostSanitaryMaterial;

    /**
     * 药品费直接成本
     */
    private BigDecimal directPharmaceuticalCost;

    /**
     * 药品费间接成本
     */
    private BigDecimal indirectPharmaceuticalCost = new BigDecimal(0);

    /**
     * 药品费总成本
     */
    private BigDecimal sumPharmaceuticalCost;

    /**
     * 固定资产直接成本
     */
    private BigDecimal directFixedAssets;

    /**
     * 固定资产间接成本
     */
    private BigDecimal indirectFixedAssets = new BigDecimal(0);

    /**
     * 固定资产总成本
     */
    private BigDecimal sumFixedAssets;

    /**
     * 无形资产直接成本
     */
    private BigDecimal directIntangibleAssets;

    /**
     * 无形资产间接成本
     */
    private BigDecimal indirectIntangibleAssets = new BigDecimal(0);

    /**
     * 无形资产总成本
     */
    private BigDecimal sumIntangibleAssets;

    /**
     * 风险医疗基金直接成本
     */
    private BigDecimal directVentureMedicalFund;

    /**
     * 风险医疗基金间接成本
     */
    private BigDecimal indirectVentureMedicalFund = new BigDecimal(0);

    /**
     * 风险医疗基金总成本
     */
    private BigDecimal sumVentureMedicalFund;

    /**
     * 其他成本直接成本
     */
    private BigDecimal directOtherCost;

    /**
     * 其他成本间接成本
     */
    private BigDecimal indirectOtherCost = new BigDecimal(0);

    /**
     * 其他成本总成本
     */
    private BigDecimal sumOtherCost;
}
