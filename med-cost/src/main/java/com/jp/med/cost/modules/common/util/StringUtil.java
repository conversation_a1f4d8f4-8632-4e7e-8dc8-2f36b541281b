package com.jp.med.cost.modules.common.util;

import com.jp.med.cost.modules.common.constant.CostIConst;
import org.apache.commons.codec.EncoderException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.*;

public class StringUtil extends StringUtils {
    private static final Logger log = LoggerFactory.getLogger(StringUtil.class);
    public static final String FOLDER_SEPARATOR = "/";
    private static final String WINDOWS_FOLDER_SEPARATOR = "\\";
    private static final String TOP_PATH = "..";
    private static final String CURRENT_PATH = ".";
    public static final String SHORT_BAR = "-";
    public static final String SEMICOLON = ";";
    public static final String UNDERLINE = "_";

    public static boolean startsWithIgnoreCase(String str, String prefix) {
        if (str != null && prefix != null) {
            if (str.startsWith(prefix)) {
                return true;
            } else if (str.length() < prefix.length()) {
                return false;
            } else {
                String lcStr = str.substring(0, prefix.length()).toLowerCase();
                String lcPrefix = prefix.toLowerCase();
                return lcStr.equals(lcPrefix);
            }
        } else {
            return false;
        }
    }

    public static int countOccurrencesOf(String str, String sub) {
        if (str != null && sub != null && str.length() != 0 && sub.length() != 0) {
            int count = 0;
            int pos = 0;

            int idx;
            for (; (idx = str.indexOf(sub, pos)) != -1; pos = idx + sub.length()) {
                ++count;
            }

            return count;
        } else {
            return 0;
        }
    }

    public static String delete(String inString, String pattern) {
        return replace(inString, pattern, "");
    }

    public static String deleteAny(String inString, String charsToDelete) {
        if (inString != null && charsToDelete != null) {
            StringBuilder out = new StringBuilder();

            for (int i = 0; i < inString.length(); ++i) {
                char c = inString.charAt(i);
                if (charsToDelete.indexOf(c) == -1) {
                    out.append(c);
                }
            }

            return out.toString();
        } else {
            return inString;
        }
    }

    public static String unqualify(String qualifiedName) {
        return unqualify(qualifiedName, '.');
    }

    public static String unqualify(String qualifiedName, char separator) {
        return qualifiedName.substring(qualifiedName.lastIndexOf(separator) + 1);
    }

    public static String getFilename(String path) {
        int separatorIndex = path.lastIndexOf('/');
        return separatorIndex != -1 ? path.substring(separatorIndex + 1) : path;
    }

    public static String applyRelativePath(String path, String relativePath) {
        int separatorIndex = path.lastIndexOf('/');
        if (separatorIndex != -1) {
            String newPath = path.substring(0, separatorIndex);
            if (!relativePath.startsWith("/")) {
                newPath = newPath + CostIConst.PATHDELIMITER;
            }

            return newPath + relativePath;
        } else {
            return relativePath;
        }
    }

    public static String cleanPath(String path) {
        String pathToUse = replace(path, "\\", "/");
        String[] pathArray = delimitedListToStringArray(pathToUse, "/");
        List pathElements = new LinkedList();
        int tops = 0;

        for (int i = pathArray.length - 1; i >= 0; --i) {
            if (!".".equals(pathArray[i])) {
                if ("..".equals(pathArray[i])) {
                    ++tops;
                } else if (tops > 0) {
                    --tops;
                } else {
                    pathElements.add(0, pathArray[i]);
                }
            }
        }

        return collectionToDelimitedString(pathElements, "/");
    }

    public static boolean pathEquals(String path1, String path2) {
        return cleanPath(path1).equals(cleanPath(path2));
    }

    public static Locale parseLocaleString(String localeString) {
        String[] parts = tokenizeToStringArray(localeString, "_ ", false, false);
        String language = parts.length > 0 ? parts[0] : "";
        String country = parts.length > 1 ? parts[1] : "";
        String variant = parts.length > 2 ? parts[2] : "";
        return language.length() > 0 ? new Locale(language, country, variant) : null;
    }

    public static String[] addStringToArray(String[] arr, String str) {
        String[] newArr = new String[arr.length + 1];
        System.arraycopy(arr, 0, newArr, 0, arr.length);
        newArr[arr.length] = str;
        return newArr;
    }

    public static String[] sortStringArray(String[] source) {
        if (source == null) {
            return new String[0];
        } else {
            Arrays.sort(source);
            return source;
        }
    }

    public static Properties splitArrayElementsIntoProperties(String[] array, String delimiter) {
        return splitArrayElementsIntoProperties(array, delimiter, (String) null);
    }

    public static Properties splitArrayElementsIntoProperties(String[] array, String delimiter, String charsToDelete) {
        if (array != null && array.length != 0) {
            Properties result = new Properties();
            String element = "";
            String[] arr$ = array.clone();
            int len$ = array.length;

            for (int i$ = 0; i$ < len$; ++i$) {
                String anArray = arr$[i$];
                element = anArray;
                if (charsToDelete != null) {
                    element = deleteAny(anArray, charsToDelete);
                }

                String[] splittedElement = split(element, delimiter);
                if (splittedElement != null) {
                    result.setProperty(splittedElement[0].trim(), splittedElement[1].trim());
                }
            }

            return result;
        } else {
            return null;
        }
    }

    public static String[] tokenizeToStringArray(String str, String delimiters) {
        return tokenizeToStringArray(str, delimiters, true, true);
    }

    public static String[] tokenizeToStringArray(String str, String delimiters, boolean trimTokens, boolean ignoreEmptyTokens) {
        StringTokenizer st = new StringTokenizer(str, delimiters);
        ArrayList tokens = new ArrayList();

        while (true) {
            String token;
            do {
                if (!st.hasMoreTokens()) {
                    return (String[]) ((String[]) tokens.toArray(new String[tokens.size()]));
                }

                token = st.nextToken();
                if (trimTokens) {
                    token = token.trim();
                }
            } while (ignoreEmptyTokens && token.length() <= 0);

            tokens.add(token);
        }
    }

    public static String[] delimitedListToStringArray(String str, String delimiter) {
        if (str == null) {
            return new String[0];
        } else if (delimiter == null) {
            return new String[]{str};
        } else {
            List result = new ArrayList();
            int pos = 0;

            int delPos;
            for (; (delPos = str.indexOf(delimiter, pos)) != -1; pos = delPos + delimiter.length()) {
                result.add(str.substring(pos, delPos));
            }

            if (str.length() > 0 && pos <= str.length()) {
                result.add(str.substring(pos));
            }

            return (String[]) ((String[]) result.toArray(new String[result.size()]));
        }
    }

    public static String[] commaDelimitedListToStringArray(String str) {
        return delimitedListToStringArray(str, ",");
    }

    public static Set commaDelimitedListToSet(String str) {
        Set set = new TreeSet();
        String[] tokens = commaDelimitedListToStringArray(str);
        String[] arr$ = tokens;
        int len$ = tokens.length;

        for (int i$ = 0; i$ < len$; ++i$) {
            String token = arr$[i$];
            set.add(token);
        }

        return set;
    }

    public static String arrayToDelimitedString(Object[] arr, String delim) {
        if (arr == null) {
            return "";
        } else {
            StringBuilder sb = new StringBuilder();

            for (int i = 0; i < arr.length; ++i) {
                if (i > 0) {
                    sb.append(delim);
                }

                sb.append(arr[i]);
            }

            return sb.toString();
        }
    }

    public static String collectionToDelimitedString(Collection coll, String delim, String prefix, String suffix) {
        if (coll == null) {
            return "";
        } else {
            StringBuilder sb = new StringBuilder();
            Iterator it = coll.iterator();

            for (int i = 0; it.hasNext(); ++i) {
                if (i > 0) {
                    sb.append(delim);
                }

                sb.append(prefix).append(it.next()).append(suffix);
            }

            return sb.toString();
        }
    }

    public static String collectionToDelimitedString(Collection coll, String delim) {
        return collectionToDelimitedString(coll, delim, "", "");
    }

    public static String arrayToCommaDelimitedString(Object[] arr) {
        return arrayToDelimitedString(arr, ",");
    }

    public static String collectionToCommaDelimitedString(Collection coll) {
        return collectionToDelimitedString(coll, ",");
    }

    public static String encodePassword(String password, String algorithm) {
        byte[] unencodedPassword = password.getBytes(Charset.forName("GBK"));

        MessageDigest md = null;

        try {
            md = MessageDigest.getInstance(algorithm);
        } catch (Exception var10) {
            log.error("Exception: " + var10);
            return password;
        }

        md.reset();
        md.update(unencodedPassword);
        byte[] encodedPassword = md.digest();
        StringBuilder buf = new StringBuilder();
        byte[] arr$ = encodedPassword;
        int len$ = encodedPassword.length;

        for (int i$ = 0; i$ < len$; ++i$) {
            byte anEncodedPassword = arr$[i$];
            if ((anEncodedPassword & 255) < 16) {
                buf.append("0");
            }

            buf.append(Long.toString((long) (anEncodedPassword & 255), 16));
        }

        return buf.toString();
    }

    public static String encodeString(String str) {
        Base64 encoder = new Base64();

        try {
            return ((String) encoder.encode(str)).trim();
        } catch (EncoderException var3) {
            log.error("UnsupportedEncoding error!");
            return null;
        }
    }

    public static String decodeString(String str) {
        Base64 dec = new Base64();
        return new String(dec.decode(str));
    }

    public static String toChinese(String strvalue) {
        try {
            if (strvalue == null) {
                return null;
            } else {
                strvalue = new String(strvalue.getBytes("ISO8859_1"), "GBK");
                return strvalue;
            }
        } catch (Exception var2) {
            log.error("UnsupportedEncoding error!");
            return null;
        }
    }

    public static int compareTo(String szStr1, String szStr2) {
        return szStr1.compareTo(szStr2);
    }

    public static String rightGBKBytePad(String str, int len, char pad) {
        try {
            byte[] bt = str.getBytes("GBK");
            String result = new String(bt, "GBK") + StringUtils.rightPad("", len - bt.length, pad);
            return result;
        } catch (UnsupportedEncodingException var5) {
            log.error("UnsupportedEncoding error!");
            return null;
        }
    }

    public static String leftGBKBytePad(String str, int len, char pad) {
        try {
            byte[] bt = str.getBytes("GBK");
            String result = StringUtils.leftPad("", len - bt.length, pad) + new String(bt, "GBK");
            return result;
        } catch (UnsupportedEncodingException var5) {
            log.error("UnsupportedEncoding error!");
            return null;
        }
    }

    public static String getPYString(String str) {
        StringBuilder tempStr = new StringBuilder("");

        for (int i = 0; i < str.length(); ++i) {
            char c = str.charAt(i);
            if (c >= '!' && c <= '~') {
                tempStr.append(String.valueOf(c));
            } else {
                tempStr.append(getPYChar(String.valueOf(c)));
            }
        }

        return tempStr.toString();
    }

    public static String getPYChar(String c) {
        if (null != c && 0 != c.trim().length()) {
            byte[] array = String.valueOf(c).getBytes(Charset.forName("GBK"));
            if (2 > array.length) {
                return c;
            } else {
                int i = (short) (array[0] - 0 + 256) * 256 + (short) (array[1] - 0 + 256);
                if (i < 45217) {
                    return "*";
                } else if (i < 45253) {
                    return "a";
                } else if (i < 45761) {
                    return "b";
                } else if (i < 46318) {
                    return "c";
                } else if (i < 46826) {
                    return "d";
                } else if (i < 47010) {
                    return "e";
                } else if (i < 47297) {
                    return "f";
                } else if (i < 47614) {
                    return "g";
                } else if (i < 48119) {
                    return "h";
                } else if (i < 49062) {
                    return "j";
                } else if (i < 49324) {
                    return "k";
                } else if (i < 49896) {
                    return "l";
                } else if (i < 50371) {
                    return "m";
                } else if (i < 50614) {
                    return "n";
                } else if (i < 50622) {
                    return "o";
                } else if (i < 50906) {
                    return "p";
                } else if (i < 51387) {
                    return "q";
                } else if (i < 51446) {
                    return "r";
                } else if (i < 52218) {
                    return "s";
                } else if (i < 52698) {
                    return "t";
                } else if (i < 52980) {
                    return "w";
                } else if (i < 53689) {
                    return "x";
                } else if (i < 54481) {
                    return "y";
                } else {
                    return i < 55290 ? "z" : "*";
                }
            }
        } else {
            return c;
        }
    }

    public static String getRandomString(int length) {
        String base = "abcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new SecureRandom();
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < length; ++i) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }

        return sb.toString();
    }

    public static String delUrlParam(String url) {
        if (url != null) {
            int i = url.indexOf('?');
            if (i > 0) {
                url = url.substring(0, i);
            }
        }

        return url;
    }

    /**
     * n转二进制字符串
     *
     * @param n
     * @return
     */
    public static String binaryToDecimal(int n) {
        StringBuffer buffer = new StringBuffer();
        for (int i = 31; i >= 0; i--) {
            buffer.append((n >>> i & 1) + "");
        }
        return buffer.toString();
    }
}
