package com.jp.med.cost.modules.analysis.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class ClinicalCostCollectionVo implements Serializable {
    /**
     * 成本总额
     */
    private BigDecimal sumCost;

    /**
     * 成本总额同比
     */
    private BigDecimal previousCostRadio;

    /**
     * 成本总额环比
     */
    private BigDecimal relativeCostRadtio;

    private List<DeptCostVo> deptCostVos;
}
