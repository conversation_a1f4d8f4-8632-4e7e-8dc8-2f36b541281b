package com.jp.med.cost.modules.dataDictionaryConfig.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.cost.modules.common.controller.HospBaseController;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import com.jp.med.cost.modules.dataDictionaryConfig.dto.ServiceStandardConfigDto;
import com.jp.med.cost.modules.dataDictionaryConfig.service.read.ServiceStandardConfigReadService;
import com.jp.med.cost.modules.dataDictionaryConfig.service.write.ServiceStandardConfigWriteService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务标准配置
 */
@Api(value = "服务标准配置", tags = "服务标准配置")
@RestController
@RequestMapping("ServiceStandardConfig")
@Validated
public class ServiceStandardConfigController extends HospBaseController {
    @Autowired
    private ServiceStandardConfigReadService serviceStandardConfigReadService;

    @Autowired
    private ServiceStandardConfigWriteService serviceStandardConfigWriteService;

    /**
     * 查询服务标准配置
     *
     * @param dto
     */
    @PostMapping("/queryServiceStandardConfig")
    public CommonResult<?> queryServiceStandardConfig(@RequestBody ServiceStandardConfigDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        return CommonResult.success(serviceStandardConfigReadService.queryServiceStandardConfig(dto));
    }

    /**
     * 更新服务标准配置
     *
     * @param dto
     */
    @PostMapping("/updateServiceStandardConfig")
    public CommonResult<?> updateServiceStandardConfig(@RequestBody ServiceStandardConfigDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        serviceStandardConfigWriteService.updateServiceStandardConfig(dto);
        return CommonResult.success();
    }
}
