package com.jp.med.cost.modules.common.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <p>
 *  医院收费项目明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2021/4/8
 */
@Getter
@Setter
public class Kkk9Vo implements Serializable {
    private static final long serialVersionUID = 5229608640853566596L;

    /**
     * 收费明细ID
     */
    private String aay010;

    /**
     * 医疗机构编号
     */
    private String akb020;

    /**
     * 病人ID
     */
    private String kkh063;

    /**
     * 主页ID
     */
    private String kkh064;

    /**
     * 住院号
     */
    private String bkc011;

    /**
     * 姓名
     */
    private String aac003;

    /**
     * 项目编码
     */
    private String kkh046;

    /**
     * 项目名称
     */
    private String kkh047;

    /**
     * 收费类型
     */
    private String kkh065;

    /**
     * 数量
     */
    private Integer bkeh32;

    /**
     * 计价单位
     */
    private String kkh054;

    /**
     * 单价
     */
    private BigDecimal bkeh31;

    /**
     * 总费用
     */
    private BigDecimal kkh066;

    /**
     * 医嘱
     */
    private String bked77;

    /**
     * 开单科室编码
     */
    private String kkh067;

    /**
     * 开单科室名称
     */
    private String kkh068;

    /**
     * 开单医生
     */
    private String kkh069;

    /**
     * 受单科室编码
     */
    private String kkh070;

    /**
     * 受单科室名称
     */
    private String kkh071;

    /**
     * 受单医生
     */
    private String kkh072;

    /**
     * 收费时间
     */
    private Timestamp kkh073;

    /**
     * 经办时间
     */
    private Timestamp aae036;

    /**
     * 有效标志
     */
    private String aae100;

    /**
     * 结算期号
     */
    private String bke930;
}
