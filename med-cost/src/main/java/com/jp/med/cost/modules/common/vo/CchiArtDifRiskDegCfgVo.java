package com.jp.med.cost.modules.common.vo;

import java.io.Serializable;

/**
 * class CchiArtDifRiskDegCfg
 * desc   CCHI技术难度和风险程度配置 - cchi_art_dif_risk_deg_cfg
 *
 * <AUTHOR>
 */
public class CchiArtDifRiskDegCfgVo implements Serializable {

    private static final long serialVersionUID = -3078339445376448222L;
    /**
     * 项目代码
     * VARCHAR(16)
     * isNullable false
     */
    private String item_code;
    /**
     * 项目名称
     * VARCHAR(64)
     * isNullable false
     */
    private String itemname;
    /**
     * 项目内涵
     * VARCHAR(2000)
     * isNullable true
     */
    private String item_cont;
    /**
     * 除外内容
     * VARCHAR(120)
     * isNullable true
     */
    private String exct_cont;
    /**
     * 低值耗材
     * VARCHAR(2)
     * isNullable true
     */
    private String lowval_mcs;
    /**
     * 基本人力消耗耗时
     * VARCHAR(80)
     * isNullable true
     */
    private String bas_labr_cosm_timecost;
    /**
     * 技术难度
     * VARCHAR(8)
     * isNullable true
     */
    private String art_dif;
    /**
     * 风险程度
     * VARCHAR(8)
     * isNullable true
     */
    private String risk_deg;
    /**
     * 计价单位
     * VARCHAR(16)
     * isNullable true
     */
    private String pric_emp;
    /**
     * 计价说明
     * VARCHAR(200)
     * isNullable true
     */
    private String pric_dscr;
    /**
     * 医生人数
     * INTEGER
     * isNullable true
     */
    private Integer dr_psncnt;
    /**
     * 护士人数
     * INTEGER
     * isNullable true
     */
    private Integer nurs_psncnt;
    /**
     * 医技人数
     * INTEGER
     * isNullable true
     */
    private Integer med_tech_psncnt;
    /**
     * 工勤人数
     * INTEGER
     * isNullable true
     */
    private Integer work_attd_psncnt;
    /**
     * 平均耗时
     * INTEGER
     * isNullable true
     */
    private Integer ave_timecost;
    /**
     * 技术难度系数
     * NUMERIC(8, 4)
     * isNullable true
     */
    private Double art_dif_cof;
    /**
     * 风险程度系数
     * NUMERIC(8, 4)
     * isNullable true
     */
    private Double risk_deg_cof;
    /**
     * 内涵一次性耗材
     * VARCHAR(200)
     * isNullable true
     */
    private String cont_dspo_mcs;

    /**
     * 设置 item_code 项目代码
     *
     * @param item_code 项目代码
     */
    public void setItem_code(String item_code) {
        this.item_code = item_code;
    }

    /**
     * 设置 itemname 项目名称
     *
     * @param itemname 项目名称
     */
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }

    /**
     * 设置 item_cont 项目内涵
     *
     * @param item_cont 项目内涵
     */
    public void setItem_cont(String item_cont) {
        this.item_cont = item_cont;
    }

    /**
     * 设置 exct_cont 除外内容
     *
     * @param exct_cont 除外内容
     */
    public void setExct_cont(String exct_cont) {
        this.exct_cont = exct_cont;
    }

    /**
     * 设置 lowval_mcs 低值耗材
     *
     * @param lowval_mcs 低值耗材
     */
    public void setLowval_mcs(String lowval_mcs) {
        this.lowval_mcs = lowval_mcs;
    }

    /**
     * 设置 bas_labr_cosm_timecost 基本人力消耗耗时
     *
     * @param bas_labr_cosm_timecost 基本人力消耗耗时
     */
    public void setBas_labr_cosm_timecost(String bas_labr_cosm_timecost) {
        this.bas_labr_cosm_timecost = bas_labr_cosm_timecost;
    }

    /**
     * 设置 art_dif 技术难度
     *
     * @param art_dif 技术难度
     */
    public void setArt_dif(String art_dif) {
        this.art_dif = art_dif;
    }

    /**
     * 设置 risk_deg 风险程度
     *
     * @param risk_deg 风险程度
     */
    public void setRisk_deg(String risk_deg) {
        this.risk_deg = risk_deg;
    }

    /**
     * 设置 pric_emp 计价单位
     *
     * @param pric_emp 计价单位
     */
    public void setPric_emp(String pric_emp) {
        this.pric_emp = pric_emp;
    }

    /**
     * 设置 pric_dscr 计价说明
     *
     * @param pric_dscr 计价说明
     */
    public void setPric_dscr(String pric_dscr) {
        this.pric_dscr = pric_dscr;
    }

    /**
     * 设置 dr_psncnt 医生人数
     *
     * @param dr_psncnt 医生人数
     */
    public void setDr_psncnt(Integer dr_psncnt) {
        this.dr_psncnt = dr_psncnt;
    }

    /**
     * 设置 nurs_psncnt 护士人数
     *
     * @param nurs_psncnt 护士人数
     */
    public void setNurs_psncnt(Integer nurs_psncnt) {
        this.nurs_psncnt = nurs_psncnt;
    }

    /**
     * 设置 med_tech_psncnt 医技人数
     *
     * @param med_tech_psncnt 医技人数
     */
    public void setMed_tech_psncnt(Integer med_tech_psncnt) {
        this.med_tech_psncnt = med_tech_psncnt;
    }

    /**
     * 设置 work_attd_psncnt 工勤人数
     *
     * @param work_attd_psncnt 工勤人数
     */
    public void setWork_attd_psncnt(Integer work_attd_psncnt) {
        this.work_attd_psncnt = work_attd_psncnt;
    }

    /**
     * 设置 ave_timecost 平均耗时
     *
     * @param ave_timecost 平均耗时
     */
    public void setAve_timecost(Integer ave_timecost) {
        this.ave_timecost = ave_timecost;
    }

    /**
     * 设置 art_dif_cof 技术难度系数
     *
     * @param art_dif_cof 技术难度系数
     */
    public void setArt_dif_cof(Double art_dif_cof) {
        this.art_dif_cof = art_dif_cof;
    }

    /**
     * 设置 risk_deg_cof 风险程度系数
     *
     * @param risk_deg_cof 风险程度系数
     */
    public void setRisk_deg_cof(Double risk_deg_cof) {
        this.risk_deg_cof = risk_deg_cof;
    }

    /**
     * 设置 cont_dspo_mcs 内涵一次性耗材
     *
     * @param cont_dspo_mcs 内涵一次性耗材
     */
    public void setCont_dspo_mcs(String cont_dspo_mcs) {
        this.cont_dspo_mcs = cont_dspo_mcs;
    }

    /**
     * 获取 item_code 项目代码
     *
     * @return item_code
     */
    public String getItem_code() {
        return this.item_code;
    }

    /**
     * 获取 itemname 项目名称
     *
     * @return itemname
     */
    public String getItemname() {
        return this.itemname;
    }

    /**
     * 获取 item_cont 项目内涵
     *
     * @return item_cont
     */
    public String getItem_cont() {
        return this.item_cont;
    }

    /**
     * 获取 exct_cont 除外内容
     *
     * @return exct_cont
     */
    public String getExct_cont() {
        return this.exct_cont;
    }

    /**
     * 获取 lowval_mcs 低值耗材
     *
     * @return lowval_mcs
     */
    public String getLowval_mcs() {
        return this.lowval_mcs;
    }

    /**
     * 获取 bas_labr_cosm_timecost 基本人力消耗耗时
     *
     * @return bas_labr_cosm_timecost
     */
    public String getBas_labr_cosm_timecost() {
        return this.bas_labr_cosm_timecost;
    }

    /**
     * 获取 art_dif 技术难度
     *
     * @return art_dif
     */
    public String getArt_dif() {
        return this.art_dif;
    }

    /**
     * 获取 risk_deg 风险程度
     *
     * @return risk_deg
     */
    public String getRisk_deg() {
        return this.risk_deg;
    }

    /**
     * 获取 pric_emp 计价单位
     *
     * @return pric_emp
     */
    public String getPric_emp() {
        return this.pric_emp;
    }

    /**
     * 获取 pric_dscr 计价说明
     *
     * @return pric_dscr
     */
    public String getPric_dscr() {
        return this.pric_dscr;
    }

    /**
     * 获取 dr_psncnt 医生人数
     *
     * @return dr_psncnt
     */
    public Integer getDr_psncnt() {
        return this.dr_psncnt;
    }

    /**
     * 获取 nurs_psncnt 护士人数
     *
     * @return nurs_psncnt
     */
    public Integer getNurs_psncnt() {
        return this.nurs_psncnt;
    }

    /**
     * 获取 med_tech_psncnt 医技人数
     *
     * @return med_tech_psncnt
     */
    public Integer getMed_tech_psncnt() {
        return this.med_tech_psncnt;
    }

    /**
     * 获取 work_attd_psncnt 工勤人数
     *
     * @return work_attd_psncnt
     */
    public Integer getWork_attd_psncnt() {
        return this.work_attd_psncnt;
    }

    /**
     * 获取 ave_timecost 平均耗时
     *
     * @return ave_timecost
     */
    public Integer getAve_timecost() {
        return this.ave_timecost;
    }

    /**
     * 获取 art_dif_cof 技术难度系数
     *
     * @return art_dif_cof
     */
    public Double getArt_dif_cof() {
        return this.art_dif_cof;
    }

    /**
     * 获取 risk_deg_cof 风险程度系数
     *
     * @return risk_deg_cof
     */
    public Double getRisk_deg_cof() {
        return this.risk_deg_cof;
    }

    /**
     * 获取 cont_dspo_mcs 内涵一次性耗材
     *
     * @return cont_dspo_mcs
     */
    public String getCont_dspo_mcs() {
        return this.cont_dspo_mcs;
    }

    /**
     * 转换为map对象
     *
     * @return Map
     */
    public java.util.Map toMap() {
        java.util.Map map = new java.util.HashMap();
        // item_code 项目代码
        map.put("item_code", getItem_code());
        // itemname 项目名称
        map.put("itemname", getItemname());
        // item_cont 项目内涵
        map.put("item_cont", getItem_cont());
        // exct_cont 除外内容
        map.put("exct_cont", getExct_cont());
        // lowval_mcs 低值耗材
        map.put("lowval_mcs", getLowval_mcs());
        // bas_labr_cosm_timecost 基本人力消耗耗时
        map.put("bas_labr_cosm_timecost", getBas_labr_cosm_timecost());
        // art_dif 技术难度
        map.put("art_dif", getArt_dif());
        // risk_deg 风险程度
        map.put("risk_deg", getRisk_deg());
        // pric_emp 计价单位
        map.put("pric_emp", getPric_emp());
        // pric_dscr 计价说明
        map.put("pric_dscr", getPric_dscr());
        // dr_psncnt 医生人数
        map.put("dr_psncnt", getDr_psncnt());
        // nurs_psncnt 护士人数
        map.put("nurs_psncnt", getNurs_psncnt());
        // med_tech_psncnt 医技人数
        map.put("med_tech_psncnt", getMed_tech_psncnt());
        // work_attd_psncnt 工勤人数
        map.put("work_attd_psncnt", getWork_attd_psncnt());
        // ave_timecost 平均耗时
        map.put("ave_timecost", getAve_timecost());
        // art_dif_cof 技术难度系数
        map.put("art_dif_cof", getArt_dif_cof());
        // risk_deg_cof 风险程度系数
        map.put("risk_deg_cof", getRisk_deg_cof());
        // cont_dspo_mcs 内涵一次性耗材
        map.put("cont_dspo_mcs", getCont_dspo_mcs());
        return map;
    }

}