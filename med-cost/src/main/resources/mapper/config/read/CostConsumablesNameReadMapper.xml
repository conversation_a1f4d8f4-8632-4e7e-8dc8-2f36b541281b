<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cost.modules.config.mapper.read.CostConsumablesNameReadMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.cost.modules.config.vo.CostConsumablesNameVo" id="consumablesNameMap">
        <result property="id" column="id"/>
        <result property="nonChargeableConsumablesName" column="non_chargeable_consumables_name"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.cost.modules.config.vo.CostConsumablesNameVo">
        select id                              as id,
               non_chargeable_consumables_name as nonChargeableConsumablesName,
               remark                          as remark,
               hospital_id                     as hospitalId,
               project_code                    as projectCode
        from hosp_consumables_name
    </select>

</mapper>
