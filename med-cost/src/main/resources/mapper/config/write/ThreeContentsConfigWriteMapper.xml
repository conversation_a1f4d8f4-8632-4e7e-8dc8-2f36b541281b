<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yinhai.drg.hosp.cost.config.mapper.write.ThreeContentsConfigWriteMapper">

    <!-- 更新收费项目信息  -->
    <update id="fnChangeThreeContentsConfig" >
        UPDATE hosp_hilist_cfg
        SET si_hilist_codg          = #{si_hilist_codg},
            si_hilist_name          = #{si_hilist_name},
            dosform                 = #{dosform},
            med_invo_item_type      = #{med_invo_item_type},
            chrg_item_lv            = #{chrg_item_lv},
            hilist_type             = #{hilist_type},
            chg_date                = current_date,
            nat_drug_codg_stan_code = #{nat_drug_codg_stan_code},
            trt_servitem_codg       = #{trt_servitem_codg},
            memo                    = #{memo},
            vali_flag               = #{vali_flag}
        WHERE si_hilist_id = #{si_hilist_id}
    </update>

    <!-- 插入单条数据 -->
    <insert id="fnAddThreeContentsConfig" >
        INSERT INTO hosp_hilist_cfg (si_hilist_id,
                                     si_hilist_codg,
                                     si_hilist_name,
                                     dosform,
                                     med_invo_item_type,
                                     chrg_item_lv,
                                     hilist_type,
                                     chg_date,
                                     nat_drug_codg_stan_code,
                                     trt_servitem_codg,
                                     memo,
                                     vali_flag)
        VALUES (nextval('se_si_hilist_id'),
                #{si_hilist_codg},
                #{si_hilist_name},
                #{dosform},
                #{med_invo_item_type},
                #{chrg_item_lv},
                #{hilist_type},
                current_date,
                #{nat_drug_codg_stan_code},
                #{trt_servitem_codg},
                #{memo},
                #{vali_flag})
    </insert>
</mapper>
