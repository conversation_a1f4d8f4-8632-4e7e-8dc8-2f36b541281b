<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jp.med.cost.modules.config.mapper.write.CCHIConfigWriteMapper">
    <!-- 更新收费项目信息  -->
    <update id="changeCCHIConfig">
        UPDATE cchi_art_dif_risk_deg_cfg
        SET
        item_codg = #{item_codg},  <!--项目编码-->
        itemname = #{itemname},  <!--项目名称-->
        item_cont = #{item_cont},
        exct_cont = #{exct_cont},
        lowval_mcs = #{lowval_mcs},
        bas_labr_cosm_timecost = #{bas_labr_cosm_timecost},
        art_dif = #{art_dif},
        risk_deg = #{risk_deg},
        pric_emp = #{pric_emp},
        pric_dscr = #{pric_dscr},
        dr_psncnt = #{dr_psncnt},
        nurs_psncnt = #{nurs_psncnt},
        med_tech_psncnt = #{med_tech_psncnt},
        work_attd_psncnt = #{work_attd_psncnt},
        ave_timecost = #{ave_timecost},
        art_dif_cof = #{art_dif_cof},
        risk_deg_cof = #{risk_deg_cof},
        cont_dspo_mcs =#{cont_dspo_mcs},
        vali_flag = #{vali_flag}  <!--有效标志-->
        WHERE cchi_art_dif_risk_deg_cfg_id = #{cchi_art_dif_risk_deg_cfg_id}
    </update>

    <!-- 插入单条数据 -->
    <insert id="addCCHIConfig">
        INSERT INTO cchi_art_dif_risk_deg_cfg (cchi_art_dif_risk_deg_cfg_id, item_codg,
                                               itemname,
                                               item_cont,
                                               exct_cont,
                                               lowval_mcs,
                                               bas_labr_cosm_timecost,
                                               art_dif,
                                               risk_deg,
                                               pric_emp,
                                               pric_dscr,
                                               dr_psncnt,
                                               nurs_psncnt,
                                               med_tech_psncnt,
                                               work_attd_psncnt,
                                               ave_timecost,
                                               art_dif_cof,
                                               risk_deg_cof,
                                               cont_dspo_mcs,
                                               vali_flag)
        VALUES (nextval('se_cchi_art_dif_risk_deg_cfg_id')
                    #{item_codg},
                #{itemname},
                #{item_cont},
                #{exct_cont},
                #{lowval_mcs},
                #{bas_labr_cosm_timecost},
                #{art_dif},
                #{risk_deg},
                #{pric_emp},
                #{pric_dscr},
                #{dr_psncnt},
                #{nurs_psncnt},
                #{med_tech_psncnt},
                #{work_attd_psncnt},
                #{ave_timecost},
                #{art_dif_cof},
                #{risk_deg_cof},
                #{cont_dspo_mcs},
                #{vali_flag})
    </insert>
</mapper>
