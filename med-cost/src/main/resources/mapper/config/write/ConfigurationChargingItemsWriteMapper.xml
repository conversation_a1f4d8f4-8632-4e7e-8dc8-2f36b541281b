<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jp.med.cost.modules.config.mapper.write.ConfigurationChargingItemsWriteMapper">

    <!-- 更新收费项目信息  -->
    <update id="fnChangeConfig">
        UPDATE hosp_chrg_item_cfg
        SET
        item_codg = #{item_codg},  <!--项目编码-->
        itemname = #{itemname},  <!--项目名称-->
        chrg_item_lv = #{chrg_item_lv},  <!--收费项目等级-->
        spec = #{spec},  <!--规格-->
        pric_emp = #{pric_emp},  <!--计价单位-->
        chrg_item_pric = #{chrg_item_pric},  <!--单价-->
        vali_flag = #{vali_flag}  <!--有效标志-->
        WHERE item_id = #{item_id}
    </update>

    <!-- 插入单条数据 -->
    <insert id="fnAddConfig">
        INSERT INTO kkj5 (
        item_id,  <!--项目ID-->
        item_codg,  <!--项目编码-->
        itemname,  <!-- 项目名称 -->
        chrg_item_lv,  <!-- 收费项目等级 -->
        spec,  <!-- 规格   -->
        pric_emp,  <!-- 计价单位 -->
        chrg_item_pric,  <!-- 单价 -->
        vali_flag   <!--  有效标志 -->
        )
        VALUES (
        nextval('se_item_id'),
        #{item_codg},
        #{itemname},
        #{chrg_item_lv},
        #{spec},
        #{pric_emp},
        #{chrg_item_pric},
        #{vali_flag}
        )
    </insert>
</mapper>
