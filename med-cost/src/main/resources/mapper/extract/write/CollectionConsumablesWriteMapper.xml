<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jp.med.cost.modules.extract.mapper.write.CollectionConsumablesWriteMapper">

    <!-- 更新收费项目信息  -->
    <update id="updateChangeCollection">
        UPDATE dept_matl_rece_rcd
        SET
        matl_name = #{matl_name},
        pric = #{pric},
        purcpric = #{purcpric},
        rece_cnt = #{rece_cnt},
        used_cnt = #{used_cnt},
        emp = #{emp},
        rece_dept_codg = #{rece_dept_codg},
        <if test="rece_dept_codg != null and rece_dept_codg != ''">
            rece_dept_name = (select dept_name from medins_dept where dept_codg = #{rece_dept_codg}),
        </if>
        recer_name = #{recer_name},
        memo = #{memo},
        vali_flag = #{vali_flag}
        WHERE matl_rece_id = #{matl_rece_id}
    </update>

    <!-- 插入单条数据 -->
    <insert id="addCollection">
        INSERT INTO dept_matl_rece_rcd (
        matl_rece_id,<!--材料领用ID-->
        matl_name,<!--材料名称-->
        pric,<!--单价-->
        purcpric,<!--进价-->
        rece_cnt,<!--领用数量-->
        used_cnt,<!--使用数量-->
        emp,<!--单位-->
        rece_dept_codg,<!--领用科室编码-->
        rece_dept_name,<!--领用科室名称-->
        recer_name,<!--领用人姓名-->
        memo,<!--备注-->
        rece_date,<!--领用日期-->
        vali_flag<!--有效标志-->
        )
        VALUES (
        nextval('se_matl_rece_id'),
        #{matl_name},
        #{pric},
        #{purcpric},
        #{rece_cnt},
        #{used_cnt},
        #{emp},
        #{rece_dept_codg},
        <if test="rece_dept_codg != null and rece_dept_codg != ''">
            (select dept_name from medins_dept where dept_codg = #{rece_dept_codg}),
        </if>
        <if test="rece_dept_codg == null or rece_dept_codg == ''">
            '',
        </if>
        #{recer_name},
        #{memo},
        current_date,
        #{vali_flag}
        )
    </insert>
    <!-- Excel导入多条数据 -->
    <insert id="insert">
        INSERT INTO dept_matl_rece_rcd (
        matl_rece_id,<!--材料领用ID-->
        matl_name,<!--材料名称-->
        pric,<!--单价-->
        purcpric,<!--进价-->
        rece_cnt,<!--领用数量-->
        used_cnt,<!--使用数量-->
        emp,<!--单位-->
        rece_dept_codg,<!--领用科室编码-->
        rece_dept_name,<!--领用科室名称-->
        recer_name,<!--领用人姓名-->
        memo,<!--备注-->
        rece_date,<!--领用日期-->
        vali_flag<!--有效标志-->
        )
        VALUES (
        nextval('se_matl_rece_id'),
        #{matl_name},
        #{pric},
        #{purcpric},
        #{rece_cnt},
        #{used_cnt},
        #{emp},
        #{rece_dept_codg},
        <if test="rece_dept_codg != null and rece_dept_codg != ''">
            (select dept_name from medins_dept where dept_codg = #{rece_dept_codg}),
        </if>
        <if test="rece_dept_codg == null or rece_dept_codg == ''">
            '',
        </if>
        #{recer_name},
        #{memo},
        <if test="rece_date != null and rece_date != ''">
            <if test="rece_date.length > 10 ">
                substr(#{rece_date},1,10),
            </if>
            <if test="rece_date.length lte 11 ">
                #{rece_date},
            </if>
        </if>
        '1'
        )
    </insert>
</mapper>
