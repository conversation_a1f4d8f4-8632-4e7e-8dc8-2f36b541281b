<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jp.med.cost.modules.extract.mapper.read.ChargingItemDetailsImportReadMapper">
    <select id="queryChargingItemDetailsList"
            parameterType="com.jp.med.cost.modules.extract.dto.ChargingItemDetailsDto"
            resultType="com.jp.med.cost.modules.common.vo.HospChrgItemDetlVo">
        SELECT
        patn_id,   <!--病人ID-->
        home_id,   <!--主页ID-->
        ipt_no,   <!--住院号-->
        name,   <!--姓名-->
        item_codg,   <!--项目编码-->
        itemname,   <!--项目名称-->
        chrg_type,   <!--收费类型-->
        cnt,   <!--数量-->
        pric_emp,   <!--计价单位-->
        pric,   <!--单价-->
        sumfee,   <!--总费用-->
        drord,   <!--医嘱-->
        bilg_dept_codg,   <!--开单科室编码-->
        bilg_dept_name,   <!--开单科室名称-->
        bilg_dr,   <!--开单医生-->
        acord_dept_codg,   <!--受单科室编码-->
        acord_dept_name,   <!--受单科室名称-->
        acord_dr,   <!--受单医生-->
        chrg_time,   <!--收费时间-->
        opt_time,   <!--经办时间-->
        vali_flag,   <!--有效标志-->
        medins_no,   <!--医疗机构编号-->
        data_ym,   <!--数据期号-->
        otp_no,   <!--门诊号-->
        chrg_detl_type,   <!--收费明细类别-->
        chrg_item_type   <!--收费项目类别-->
        FROM hosp_chrg_item_detl
        <where>
            <!--住院号-->
            <if test="ipt_no!=null and ipt_no!=''">
                AND ipt_no=#{ipt_no,jdbcType=VARCHAR}
            </if>
            <!--姓名-->
            <if test="name!=null and name!=''">
                AND name LIKE '%'||#{name,jdbcType=VARCHAR}||'%'
            </if>
            <!--项目名称-->
            <if test="itemname!=null and itemname!=''">
                AND itemname LIKE '%'||#{itemname,jdbcType=VARCHAR}||'%'
            </if>
            <!--项目分类-->
            <if test="chrg_type!=null and chrg_type!=''">
                AND chrg_type=#{chrg_type,jdbcType=VARCHAR}
            </if>
            <!--开单科室编码-->
            <if test="bilg_dept_codg!=null and bilg_dept_codg!=''">
                AND bilg_dept_codg=#{bilg_dept_codg,jdbcType=VARCHAR}
            </if>
            <!--受单科室编码-->
            <if test="acord_dept_codg!=null and acord_dept_codg!=''">
                AND acord_dept_codg=#{acord_dept_codg,jdbcType=VARCHAR}
            </if>
            <!--收费时间大于某个时间段-->
            <if test="datetime_start!=null and datetime_start!=''">
                AND <![CDATA[ TO_CHAR(chrg_time,'yyyy-mm')>=#{datetime_start,jdbcType=VARCHAR}]]>
            </if>
            <!--收费时间小于某个时间段-->
            <if test="datetime_end!=null and datetime_end!=''">
                AND <![CDATA[ TO_CHAR(chrg_time,'yyyy-mm')<=#{datetime_end,jdbcType=VARCHAR}]]>
            </if>
        </where>
    </select>
</mapper>
