<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jp.med.cost.modules.dataDictionaryConfig.mapper.read.ServiceStandardConfigReadMapper">
    <select id="queryServiceStandardConfig" resultType="com.jp.med.cost.modules.common.vo.DeptCchiResuCosmCfgVo">
        select dept_job_item_resu_cosm_cfg_tab_id,
        dept_job_item_resu_cosm_cfg_tab_id as key,
        year,
        a.medins_no,
        a.dept_codg,
        a.dept_name,
        medins_hilist_codg,
        medins_hilist_name,
        cchi_item_codg,
        cchi_itemname,
        cchi_item_cont,
        lowval_mcs,
        art_dif,
        dr_psncnt,
        medi_care_psncnt,
        med_tech_psncnt,
        work_attd_psncnt,
        ave_timecost,
        timecost_emp,
        pool_coty_codg
        from dept_cchi_resu_cosm_cfg a
        left join medins_dept b
        on a.dept_codg = b.dept_codg
        and b.vali_flag = '1'
        where a.vali_flag = '1'
        <if test="medins_no != null and medins_no != ''">
            AND A.medins_no = #{medins_no,jdbcType=VARCHAR}
        </if>

        <if test="pool_coty_codg != null and pool_coty_codg != ''">
            AND A.pool_coty_codg = #{pool_coty_codg,jdbcType=VARCHAR}
        </if>

        <if test="cchi_itemname != null and cchi_itemname != ''">
            AND A.cchi_itemname like '%' || #{cchi_itemname,jdbcType=VARCHAR} || '%'
        </if>

        <if test="dept_name != null and dept_name != ''">
            AND A.dept_name like '%' || #{dept_name,jdbcType=VARCHAR} || '%'
        </if>
    </select>
</mapper>
