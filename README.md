# HRP项目后台

## 项目介绍
HRP(Hospital Resource Planning)是一个医院资源规划系统，采用微服务架构设计，基于Spring Cloud技术栈开发。系统整合了医院人力资源、资产管理、预算管理、费用报销、绩效考核、采购管理、物资管理等多个业务模块，实现医院各类资源的统一管理和调配。

### 系统特点
1. 微服务架构：基于Spring Cloud实现微服务架构，支持服务的独立部署和扩展
2. 多数据源支持：支持Oracle、PostgreSQL、SQL Server等多种数据库，满足不同业务场景
3. 分布式设计：使用分布式事务、分布式缓存、分布式文件系统等技术
4. 工作流驱动：基于Flowable的工作流引擎，支持灵活的业务流程定制
5. 安全性：完善的权限控制和数据安全机制
6. 高扩展性：模块化设计，支持业务模块的灵活扩展

## 技术栈

### 基础框架
- JDK版本：11
- Spring Boot：2.3.12.RELEASE
- Spring Cloud：Hoxton.SR12
- Spring Cloud Alibaba：2.2.10-RC1

### 微服务组件
- 注册/配置中心：Nacos
- 网关：Spring Cloud Gateway
- 负载均衡：Spring Cloud Loadbalancer
- 服务调用：Spring Cloud OpenFeign
- 分布式事务：Seata 2.0.0
- 链路追踪：Zipkin

### 数据存储
- 关系型数据库
  - PostgreSQL
  - Oracle ********
  - SQL Server
- 缓存：Redis
- 消息队列：RabbitMQ
- 对象存储：MinIO 8.2.1

### 数据访问
- ORM框架：MyBatis-Plus 3.5.3
- 数据库连接池：Druid 1.2.20
- 分页插件：PageHelper 1.4.6
- 动态数据源：Dynamic-Datasource 4.3.0

### 工作流引擎
- Flowable 6.8.0
  - flowable-spring-boot-starter-process
  - flowable-spring-boot-starter-actuator

### 文档处理
- Excel处理
  - EasyPOI 4.1.2
  - EasyExcel 2.2.10
  - Apache POI
- Word处理：POI-TL 1.9.1
- Office文档处理：Aspose
  - Aspose.Words 15.8.0
  - Aspose.Cells 20.7
  - Aspose.Slides 21.10
- OFD文档转换：OFDRW 2.0.7

### 开发工具
- Lombok
- Hutool 5.8.29
- MapStruct 1.5.1
- FastJson 1.2.62
- Gson 2.8.5
- Guava 33.2.1
- Commons工具包
  - commons-lang 2.6
  - commons-io 2.5
  - commons-codec 1.10
  - commons-collections4 4.1
  - commons-configuration 1.10
  - commons-fileupload 1.2.2

### API文档
- Swagger 2.7.0

### 安全框架
- Spring Security
- JWT 0.7.0
- Nimbus JOSE JWT 8.16

### 其他功能
- 验证码：EasyCaptcha 1.6.2
- 二维码：ZXing 3.3.0
- 拼音转换：Pinyin4j 2.5.0
- Magic-API 2.1.1
  - magic-api-plugin-redis
  - magic-api-plugin-task
- WebMagic爬虫：0.7.3
- Time4J时间处理

## 系统模块

### 基础架构
- med-common -- 公共模块
  - 公共工具类
  - 通用配置
  - 共享组件
  - 基础实体类

- med-gateway -- 网关服务
  - 统一鉴权
  - 路由转发
  - 限流控制
  - 日志记录

- med-core -- 核心业务模块
  - 用户管理
  - 权限管理
  - 系统配置
  - 日志管理

### 工作流模块
- med-bpm -- 工作流引擎
  - med-bpm-api：工作流服务接口定义
  - med-bpm-biz：工作流具体实现
    - 流程设计
    - 表单配置
    - 流程管理
    - 任务处理

### 业务系统
- med-hrm -- 人力资源管理系统
  - 组织架构管理
  - 人员信息管理
  - 考勤管理
  - 培训管理
  - 招聘管理

- med-ams -- 资产管理系统
  - 固定资产管理
  - 设备管理
  - 资产盘点
  - 折旧管理
  - 维修管理

- med-bms -- 预算管理系统
  - 预算编制
  - 预算执行
  - 预算调整
  - 预算分析
  - 预算考核

- med-ecs -- 费用报销系统
  - 报销申请
  - 费用控制
  - 额度管理
  - 报销审批
  - 报销统计

- med-pms -- 绩效管理系统
  - 绩效指标
  - 绩效考核
  - 绩效分析
  - 奖金分配
  - 绩效报告

- med-purms -- 采购管理系统
  - 采购计划
  - 采购申请
  - 供应商管理
  - 合同管理
  - 采购统计

- med-mmis -- 物资管理系统
  - 库存管理
  - 物料管理
  - 入库管理
  - 出库管理
  - 盘点管理

- med-cms -- 合同管理系统
  - 合同起草
  - 合同审批
  - 合同执行
  - 合同变更
  - 合同档案

- med-audit -- 智能审核系统
  - 规则引擎
  - 自动审核
  - 风险控制
  - 异常预警
  - 审核报告

- med-bdp -- 数据平台
  - 数据采集
  - 数据分析
  - 报表生成
  - 数据可视化
  - 决策支持

- med-erp -- 财务核算系统
  - 总账管理
  - 应收管理
  - 应付管理
  - 财务报表
  - 财务分析

- med-rms -- 科研管理系统
  - 项目管理
  - 成果管理
  - 经费管理
  - 人员管理
  - 统计分析

### 应用服务
- med-app -- 移动应用服务
  - 移动端接口
  - 消息推送
  - 移动审批
  - 移动查询

- med-itf -- 接口服务
  - 外部系统接口
  - 数据同步
  - 接口监控
  - 接口文档