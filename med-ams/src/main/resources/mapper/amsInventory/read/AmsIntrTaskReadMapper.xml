<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ams.modules.inventory.mapper.read.AmsIntrTaskReadMapper">
    <select id="queryList" resultType="com.jp.med.ams.modules.inventory.vo.AmsIntrTaskVo">
        select a.id                                                         as id,
               a.id                                                         as key,
               a.hospital_id                                                as hospitalId,
               a.intr_task                                                  as intrTask,
               a.dept                                                       as dept,
               d.org_name                                                   as orgName,
               coalesce(a.totlcnt, 0)                                       as totlcnt,
               a.crter                                                      as crter,
               a.create_time                                                as createTime,
               a.flag                                                       as flag,
               a.status                                                     as status,
               greatest(coalesce(a.totlcnt,
                                 0) - coalesce(c.lossCount,
                                               0), c.ct)                    as ct,
               coalesce(c.lossCount, 0)                                     AS lossCount,
               (c.ct - (coalesce(a.totlcnt, 0) - coalesce(c.lossCount, 0))) as profitCount
        from ams_intr_task a
                 left join (select count(case when x.id = 0 then 1 else null end)  lossCount,
                                   count(case when x.id != 0 then 1 else null end) ct,
                                   x.task_id
                            from (select n.uid,
                                         n.task_id,
                                         count(m.id) as id
                                  from ams_intr_detail m
                                           right join ams_intr_todo n
                                                      on m.uid = n.uid and m.task_id = n.task_id
                                  group by n.uid, n.task_id) x
                            group by x.task_id) c on a.id = c.task_id
                 left join hrm_org d
                           on a.dept = d.org_id
                left join hrm_employee_info hei on hei.emp_code = a.crter
        <where>
            <if test="intrTask != null and intrTask != ''">
                and a.intr_task like concat('%', #{intrTask,jdbcType=VARCHAR}, '%')
            </if>
            <if test="dept != null and dept != ''">
                and a.dept = #{dept,jdbcType=VARCHAR}
            </if>

            <if test="status != '' and status != null">
                and a.status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="curSysOrgId!= null and curSysOrgId!= ''">
                and hei.org_id = #{curSysOrgId,jdbcType=VARCHAR}
            </if>
        </where>
        order by a.id desc
    </select>

    <select id="queryIntr" resultType="com.jp.med.ams.modules.inventory.vo.AmsIntrDetailVo">
        select x.* from
        (select a.id                                                        as key,
                b.id                                                        as assetId,
                b.asset_code                                                as assetCode,
                b.asset_name                                                as assetName,
                b.fa_code                                                   as faCode,
                b.dept_use                                                  as deptUse,


                coalesce(d.org_name,
                         (SELECT string_agg(t2.org_name, ',')
                          FROM ams_depr_asgn t1
                                   INNER JOIN hrm_org t2 ON t2.org_id = t1.org_id
                          WHERE t1.fa_code = b.asset_code),
                         ','
                )                                                           as orgName,


                f.asset_type_name                                           as assetTypeNName,
                b.uid                                                       as uid,
                b.asset_nav                                                 as assetNav,
                coalesce(asc2.storage_area, b.storage_area)                 as storageArea,
                case when c.uid is not null then '已盘点' else '未盘点' end as isIntr,
                case when c.uid is not null then '1' else '0' end           as isIntr1,
                c.is_manual                                                 as isManual,
                e.intr_task                                                 as taskName,
                a.remark
        from ams_intr_todo a
            left join ams_intr_task e on a.task_id = e.id
            inner join ams_property b
                       on a.uid = b.uid
            left join (
        select aid.uid, aid.is_manual
        from ams_intr_detail aid
        <if test="id != null and id != ''">
            where aid.task_id = #{id,jdbcType=INTEGER}
        </if>

        group by aid.uid, aid.is_manual
        ) c on a.uid = c.uid
            left join hrm_org d on b.dept_use = d.org_id
            left join ams_storage_cfg asc2 on asc2.storage_area_code = b.storage_area
            left join
        <choose>
            <when test="assetTypeN != null and assetTypeN != ''">
                (SELECT h.*
                FROM (
                WITH RECURSIVE temp AS
                (SELECT *
                 FROM ams_typen_cfg r
                <if test="assetTypeN != null and assetTypeN != ''">
                    where asset_type_code = #{assetTypeN,jdbcType=VARCHAR}
                </if>

                UNION
                    ALL
                SELECT b.*
                FROM ams_typen_cfg b,
                     temp t
                WHERE b.parent_code = t.asset_type_code )
                SELECT *
                FROM temp ) h
                ) f
            </when>
            <otherwise>
                ams_typen_cfg f
            </otherwise>
        </choose>
        on b.asset_type_n = f.asset_type_code
        <where>
            <if test="id != null and id != ''">
                a.task_id = #{id,jdbcType=INTEGER}
            </if>
            <if test="storageArea != null and storageArea != ''">
                and b.storage_area = #{storageArea,jdbcType=VARCHAR}
            </if>
            <if test="assetTypeN != null and assetTypeN != ''">
                and f.asset_type_name is not null
            </if>
            <if test="faCode != null and faCode != ''">
                and b.fa_code = #{faCode,jdbcType=VARCHAR}
            </if>
        </where>

        ) x

        <if test="isIntr != null and isIntr != ''">
            where x.isIntr1 = #{isIntr,jdbcType=VARCHAR}
        </if>
        order by x.faCode::int
    </select>

    <select id="queryListNoPaging" resultType="com.jp.med.ams.modules.inventory.vo.AmsIntrTaskVo">
        select a.id                                                         as id,
               a.id                                                         as key,
               a.hospital_id                                                as hospitalId,
               a.intr_task                                                  as intrTask,
               a.dept                                                       as dept,
               d.org_name                                                   as orgName,
               coalesce(a.totlcnt, 0)                                       as totlcnt,
               a.crter                                                      as crter,
               a.create_time                                                as createTime,
               a.flag                                                       as flag,
               a.status                                                     as status,
               greatest(coalesce(a.totlcnt,
                                 0) - coalesce(c.lossCount,
                                               0), c.ct)                    as already,
               coalesce(c.lossCount, 0)                                     AS lossCount,
               (c.ct - (coalesce(a.totlcnt, 0) - coalesce(c.lossCount, 0))) as profitCount
        from ams_intr_task a
                 left join (select count(case when x.id = 0 then 1 else null end)  lossCount,
                                   count(case when x.id != 0 then 1 else null end) ct,
                                   x.task_id
                            from (select n.uid,
                                         n.task_id,
                                         count(m.id) as id
                                  from ams_intr_detail m
                                           right join ams_intr_todo n
                                                      on m.uid = n.uid and m.task_id = n.task_id
                                  group by n.uid, n.task_id) x
                            group by x.task_id) c on a.id = c.task_id
                 left join hrm_org d
                           on a.dept = d.org_id
        <where>
            <if test="status != '' and status != null">
                and a.status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="id != '' and id != null">
                and a.id = #{id,jdbcType=VARCHAR}
            </if>
        </where>
        order by a.id desc
        <!--        select-->
        <!--            a.id as key,-->
        <!--            a.hospital_id as hospitalId,-->
        <!--            a.intr_task as intrTask,-->
        <!--            a.dept as dept,-->
        <!--            b.org_name as orgName,-->
        <!--            coalesce(a.totlcnt, 0) as totlcnt-->
        <!--        from ams_intr_task a-->
        <!--        left join hrm_org b-->
        <!--        on a.dept = b.org_id-->
        <!--        <where>-->
        <!--            <if test="status != '' and status != null">-->
        <!--                and a.status = #{status,jdbcType=VARCHAR}-->
        <!--            </if>-->
        <!--            <if test="id != '' and id != null">-->
        <!--                and a.id = #{id,jdbcType=VARCHAR}-->
        <!--            </if>-->
        <!--        </where>-->
        <!--        order by a.create_time desc-->
    </select>
</mapper>
