<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ams.modules.changes.mapper.read.AmsAllocReadMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.ams.modules.changes.vo.AmsAllocVo" id="allocMap">
        <result property="id" column="id"/>
        <result property="trafOutDept" column="traf_out_dept"/>
        <result property="trafInDept" column="traf_in_dept"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="remarks" column="remarks"/>
        <result property="prosstas" column="prosstas"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="activeFlag" column="active_flag"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.ams.modules.changes.vo.AmsAllocVo">
        select a.id              as id,
               a.fa_code         as faCode,
               a.traf_out_dept   as trafOutDept,
               a.traf_in_dept    as trafInDept,
               a.crter           as crter,
               a.create_time     as createTime,
               a.remarks         as remarks,
               a.prosstas        as prosstas,
               a.hospital_id     as hospitalId,
               a.active_flag     as activeFlag,
               a.bchno           as bchno,
               b.org_name        as trafOutDeptName,
               c.org_name        as trafInDeptName,
               d.emp_name        as crterName,
               f.asset_name      as assetName,
               a.traf_in_storage_location,
               bb.org_name       as traf_in_dept_manage_name,
               asc2.storage_area as traf_in_storage_area_name
        from ams_alloc a
                 left join
             hrm_org b
             on a.traf_out_dept = b.org_id
                 left join
             hrm_org c
             on a.traf_in_dept = c.org_id
                 left join
             hrm_employee_info d
             on a.crter = d.emp_code
                 left join
             ams_property f
             on a.fa_code = f.fa_code
                 left join hrm_org bb
                           on a.traf_in_dept_manage = bb.org_id
                 left join ams_storage_cfg asc2
                           on asc2.storage_area_code = a.traf_in_storage_area
        <where>
            <if test="prosstas != null and prosstas != '' and status != '2'">
                a.prosstas = #{prosstas,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                <choose>
                    <when test='status == "1" and sysUser != null'>
                        <if test="curEmpCode != null and curEmpCode != ''">
                            AND a.crter = #{sysUser.hrmUser.empCode,jdbcType=VARCHAR}
                                AND a.prosstas = '1'
                        </if>
                    </when>


                    <!-- 申请 -->
                    <when test='status == "1" and sysUser != null and sysUser.hrmUser != null'>
                        AND exists(select *
                                   from ams_audit_rcdfm
                                   where bchno = a.bchno)
                        <if test="curEmpCode != null and curEmpCode != ''">
                            and chker != #{sysUser.hrmUser.empCode,jdbcType=VARCHAR}
                        </if>
                    </when>

                    <!-- 审核 -->
                    <when test='status == "2" and sysUser != null and sysUser.hrmUser != null'>
                        AND exists(select *
                                   from (select chker, chk_seq, min(chk_seq) over (partition by bchno) as seq
                                         from ams_audit_rcdfm
                                         where chk_state = '0'
                                           and chk_time is null
                                           and bchno = a.bchno) t1
                                   where t1.seq = t1.chk_seq
                                     and t1.chker = #{sysUser.hrmUser.empCode,jdbcType=VARCHAR})
                            AND a.prosstas != '4'
                    </when>

                    <!-- 我的申请 -->
                    <when test='status == "3" and sysUser != null'>
                        AND a.crter = #{sysUser.hrmUser.empCode,jdbcType=VARCHAR}
                    </when>

                    <!-- 我的审核 -->
                    <when test='status == "4" and sysUser != null'>
                        AND exists(select *
                                   from ams_audit_rcdfm
                                   where bchno = a.bchno
                                     and chker = #{sysUser.hrmUser.empCode,jdbcType=VARCHAR}
                                     and chk_time is not null)
                    </when>
                </choose>
            </if>
            <if test="notQueryPassed">
                and a.prosstas != '3'
            </if>
            <if test="faCode != null and faCode != ''">
                AND a.id IN (select distinct aaf.ams_alloc_id
                             from ams_alloc_ref as aaf
                             where aaf.ams_property_facode::varchar = #{faCode,jdbcType=VARCHAR})
            </if>
            <if test="deptField != null and deptField != '' and deptCode != null and deptCode != ''">
                AND ${deptField} = #{deptCode,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>


    <!--划拨申请提交校验-->
    <select id="validateAllocApply" resultType="java.lang.Boolean">
        <!--    select case when exists(-->
        <!--      select bchno-->
        <!--      from ams_transfer-->
        <!--      where fa_code=#{faCode,jdbcType=VARCHAR}-->
        <!--        and prosstas in ('1','2')-->
        <!--      union all-->
        <!--      select bchno-->
        <!--      from ams_alloc-->
        <!--      where fa_code=#{faCode,jdbcType=VARCHAR}-->
        <!--        and prosstas in ('1','2')-->
        <!--    ) then true else false end-->

        select case
                   when exists(select bchno
                               from ams_transfer
                               where id in (select ams_transfer_id
                                            from ams_transfer_ref atf
                                            where atf.ams_property_facode = #{faCode,jdbcType=VARCHAR})
                                 and prosstas in ('1', '2')
                               union all
                               select bchno
                               from ams_alloc
                               where id in (select ams_transfer_id
                                            from ams_transfer_ref atf
                                            where atf.ams_property_facode = #{faCode,jdbcType=VARCHAR})
                                 and prosstas in ('1', '2')) then true
                   else false end
    </select>

    <select id="selectAllocPropertyRefFacode" resultType="com.jp.med.ams.modules.property.dto.AmsPropertyDto">
        select ams_property_facode as faCode, hospital_id as hospitalId
        from ams_alloc_ref
        where ams_alloc_id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="queryAuditCount" resultType="java.lang.Integer">
        select count(*)
        from ams_alloc a
        where exists(select *
                     from ams_audit_rcdfm
                     where bchno = a.bchno
                       and chker = #{sysUser.hrmUser.empCode,jdbcType=VARCHAR}
                       and chk_state = '0'
                       and chk_time is null)
    </select>

    <select id="validateAllocCanDel" resultType="boolean">
        select case
                   when exists(select id
                               from ams_audit_rcdfm aar
                               where aar.chk_seq = '1'
                                 and bchno = (select bchno
                                              from ams_alloc
                                              where id = #{id,jdbcType=VARCHAR})
                                 and chk_state != '0') then false
                   else true end
    </select>
</mapper>
