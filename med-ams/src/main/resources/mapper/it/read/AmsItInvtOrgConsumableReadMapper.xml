<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ams.modules.it.mapper.read.AmsItInvtOrgConsumableReadMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.ams.modules.it.vo.AmsItInvtOrgConsumableVo" id="itInvtOrgConsumableMap">
        <result property="id" column="id"/>
        <result property="orgId" column="org_id"/>
        <result property="equCode" column="equ_code"/>
        <result property="consumCode" column="consum_code"/>
        <result property="parentCode" column="parent_code"/>
        <result property="consumName" column="consum_name"/>
        <result property="remark" column="remark"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
        <result property="updtr" column="updtr"/>
        <result property="updateTime" column="update_time"/>
        <result property="delter" column="delter"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="activeFlag" column="active_flag"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="equName" column="equ_name"/>
        <result property="equTypeCode" column="equ_type_code"/>
        <result property="equTypeName" column="equ_type_name"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.ams.modules.it.vo.AmsItInvtOrgConsumableVo">
        select
        a.id as id,
        a.org_id as orgId,
        a.equ_code as equCode,
        a.consum_code as consumCode,
        a.parent_code as parentCode,
        a.consum_name as consumName,
        a.remark as remark,
        a.crter as crter,
        a.create_time as createTime,
        a.updtr as updtr,
        a.update_time as updateTime,
        a.delter as delter,
        a.delete_time as deleteTime,
        a.active_flag as activeFlag,
        a.hospital_id as hospitalId,
        a.equ_name as equName,
        a.equ_type_code as equTypeCode,
        a.equ_type_name as equTypeName,
        b.org_name as orgName
        from ams_it_invt_org_consumable as a
        left join hrm_org as b
        on a.org_id = b.org_id
        <where>
            <if test="orgId != null and orgId != '' ">
                AND a.org_id = #{orgId,jdbcType=VARCHAR}
            </if>
            AND (a.active_flag != '1' or a.active_flag is NULL)
        </where>
    </select>

    <select id="selectByOrgId" parameterType="string"
            resultType="com.jp.med.ams.modules.it.vo.AmsItInvtOrgConsumableVo">
        select
        id as id,
        org_id as orgId,
        equ_code as equCode,
        consum_code as consumCode,
        parent_code as parentCode,
        consum_name as consumName,
        remark as remark,
        crter as crter,
        create_time as createTime,
        updtr as updtr,
        update_time as updateTime,
        delter as delter,
        delete_time as deleteTime,
        active_flag as activeFlag,
        hospital_id as hospitalId,
        equ_name as equName,
        equ_type_code as equTypeCode,
        equ_type_name as equTypeName
        from ams_it_invt_org_consumable
        <where>
            <if test="orgId != null and orgId != '' ">
                AND org_id = #{orgId,jdbcType=VARCHAR}
            </if>
            AND (active_flag != '1' or active_flag is NULL)
        </where>
    </select>


    <select id="selectOrgList" resultType="com.jp.med.ams.modules.it.vo.AmsItInvtOrgConsumableVo">
        SELECT
        a.id as id,
        a.org_id as orgId,
        a.equ_code as equCode,
        a.consum_code as consumCode,
        a.parent_code as parentCode,
        a.remark as remark,
        a.hospital_id as hospitalId,
        a.equ_type_code as equTypeCode,
        b.org_name as orgName,
        e.name as equTypeName,
        c.name as equName,
        d.name as consumName
        from ams_it_invt_org_consumable as a
        left join hrm_org as b
        on a.org_id = b.org_id
        left join ams_it_invt_cfg as c
        on a.equ_code = c.code
        left join ams_it_invt_cfg as d
        on a.consum_code = d.code
        left join ams_it_invt_cfg as e
        on a.equ_type_code = e.code
        <where>
            <if test="orgId != null and type != '' ">
                AND a.org_id = #{orgId,jdbcType=VARCHAR}
            </if>
            AND (a.active_flag != '1' or a.active_flag is NULL)
        </where>
    </select>

    <!--删除配置时查询当前配置是否被此处使用-->
    <select id="queryIsUsed" resultType="com.jp.med.ams.modules.it.vo.AmsItInvtOrgConsumableVo">
        select id            as id,
               org_id        as orgId,
               equ_code      as equCode,
               consum_code   as consumCode,
               parent_code   as parentCode,
               consum_name   as consumName,
               remark        as remark,
               crter         as crter,
               create_time   as createTime,
               updtr         as updtr,
               update_time   as updateTime,
               delter        as delter,
               delete_time   as deleteTime,
               active_flag   as activeFlag,
               hospital_id   as hospitalId,
               equ_name      as equName,
               equ_type_code as equTypeCode,
               equ_type_name as equTypeName
        from ams_it_invt_org_consumable
        where (active_flag != '1'
           or active_flag is NULL)
          AND (
                    equ_code = #{code,jdbcType=VARCHAR}
                OR consum_code = #{code,jdbcType=VARCHAR}
                OR equ_type_code = #{code,jdbcType=VARCHAR}
            )
    </select>
</mapper>
