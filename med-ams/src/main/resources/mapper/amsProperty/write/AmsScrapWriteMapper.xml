<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ams.modules.property.mapper.write.AmsScrapWriteMapper">

    <!--写入数据-->
    <insert id="insertBatch">
        insert into ams_scrap(
           fa_code,
           scrap_rea,
           hospital_id,
           scrap_apply_id,
           active_flag
        ) values (
           #{faCode,jdbcType=VARCHAR},
           #{scrapRea,jdbcType=VARCHAR},
           #{hospitalId,jdbcType=VARCHAR},
           #{scrapApplyId,jdbcType=VARCHAR},
           '1'
        )
    </insert>
</mapper>
