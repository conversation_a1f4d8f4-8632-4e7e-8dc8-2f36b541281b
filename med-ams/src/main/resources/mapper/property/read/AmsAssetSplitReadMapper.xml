<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ams.modules.property.mapper.read.AmsAssetSplitReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.ams.modules.property.vo.AmsAssetSplitVo" id="assetSplitMap">
        <result property="id" column="id"/>
        <result property="createdAt" column="created_at"/>
        <result property="faCode" column="fa_code"/>
        <result property="splitAssetCodes" column="split_asset_codes"/>
        <result property="splitCount" column="split_count"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.ams.modules.property.vo.AmsAssetSplitVo">
        select
            *
        from ams_asset_split
    </select>

</mapper>
