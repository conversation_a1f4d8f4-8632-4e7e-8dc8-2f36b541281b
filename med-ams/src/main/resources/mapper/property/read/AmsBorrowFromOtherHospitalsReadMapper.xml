<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper
    namespace="com.jp.med.ams.modules.property.mapper.read.AmsBorrowFromOtherHospitalsReadMapper">

    <sql id="Base_Column_List">
        ams.id,
        ams.borrow_hospital,
        ams.borrow_asset_fa_code,
        ams.borrow_time,
        ams.borrow_start_time,
        ams.borrow_end_time,
        ams.borrow_attachments,
        ams.asset_quantity,
        ams.return_time,
        ams.returner,
        ams.return_confirm,
        ams.is_archived,
        ams.accessories_intact,
        ams.approver_id,
        ams.applicant,
        ams.manager,
        ams.phone,
        ams.process_instance_id,
        ams.prosstas,
        ams.created_at,
        ams.updated_at,
        ams.return_attachments,
        emp.emp_name      as
                             applicant_name,
        org.org_id        as applicant_org_id,
        org.org_name      as applicant_org_name,
        prop.asset_name  as borrow_asset_name,
        prop.asset_type   as borrow_asset_type,
        prop.dept_use
                          as borrow_asset_department,
        prop.asset_status as borrow_asset_status
    </sql>

    <sql id="Base_Join_Clause">
        FROM ams_borrow_from_other_hospitals ams
                 LEFT JOIN hrm_employee_info emp ON ams.applicant = emp.emp_code
                 LEFT JOIN hrm_org org ON emp.org_id = org.org_id
                 LEFT JOIN LATERAL ( SELECT string_agg(prop0.asset_name||'('|| prop0.fa_code||')', ',') as asset_name,
                                            MAX(prop0.asset_type)             as asset_type,
                                            MAX(prop0.dept_use)               as dept_use,
                                            MAX(prop0.asset_status)           as asset_status
                                     FROM ams_property prop0
                                     WHERE prop0.fa_code IN
                                           (SELECT unnest(string_to_array(ams.borrow_asset_fa_code, ','))) ) prop
                           ON true
    </sql>

    <sql id="Base_Where_Clause">
        <where>
            <!-- 主键 -->
            <if test="id != null">
                AND ams.id = #{id}
            </if>
            <!-- 借用医院 -->
            <if
                    test="borrowHospital != null and borrowHospital != ''">
                AND ams.borrow_hospital LIKE
                    CONCAT('%', #{borrowHospital}, '%')
            </if>
            <!-- 资产编码(多个以逗号分隔) -->
            <if
                    test="borrowAssetFaCode != null and borrowAssetFaCode != ''">
                AND (
                    ams.borrow_asset_fa_code = #{borrowAssetFaCode} OR ams.borrow_asset_fa_code LIKE
                                                                       CONCAT(#{borrowAssetFaCode}, ',%') OR
                    ams.borrow_asset_fa_code LIKE CONCAT('%,',
                                                         #{borrowAssetFaCode}) OR
                    ams.borrow_asset_fa_code LIKE CONCAT('%,', #{borrowAssetFaCode},
                                                         ',%') OR
                    position(#{borrowAssetFaCode} IN ams.borrow_asset_fa_code) &gt; 0)
            </if>
            <!-- 借用时间 -->
            <if
                    test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                AND ams.borrow_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <!-- 借用开始时间 -->
            <if
                    test="borrowStartTime != null">
                AND ams.borrow_start_time &gt;= #{borrowStartTime}
            </if>
            <!-- 借用结束时间 -->
            <if
                    test="borrowEndTime != null">
                AND ams.borrow_end_time &lt;= #{borrowEndTime}
            </if>
            <!-- 资产数量 -->
            <if
                    test="assetQuantity != null">
                AND ams.asset_quantity = #{assetQuantity}
            </if>
            <!-- 归还人 -->
            <if
                    test="returner != null and returner != ''">
                AND ams.returner = #{returner}
            </if>
            <!-- 归还确认 -->
            <if
                    test="returnConfirm != null and returnConfirm != ''">
                AND ams.return_confirm =
                    #{returnConfirm}
            </if>
            <!-- 归档状态 -->
            <if
                    test="isArchived != null and isArchived != ''">
                AND ams.is_archived = #{isArchived}
            </if>
            <!-- 配件是否完好 -->
            <if
                    test="accessoriesIntact != null and accessoriesIntact != ''">
                AND ams.accessories_intact = #{accessoriesIntact}
            </if>
            <!-- 申请人 -->
            <if
                    test="applicant != null and applicant != ''">
                AND ams.applicant = #{applicant}
            </if>
            <!-- 申请人科室 -->
            <if
                    test="applicantOrgId != null and applicantOrgId != ''">
                AND org.org_id =
                    #{applicantOrgId}
            </if>
            <!-- 申请人科室名称 -->
            <if test="applicantOrgName != null and applicantOrgName != ''">
                AND org.org_name LIKE CONCAT('%', #{applicantOrgName}, '%')
            </if>
            <!-- 管理员 -->
            <if
                    test="manager != null and manager != ''">
                AND ams.manager = #{manager}
            </if>
            <!-- 流程状态 -->
            <if
                    test="prosstas != null and prosstas != ''">
                AND ams.prosstas = #{prosstas}
            </if>


            <!-- 流程状态数组 -->
            <if
                    test="prosstasArray != null and prosstasArray.size() > 0">
                AND ams.prosstas IN
                <foreach
                        collection="prosstasArray" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <!-- 流程实例ID -->
            <if
                    test="processInstanceId != null and processInstanceId != ''">
                AND ams.process_instance_id = #{processInstanceId}
            </if>
            <!-- 创建时间开始 -->
            <if test="createdAtStart != null">
                AND ams.created_at &gt;= #{createdAtStart}
            </if>
            <!-- 创建时间结束 -->
            <if test="createdAtEnd != null">
                AND ams.created_at &lt;= #{createdAtEnd}
            </if>
            <!-- 更新时间开始 -->
            <if test="updatedAtStart != null">
                AND ams.updated_at &gt;= #{updatedAtStart}
            </if>
            <!-- 更新时间结束 -->
            <if test="updatedAtEnd != null">
                AND ams.updated_at &lt;= #{updatedAtEnd}
            </if>
            <!-- 关键字 -->
            <if test="keyword != null and keyword != ''">
                AND (ams.borrow_hospital LIKE CONCAT('%', #{keyword}, '%') OR ams.borrow_asset_fa_code LIKE
                                                                              CONCAT('%', #{keyword}, '%') OR
                     emp.emp_name LIKE CONCAT('%', #{keyword}, '%') OR
                     org.org_name LIKE CONCAT('%', #{keyword}, '%') OR ams.manager LIKE CONCAT('%', #{keyword},
                                                                                               '%') OR
                     prop.asset_name LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <!-- 是否逾期 -->
            <if
                    test="isOverdue != null and isOverdue == '1'">
                AND ams.borrow_end_time &lt; NOW()
                AND (ams.return_time IS NULL OR ams.return_confirm != '1')
            </if>
            <!-- 是否逾期 -->
            <if
                    test="isOverdue != null and isOverdue == '0'">
                AND (ams.borrow_end_time &gt;= NOW()
                    OR ams.return_time IS NOT NULL OR ams.return_confirm = '1')
            </if>
            <!-- 资产状态 -->
            <if
                    test="borrowAssetStatus != null and borrowAssetStatus != ''">
                AND prop.asset_status
                    = #{borrowAssetStatus}
            </if>
            <!-- 资产名称 -->
            <if test="borrowAssetName != null and borrowAssetName != ''">
                AND prop.asset_name LIKE CONCAT('%', #{borrowAssetName}, '%')
            </if>
            <!-- 资产部门 -->
            <if
                    test="borrowAssetDepartment != null and borrowAssetDepartment != ''">
                <!--                AND prop.dept_use LIKE CONCAT('%', #{borrowAssetDepartment}, '%')-->

                and #{borrowAssetDepartment,jdbcType=VARCHAR} = ANY (string_to_array(prop.dept_use
                    , ','))
            </if>
            <!-- 资产类型 -->
            <if
                    test="borrowAssetType != null and borrowAssetType != ''">
                AND prop.asset_type =
                    #{borrowAssetType}
            </if>
        </where>
    </sql>

    <sql id="Order_By_Clause">
        <if
            test="orderBy != null and orderBy != '' and orderDirection != null and orderDirection != ''">
        ORDER BY ${orderBy} ${orderDirection} </if>
        <if
            test="orderBy == null or orderBy == '' or orderDirection == null or orderDirection == ''">
        ORDER BY ams.created_at DESC </if>
    </sql>

    <select id="queryList" resultType="com.jp.med.ams.modules.property.vo.AmsBorrowFromOtherHospitalsVo"> SELECT <include
            refid="Base_Column_List" />
        <include refid="Base_Join_Clause" />
        <include
            refid="Base_Where_Clause" />
        <include refid="Order_By_Clause" />
    </select>

    <select id="queryById" resultType="com.jp.med.ams.modules.property.vo.AmsBorrowFromOtherHospitalsVo"> SELECT <include
            refid="Base_Column_List" />
        <include refid="Base_Join_Clause" /> WHERE ams.id = #{id} </select>

    <select id="queryByProcessInstanceId" resultType="com.jp.med.ams.modules.property.vo.AmsBorrowFromOtherHospitalsVo"> SELECT <include
            refid="Base_Column_List" />
        <include refid="Base_Join_Clause" /> WHERE
        ams.process_instance_id = #{processInstanceId} </select>

    <select id="queryByApplicant" resultType="com.jp.med.ams.modules.property.vo.AmsBorrowFromOtherHospitalsVo"> SELECT <include
            refid="Base_Column_List" />
        <include refid="Base_Join_Clause" /> WHERE ams.applicant =
        #{applicant} ORDER BY ams.created_at DESC </select>

    <select id="queryByBorrowHospital" resultType="com.jp.med.ams.modules.property.vo.AmsBorrowFromOtherHospitalsVo"> SELECT <include
            refid="Base_Column_List" />
        <include refid="Base_Join_Clause" /> WHERE
        ams.borrow_hospital = #{borrowHospital} ORDER BY ams.created_at DESC </select>

    <select id="queryByProsstas" resultType="com.jp.med.ams.modules.property.vo.AmsBorrowFromOtherHospitalsVo"> SELECT <include
            refid="Base_Column_List" />
        <include refid="Base_Join_Clause" /> WHERE ams.prosstas =
        #{prosstas} ORDER BY ams.created_at DESC </select>

    <select id="queryByIsArchived" resultType="com.jp.med.ams.modules.property.vo.AmsBorrowFromOtherHospitalsVo"> SELECT <include
            refid="Base_Column_List" />
        <include refid="Base_Join_Clause" /> WHERE ams.is_archived =
        #{isArchived} ORDER BY ams.created_at DESC </select>

    <select id="queryByApplicantOrg" resultType="com.jp.med.ams.modules.property.vo.AmsBorrowFromOtherHospitalsVo"> SELECT <include
            refid="Base_Column_List" />
        <include refid="Base_Join_Clause" /> WHERE org.org_id =
        #{applicantOrgId} ORDER BY ams.created_at DESC </select>

    <select id="queryByIsOverdue" resultType="com.jp.med.ams.modules.property.vo.AmsBorrowFromOtherHospitalsVo"> SELECT <include
            refid="Base_Column_List" />
        <include refid="Base_Join_Clause" />
        <if
            test="isOverdue == '1'"> WHERE ams.borrow_end_time &lt; NOW() AND (ams.return_time IS
        NULL OR ams.return_confirm != '1') </if>
        <if test="isOverdue == '0'"> WHERE
        (ams.borrow_end_time &gt;= NOW() OR ams.return_time IS NOT NULL OR ams.return_confirm = '1') </if>
        ORDER BY ams.created_at DESC </select>
</mapper>