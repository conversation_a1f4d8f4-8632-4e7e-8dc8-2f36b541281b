<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ams.modules.depr.mapper.write.AmsPropertyDeprWriteMapper">
    <!--写入每月折旧表，这里只是当前暂时的写法，实际按照最终资产系统迁移完成后核对数据-->
    <insert id="in">
        insert into ams_property_depr(
        fa_code,dep,asset_nav,depr_amt,ym,active_flag
        )
        select
        x.fa_code,x.dep,x.asset_nav,
        <![CDATA[
            case when x.depr_mon < x.asset_nav - COALESCE(x.dep,0) then x.depr_mon else x.asset_nav -  COALESCE(x.dep,0) end as depr_mon, '202401','1'
        ]]>
        from
        (select fa_code,dep,asset_nav,
        round(case when depr_mon is not null then depr_mon else asset_nav / exp /12 end,2) depr_mon
            from  ams_property a where 1 = 1
            and dm in ('平均年限法（一）','平均年限法（二）')
        ) x
        where x.depr_mon is not null
    </insert>
</mapper>
