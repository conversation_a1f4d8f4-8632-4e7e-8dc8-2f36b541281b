<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ams.modules.changes.mapper.read.AmsChgRcdReadMapper">
    <select id="queryList" resultType="com.jp.med.ams.modules.changes.vo.AmsChgRcdVo">
        select a.id
                                                   as id,
               a.chg_no                            as chgNo,
               a.fa_code                           as faCode,
               b.asset_name                        as assetName,
               a.chg_before                        as
                                                      chgBefore,
               a.chg_after                            aschgAfter,
               a.chg_date                          as chgDate,
               a.chger                             as chger,
               hei1.emp_name                       as
                                                      chgerName,
               a.chker                             as chker,
               hei2.emp_name                       as chkerName,
               a.chg_type                          as chgType,
               a.chg_rea                           as
                                                      chgRea,
               a.memo                              as memo,
               a.create_time                       as createTime,
               a.hospital_id                       as hospitalId,
               c.asset_type_name                   as assetTypeName,
               cn.asset_type_name                  as assetTypeNName,
               a.redc_way                          as
                                                      redcWay,
               a.transfer_to                       as transferTo,
               jsonb_build_object('property', b.*) as
                                                      propertyJsonData,
               coalesce(ff.org_name,
                        (SELECT string_agg(t2.org_name, ',')
                         FROM ams_depr_asgn t1
                                  INNER JOIN hrm_org t2 ON t2.org_id = t1.org_id
                         WHERE t1.fa_code = a.fa_code),
                        ','
               )                                   as deptUseName
        <!--               ff.org_name                         as deptUseName-->
        from ams_chg_rcd a
                 left join ams_property b on
            a.fa_code = b.fa_code
                 left join hrm_employee_info hei1 on a.chger = hei1.emp_code
                 left join
             hrm_employee_info hei2 on a.chker = hei2.emp_code
                 LEFT JOIN hrm_org ff ON b.dept_use =
                                         ff.org_id
                 left join ams_type_cfg c on c.asset_type_code = b.asset_type
                 left join
             ams_typen_cfg cn on cn.asset_type_code = b.asset_type_n
        <where>
            <if test="chgDateRange != null and chgDateRange.length > 0">
                <![CDATA[
                and (a.chg_date >= #{chgDateRange.[0],jdbcType=VARCHAR} and a.chg_date <= #{chgDateRange.[1],jdbcType=VARCHAR})
                ]]>
            </if>
            <if
                    test="faCode != '' and faCode != null">
                and a.fa_code =
                #{faCode,jdbcType=VARCHAR}
            </if>
            <if
                    test="assetName != null and assetName != ''">
                and
                b
                .
                asset_name
                like
                concat
                (
                '%'
                ,
                concat
                (
                #{assetName,jdbcType=VARCHAR}
                ,
                '%'
                )
                )
            </if>
            <if
                    test="chgType != '' and chgType != null">
                and
                a
                .
                chg_type
                =
                #{chgType,jdbcType=VARCHAR}
            </if>
            <if test="pass != null and pass == true">
                and
                (
                a
                .
                chker
                is
                not
                null
                or
                a
                .
                chker
                !=
                ''
                )
            </if>
            <if test="pass != null and pass == false">
                and
                (
                a
                .
                chker
                is
                null
                or
                a
                .
                chker
                =
                ''
                )
            </if>
            <if test="redcWay != null and redcWay != ''">
                and
                a
                .
                redc_way
                =
                #{redcWay,jdbcType=VARCHAR}
            </if>
        </where>
        order by a.id desc
    </select>


    <select id="queryList2Polymerization" resultType="com.jp.med.ams.modules.changes.vo.AmsChgRcdVo">
        select * from (
        <!-- 信息变动 -->
        select a.id                                             as id,
               a.fa_code::varchar                               as faCode,
               b.asset_name::varchar                            as
                                                                   assetName,
               a.chg_name::varchar                              as chgType,
               coalesce(a.chg_before_name,
                        a.chg_before)::varchar                  as chgBefore,
               coalesce(a.chg_after_name, a.chg_after)::varchar as
                                                                   chgAfter,
               a.chg_date::varchar                              as chgDate,
               coalesce(a.chger_name, a.chger)::varchar         as chger,
               coalesce(a.chker_name, a.chker)::varchar         as chker,
               coalesce(hei1.emp_name, '')::varchar             as
                                                                   chgerName,
               coalesce(hei2.emp_name, '')::varchar             as chkerName,
               coalesce(a.memo, '')::varchar
                                                                as memo,
               coalesce(a.chg_rea, '')::varchar                 as chgRea,
               a.chg_date::varchar                              as createTime,
               a.hospital_id::varchar                           as hospitalId,
               coalesce(a.memo, '')::varchar                    as chgNo
        from ams_info_changes a
                 left join ams_property b on a.fa_code = b.fa_code
                 left join
             hrm_employee_info hei1 on a.chger = hei1.emp_code
                 left join hrm_employee_info hei2 on
            a.chker = hei2.emp_code
        <where>
            <if test="faCode != '' and faCode != null">
                and a.fa_code = #{faCode,jdbcType=VARCHAR}
            </if>
        </where>
        union all <!-- 变动 -->
        select a.id                                 as id,
               a.fa_code::varchar                   as faCode,
               b.asset_name::varchar                as
                                                       assetName,
               a.chg_type::varchar                  as chgType,
               coalesce(a.chg_before, '')::varchar  as chgBefore,
               coalesce(a.chg_after, '')::varchar   as chgAfter,
               a.chg_date::varchar                  as chgDate,
               coalesce(a.chger, '')::varchar       as chger,
               coalesce(a.chker, '')::varchar       as chker,
               coalesce(hei1.emp_name, '')::varchar as chgerName,
               coalesce(hei2.emp_name, '')::varchar as
                                                       chkerName,
               coalesce(a.memo, '')::varchar        as memo,
               coalesce(a.chg_rea, '')::varchar     as
                                                       chgRea,
               a.create_time::varchar               as createTime,
               a.hospital_id::varchar               as hospitalId,
               coalesce(a.chg_no, 0)::VARCHAR       as chgNo
        from ams_chg_rcd a
                 left join ams_property b on
            a.fa_code = b.fa_code
                 left join hrm_employee_info hei1 on a.chger = hei1.emp_code
                 left join
             hrm_employee_info hei2 on a.chker = hei2.emp_code
        <where>
            <if test="faCode != '' and faCode != null">
                and a.fa_code = #{faCode,jdbcType=VARCHAR}
            </if>
        </where>
        union all <!-- 划拨 -->
        select al.id                                as id,
               ar.ams_property_facode::varchar      as faCode,
               coalesce(b.asset_name, '')::varchar  as assetName,
               '划拨'::varchar                      as chgType,
               coalesce(o1.org_name, '')::varchar   as chgBefore,
               coalesce(o2.org_name, '')::varchar   as
                                                       chgAfter,
               al.create_time::varchar              as chgDate,
               coalesce(al.crter, '')::varchar      as chger,
               ''::varchar                          as chker,
               coalesce(hei1.emp_name, '')::varchar as chgerName,
               ''::varchar                          as
                                                       chkerName,
               coalesce(al.remarks, '')::varchar    as memo,
               '资产划拨'::varchar                  as chgRea,
               al.create_time::varchar              as createTime,
               al.hospital_id::varchar              as hospitalId,
               coalesce(al.bchno, '')::varchar      as chgNo
        from ams_alloc al
                 join ams_alloc_ref ar on al.id =
                                          ar.ams_alloc_id
                 left join ams_property b on ar.ams_property_facode = b.fa_code
                 left join
             hrm_org o1 on al.traf_out_dept = o1.org_id
                 left join hrm_org o2 on al.traf_in_dept =
                                         o2.org_id
                 left join hrm_employee_info hei1 on al.crter = hei1.emp_code
        <where>
            <if test="faCode != '' and faCode != null">
                and ar.ams_property_facode =
                    #{faCode,jdbcType=VARCHAR}
            </if>
            <!-- 只查询审批通过的资产划拨数据 -->
            and al.prosstas = '3'
        </where>
        union all <!-- 报废 -->
        select s.id                                  as
                                                        id,
               s.fa_code::varchar                    as faCode,
               coalesce(b.asset_name, '')::varchar   as assetName,
               '报废'::varchar                       as chgType,
               '正常使用'::varchar                   as chgBefore,
               '已报废'::varchar                     as chgAfter,
               s.identified_time::varchar            as chgDate,
               coalesce(sa.applyer, '')::varchar     as chger,
               ''::varchar                           as chker,
               coalesce(hei1.emp_name, '')::varchar  as chgerName,
               ''::varchar                           as
                                                        chkerName,
               coalesce(s.scrap_rea, '')::varchar    as memo,
               coalesce(s.scrap_rea_ext,
                        '')::varchar                 as chgRea,
               sa.apply_time::varchar                as createTime,
               s.hospital_id::varchar                as
                                                        hospitalId,
               coalesce(sa.apply_docno, '')::varchar as chgNo
        from ams_scrap s
                 left join
             ams_property b on s.fa_code = b.fa_code
                 left join ams_scrap_apply sa on s.apply_docno =
                                                 sa.apply_docno
                 left join hrm_employee_info hei1 on sa.applyer = hei1.emp_code
        <where>
            <if test="faCode != '' and faCode != null">
                and s.fa_code = #{faCode,jdbcType=VARCHAR}
            </if>
            <!-- 添加筛选条件，只查询审批通过且鉴定通过的报废数据 -->
            and sa.prosstas = '3' <!-- 审批通过 -->
            and s.allo = '1' <!-- 鉴定通过 -->
        </where>
        union all <!-- 转移 -->
        select t.id                                 as id,
               tr.ams_property_facode::varchar      as faCode,
               coalesce(p.asset_name, '')::varchar  as assetName,
               '转移'::varchar                      as chgType,
               coalesce(o1.org_name, '')::varchar   as chgBefore,
               coalesce(o2.org_name, '')::varchar   as chgAfter,
               t.create_time::varchar               as chgDate,
               coalesce(t.crter, '')::varchar       as chger,
               coalesce(t.recer, '')::varchar       as chker,
               coalesce(hei1.emp_name, '')::varchar as chgerName,
               coalesce(hei2.emp_name, '')::varchar as
                                                       chkerName,
               coalesce(t.remarks, '')::varchar     as memo,
               '资产转移'::varchar                  as chgRea,
               t.create_time::varchar               as createTime,
               t.hospital_id::varchar               as hospitalId,
               coalesce(t.bchno, '')::varchar       as chgNo
        from ams_transfer t
                 join ams_transfer_ref tr on t.id
            = tr.ams_transfer_id
                 left join ams_property p on tr.ams_property_facode = p.fa_code
                 left join hrm_org o1 on t.traf_out_dept = o1.org_id
                 left join hrm_org o2 on t.traf_in_dept =
                                         o2.org_id
                 left join hrm_employee_info hei1 on t.crter = hei1.emp_code
                 left join
             hrm_employee_info hei2 on t.recer = hei2.emp_code
        <where>
            <if test="faCode != '' and faCode != null">
                and tr.ams_property_facode =
                    #{faCode,jdbcType=VARCHAR}
            </if>
            <!-- 添加筛选条件，只查询审批通过的资产转移数据 -->
            and t.prosstas = '3' <!-- 审批通过 -->
        </where>
        ) as t
        order by t.chgDate desc
    </select>
</mapper>