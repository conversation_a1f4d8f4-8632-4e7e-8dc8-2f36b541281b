<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ams.modules.config.mapper.read.AmsDeprCfgReadMapper">


    <select id="queryList" resultType="com.jp.med.ams.modules.config.vo.AmsDeprCfgVo">
        select
            id as id,
            depr_code as deprCode,
            depr_name as deprName,
            flag as flag
        from ams_depr_cfg
        where flag = '1'
        order by id


    </select>

</mapper>
