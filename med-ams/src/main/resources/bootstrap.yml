#-Dspring.cloud.nacos.discovery.ip
spring:
  application:
    name: med-ams
  cloud:
    nacos:
      #      discovery:
      #        ip: *************
      config:
        #        server-addr: *************:8848
        server-addr: ***********:8848
        namespace: dev
        extension-configs:
          - data-id: core-db.yml
            group: core
            refresh: true

          - data-id: ams-others.yml
            group: ams
            refresh: true

        shared-configs:
          - data-id: shared-redis.yml
            group: shared
            refresh: true

          - data-id: shared-feign.yml
            group: shared
            refresh: true

          - data-id: shared-mybatis.yml
            group: shared
            refresh: true

          - data-id: shared-others.yml
            group: shared
            refresh: true

          - data-id: shared-oss.yml
            group: shared
            refresh: true

          - data-id: shared-nacos.yml
            group: shared
            refresh: true

          - data-id: shared-sleuth.yml
            group: shared
            refresh: true

          - data-id: shared-logback.yml
            group: shared
            refresh: true

          - data-id: shared-activiti.yml
            group: shared
            refresh: true

          - data-id: shared-seata.yml
            group: shared
            refresh: true

server:
  port: 10706
  tomcat:
    max-http-form-post-size: 1000MB
    max-swallow-size: 1000MB
    max-parameters: 20000
logging:
  level:
    com.alibaba.nacos.client.auth: off
    com.alibaba.nacos.client.worker: off
