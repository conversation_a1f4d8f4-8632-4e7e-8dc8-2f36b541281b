package com.jp.med.ams.modules.inventory.mapper.read;

import com.jp.med.ams.modules.inventory.dto.AmsIntrTaskDto;
import com.jp.med.ams.modules.inventory.vo.AmsIntrDetailVo;
import com.jp.med.ams.modules.inventory.vo.AmsIntrTaskVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 资产盘点任务
 * <AUTHOR>
 * @email -
 * @date 2023-10-07 10:12:34
 */
@Mapper
public interface AmsIntrTaskReadMapper extends BaseMapper<AmsIntrTaskDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsIntrTaskVo> queryList(AmsIntrTaskDto dto);

    /**
     * 查询资产盘点情况
     * @param dto
     * @return
     */
    List<AmsIntrDetailVo> queryIntr(AmsIntrTaskDto dto);

    /**
     * 查询非分页数据
     * @param dto
     * @return
     */
    List<AmsIntrTaskVo> queryListNoPaging(AmsIntrTaskDto dto);
}
