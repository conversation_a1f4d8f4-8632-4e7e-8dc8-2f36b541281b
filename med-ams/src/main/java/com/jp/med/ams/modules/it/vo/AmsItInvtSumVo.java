package com.jp.med.ams.modules.it.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 耗材库存汇总
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Data
public class AmsItInvtSumVo {


    private Integer id;

	private String key;

	/** 名称 */
	private String name;

	/** 耗材名称 */
	private String name2;

	/** 耗材型号 */
	private String model2;

	/** 物品类型(0:设备,1:耗材) */
	private String type;

	/** 供应商 */
	private String provider;

	/** 品牌 */
	private String brand;

	/** 生产厂家 */
	private String factory;

	/** 型号 */
	private String model;

	/** 价格 */
	@TableField("price")
	private BigDecimal price;

	/** 库存数量 */
	private Integer num;

	/** 备注 */
	private String remark;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 更新人 */
	private String updtr;

	/** 更新时间 */
	private String updateTime;

	/** 删除人 */
	private String delter;

	/** 删除时间 */
	private String deleteTime;

	/** 有效标志(1:删除) */
	private String activeFlag;

	/** 医疗机构id */
	private String hospitalId;

	/** 创建人姓名*/
	private String crterName;

	/** 更新人姓名*/
	private String updtrName;

	/** 入库数量 */
	private Integer addNum;

	/** 出库数量 */
	private Integer reduceNum;

	/** 耗材阈值 */
	private Integer threshold;

	/** 状态(是否需要补货) 0: 库存充足  1: 需补货  2:未设定阈值 */
	private Integer stockStatus;

	/** 需补货数量 */
	private Integer number;

	private Integer serial;
}
