package com.jp.med.ams.modules.amsPropertyInAndOut.service.write.impl;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;

import javax.imageio.ImageIO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsInQrcodeConfigDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.mapper.write.AmsInQrcodeConfigWriteMapper;
import com.jp.med.ams.modules.amsPropertyInAndOut.service.write.AmsInQrcodeConfigWriteService;
import com.jp.med.common.util.QRCodeUtil;

import cn.hutool.core.lang.UUID;

/**
 * 资产入库二维码配置
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-06-03 21:08:37
 */
@Service
@Transactional(readOnly = false)
public class AmsInQrcodeConfigWriteServiceImpl extends ServiceImpl<AmsInQrcodeConfigWriteMapper, AmsInQrcodeConfigDto>
        implements AmsInQrcodeConfigWriteService {

    @Autowired
    private AmsInQrcodeConfigWriteMapper amsInQrcodeConfigWriteMapper;

    @Override
    public void generateQrCode(AmsInQrcodeConfigDto dto) throws IOException {
        UUID uuid = UUID.fastUUID();
        // long mostSignificantBits = Math.abs(uuid.getMostSignificantBits());
        // String qrId = String.valueOf(mostSignificantBits);
        String qrId = uuid.toString(true);
        dto.setQrId(qrId);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        String suffix = "/#/ams/amsPropertyInAndOut/in/thirdPartyIn" + "?qrId=" + dto.getQrId() + "&tmplId="
                + dto.getId();
        dto.setUrlPreFix("https://hrp.zjxrmyy.cn:18090/hrp");
        BufferedImage qrCode = QRCodeUtil.getQRCode(dto.getUrlPreFix() + suffix);
        // 使用ImageIO将BufferedImage写入输出流
        ImageIO.write(qrCode, "jpg", byteArrayOutputStream);

        // 创建一个ByteArrayInputStream对象，用于读取图像数据
        byte[] byteArray = byteArrayOutputStream.toByteArray();
        String base64qrCode = Base64.getEncoder().encodeToString(byteArray);
        dto.setQrCodeBase64(base64qrCode);
        dto.setCreateUser(dto.getSysUser().getHrmUser().getEmpCode());
        dto.setStop(false);
        amsInQrcodeConfigWriteMapper.insert(dto);
    }

}
