package com.jp.med.ams.modules.property.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 资产借用申请表（外院）
 *
 * <AUTHOR>
 * @email -
 * @date 2025-03-14 15:27:44
 */
@Data
@TableName("ams_borrow_from_other_hospitals")
public class AmsBorrowFromOtherHospitalsEntity {

    /**
     * 主键ID
     */
    @TableId("id")
    private Integer id;

    /**
     * 借用医院
     */
    @TableField("borrow_hospital")
    private String borrowHospital;

    /**
     * 借用资产ID
     */
    @TableField("borrow_asset_fa_code")
    private String borrowAssetFaCode;

    /**
     * 借用时间
     */
    @TableField("borrow_time")
    private Date borrowTime;

    /**
     * 借用开始时间
     */
    @TableField("borrow_start_time")
    private Date borrowStartTime;

    /**
     * 借用结束时间
     */
    @TableField("borrow_end_time")
    private Date borrowEndTime;

    /**
     * 借用附件
     */
    @TableField("borrow_attachments")
    private String borrowAttachments;

    /**
     * 资产数量
     */
    @TableField("asset_quantity")
    private Integer assetQuantity;

    /**
     * 归还时间
     */
    @TableField("return_time")
    private Date returnTime;

    /**
     * 归还人
     */
    @TableField("returner")
    private String returner;

    /**
     * 归还确认
     */
    @TableField("return_confirm")
    private String returnConfirm;

    /**
     * 配件是否完好
     */
    @TableField("accessories_intact")
    private String accessoriesIntact;

    /**
     * 审批人ID
     */
    @TableField("approver_id")
    private String approverId;

    /**
     * 申请人
     */
    @TableField("applicant")
    private String applicant;

    /**
     * 负责人
     */
    @TableField("manager")
    private String manager;

    /**
     * 电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 流程实例ID
     */
    @TableField("process_instance_id")
    private String processInstanceId;

    /**
     * 流程状态
     */
    @TableField("prosstas")
    private String prosstas;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

}
