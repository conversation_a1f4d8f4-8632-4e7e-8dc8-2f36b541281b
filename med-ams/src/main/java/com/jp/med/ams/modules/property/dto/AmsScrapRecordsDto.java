package com.jp.med.ams.modules.property.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.ofdrw.layout.edit.Attachment;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 资料报废申请资料表
 * <AUTHOR>
 * @email -
 * @date 2023-09-05 17:47:27
 */
@Data
@TableName("ams_scrap_records" )
public class AmsScrapRecordsDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /**
     * 报废年度
     */
    @TableField("annual")
    private Integer annual;


    /**
     * 附件
     */
    @TableField("data")
    @JsonIgnore
    private String data;


    @TableField(exist = false)
    private List<Attachment> attachmentList;

    @TableField(exist = false)
    private ApprovalOpinions approvalOpinions;


    @Data
    public static class ApprovalOpinions {
        /**
         * 国有资产领导小组意见
         */
        private String stateOwnedAssetLeadershipGroup;
        /**
         * 院长办公会意见
         */
        private String presidentOffice;
        /**
         * 党委会意见
         */
        private String partyCommittee;
    }

    @Getter
    @Setter
    public static class Attachment {
        private String label;
        @JsonIgnore
        private MultipartFile file;
        private String fileUrl;
        /**
         * fileUrl alis
         */
        private String value;
        private String type;
    }

}
