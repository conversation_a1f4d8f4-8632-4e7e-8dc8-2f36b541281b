package com.jp.med.ams.modules.property.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.util.Date;

/**
 * 资产人员科室分类代理管理表
 *
 * <AUTHOR>
 * @email -
 * @date 2024-11-05 11:43:44
 */
@Data
@TableName("ams_asset_proxy")
public class AmsAssetProxyDto extends CommonQueryDto {

    /**
     * 主键ID
     */
    @TableId("id")
    private Integer id;

    /**
     * 代理类型(1:指定科室代理人 2:指定人员代理人 3:指定科室代理分类 4:指定人员代理分类)
     */
    @TableField("proxy_type")
    private String proxyType;

    /**
     * 代理人科室编码
     */
    @TableField("proxy_dept_code")
    private String proxyDeptCode;

    /**
     * 代理人代码
     */
    @TableField("proxy_user_code")
    private String proxyUserCode;

    /**
     * 资产分类（使用逗号分隔的文本数组）
     */
    @TableField("asset_type")
    private String assetType;

    /**
     * 资产新分类（使用逗号分隔的文本数组）
     */
    @TableField("asset_type_new")
    private String assetTypeNew;

    /**
     * 状态(0:禁用 1:启用)
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("creater")
    private String creater;

    /**
     * 最后更新时间
     */
    @TableField("latest_update_time")
    private Date latestUpdateTime;

    /**
     * 更新人
     */
    @TableField("updator")
    private String updator;

    @TableField(exist = false)
    @ExcelIgnore
    private boolean sqlAutowiredHospitalCondition = true;

}
