package com.jp.med.ams.modules.inventory.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.ams.modules.inventory.mapper.write.AmsIntrTodoWriteMapper;
import com.jp.med.ams.modules.inventory.vo.AmsIntrDetailVo;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.mapper.read.AmsPropertyReadMapper;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.exception.AppException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.inventory.mapper.read.AmsIntrTaskReadMapper;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTaskDto;
import com.jp.med.ams.modules.inventory.vo.AmsIntrTaskVo;
import com.jp.med.ams.modules.inventory.service.read.AmsIntrTaskReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
public class AmsIntrTaskReadServiceImpl extends ServiceImpl<AmsIntrTaskReadMapper, AmsIntrTaskDto> implements AmsIntrTaskReadService {

    @Autowired
    private AmsIntrTaskReadMapper amsIntrTaskReadMapper;
    @Qualifier("amsPropertyReadMapper")
    @Autowired
    private AmsPropertyReadMapper amsPropertyReadMapper;
    @Qualifier("amsIntrTodoWriteMapper")
    @Autowired
    private AmsIntrTodoWriteMapper amsIntrTodoWriteMapper;

    @Override
    public List<AmsIntrTaskVo> queryList(AmsIntrTaskDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsIntrTaskReadMapper.queryList(dto);
    }

    @Override
    public List<AmsIntrTaskVo> queryListNoPaging(AmsIntrTaskDto dto) {
        dto.setStatus("0");
        return amsIntrTaskReadMapper.queryListNoPaging(dto);
    }

    @Override
    public List<AmsIntrDetailVo> queryIntr(AmsIntrTaskDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return amsIntrTaskReadMapper.queryIntr(dto);
    }

    @Override
    public Map<String, List<AmsIntrDetailVo>> diff(AmsIntrTaskDto dto) {
        Integer taskId = dto.getId();
        AmsIntrTaskDto amsIntrTaskDto = amsIntrTaskReadMapper.selectById(taskId);
        if (amsIntrTaskDto == null) {
            throw new AppException("任务不存在");

        }
        List<AmsIntrDetailVo> amsIntrDetailVos = queryIntr(dto);


        String intrTaskDtoDept = amsIntrTaskDto.getDept();
        AmsPropertyDto amsPropertyDto = new AmsPropertyDto();
        amsPropertyDto.setType(MedConst.TYPE_1);
        amsPropertyDto.setDeptUse(intrTaskDtoDept);
        amsPropertyDto.setDeptField("dept_use");
        amsPropertyDto.setDeptCode(intrTaskDtoDept);

        List<AmsPropertyVo> amsPropertyVos = amsPropertyReadMapper.queryMainList(amsPropertyDto);
        List<AmsIntrDetailVo> newAmsIntrDetailVo = amsPropertyVos.stream().map(item -> {
            AmsIntrDetailVo amsIntrDetailVo = new AmsIntrDetailVo();
            amsIntrDetailVo.setFaCode(item.getFaCode());
            amsIntrDetailVo.setAssetName(item.getAssetName());
            amsIntrDetailVo.setDeptUse(item.getDeptUse());
            amsIntrDetailVo.setOrgName(item.getDeptName());
            return amsIntrDetailVo;
        }).collect(Collectors.toList());

        // 将列表转换为集合，便于比较
        Set<String> amsIntrDetailFaCodes = amsIntrDetailVos.stream()
                .map(AmsIntrDetailVo::getFaCode)
                .collect(Collectors.toSet());

        Set<String> newAmsIntrDetailFaCodes = newAmsIntrDetailVo.stream()
                .map(AmsIntrDetailVo::getFaCode)
                .collect(Collectors.toSet());

        // 找出缺少的资产（存在于newAmsIntrDetailVo但不存在于amsIntrDetailVos）
        List<AmsIntrDetailVo> in = newAmsIntrDetailVo.stream()
                .filter(vo -> !amsIntrDetailFaCodes.contains(vo.getFaCode()))
                .collect(Collectors.toList());

        // 找出多余的资产（存在于amsIntrDetailVos但不存在于newAmsIntrDetailVo）
        List<AmsIntrDetailVo> out = amsIntrDetailVos.stream()
                .filter(vo -> !newAmsIntrDetailFaCodes.contains(vo.getFaCode()))
                .collect(Collectors.toList());

        // 将结果放入map中
        Map<String, List<AmsIntrDetailVo>> resultMap = new HashMap<>();
        resultMap.put("in", in);
        resultMap.put("out", out);
        return resultMap;
    }

}
