package com.jp.med.ams.modules.property.demo;

import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.service.write.AmsPropertyInboundWriteService;
import com.jp.med.ams.modules.property.strategy.AmsPropertyInboundStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 资产入库策略模式演示类
 * 
 * 展示如何使用重构后的策略模式进行资产入库操作
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Component
public class AmsPropertyInboundStrategyDemo {

    @Autowired
    private AmsPropertyInboundWriteService inboundService;

    @Autowired
    private AmsPropertyInboundStrategyFactory strategyFactory;

    /**
     * 演示不同类型资产的入库操作
     */
    public void demonstrateInboundStrategies() {
        log.info("=== 资产入库策略模式演示开始 ===");

        // 展示所有支持的资产类型
        showSupportedAssetTypes();

        // 演示固定资产入库
        demonstrateFixedAssetInbound();

        // 演示非固定资产入库
        demonstrateNonFixedAssetInbound();

        // 演示子项资产入库
        demonstrateSubAssetInbound();

        // 演示预入库资产
        demonstratePreWarehouseInbound();

        // 演示低值易耗资产入库
        demonstrateConsumableInbound();

        // 演示批量入库
        demonstrateBatchInbound();

        log.info("=== 资产入库策略模式演示结束 ===");
    }

    /**
     * 展示所有支持的资产类型
     */
    private void showSupportedAssetTypes() {
        log.info("--- 支持的资产类型 ---");
        var supportedTypes = strategyFactory.getSupportedAssetTypes();
        var descriptions = strategyFactory.getAllStrategyDescriptions();

        for (String type : supportedTypes) {
            log.info("资产类型: {} - {}", type, descriptions.get(type));
        }
    }

    /**
     * 演示固定资产入库
     */
    private void demonstrateFixedAssetInbound() {
        log.info("--- 演示固定资产入库 ---");

        AmsPropertyDto fixedAsset = createBaseAssetDto("1", "演示固定资产", new BigDecimal("50000"));
        fixedAsset.setAssetType("01"); // 设备类

        if (inboundService.isSupportedAssetType("1")) {
            log.info("策略描述: {}", inboundService.getStrategyDescription("1"));
            // 注意：实际入库需要完整的数据库环境
            // inboundService.saveInboundData(fixedAsset);
            log.info("固定资产入库演示完成");
        }
    }

    /**
     * 演示非固定资产入库
     */
    private void demonstrateNonFixedAssetInbound() {
        log.info("--- 演示非固定资产入库 ---");

        AmsPropertyDto nonFixedAsset = createBaseAssetDto("2", "演示非固定资产", new BigDecimal("3000"));

        if (inboundService.isSupportedAssetType("2")) {
            log.info("策略描述: {}", inboundService.getStrategyDescription("2"));
            // inboundService.saveInboundData(nonFixedAsset);
            log.info("非固定资产入库演示完成");
        }
    }

    /**
     * 演示子项资产入库
     */
    private void demonstrateSubAssetInbound() {
        log.info("--- 演示子项资产入库 ---");

        AmsPropertyDto subAsset = createBaseAssetDto("3", "演示子项资产", new BigDecimal("5000"));
        subAsset.setSaCode("PARENT001"); // 设置父项资产码

        if (inboundService.isSupportedAssetType("3")) {
            log.info("策略描述: {}", inboundService.getStrategyDescription("3"));
            // inboundService.saveInboundData(subAsset);
            log.info("子项资产入库演示完成");
        }
    }

    /**
     * 演示预入库资产
     */
    private void demonstratePreWarehouseInbound() {
        log.info("--- 演示预入库资产 ---");

        AmsPropertyDto preWarehouseAsset = createBaseAssetDto("4", "演示预入库资产", new BigDecimal("20000"));
        preWarehouseAsset.setQrId("QR001");
        preWarehouseAsset.setThirdPartyInDept("DEPT001");
        preWarehouseAsset.setThirdPartyInUserName("USER001");

        if (inboundService.isSupportedAssetType("4")) {
            log.info("策略描述: {}", inboundService.getStrategyDescription("4"));
            // inboundService.saveInboundData(preWarehouseAsset);
            log.info("预入库资产演示完成");
        }
    }

    /**
     * 演示低值易耗资产入库
     */
    private void demonstrateConsumableInbound() {
        log.info("--- 演示低值易耗资产入库 ---");

        AmsPropertyDto consumableAsset = createBaseAssetDto("8", "演示低值易耗资产", new BigDecimal("500"));

        if (inboundService.isSupportedAssetType("8")) {
            log.info("策略描述: {}", inboundService.getStrategyDescription("8"));
            // inboundService.saveInboundData(consumableAsset);
            log.info("低值易耗资产入库演示完成");
        }
    }

    /**
     * 演示批量入库
     */
    private void demonstrateBatchInbound() {
        log.info("--- 演示批量入库 ---");

        AmsPropertyDto batchAsset = createBaseAssetDto("2", "批量入库资产", new BigDecimal("1000"));
        batchAsset.setStockNumber(5); // 设置批量数量

        if (inboundService.isSupportedAssetType("2")) {
            log.info("批量入库数量: {}", batchAsset.getStockNumber());
            // String result = inboundService.saveTempInboundData(batchAsset);
            // log.info("批量入库结果: {}", result);
            log.info("批量入库演示完成");
        }
    }

    /**
     * 创建基础资产DTO
     * 
     * @param type  资产类型
     * @param name  资产名称
     * @param value 资产原值
     * @return 资产DTO
     */
    private AmsPropertyDto createBaseAssetDto(String type, String name, BigDecimal value) {
        AmsPropertyDto dto = new AmsPropertyDto();
        dto.setType(type);
        dto.setAssetName(name);
        dto.setAssetNav(value);
        dto.setHospitalId("H001");
        dto.setAssetType("01");
        dto.setUnit("台");
        dto.setSupplier("演示供应商");
        dto.setBrand("演示品牌");
        dto.setManufacturer("演示厂家");
        return dto;
    }

    /**
     * 检查策略支持情况
     *
     * @param assetType 资产类型
     */
    public void checkStrategySupport(String assetType) {
        if (inboundService.isSupportedAssetType(assetType)) {
            log.info("支持资产类型 {}: {}", assetType, inboundService.getStrategyDescription(assetType));
        } else {
            log.warn("不支持的资产类型: {}", assetType);
        }
    }

    /**
     * 演示完整的入库流程（包括记录处理）
     */
    public void demonstrateCompleteInboundProcess() {
        log.info("--- 演示完整的入库流程 ---");

        AmsPropertyDto asset = createBaseAssetDto("1", "完整流程演示资产", new BigDecimal("30000"));

        // 模拟添加记录（实际使用中会有文件上传等）
        // List<AmsRecordsDto> records = new ArrayList<>();
        // AmsRecordsDto record = new AmsRecordsDto();
        // record.setFileName("测试文件.pdf");
        // record.setFlag("1");
        // records.add(record);
        // asset.setRecords(records);

        log.info("完整入库流程演示：");
        log.info("1. 策略验证和选择");
        log.info("2. 入库前信息校验补全");
        log.info("3. 资产编码生成");
        log.info("4. 特定属性设置");
        log.info("5. 记录和附件处理");
        log.info("6. 数据库操作");
        log.info("7. 后处理逻辑");

        // 注意：实际入库需要完整的数据库环境
        // inboundService.saveInboundData(asset);

        log.info("完整入库流程演示完成");
    }
}
