package com.jp.med.ams.modules.config.service.read.impl;

import com.jp.med.ams.modules.config.dto.AmsStockCfgDto;
import com.jp.med.ams.modules.config.mapper.read.AmsStockCfgReadMapper;
import com.jp.med.ams.modules.config.service.read.AmsStockCfgReadService;
import com.jp.med.ams.modules.config.vo.AmsStockCfgVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
public class AmsStockCfgReadServiceImpl implements AmsStockCfgReadService {

    @Autowired
    private AmsStockCfgReadMapper amsStockCfgReadMapper;


    @Override
    public List<AmsStockCfgVo> queryTableColumn() {
        return amsStockCfgReadMapper.queryTableColumn();
    }

    @Override
    public List<AmsStockCfgVo> queryList(AmsStockCfgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return amsStockCfgReadMapper.queryList(dto);
    }

    @Override
    public List<AmsStockCfgVo> queryCard(AmsStockCfgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return amsStockCfgReadMapper.queryCard(dto);
    }


}
