package com.jp.med.ams.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.util.OSSUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.config.mapper.read.AmsLablCfgReadMapper;
import com.jp.med.ams.modules.config.dto.AmsLablCfgDto;
import com.jp.med.ams.modules.config.vo.AmsLablCfgVo;
import com.jp.med.ams.modules.config.service.read.AmsLablCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsLablCfgReadServiceImpl extends ServiceImpl<AmsLablCfgReadMapper, AmsLablCfgDto> implements AmsLablCfgReadService {

    @Autowired
    private AmsLablCfgReadMapper amsLablCfgReadMapper;

    @Override
    public List<AmsLablCfgVo> queryList(AmsLablCfgDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<AmsLablCfgVo> amsLablCfgVos = amsLablCfgReadMapper.queryList(dto);
        for (AmsLablCfgVo amsLablCfgVo : amsLablCfgVos) {
            if (!StringUtils.isEmpty(amsLablCfgVo.getImg())){
                amsLablCfgVo.setImgPath(OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_AMS, amsLablCfgVo.getImg()));
            }
            amsLablCfgVo.setAttachmentPath(OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_AMS, amsLablCfgVo.getAttachment()));
        }
        return amsLablCfgVos;
    }

}
