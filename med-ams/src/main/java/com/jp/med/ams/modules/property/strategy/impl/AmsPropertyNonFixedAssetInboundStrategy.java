package com.jp.med.ams.modules.property.strategy.impl;

import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.mapper.read.AmsPropertyReadMapper;
import com.jp.med.ams.modules.property.strategy.AmsPropertyInboundStrategy;
import com.jp.med.common.exception.AppException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * 非固定资产入库策略
 * 
 * 处理非固定资产（type=2）的入库逻辑
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Component
public class AmsPropertyNonFixedAssetInboundStrategy implements AmsPropertyInboundStrategy {

    @Autowired
    private AmsPropertyReadMapper amsPropertyReadMapper;

    /**
     * 非固定资产UDI长度
     */
    private static final int UDI_LENGTH_1 = 11;

    /**
     * 非固定资产编码开头(二维码)
     */
    private static final String TYPE_1_START = "1";

    /**
     * 非固定资产编码开头(固定资产码)
     */
    private static final String TYPE_2_START = "W";

    @Override
    public String getSupportedAssetType() {
        return "2";
    }

    @Override
    public void validateAssetData(AmsPropertyDto dto) {
        // 非固定资产基本验证
        if (dto.getAssetNav() == null || dto.getAssetNav().compareTo(BigDecimal.ZERO) <= 0) {
            throw new AppException("非固定资产原值必须大于0");
        }

        if (StringUtils.isBlank(dto.getAssetName())) {
            throw new AppException("非固定资产名称不能为空");
        }
    }

    @Override
    public void generateAssetCodes(AmsPropertyDto dto) {
        // 查询当前最大的ID
        Long maxCode = amsPropertyReadMapper.queryMaxId(dto);
        // 将最大ID加1后转换为字符串形式作为基础编码
        String faCode = String.valueOf(maxCode + 1);

        Long maxAssetCode = amsPropertyReadMapper.queryMaxAssetCode(dto);
        String assetCode;
        if (maxAssetCode != 0) {
            assetCode = String.valueOf(maxAssetCode + 1);
        } else {
            assetCode = "1";
        }

        // 生成二维码
        int zerosToAdd = UDI_LENGTH_1 - faCode.length();
        StringBuilder stringBuilder = new StringBuilder(faCode);
        for (int i = 0; i < zerosToAdd; i++) {
            stringBuilder.insert(0, '0');
        }
        String uid = stringBuilder.toString();

        // 添加前缀
        faCode = TYPE_2_START + faCode;
        uid = TYPE_1_START + uid;
        assetCode = TYPE_2_START + assetCode;

        dto.setFaCode(faCode);
        dto.setUid(uid);
        dto.setAssetCode(assetCode);
    }

    @Override
    public void setAssetSpecificProperties(AmsPropertyDto dto) {
        // 设置数量默认值
        if (Objects.isNull(dto.getCnt()) || BigDecimal.ZERO.compareTo(dto.getCnt()) < 1) {
            dto.setCnt(BigDecimal.ONE);
        }

        // 设置入库日期
        if (Objects.isNull(dto.getInptDate()) || dto.getInptDate().isEmpty()) {
            dto.setInptDate(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        }

        // 设置资产大类
        if (dto.getAssetType() != null && !dto.getAssetType().isEmpty()) {
            dto.setAssetClassification(dto.getAssetType().substring(0, 1));
        }

        // 设置注销标志为0（未注销）
        dto.setIsCanc("0");

        // 注意：净值、残值、累计折旧等基础属性在 checkAndCompleteInfo 中统一设置
    }

    @Override
    public void preProcessInbound(AmsPropertyDto dto, Boolean isConfirmAdd) {
        // 非固定资产入库前处理
        // 设置入库日期
        if (dto.getInptDate() == null || dto.getInptDate().isEmpty()) {
            dto.setInptDate(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        }
    }

    @Override
    public void postProcessInbound(AmsPropertyDto dto, Boolean isConfirmAdd) {
        // 非固定资产入库后处理逻辑
        // 可以在这里添加非固定资产特有的后处理逻辑
    }

    @Override
    public boolean requiresPostingLockCheck() {
        return false;
    }

    @Override
    public String getStrategyDescription() {
        return "非固定资产入库策略 - 处理非固定资产的编码生成、验证和入库逻辑";
    }
}
