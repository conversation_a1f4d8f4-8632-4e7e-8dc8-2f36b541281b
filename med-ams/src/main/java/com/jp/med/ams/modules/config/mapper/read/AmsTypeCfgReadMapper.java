package com.jp.med.ams.modules.config.mapper.read;

import com.jp.med.ams.modules.config.dto.AmsTypeCfgDto;
import com.jp.med.ams.modules.config.vo.AmsTypeCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 资产类型配表
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 14:10:05
 */
@Mapper
public interface AmsTypeCfgReadMapper extends BaseMapper<AmsTypeCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsTypeCfgVo> queryList(AmsTypeCfgDto dto);

    /**
     * 查询父级
     */
    @Select("select * from ams_type_cfg where parent_code is null or parent_code = ''")
    List<AmsTypeCfgVo> queryParent();
}
