package com.jp.med.ams.modules.property.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 资产月度折旧扎帐
 *
 * <AUTHOR>
 * @email -
 * @date 2025-02-20 10:40:00
 */
@Data
public class AmsMonthlyDepreciationPostingVo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 医院ID
     */
    private String hospitalId;

    /**
     * 折旧日期 格式:yyyymmdd
     */
    private String postingDate;

    /**
     * 扎帐状态
     */
    private String postingStatus;

    /**
     * 折旧总金额
     */
    private BigDecimal totalAmount;

    /**
     * 备注说明
     */
    private String remarks;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

}
