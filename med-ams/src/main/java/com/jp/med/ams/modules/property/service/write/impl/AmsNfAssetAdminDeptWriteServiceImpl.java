package com.jp.med.ams.modules.property.service.write.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.property.dto.AmsNfAssetAdminDeptDto;
import com.jp.med.ams.modules.property.mapper.write.AmsNfAssetAdminDeptWriteMapper;
import com.jp.med.ams.modules.property.service.write.AmsNfAssetAdminDeptWriteService;

/**
 * 科室固定资产归口科室表
 * 
 * <AUTHOR>
 * @email -
 * @date 2025-03-13 11:48:41
 */
@Service
@Transactional(readOnly = false)
public class AmsNfAssetAdminDeptWriteServiceImpl extends
        ServiceImpl<AmsNfAssetAdminDeptWriteMapper, AmsNfAssetAdminDeptDto> implements AmsNfAssetAdminDeptWriteService {

    @Autowired
    private AmsNfAssetAdminDeptWriteMapper amsNfAssetAdminDeptWriteMapper;

    /**
     * @param dto 实体对象
     * @return
     */
    @Override
    public boolean save(AmsNfAssetAdminDeptDto dto) {
        super.save(dto);
//        amsNfAssetAdminDeptWriteMapper.syncAssetManageDeptCode(dto.getId());
        return false;
    }

    /**
     * @param dto
     * @return
     */
    @Override
    public boolean update(AmsNfAssetAdminDeptDto dto) {
        super.updateById(dto);
//        amsNfAssetAdminDeptWriteMapper.syncAssetManageDeptCode(dto.getId());
        return false;
    }
}
