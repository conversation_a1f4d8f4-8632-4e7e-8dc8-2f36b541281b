package com.jp.med.ams.modules.config.service.write.impl;
import com.jp.med.ams.modules.config.mapper.read.AmsTypenCfgReadMapper;
import com.jp.med.ams.modules.config.vo.AmsTypenCfgVo;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.BatchUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.ams.modules.config.mapper.write.AmsTypenCfgWriteMapper;
import com.jp.med.ams.modules.config.dto.AmsTypenCfgDto;
import com.jp.med.ams.modules.config.service.write.AmsTypenCfgWriteService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 新资产分类
 * <AUTHOR>
 * @email -
 * @date 2023-12-06 11:20:53
 */
@Service
@Transactional(readOnly = false)
public class AmsTypenCfgWriteServiceImpl extends ServiceImpl<AmsTypenCfgWriteMapper, AmsTypenCfgDto> implements AmsTypenCfgWriteService {

    @Resource
    private AmsTypenCfgWriteMapper amsTypenCfgWriteMapper;

    @Resource
    private AmsTypenCfgReadMapper amsTypenCfgReadMapper;

    @Override
    public boolean updateById(AmsTypenCfgDto dto){
        dto.setSqlAutowiredHospitalCondition(true);
        List<AmsTypenCfgVo> cfgVos = amsTypenCfgReadMapper.queryParentOrChild(dto);
        List<AmsTypenCfgDto> changeList = new ArrayList<>();
        for (AmsTypenCfgVo cfgVo : cfgVos) {
            AmsTypenCfgDto cfgDto = new AmsTypenCfgDto();
            cfgDto.setId(cfgVo.getId());
            cfgDto.setFlag(dto.getFlag());
            changeList.add(cfgDto);
        }
        BatchUtil.batch("updateById", changeList, AmsTypenCfgWriteMapper.class);
        amsTypenCfgWriteMapper.updateById(dto);
        return true;
    }



}
