package com.jp.med.ams.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 资产类型配表
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 14:10:05
 */
@Data
@TableName("ams_type_cfg" )
public class AmsTypeCfgDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 类型编码 */
    @TableField("asset_type_code")
    private String assetTypeCode;

    /** 类型名称 */
    @TableField("asset_type_name")
    private String assetTypeName;

    /** 上级编码 */
    @TableField("parent_code")
    private String parentCode;

    /** 有效标志 */
    @TableField("flag")
    private String flag;

    /** 建议使用年限 */
    @TableField("years")
    private Integer years;

    /** 折旧方式 */
    @TableField("depr_code")
    private String deprCode;

    /** 计量单位 */
    @TableField("unit")
    private String unit;

    /** 残值率 */
    @TableField("resr")
    private BigDecimal resr;

    /** 卡片样式 */
    @TableField("stock_cfg")
    private String stockCfg;
}
