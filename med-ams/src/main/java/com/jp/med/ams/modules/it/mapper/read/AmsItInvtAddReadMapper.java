package com.jp.med.ams.modules.it.mapper.read;

import com.jp.med.ams.modules.it.dto.AmsItInvtAddDto;
import com.jp.med.ams.modules.it.dto.AmsItInvtCfgDto;
import com.jp.med.ams.modules.it.dto.AmsItInvtSumDto;
import com.jp.med.ams.modules.it.vo.AmsItInvtAddVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 *  耗材入库日志
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Mapper
public interface AmsItInvtAddReadMapper extends BaseMapper<AmsItInvtAddDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsItInvtAddVo> queryList(AmsItInvtAddDto dto);

    List<AmsItInvtAddVo> queryIsUsed(AmsItInvtCfgDto dto);

}
