package com.jp.med.ams.modules.property.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.property.dto.AmsAssetSplitDto;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;

import java.util.List;

/**
 * 资产拆分表
 *
 * <AUTHOR>
 * @email -
 * @date 2025-01-09 12:51:02
 */
public interface AmsAssetSplitWriteService extends IService<AmsAssetSplitDto> {

    /**
     * 拆分资产
     *
     * @param dto 包含要拆分的资产信息
     * @return 返回拆分后新增的资产列表
     */
    List<AmsPropertyDto> splitAsset(AmsPropertyDto dto);
}

