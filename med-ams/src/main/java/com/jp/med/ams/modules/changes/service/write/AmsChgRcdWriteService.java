package com.jp.med.ams.modules.changes.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;

/**
 * 资产变更记录表
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-12-25 20:45:51
 */
public interface AmsChgRcdWriteService extends IService<AmsChgRcdDto> {

    boolean remove(AmsChgRcdDto amsChgRcdDto);

    Object saveAmsChgRcd(AmsChgRcdDto amsChgRcdDto);

    void approval(AmsChgRcdDto dto);
}
