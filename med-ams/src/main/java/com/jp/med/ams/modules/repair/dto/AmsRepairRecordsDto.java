package com.jp.med.ams.modules.repair.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 资料报废申请资料表
 * <AUTHOR>
 * @email -
 * @date 2023-09-05 17:47:27
 */
@Data
@TableName("ams_repair_records" )
public class AmsRepairRecordsDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 资产维修申请ID */
    @TableField("repair_id")
    private Integer repairId;

    /** 上传时间 */
    @TableField("upload_time")
    private String uploadTime;

    /** 附件 */
    @TableField("attachment")
    private String attachment;

    /** 附件名称 */
    @TableField("file_name")
    private String fileName;

    /** 有效标志 */
    @TableField("flag")
    private String flag;

    @TableField(exist = false)
    private MultipartFile file;

}
