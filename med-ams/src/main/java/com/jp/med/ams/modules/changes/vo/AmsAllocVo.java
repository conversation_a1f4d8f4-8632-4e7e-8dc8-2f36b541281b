package com.jp.med.ams.modules.changes.vo;

import java.util.List;

import lombok.Data;

/**
 * 资产划拨
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-09-08 11:32:33
 */
@Data
public class AmsAllocVo {

	/**
	 * id
	 */
	private Long id;

	/**
	 * 资产
	 */
	private String faCode;

	/**
	 * 转出科室
	 */
	private String trafOutDept;

	/**
	 * 转入科室
	 */
	private String trafInDept;

	/**
	 * 创建人
	 */
	private String crter;

	/**
	 * 创建时间
	 */
	private String createTime;

	/**
	 * 备注
	 */
	private String remarks;

	/**
	 * 业务状态
	 */
	private String prosstas;

	/**
	 * 医疗机构id
	 */
	private String hospitalId;

	/**
	 * 有效标准
	 */
	private String activeFlag;

	/**
	 * 转出科室名称
	 */
	private String trafOutDeptName;

	/**
	 * 转入科室名称
	 */
	private String trafInDeptName;

	/**
	 * 创建人名称
	 */
	private String crterName;

	/**
	 * 接收人名称
	 */
	private String recerName;

	/**
	 * 资产名称
	 */
	private String assetName;

	/**
	 * 审核批次号
	 */
	private String bchno;

    // a.traf_in_storage_location ,
    // bb.org_name as traf_in_dept_manage_name,
    // asc2.storage_area as traf_in_storage_area_name

	private String trafInStorageLocation;

	private String trafInDeptManageName;

	private String trafInStorageAreaName;

    private String assetNames;

    private List<AmsPropertyInfo> propertyInfos;
}
