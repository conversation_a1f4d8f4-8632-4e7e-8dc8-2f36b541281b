package com.jp.med.ams.modules.it.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.it.dto.AmsItInvtCfgDto;
import com.jp.med.ams.modules.it.dto.AmsItInvtOrgConsumableDto;
import com.jp.med.ams.modules.it.vo.AmsItInvtCfgVo;
import com.jp.med.ams.modules.it.vo.AmsItInvtOrgConsumableVo;

import java.util.List;

/**
 * 科室耗材配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-15 15:04:47
 */
public interface AmsItInvtOrgConsumableReadService extends IService<AmsItInvtOrgConsumableDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<AmsItInvtOrgConsumableVo> queryList(AmsItInvtOrgConsumableDto dto);

    List<AmsItInvtCfgVo>   findEquType();

    List<AmsItInvtCfgVo>   findEquName();

    List<AmsItInvtCfgVo>  findConsumName();

    List<AmsItInvtOrgConsumableVo>  findcfgByOrgId(AmsItInvtOrgConsumableDto dto);

    List<AmsItInvtOrgConsumableVo> SearchOrgList(AmsItInvtOrgConsumableDto dto);
}

