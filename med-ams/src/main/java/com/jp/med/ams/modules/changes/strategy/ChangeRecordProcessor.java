package com.jp.med.ams.modules.changes.strategy;

import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;

import java.util.List;

/**
 * 变更记录处理器接口
 * 使用策略模式处理不同类型的资产变更
 */
public interface ChangeRecordProcessor {
    
    /**
     * 判断是否支持处理该类型的变更
     * @param dto 变更记录DTO
     * @return 是否支持
     */
    boolean supports(AmsChgRcdDto dto);
    
    /**
     * 处理变更记录
     * @param dto 变更记录DTO
     * @param context 处理上下文
     * @return 处理结果
     */
    ProcessResult process(AmsChgRcdDto dto, ProcessContext context);
    
    /**
     * 处理结果
     */
    class ProcessResult {
        private List<AmsChgRcdDto> changeRecords;
        private List<AmsPropertyDto> propertyUpdates;
        private boolean success;
        private String message;
        
        public ProcessResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        public ProcessResult(List<AmsChgRcdDto> changeRecords, List<AmsPropertyDto> propertyUpdates) {
            this.changeRecords = changeRecords;
            this.propertyUpdates = propertyUpdates;
            this.success = true;
        }
        
        // Getters and Setters
        public List<AmsChgRcdDto> getChangeRecords() {
            return changeRecords;
        }
        
        public void setChangeRecords(List<AmsChgRcdDto> changeRecords) {
            this.changeRecords = changeRecords;
        }
        
        public List<AmsPropertyDto> getPropertyUpdates() {
            return propertyUpdates;
        }
        
        public void setPropertyUpdates(List<AmsPropertyDto> propertyUpdates) {
            this.propertyUpdates = propertyUpdates;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
    }
}
