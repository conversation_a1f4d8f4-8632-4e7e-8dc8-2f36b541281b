package com.jp.med.ams.modules.property.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * 资产报废人员管理
 * <AUTHOR>
 * @email -
 * @date 2024-03-28 09:39:22
 */
@Data
@TableName("ams_scrap_admin" )
public class AmsScrapAdminDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 操作人 */
    @TableField("opter")
    private String opter;

    /** 资产类型 */
    @TableField("asset_type")
    private String assetType;

    /** 资产类型(新) */
    @TableField("asset_type_n")
    private String assetTypeN;

    /** 科室 */
    @TableField("dept")
    private String dept;

    /**
     * 科室
     */
    @TableField("storage_areas")
    private String storageAreas;

    /** 有效标志 */
    @TableField("active_flag")
    private String activeFlag;

    /** 医疗机构编码 */
    @TableField("hospital_id")
    private String hospitalId;

    /** 资产类型 */
    @TableField(exist = false)
    private List<String> assetTypes;

    /** 资产类型(新) */
    @TableField(exist = false)
    private List<String> assetTypeNs;

    /** 科室(集合) */
    @TableField(exist = false)
    private List<String> depts;

    /**
     * 科室(集合)
     */
    @TableField(exist = false)
    private List<String> storageAreaList;

}
