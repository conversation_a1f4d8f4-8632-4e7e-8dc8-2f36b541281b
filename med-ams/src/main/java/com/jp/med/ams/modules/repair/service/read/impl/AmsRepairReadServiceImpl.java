package com.jp.med.ams.modules.repair.service.read.impl;

import com.github.pagehelper.PageHelper;
import com.jp.med.ams.modules.property.vo.AmsRecordsVo;
import com.jp.med.ams.modules.repair.vo.AmsRepairRecordsVo;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.util.OSSUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ams.modules.repair.mapper.read.AmsRepairReadMapper;
import com.jp.med.ams.modules.repair.dto.AmsRepairDto;
import com.jp.med.ams.modules.repair.vo.AmsRepairVo;
import com.jp.med.ams.modules.repair.service.read.AmsRepairReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class AmsRepairReadServiceImpl extends ServiceImpl<AmsRepairReadMapper, AmsRepairDto> implements AmsRepairReadService {

    @Autowired
    private AmsRepairReadMapper amsRepairReadMapper;

    @Override
    public List<AmsRepairVo> queryList(AmsRepairDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return amsRepairReadMapper.queryList(dto);
    }

    @Override
    public List<AmsRepairRecordsVo> queryOne(AmsRepairDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<AmsRepairRecordsVo> recordsVos = amsRepairReadMapper.queryRepairRecord(dto);
        if (recordsVos.isEmpty()){
            return new ArrayList<>();
        }
        for (AmsRepairRecordsVo recordsVo : recordsVos) {
                String attachment = recordsVo.getAttachment();
                if (StringUtils.isEmpty(attachment)){
                    break;
                }
                String url = OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_AMS, attachment);
                recordsVo.setUrl(url);
        }
        return recordsVos;
    }

}
