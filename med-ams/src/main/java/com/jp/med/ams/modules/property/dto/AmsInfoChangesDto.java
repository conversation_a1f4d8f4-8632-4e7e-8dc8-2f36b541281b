package com.jp.med.ams.modules.property.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.util.List;
import lombok.Data;

/**
 * 资产信息变更记录表
 * <AUTHOR>
 * @email -
 * @date 2023-09-06 09:52:16
 */
@Data
@TableName("ams_info_changes" )
public class AmsInfoChangesDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 固定资产码 */
    @TableField("fa_code")
    @ExcelProperty(value = "固定资产码")
    private String faCode;

    /** 变更字段编码 */
    @TableField("chg_code")
    private String chgCode;

//    @TableField(exist = false)
//    private String chgCodeCn;


    /** 变更字段名称 */
    @TableField("chg_name")
    @ExcelProperty(value = "变更项目")
    private String chgName;

    /** 变更前 */
    @TableField("chg_before")
    @ExcelProperty(value = "变更前")
    private String chgBefore;

    /** 变更后 */
    @TableField("chg_after")
    @ExcelProperty(value = "变更后")
    private String chgAfter;

    /** 变更前 */
    @TableField("chg_before_name")
    private String chgBeforeName;

    /** 变更后 */
    @TableField("chg_after_name")
    private String chgAfterName;

    /** 变更时间 */
    @TableField("chg_date")
    @ExcelProperty(value = "变更时间")
    private String chgDate;

    /** 变更人 */
    @TableField("chger")
    @ExcelProperty(value = "变更人工号")
    private String chger;

    /** 变更人 */
    @TableField("chger_name")
    @ExcelProperty(value = "变更人名称")
    private String chgerName;

    /**
     * 审核人
     */
    @TableField("chker")
    private String chker;

    /**
     * 审核人
     */
    @TableField("chker_name")
    private String chkerName;
    /** 变更状态 */
    @TableField("status")
    private String status;

    /** 查询条件 */
    @TableField(exist = false)
    private String asset;

    /** 变更时间范围 */
    @TableField(exist = false)
    private String[] chgDateRange;

    /** 变更的所有项目 */
    @TableField(exist = false)
    private List<AmsInfoChangesDto> changeList;

    /** 项目信息 */
    @TableField(exist = false)
    private AmsPropertyDto amsPropertyDto;

    /** 信息变更记录 ids */
    @TableField(exist = false)
    private List<Integer> ids;


}
