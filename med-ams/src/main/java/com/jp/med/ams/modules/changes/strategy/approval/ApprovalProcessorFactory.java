package com.jp.med.ams.modules.changes.strategy.approval;

import com.jp.med.ams.modules.changes.dto.AmsChgRcdDto;
import com.jp.med.common.exception.AppException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 审批处理器工厂
 * 使用工厂模式管理不同的审批处理器
 */
@Component
public class ApprovalProcessorFactory {

    private final List<ApprovalProcessor> processors;

    @Autowired
    public ApprovalProcessorFactory(List<ApprovalProcessor> processors) {
        this.processors = processors;
    }

    /**
     * 根据变更记录和审批DTO获取合适的处理器
     * 
     * @param changeRecord 变更记录
     * @param approvalDto  审批DTO
     * @return 处理器
     */
    public ApprovalProcessor getProcessor(AmsChgRcdDto changeRecord, AmsChgRcdDto approvalDto) {
        return processors.stream()
                .filter(processor -> processor.supports(changeRecord, approvalDto))
                .findFirst()
                .orElseThrow(() -> new AppException("未找到合适的审批处理器"));
    }
}
