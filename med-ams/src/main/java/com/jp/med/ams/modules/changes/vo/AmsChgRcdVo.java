package com.jp.med.ams.modules.changes.vo;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 资产变更记录表
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-12-25 20:45:51
 */
@Data
public class AmsChgRcdVo {

	/** ID */
	private Integer id;

	/** 变动编号 */
	private String chgNo;

	/** 固定资产码 */
	private String faCode;

	/** 变动前值 */
	private String chgBefore;

	private String chgBeforeName;

	/** 变动后值 */
	private String chgAfter;

	private String transferTo;

	private String chgAfterName;

	/** 变更时间 */
	private String chgDate;

	/** 变更人 */
	private String chger;
	private String chgerName;

	/** 审核人 */
	private String chker;
	private String chkerName;

	/** 变动类型 */
	private String chgType;
	private String chgName;

	/** 变动原因 */
	private String chgRea;

	/** 备注 */
	private String memo;

	/** 创建时间 */
	private String createTime;

	/** 医疗机构编码 */
	private String hospitalId;

	/** 资产名称 */
	private String assetName;

	private String deptUseName;

	@JsonIgnore
	private String propertyJsonData;

	private Object propertyData;

	public Object getPropertyData() {
		if (StrUtil.isNotBlank(propertyJsonData)) {
			return JSONUtil.parse(propertyJsonData).getByPath("property");

		} else {
			return "";
		}
	}

	private String assetTypeName;

	private String assetTypeNName;

	private String redcWay;

	public String getAssetTypeNName() {
		return assetTypeNName;
	}

	public void setAssetTypeNName(String assetTypeNName) {
		this.assetTypeNName = assetTypeNName;
	}

	public String getAssetTypeName() {
		return assetTypeName;
	}

	public void setAssetTypeName(String assetTypeName) {
		this.assetTypeName = assetTypeName;
	}

}
