package com.jp.med.ams.modules.depr.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 资产折旧表
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 20:21:29
 */
@Data
public class AmsPropertyDeprVo {

	/** ID */
	private Integer id;

	/** 固定资产码 */
	private String faCode;

	/** 折旧额 */
	private BigDecimal deprAmt;

	/** 资产原值 */
	private BigDecimal assetNav;

	/** 累计折旧 */
	private BigDecimal dep;

	/** 资产净值 */
	private BigDecimal nbv;

	/** 期号 */
	private String ym;

	/** 有效标志 */
	private String activeFlag;

	/** 资产名称 */
	private String assetName;

	/** 使用科室 */
	private String orgName;

	/** 资产分类 */
	private String assetTypeName;

}
