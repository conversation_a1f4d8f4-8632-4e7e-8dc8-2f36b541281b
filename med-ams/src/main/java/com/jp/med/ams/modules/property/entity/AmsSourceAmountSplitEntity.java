package com.jp.med.ams.modules.property.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 资产金额拆分
 *
 * <AUTHOR>
 * @email -
 * @date 2025-02-11 21:04:01
 */
@Data
@TableName("ams_source_amount_split")
public class AmsSourceAmountSplitEntity {

    /**
     * 主键，每条记录的唯一标识符。
     */
    @TableId("id")
    private Integer id;

    /**
     * 外汇账户代码，标识关联的财务账户。
     */
    @TableField("fa_code")
    private String faCode;

    /**
     * 记录创建的时间戳。
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 记录更新的时间戳。
     */
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 金额拆分的来源类型。
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 拆分金额 1。
     */
    @TableField("split_amount_1")
    private BigDecimal splitAmount1;

    /**
     * 拆分金额 2。
     */
    @TableField("split_amount_2")
    private BigDecimal splitAmount2;

    /**
     * 拆分金额 3。
     */
    @TableField("split_amount_3")
    private BigDecimal splitAmount3;

    /**
     * 拆分类型 1。
     */
    @TableField("split_type_1")
    private String splitType1;

    /**
     * 拆分类型 2。
     */
    @TableField("split_type_2")
    private String splitType2;

    /**
     * 拆分类型 3。
     */
    @TableField("split_type_3")
    private String splitType3;

}
