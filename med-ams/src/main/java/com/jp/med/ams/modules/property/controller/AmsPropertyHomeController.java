package com.jp.med.ams.modules.property.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.mapper.read.AmsPropertyReadMapper;
import com.jp.med.ams.modules.property.mapper.write.AmsAssetSplitWriteMapper;
import com.jp.med.ams.modules.property.service.read.AmsPropertyReadService;
import com.jp.med.ams.modules.property.service.write.AmsPropertyWriteService;
import com.jp.med.common.entity.common.CommonResult;

import io.swagger.annotations.Api;

/**
 * 资产工作台
 *
 * <AUTHOR>
 * @email -
 * @date 2023-08-29 17:33:47
 */
@Api(value = "资产详情", tags = "资产详情")
@RestController
@RequestMapping("amsProperty")
public class AmsPropertyHomeController {

    @Autowired
    private AmsPropertyReadService amsPropertyReadService;

    @Autowired
    private AmsPropertyReadMapper amsPropertyReadMapper;

    @Autowired
    private AmsPropertyWriteService amsPropertyWriteService;

    @Autowired
    private AmsAssetSplitWriteMapper amsAssetSplitWriteMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 查询资产按分类的数量汇总
     */
    @PostMapping("/property/countByCategory")
    public CommonResult<?> countByCategory(@RequestBody AmsPropertyDto dto) {
        return CommonResult.success(amsPropertyReadService.countByCategory(dto));
    }

    /**
     * 查询资产按分类的原值汇总
     */
    @PostMapping("/property/sumByCategory")
    public CommonResult<?> sumByCategory(@RequestBody AmsPropertyDto dto) {
        return CommonResult.success(amsPropertyReadService.sumByCategory(dto));
    }

    /**
     * 查询资产科室分布
     */
    @PostMapping("/property/topByDept")
    public CommonResult<?> topByDept(@RequestBody AmsPropertyDto dto) {
        return CommonResult.success(amsPropertyReadService.topCountByDept(dto));
    }

    /**
     * 查询资产概况
     * 1.固定资产总数
     * 2.资产原值合计
     * 3.资产净值合计
     * 4.累计折旧合计
     */
    // 复用资产列表的查询

}
