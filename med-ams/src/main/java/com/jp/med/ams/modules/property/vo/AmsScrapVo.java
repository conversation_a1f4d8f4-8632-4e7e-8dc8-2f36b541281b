package com.jp.med.ams.modules.property.vo;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

/**
 * 资产报废
 * <AUTHOR>
 * @email -
 * @date 2023-09-04 21:48:57
 */
@Data
public class AmsScrapVo {
	
	/** ID */
	private Integer id;
	private Integer scrapId;

	/** 固定资产码 */
	private String faCode;

    /**
     * 报废原因 选项
     */
	private String scrapRea;
    /**
     * 报废原因 手填
     */
    private String scrapReaExt;

	/** 上报科室 */
	private String dept;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 审批状态 */
	private String applstas;

	/** 医疗机构ID */
	private String hospitalId;

	/** 有效标志 */
	private String activeFlag;

	/** 资产编码 */
	private String assetCode;


	/** 资产名称 */
	private String assetName;

	/** 资产类别 */
	private String assetType;

	/** 资产状态 */
	private String assetStatus;

	/** 序列号 */
	private String serialNumber;

	/** 是否可以报废*/
	private String allo;

	/** 申请人 */

	private String applyer;

	private String deptName;
	/** 申请时间 */
	private String appyDate;

	/** 驳回原因*/
	private String rejtRea;

	/** 有效标志 */
	private String flag;

	/** 申请单号 */
	private String applyDocno;

	/** 资产原值 */
	private String assetNav;

	/** 累计折旧 */
	private String dep;

	/** 资产类型 */
	private String assetTypeName;

    /*鉴定人*/
    private String identificationUserNames;

	/**
	 * images
	 */
	private List<String> images;

	@JsonIgnore
	private String propertyJsonData;

	private Object propertyData;

	public Object getPropertyData() {
		if (StrUtil.isNotBlank(propertyJsonData)) {
			return JSONUtil.parse(propertyJsonData).getByPath("property");

		} else {
			return "";
		}
	}
}
