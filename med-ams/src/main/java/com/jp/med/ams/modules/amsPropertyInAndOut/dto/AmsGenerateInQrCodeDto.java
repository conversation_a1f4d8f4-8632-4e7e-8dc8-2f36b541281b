package com.jp.med.ams.modules.amsPropertyInAndOut.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/3
 */
@Data
@TableName("ams_in_qrcode_config" )
public class AmsGenerateInQrCodeDto {

    /** ID */
    @TableId("id")
    private Long id;

    /** 二维码名称 */
    @TableField("qr_code_name")
    private String qrCodeName;

    /** 二维码描述 */
    @TableField("description")
    private String description;

    /** 二维码有效开始*/
    @TableField("start_date")
    private Date startDate;

    /** 二维码有效结束 */
    @TableField("end_date")
    private Date endDate;

    @TableField("qr_code_base64")
    private String qrCodeBase64;
}
