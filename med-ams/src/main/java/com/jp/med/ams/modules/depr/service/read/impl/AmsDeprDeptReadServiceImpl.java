package com.jp.med.ams.modules.depr.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.depr.dto.AmsDeprDeptDto;
import com.jp.med.ams.modules.depr.mapper.read.AmsDeprDeptReadMapper;
import com.jp.med.ams.modules.depr.mapper.read.AmsPropertyDeprReadMapper;
import com.jp.med.ams.modules.depr.service.read.AmsDeprDeptReadService;
import com.jp.med.ams.modules.depr.vo.AmsDeprDeptVo;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.mapper.read.AmsPropertyReadMapper;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Transactional
@Service
public class AmsDeprDeptReadServiceImpl extends ServiceImpl<AmsDeprDeptReadMapper, AmsDeprDeptDto>
        implements AmsDeprDeptReadService {

    @Autowired
    private AmsPropertyDeprReadMapper amsPropertyDeprReadMapper;

    @Autowired
    private AmsPropertyReadMapper amsPropertyReadMapper;

    @Override
    public List<AmsDeprDeptVo> queryList(AmsDeprDeptDto dto) {
        return null;
    }

    @Override
    public List<AmsDeprDeptVo> queryPageList(AmsDeprDeptDto dto) {
        return List.of();
    }

}
