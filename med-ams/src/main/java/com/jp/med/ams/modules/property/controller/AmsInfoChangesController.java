package com.jp.med.ams.modules.property.controller;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.jp.med.ams.modules.config.mapper.read.AmsStockCfgReadMapper;
import com.jp.med.ams.modules.config.vo.AmsStockCfgVo;
import com.jp.med.ams.modules.property.dto.AmsInfoChangesDto;
import com.jp.med.ams.modules.property.service.read.AmsInfoChangesReadService;
import com.jp.med.ams.modules.property.service.read.AmsMonthlyDepreciationPostingReadService;
import com.jp.med.ams.modules.property.service.write.AmsInfoChangesWriteService;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.OSSUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 资产信息变更记录表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-06 09:52:16
 */
@Api(value = "资产信息变更记录表", tags = "资产信息变更记录表")
@RestController
@RequestMapping("amsInfoChanges")
public class AmsInfoChangesController {

    @Autowired
    private AmsInfoChangesReadService amsInfoChangesReadService;

    @Autowired
    private AmsInfoChangesWriteService amsInfoChangesWriteService;

    @Autowired
    private AmsStockCfgReadMapper amsStockCfgReadMapper;

    @Autowired
    private AmsMonthlyDepreciationPostingReadService amsMonthlyDepreciationPostingReadService;

    /**
     * 列表
     */
    @ApiOperation("查询资产信息变更记录表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsInfoChangesDto dto) {

        return CommonResult.paging(amsInfoChangesReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产信息变更记录表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsInfoChangesDto dto) {
        amsInfoChangesWriteService.saveData(dto);
        return CommonResult.success();
    }

    @ApiOperation("导入变更记录")
    @PostMapping("/upload")
    public CommonResult<?> upload(MultipartFile[] files) {
        // 检测固定资产扎帐导入操作
        if (amsMonthlyDepreciationPostingReadService.isExistPostingLock()) {
            throw new AppException("扎帐期间不允许导入固定资产变更");
        }
        List<AmsStockCfgVo> amsStockCfgVos = amsStockCfgReadMapper.queryTableColumn();
        Map<String, String> propertyTableColumnNameMap = amsStockCfgVos.stream()
                .collect(Collectors.toMap(AmsStockCfgVo::getColumnName, AmsStockCfgVo::getColumnComment));
        try {
            EasyExcel.read(files[0].getInputStream(), AmsInfoChangesDto.class,
                    new AnalysisEventListener<AmsInfoChangesDto>() {
                        public static final int BATCH_COUNT = 10000;
                        private final List<AmsInfoChangesDto> amsPropertyDtos = new ArrayList<>();

                        @Override
                        public void invoke(AmsInfoChangesDto data, AnalysisContext context) {
                            String chgName = data.getChgName();
                            String columnName = propertyTableColumnNameMap.getOrDefault(chgName, null);
                            data.setChgCode(columnName);
                            amsPropertyDtos.add(data);
                            if (amsPropertyDtos.size() >= BATCH_COUNT) {
                                saveData();
                                // 存储完成清理 list
                                amsPropertyDtos.clear();
                            }
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                            saveData();
                        }

                        private void saveData() {
                            // amsPropertyWriteMapper.insert()
                            amsInfoChangesWriteService.saveBatch(amsPropertyDtos);
                        }
                    }).sheet(0).doRead();
        } catch (Exception e) {
            e.printStackTrace();
            throw new AppException("文件解析失败");
        }
        return CommonResult.success();
    }

    @ApiOperation("downloadExcelTemplate")
    @GetMapping("/downloadExcelTemplate")
    public void downloadExcelTemplate(HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=amsInfoChanges.xlsx");
        // OSSUtil.getBucket().downloadFile("amsInfoChanges.xlsx", response);
        ServletOutputStream outputStream = response.getOutputStream();
        BufferedInputStream bufferedInputStream = new BufferedInputStream(
                OSSUtil.getObject(OSSConst.BUCKET_AMS, "资产信息变更导入模版.xlsx"));
        BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(outputStream);
        byte[] buffer = new byte[1024];
        int len;
        while ((len = bufferedInputStream.read(buffer)) != -1) {
            bufferedOutputStream.write(buffer, 0, len);
        }
        bufferedOutputStream.flush();
        bufferedInputStream.close();
        bufferedOutputStream.close();
    }

    /**
     * 修改
     */
    @ApiOperation("确认/撤销 资产信息变更")
    @PostMapping("/confirmInfoChange")
    public CommonResult<?> confirmInfoChange(@RequestBody AmsInfoChangesDto dto) {
        amsInfoChangesWriteService.confirmInfoChange(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产信息变更记录表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsInfoChangesDto dto) {
        amsInfoChangesWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产信息变更记录表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsInfoChangesDto dto) {
        if (dto.getIds() != null) {
            amsInfoChangesWriteService.removeBatchByIds(dto.getIds());
        } else if (dto.getId() != null) {
            amsInfoChangesWriteService.removeById(dto);
        } else {
            throw new AppException("删除条件不能为空");
        }
        return CommonResult.success();
    }

}
