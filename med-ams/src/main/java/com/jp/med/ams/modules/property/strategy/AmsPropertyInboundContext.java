package com.jp.med.ams.modules.property.strategy;

import cn.hutool.core.util.StrUtil;
import com.jp.med.ams.modules.depr.dto.AmsDeprAsgnDto;
import com.jp.med.ams.modules.depr.mapper.write.AmsDeprAsgnWriteMapper;
import com.jp.med.ams.modules.inventory.dto.AmsIntrDetailDto;
import com.jp.med.ams.modules.inventory.dto.AmsIntrTodoDto;
import com.jp.med.ams.modules.inventory.mapper.write.AmsIntrDetailWriteMapper;
import com.jp.med.ams.modules.inventory.mapper.write.AmsIntrTodoWriteMapper;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.dto.AmsRecordsDto;
import com.jp.med.ams.modules.property.mapper.read.AmsRecordsReadMapper;
import com.jp.med.ams.modules.property.mapper.write.AmsPropertyWriteMapper;
import com.jp.med.ams.modules.property.mapper.write.AmsRecordsWriteMapper;
import com.jp.med.ams.modules.property.vo.AmsRecordsVo;
import com.jp.med.ams.modules.property.service.write.impl.AmsSourceAmountSplitWriteServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.OSSUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资产入库策略上下文
 * 
 * 负责协调不同的入库策略，处理通用的入库逻辑
 * 
 */
@Component
public class AmsPropertyInboundContext {

    @Autowired
    private AmsPropertyInboundStrategyFactory strategyFactory;

    @Autowired
    private AmsPropertyWriteMapper amsPropertyWriteMapper;

    @Autowired
    private AmsIntrDetailWriteMapper amsIntrDetailWriteMapper;

    @Autowired
    private AmsIntrTodoWriteMapper amsIntrTodoWriteMapper;

    @Autowired
    private AmsSourceAmountSplitWriteServiceImpl amsSourceAmountSplitWriteService;

    @Autowired
    private AmsRecordsReadMapper amsRecordsReadMapper;

    @Autowired
    private AmsRecordsWriteMapper amsRecordsWriteMapper;

    @Autowired
    private AmsDeprAsgnWriteMapper amsDeprAsgnWriteMapper;

    /**
     * 执行资产入库操作
     * 
     * @param dto          资产数据传输对象
     * @param isConfirmAdd 是否为确认添加操作
     */
    public void executeInbound(AmsPropertyDto dto, Boolean isConfirmAdd) {
        // 设置SQL自动注入医院条件为true
        dto.setSqlAutowiredHospitalCondition(true);

        // 如果不是确认添加且ID不为null，则直接返回
        if (!isConfirmAdd && dto.getId() != null) {
            return;
        }

        // 获取对应的策略
        AmsPropertyInboundStrategy strategy = strategyFactory.getStrategy(dto.getType());
        if (strategy == null) {
            throw new AppException("不支持的资产类型: " + dto.getType());
        }

        // 执行策略验证
        strategy.validateAssetData(dto);

        // 执行策略前处理
        strategy.preProcessInbound(dto, isConfirmAdd);

        // 如果不是确认添加，执行编码生成和属性设置
        if (!isConfirmAdd) {
            strategy.generateAssetCodes(dto);
            strategy.setAssetSpecificProperties(dto);

            // 执行入库前信息校验补全
            checkAndCompleteInfo(dto);

            // 处理盘盈情况
            handleInventoryProfit(dto);

            // 插入新的资产记录
            amsPropertyWriteMapper.insert(dto);
        } else {
            // 确认入库时的处理
            handleConfirmInbound(dto);
            // 更新资产记录
            amsPropertyWriteMapper.updateById(dto);
        }

        // 执行策略后处理
        strategy.postProcessInbound(dto, isConfirmAdd);

        // 处理相关记录（附件等）
        handleRecords(dto, isConfirmAdd);

        // 同步资金来源拆分记录
        amsSourceAmountSplitWriteService.syncSplitAmount(dto);

        // 处理使用科室预分配
        handleDeptUsePreAllocation(dto, isConfirmAdd);
    }

    /**
     * 处理盘盈情况
     * 
     * @param dto 资产DTO
     */
    private void handleInventoryProfit(AmsPropertyDto dto) {
        if (StrUtil.equals(dto.getIsProfit(), MedConst.ACTIVE_FLAG_1)) {
            if (StrUtil.isNotBlank(dto.getAmsIntrTaskId())) {
                dto.setIncrWay("4"); // 盘盈

                // 创建并插入盘点详情
                AmsIntrDetailDto amsIntrDetailDto = new AmsIntrDetailDto();
                amsIntrDetailDto.setTaskId(Integer.valueOf(dto.getAmsIntrTaskId()));
                amsIntrDetailDto.setIsManual(2);
                amsIntrDetailDto.setUid(dto.getUid());
                amsIntrDetailWriteMapper.insert(amsIntrDetailDto);

                // 创建并插入盘点待办
                AmsIntrTodoDto amsIntrTodoDto = new AmsIntrTodoDto();
                amsIntrTodoDto.setTaskId(Integer.valueOf(dto.getAmsIntrTaskId()));
                amsIntrTodoDto.setUid(dto.getUid());
                amsIntrTodoWriteMapper.insert(amsIntrTodoDto);
            }
        }
    }

    /**
     * 处理确认入库
     * 
     * @param dto 资产DTO
     */
    private void handleConfirmInbound(AmsPropertyDto dto) {
        // 设置入库时间为现在
        if (dto.getInptDate() == null || dto.getInptDate().isEmpty()) {
            dto.setInptDate(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        }

        // 开始使用时间
        if (dto.getOpeningDate() == null || dto.getOpeningDate().isEmpty()) {
            dto.setOpeningDate(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        }

        if (dto.getStoinDate() == null || dto.getStoinDate().isEmpty()) {
            dto.setStoinDate(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        }
    }

    /**
     * 入库前信息校验补全
     *
     * @param dto 资产DTO
     */
    private void checkAndCompleteInfo(AmsPropertyDto dto) {
        // 设置激活标志为非激活
        if (dto.getIsChk() == MedConst.ACTIVE_FLAG_1) {
            dto.setIsChk(MedConst.ACTIVE_FLAG_0);
        }

        // 初始化 残值为 原值，净值为 原值，累计折旧为 0
        if (dto.getAssetNav() != null) {
            if (dto.getRv() == null) {
                dto.setRv(dto.getAssetNav());
            }
            if (dto.getNbv() == null) {
                dto.setNbv(dto.getAssetNav());
            }
        }

        if (dto.getDep() == null) {
            dto.setDep(BigDecimal.ZERO);
        }
    }

    /**
     * 处理相关记录（附件等）
     *
     * @param dto          资产DTO
     * @param isConfirmAdd 是否为确认添加
     */
    private void handleRecords(AmsPropertyDto dto, Boolean isConfirmAdd) {
        // 创建AmsRecordsDto实例
        AmsRecordsDto queryRecordsDto = new AmsRecordsDto();
        if (!isConfirmAdd) {
            // 设置faCode
            queryRecordsDto.setFaCode(dto.getFaCode());
        }
        queryRecordsDto.setPropertyId(dto.getId());

        if (dto.getFaCode() == null && dto.getId() == null) {
            throw new AppException("资产编码和ID不能同时为空");
        }

        // 根据dto查询记录列表
        List<AmsRecordsVo> recordsVos = amsRecordsReadMapper.queryList(queryRecordsDto);

        if (isConfirmAdd) {
            // 确认添加时的处理
            handleConfirmAddRecords(dto, recordsVos);
            return;
        }

        // 处理新增时的记录逻辑
        handleNewAddRecords(dto, recordsVos);
    }

    /**
     * 处理确认添加时的记录
     *
     * @param dto        资产DTO
     * @param recordsVos 记录VO列表
     */
    private void handleConfirmAddRecords(AmsPropertyDto dto, List<AmsRecordsVo> recordsVos) {
        for (AmsRecordsVo recordsVo : recordsVos) {
            recordsVo.setFaCode(dto.getFaCode());
        }

        List<AmsRecordsDto> updateList = new ArrayList<>();
        for (AmsRecordsVo recordsVo : recordsVos) {
            // 为每个recordsVo创建一个新的AmsRecordsDto实例
            AmsRecordsDto amsRecordsDto = new AmsRecordsDto();
            amsRecordsDto.setId(recordsVo.getId());
            amsRecordsDto.setFlag("1");
            amsRecordsDto.setFaCode(dto.getFaCode());
            updateList.add(amsRecordsDto);
        }
        // 执行批量更新操作
        if (!updateList.isEmpty()) {
            BatchUtil.batch("updateById", updateList, AmsRecordsWriteMapper.class);
        }
    }

    /**
     * 处理新增时的记录
     *
     * @param dto        资产DTO
     * @param recordsVos 记录VO列表
     */
    private void handleNewAddRecords(AmsPropertyDto dto, List<AmsRecordsVo> recordsVos) {
        // 将查询结果转换为Map，以记录ID为键
        Map<Integer, AmsRecordsVo> voMap = recordsVos.stream().collect(
                Collectors.toMap(AmsRecordsVo::getId, recordsVo -> recordsVo, (o1, o2) -> o2));

        // 获取待处理的记录列表
        List<AmsRecordsDto> records = dto.getRecords();
        // 初始化待移除记录列表
        List<AmsRecordsDto> removeList = new ArrayList<>();

        // 如果records为空或为空集合，检查recordsVos是否为空
        // 删除操作
        if (Objects.isNull(records) || records.isEmpty()) {
            // 如果recordsVos不为空，遍历recordsVos中的每一个元素
            if (!recordsVos.isEmpty()) {
                for (AmsRecordsVo recordsVo : recordsVos) {
                    // 为每个recordsVo创建一个新的AmsRecordsDto实例
                    AmsRecordsDto amsRecordsDto = new AmsRecordsDto();
                    amsRecordsDto.setId(recordsVo.getId());
                    amsRecordsDto.setFlag("-1");
                    amsRecordsDto.setPropertyId(dto.getId());
                    // 将新创建的实例添加到移除列表中
                    removeList.add(amsRecordsDto);
                }
                // 执行批量更新操作
                BatchUtil.batch("updateById", removeList, AmsRecordsWriteMapper.class);
            }
            // 如果records为空或为空集合，且recordsVos不为空，则执行完批量更新后直接返回
            return;
        }

        // 处理有记录的情况
        processRecordsWithFiles(dto, records, voMap, removeList);
    }

    /**
     * 处理包含文件的记录
     *
     * @param dto        资产DTO
     * @param records    记录列表
     * @param voMap      记录VO映射
     * @param removeList 待移除记录列表
     */
    private void processRecordsWithFiles(AmsPropertyDto dto, List<AmsRecordsDto> records,
            Map<Integer, AmsRecordsVo> voMap, List<AmsRecordsDto> removeList) {
        // 创建一个空的AMS记录列表
        List<AmsRecordsDto> saveList = new ArrayList<>();

        // 遍历记录列表，对每个记录进行处理
        for (AmsRecordsDto record : records) {
            // 如果记录包含非空文件，则进行文件上传并更新记录信息
            if (!Objects.isNull(record.getFile())) {
                // 上传文件到OSS，并构建文件访问URL
                String str = OSSUtil.uploadFile(OSSConst.BUCKET_AMS, "base/", record.getFile());
                // 更新记录的附件URL和相关属性
                record.setAttachment(str);
                record.setFaCode(dto.getFaCode());
                record.setFileName(record.getFile().getOriginalFilename());
                record.setPropertyId(dto.getId());
                record.setFlag("1");
                // 将更新后的记录加入保存列表
                saveList.add(record);
            } else {
                // 如果记录没有文件，则尝试从voMap中移除对应的VO对象
                AmsRecordsVo vo = voMap.get(record.getId());
                if (!Objects.isNull(vo)) {
                    voMap.remove(record.getId());
                }
            }
        }

        // 处理剩余的记录（需要删除的）
        for (AmsRecordsVo value : voMap.values()) {
            AmsRecordsDto amsRecordsDto = new AmsRecordsDto();
            amsRecordsDto.setId(value.getId());
            amsRecordsDto.setFlag("-1");
            amsRecordsDto.setPropertyId(dto.getId());
            removeList.add(amsRecordsDto);
        }

        // 执行批量操作
        if (!removeList.isEmpty()) {
            BatchUtil.batch("updateById", removeList, AmsRecordsWriteMapper.class);
        }
        if (!saveList.isEmpty()) {
            BatchUtil.batch("insertBatch", saveList, AmsRecordsWriteMapper.class);
        }
    }

    /**
     * 处理使用科室预分配
     *
     * 当资产有预配置分配信息时，自动创建折旧分配记录，实现预配置功能
     * 支持用户自定义分配比例，不再强制平均分配
     *
     * @param dto          资产DTO
     * @param isConfirmAdd 是否为确认添加操作
     */
    private void handleDeptUsePreAllocation(AmsPropertyDto dto, Boolean isConfirmAdd) {
        // 只有在非确认添加模式下才处理预分配
        if (isConfirmAdd) {
            return;
        }

        // // 只有固定资产才需要折旧分配
        // if (!"1".equals(dto.getType())) {
        // return;
        // }

        try {
            // 优先使用预配置分配信息，如果没有则使用deptUse进行平均分配
            if (StrUtil.isNotBlank(dto.getPreConfigAllocation())) {
                handleCustomAllocation(dto);
            } else if (StrUtil.isNotBlank(dto.getDeptUse())) {
                handleAverageAllocation(dto);
            }

        } catch (Exception e) {
            // 预分配失败不影响资产入库，只记录错误日志
            System.err.println("处理使用科室预分配失败，资产编码: " + dto.getFaCode() + "，错误信息: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理用户自定义分配比例
     *
     * @param dto 资产DTO
     */
    private void handleCustomAllocation(AmsPropertyDto dto) {
        // 解析预配置分配信息
        // 格式: "科室ID1:比例1,科室ID2:比例2"
        String[] allocationItems = dto.getPreConfigAllocation().split(",");
        if (allocationItems.length == 0) {
            return;
        }

        List<AmsDeprAsgnDto> deprAsgnList = new ArrayList<>();

        for (String item : allocationItems) {
            String[] parts = item.trim().split(":");
            if (parts.length != 2) {
                continue;
            }

            String deptId = parts[0].trim();
            String propStr = parts[1].trim();

            if (StrUtil.isBlank(deptId) || StrUtil.isBlank(propStr)) {
                continue;
            }

            try {
                BigDecimal prop = new BigDecimal(propStr);

                AmsDeprAsgnDto deprAsgnDto = new AmsDeprAsgnDto();
                deprAsgnDto.setFaCode(dto.getFaCode());
                deprAsgnDto.setOrgId(deptId);
                deprAsgnDto.setProp(prop);
                deprAsgnDto.setModiRea("预配置自动生成");
                deprAsgnDto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
                deprAsgnDto.setOpter(dto.getSysUser() != null ? dto.getSysUser().getUsername() : "系统");
                deprAsgnDto.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

                deprAsgnList.add(deprAsgnDto);
            } catch (NumberFormatException e) {
                System.err.println("解析分配比例失败: " + propStr + "，科室: " + deptId);
            }
        }

        // 批量插入折旧分配记录
        if (!deprAsgnList.isEmpty()) {
            BatchUtil.batch(deprAsgnList, AmsDeprAsgnWriteMapper.class);
            System.out.println("已为资产 " + dto.getFaCode() + " 自动创建 " + deprAsgnList.size() + " 条自定义折旧分配记录");
        }
    }

    /**
     * 处理平均分配（兼容旧逻辑）
     *
     * @param dto 资产DTO
     */
    private void handleAverageAllocation(AmsPropertyDto dto) {
        // 解析使用科室信息
        String[] deptIds = dto.getDeptUse().split(",");
        if (deptIds.length == 0) {
            return;
        }

        // 创建折旧分配记录列表
        List<AmsDeprAsgnDto> deprAsgnList = new ArrayList<>();

        // 平均分配比例
        BigDecimal avgProp = BigDecimal.ONE.divide(BigDecimal.valueOf(deptIds.length), 4, RoundingMode.HALF_UP);
        BigDecimal totalProp = BigDecimal.ZERO;

        for (int i = 0; i < deptIds.length; i++) {
            String deptId = deptIds[i].trim();
            if (StrUtil.isBlank(deptId)) {
                continue;
            }

            AmsDeprAsgnDto deprAsgnDto = new AmsDeprAsgnDto();
            deprAsgnDto.setFaCode(dto.getFaCode());
            deprAsgnDto.setOrgId(deptId);

            // 最后一个科室分配剩余比例，确保总和为1
            if (i == deptIds.length - 1) {
                deprAsgnDto.setProp(BigDecimal.ONE.subtract(totalProp));
            } else {
                deprAsgnDto.setProp(avgProp);
                totalProp = totalProp.add(avgProp);
            }

            deprAsgnDto.setModiRea("预配置平均分配");
            deprAsgnDto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
            deprAsgnDto.setOpter(dto.getSysUser() != null ? dto.getSysUser().getUsername() : "系统");
            deprAsgnDto.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            deprAsgnList.add(deprAsgnDto);
        }

        // 批量插入折旧分配记录
        if (!deprAsgnList.isEmpty()) {
            BatchUtil.batch(deprAsgnList, AmsDeprAsgnWriteMapper.class);
            System.out.println("已为资产 " + dto.getFaCode() + " 自动创建 " + deprAsgnList.size() + " 条平均折旧分配记录");
        }
    }
}
