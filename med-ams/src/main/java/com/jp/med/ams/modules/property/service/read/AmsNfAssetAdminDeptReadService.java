package com.jp.med.ams.modules.property.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.property.dto.AmsNfAssetAdminDeptDto;
import com.jp.med.ams.modules.property.vo.AmsNfAssetAdminDeptVo;

import java.util.List;

/**
 * 科室固定资产归口科室表
 * 
 * <AUTHOR>
 * @email -
 * @date 2025-03-13 11:48:41
 */
public interface AmsNfAssetAdminDeptReadService extends IService<AmsNfAssetAdminDeptDto> {

    /**
     * 查询列表
     * 
     * @param dto
     * @return
     */
    List<AmsNfAssetAdminDeptVo> queryList(AmsNfAssetAdminDeptDto dto);

    /**
     * 分页查询列表
     * 
     * @param dto
     * @return
     */
    List<AmsNfAssetAdminDeptVo> queryPageList(AmsNfAssetAdminDeptDto dto);


}
