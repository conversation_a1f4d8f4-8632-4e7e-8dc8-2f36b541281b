package com.jp.med.ams.modules.property.strategy;

import com.jp.med.ams.modules.property.dto.AmsPropertyDto;

/**
 * 资产入库策略接口
 * 
 * 使用策略模式重构入库资产功能，根据不同的资产类型采用不同的处理策略
 * 
 */
public interface AmsPropertyInboundStrategy {

    /**
     * 支持的资产类型
     * 
     * @return 资产类型编码
     */
    String getSupportedAssetType();

    /**
     * 验证资产数据
     * 
     * @param dto 资产数据传输对象
     * @throws com.jp.med.common.exception.AppException 验证失败时抛出异常
     */
    void validateAssetData(AmsPropertyDto dto);

    /**
     * 生成资产编码
     * 包括：faCode（固定资产码）、uid（二维码）、assetCode（资产编码）
     * 
     * @param dto 资产数据传输对象
     */
    void generateAssetCodes(AmsPropertyDto dto);

    /**
     * 设置资产特定属性
     * 根据不同资产类型设置特定的属性值
     * 
     * @param dto 资产数据传输对象
     */
    void setAssetSpecificProperties(AmsPropertyDto dto);

    /**
     * 执行资产入库前的预处理
     * 
     * @param dto          资产数据传输对象
     * @param isConfirmAdd 是否为确认添加操作
     */
    void preProcessInbound(AmsPropertyDto dto, Boolean isConfirmAdd);

    /**
     * 执行资产入库后的后处理
     * 
     * @param dto          资产数据传输对象
     * @param isConfirmAdd 是否为确认添加操作
     */
    void postProcessInbound(AmsPropertyDto dto, Boolean isConfirmAdd);

    /**
     * 检查是否需要扎帐验证
     * 
     * @return true表示需要扎帐验证，false表示不需要
     */
    default boolean requiresPostingLockCheck() {
        return false;
    }

    /**
     * 获取策略描述
     * 
     * @return 策略描述信息
     */
    default String getStrategyDescription() {
        return "资产入库策略 - " + getSupportedAssetType();
    }
}
