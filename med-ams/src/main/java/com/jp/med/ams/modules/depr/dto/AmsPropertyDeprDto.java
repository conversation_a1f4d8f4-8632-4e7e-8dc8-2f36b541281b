package com.jp.med.ams.modules.depr.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 资产折旧表
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 20:21:29
 */
@Data
@TableName("ams_property_depr")
public class AmsPropertyDeprDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 固定资产码 */
    @TableField(value = "fa_code", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String faCode;

    /** 折旧额 */
    @TableField(value = "depr_amt", insertStrategy = FieldStrategy.NOT_EMPTY)
    private BigDecimal deprAmt;

    /** 期号 */
    @TableField(value = "ym", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String ym;

    /** 有效标志 */
    @TableField(value = "active_flag", insertStrategy = FieldStrategy.NOT_EMPTY)
    private String activeFlag;

    /** 资产 */
    @TableField(exist = false)
    private String asset;

    /**
     * 资产类型代码
     */
    @TableField(exist = false)
    private String assetTypeCode;

    /** 部门代码 */
    @TableField(exist = false)
    private String deptCode;

    /** 折旧率 */
    @TableField(exist = false)
    private Double rate;

    /**
     * 查询新分类
     */
    @TableField(exist = false)
    private Boolean typen;

    /**
     * 查询截止年度
     */
    @TableField(exist = false)
    private String queryEndYear;

    /**
     * 价值构成大类模式
     */
    @TableField(exist = false)
    private Boolean bigCategoryModel = false;

    /**
     * 价值构成：使用折旧快照
     */
    @TableField(exist = false)
    private Boolean useSnapshot = false;

    /**
     * 计算往年财政资金
     */
    @TableField(exist = false)
    private Boolean calcFinaSubsidy = true;

    /**
     * 按大类计算折旧分摊
     */
    @TableField(exist = false)
    private Boolean calcBigCategoryDepr = true;
}
