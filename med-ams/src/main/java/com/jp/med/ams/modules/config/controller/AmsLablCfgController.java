package com.jp.med.ams.modules.config.controller;

import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.util.OSSUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.config.dto.AmsLablCfgDto;
import com.jp.med.ams.modules.config.service.read.AmsLablCfgReadService;
import com.jp.med.ams.modules.config.service.write.AmsLablCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 资产标签配置
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 17:36:48
 */
@Api(value = "资产标签配置", tags = "资产标签配置")
@RestController
@RequestMapping("amsLablCfg")
public class AmsLablCfgController {

    @Autowired
    private AmsLablCfgReadService amsLablCfgReadService;

    @Autowired
    private AmsLablCfgWriteService amsLablCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询资产标签配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsLablCfgDto dto){
        return CommonResult.paging(amsLablCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产标签配置")
    @PostMapping("/save")
    public CommonResult<?> save(AmsLablCfgDto dto){
        amsLablCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产标签配置")
    @PostMapping("/update")
    public CommonResult<?> update(AmsLablCfgDto dto){
        amsLablCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产标签配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsLablCfgDto dto){
        amsLablCfgWriteService.removeById(dto);
        OSSUtil.removeFile(OSSConst.BUCKET_AMS, dto.getAttachment());
        return CommonResult.success();
    }

}
