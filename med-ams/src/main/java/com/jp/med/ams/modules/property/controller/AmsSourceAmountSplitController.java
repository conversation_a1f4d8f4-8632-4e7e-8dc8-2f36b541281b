package com.jp.med.ams.modules.property.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.property.dto.AmsSourceAmountSplitDto;
import com.jp.med.ams.modules.property.service.read.AmsSourceAmountSplitReadService;
import com.jp.med.ams.modules.property.service.write.AmsSourceAmountSplitWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * 资产金额拆分
 *
 * <AUTHOR>
 * @email -
 * @date 2025-02-11 21:04:01
 */
@Api(value = "资产金额拆分", tags = "资产金额拆分")
@RestController
@RequestMapping("amsSourceAmountSplit")
public class AmsSourceAmountSplitController {

    @Autowired
    private AmsSourceAmountSplitReadService amsSourceAmountSplitReadService;

    @Autowired
    private AmsSourceAmountSplitWriteService amsSourceAmountSplitWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询资产金额拆分")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody AmsSourceAmountSplitDto dto) {
        return CommonResult.paging(amsSourceAmountSplitReadService.queryPageList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询资产金额拆分")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsSourceAmountSplitDto dto) {
        return CommonResult.success(amsSourceAmountSplitReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产金额拆分")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsSourceAmountSplitDto dto) {
        amsSourceAmountSplitWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产金额拆分")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsSourceAmountSplitDto dto) {
        amsSourceAmountSplitWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产金额拆分")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsSourceAmountSplitDto dto) {
        amsSourceAmountSplitWriteService.removeById(dto);
        return CommonResult.success();
    }

}
