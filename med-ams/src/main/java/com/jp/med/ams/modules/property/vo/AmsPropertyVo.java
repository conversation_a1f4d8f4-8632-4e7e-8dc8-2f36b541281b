package com.jp.med.ams.modules.property.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 资产详情
 *
 * <AUTHOR>
 * @email -
 * @date 2023-08-31 16:53:56
 */
@Data
public class AmsPropertyVo {

    /**
     * ID
     */
    private Integer id;

    /**
     * 资产编码
     */
    private String assetCode;

    /**
     * 父项资产码
     */
    private String saCode;

    /**
     * 资产名称
     */
    private String assetName;

    /**
     * 固定资产码
     */
    private String faCode;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 资产类型
     */
    private String assetType;

    /**
     * 设备类别
     */
    private String assetCategory;

    /**
     * 资产状态
     */
    private String assetStatus;

    /**
     * 资产型号
     */
    private String assetMol;

    /**
     * 资产原值
     */
    private BigDecimal assetNav;

    /**
     * 资产净值
     */
    private BigDecimal nbv;

    /**
     * 资产残值
     */
    private BigDecimal rv;

    /**
     * 通用名
     */
    private String genname;

    /**
     * 规格
     */
    private String spec;

    /**
     * 增加方式
     */
    private String incrWay;

    /**
     * 资金来源
     */
    private String source;

    /* 财政补助金 */

    private String financialAssistanceFunds;

    /* 自有资金 */
    private String ownFunds;

    /**
     * 其他编号(科室内的编号)
     */
    private String otherRef;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 供应商联系人
     */
    private String supplierConer;

    /**
     * 供应商电话
     */
    private String supplierTel;

    /**
     * 售后工程师
     */
    private String fse;

    /**
     * 售后电话
     */
    private String cs;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 配置描述
     */
    private String describe;

    /**
     * 使用科室
     */
    private String deptUse;

    /**
     * 管理科室
     */
    private String dept;

    /**
     * 存放地点
     */
    private String storageArea;

    /**
     * 位置
     */
    private String location;

    /**
     * 建议使用年限
     */
    private BigDecimal exp;

    /**
     * 已使用年限
     */
    private BigDecimal usedExp;

    /**
     * 采购单价
     */
    private BigDecimal unitPrice;

    /**
     * 折旧方式
     */
    private String dm;

    /**
     * 折旧年限
     */
    private BigDecimal ul;

    /**
     * 累计折旧
     */
    private BigDecimal dep;

    /**
     * 维保状态
     */
    private String amStatus;

    /**
     * 脱保日期
     */
    private String oowDate;

    /**
     * 注册证号
     */
    private String rcn;

    /**
     * 出厂编号
     */
    private String sn;

    /**
     * 保修期限
     */
    private String wp;

    /**
     * 合同编号
     */
    private String cntrCode;

    /**
     * 合同名称
     */
    private String cntrName;

    /**
     * 发票号
     */
    private String invono;

    /**
     * 原厂保修
     */
    private String oemw;

    /**
     * 采购日期
     */
    private String purcDate;

    /**
     * 安装日期
     */
    private String instDate;

    /**
     * 开始使用日期
     */
    private String openingDate;

    /**
     * 入库日期
     */
    private String stoinDate;

    /**
     * 验收日期
     */
    private String acpDate;

    /**
     * 出库日期
     */
    private String stooutDate;

    /**
     * 设备用途
     */
    private String assetUsed;

    /**
     * 自筹金额
     */
    private BigDecimal oop;

    /**
     * 财政拨款
     */
    private BigDecimal gf;

    /**
     * 科研经费
     */
    private BigDecimal researchFunding;

    /**
     * 教学经费
     */
    private BigDecimal tef;

    /**
     * 是否成本核算
     */
    private String adjact;

    /**
     * 是否进口
     */
    private String imp;

    /**
     * 二维码
     */
    private String uid;

    /**
     * 第三方编号
     */
    private String tpn;

    /**
     * 在保类型
     */
    private String wts;

    /**
     * 买保开始日期
     */
    private String ipd;

    /**
     * 买保结束日期
     */
    private String epd;

    /**
     * 生产日期
     */
    private String pd;

    /**
     * 负责人
     */
    private String resper;

    /**
     * 便用状态
     */
    private String us;

    /**
     * 财务分类
     */
    private String fc;

    /**
     * 医械分类
     */
    private String mdr;

    /**
     * 风险等级
     */
    private String rsl;

    /**
     * 管理分类
     */
    private String cm;

    /**
     * 生命支持
     */
    private String ls;

    /**
     * 是否应急设备
     */
    private String ed;

    /**
     * 中医诊疗
     */
    private String tcm;

    /**
     * 检验设备
     */
    private String te;

    /**
     * 特种设备
     */
    private String se;

    /**
     * 辐射设备
     */
    private String rs;

    /**
     * 辅助分类
     */
    private String ac;

    /**
     * 附属设备
     */
    private String ae;

    /**
     * 出厂日期
     */
    private String manuDate;

    /**
     * 是否计量设备
     */
    private String me;

    /**
     * 计量编码
     */
    private String mc;

    /**
     * 数量
     */
    private BigDecimal cnt;

    /**
     * 残值率
     */
    private BigDecimal resr;

    /**
     * 建筑面积
     */
    private BigDecimal area;

    /**
     * 发证日期
     */
    private String issucertDate;

    /**
     * 权属证明
     */
    private String proofOfTitle;

    /**
     * 权属证书编号
     */
    private String certificateNum;

    /**
     * 权属年限
     */
    private BigDecimal tenure;

    /**
     * 坐落位置
     */
    private String loc;

    /**
     * 土地使用权类型
     */
    private String landUseRightsType;

    /**
     * 产权形式
     */
    private String propertyForm;

    /**
     * 医疗机构编号
     */
    private String hospitalId;

    /**
     * 月折旧率
     */
    private BigDecimal deprratMon;

    /**
     * 月折旧额
     */
    private BigDecimal deprMon;

    /**
     * 已使用月份
     */
    private Integer usedMon;

    /**
     * 车牌照号
     */
    private String lpn;

    /**
     * 减少方式
     */
    private String redcWay;

    /**
     * 录入人
     */
    private String inpter;

    /**
     * 录入日期
     */
    private String inptDate;

    /**
     * 是否已编制凭证
     */
    private String isPrepareVouchers;

    /**
     * 是否已审核
     */
    private String isChk;

    /**
     * 审核人
     */
    private String chker;

    /**
     * 备注
     */
    private String memo;

    /**
     * 土地面积
     */
    private BigDecimal landArea;

    /**
     * 权属证书所有权人
     */
    private String coOwner;

    /**
     * 土地证载明面积
     */
    private BigDecimal zkc;

    /**
     * 入账形式
     */
    private String entryForm;

    /**
     * 土地来源
     */
    private String landSouc;

    /**
     * 使用方向
     */
    private String usage;

    /**
     * 权属性质
     */
    private String propertyChar;

    /**
     * 权属证书发证时间
     */
    private String coDate;

    /**
     * 权属所有人
     */
    private String propertyOwner;

    /**
     * 建筑结构
     */
    private String buildingStructure;

    /**
     * 使用状况
     */
    private String use;

    /**
     * 价值类型
     */
    private String valueType;

    /**
     * 发动机号
     */
    private String engineNo;

    /**
     * 资产大类
     */
    private String assetClassification;

    /**
     * 土地使用权面积
     */
    private BigDecimal landUsageArea;

    /**
     * 土地使用权人
     */
    private String landUser;

    /**
     * 地类(用途)
     */
    private String landUsed;

    /**
     * 房屋所有权人
     */
    private String ownerOfHouse;

    /**
     * 记账凭证号
     */
    private String avn;

    /**
     * 设备用途
     */
    private String devUsed;

    /**
     * 车辆识别代码
     */
    private String vin;

    /**
     * 是否拆分卡片
     */
    private String isSplit;

    /**
     * 是否已注销
     */
    private String isCanc;

    /**
     * 注销人
     */
    private String cancPsn;

    /**
     * 注销日期
     */
    private String cancDate;

    /**
     * 证书号
     */
    private String certNo;

    /**
     * 已折旧月份
     */
    private Integer deprM;

    /**
     * 保存地点
     */
    private String storageLocation;

    /**
     * 保存地点
     */
    private String storageLocationName;

    /**
     * 取得方式
     */
    private String obtainWay;

    /**
     * 取得日期
     */
    private String obtainDate;

    /**
     * 财务入账状态
     */
    private String entryStatus;

    /**
     * 财务入账日期
     */
    private String entryDate;

    /**
     * 采购组织形式
     */
    private String pof;

    /**
     * 会计凭证号
     */
    private String jvn;

    /**
     * 竣工日期
     */
    private String completedDate;

    /**
     * 设计用途
     */
    private String dsgnUsed;

    /**
     * 持证人
     */
    private String holder;

    /**
     * 折旧状态
     */
    private String deprStatus;

    /**
     * 其中:取暖面积
     */
    private BigDecimal heatingArea;

    /**
     * 其中:危房面积
     */
    private BigDecimal dangerArea;

    /**
     * 非财政拨款
     */
    private BigDecimal unGf;

    /**
     * 闲置面积
     */
    private BigDecimal idleArea;

    /**
     * 自用面积
     */
    private BigDecimal selfArea;

    /**
     * 出借面积
     */
    private BigDecimal lendArea;

    /**
     * 出租面积
     */
    private BigDecimal hireArea;

    /**
     * 其他面积
     */
    private BigDecimal otherArea;

    /**
     * 行驶证登记日期
     */
    private String regisDate;

    /**
     * 车辆产地
     */
    private String vehicleOrigin;

    /**
     * 车辆品牌
     */
    private String vehicleBrand;

    /**
     * 排气量
     */
    private String displacement;

    /**
     * 编制情况
     */
    private String compilationStatus;

    /**
     * 车辆用途
     */
    private String vehicleUsage;

    /**
     * 出版社
     */
    private String press;

    /**
     * 出版日期
     */
    private String publicationDate;

    /**
     * 档案号
     */
    private String fileNo;

    /**
     * 保存年限(档案)
     */
    private Integer storagePeriod;

    /**
     * 文物等级
     */
    private String cocr;

    /**
     * 来源地(文物)
     */
    private String origin;

    /**
     * 藏品年代
     */
    private String collectionAge;

    /**
     * 栽种年龄
     */
    private Integer plantingAge;

    /**
     * 栽种年份
     */
    private String plantingYear;

    /**
     * 纲属科
     */
    private String genus;

    /**
     * 产地(动植物)
     */
    private String producer;

    /**
     * 分摊面积
     */
    private BigDecimal sharedArea;

    /**
     * 独用面积
     */
    private BigDecimal exclusiveArea;

    /**
     * 土地级次
     */
    private String landLevel;

    private String key;

    /**
     * 报废申请审核状态
     */
    private String applstas;

    /**
     * 资产类型名称
     */
    private String assetTypeName;

    /**
     * 资产类型名称(新分类)
     */
    private String assetTypeNName;

    /**
     * 设备类型
     */
    private String ccmName;

    /**
     * 卡片配置
     */
    private String stockCfg;

    private List<AmsRecordsVo> recordsVos;

    private String deptName;

    /**
     * 使用科室名称
     */
    private String deptUseName;

    /**
     * 资产类型(新)
     */
    private String assetTypeN;

    /*
     * 预入库
     */
    private String preWarehousing;

    /**
     * 资产类型 固定资产 非固定资产
     */
    private String type;

    private String storageAreaName;

    private String stockNumber;

    private BigDecimal totalAssetNav;

    private BigDecimal totalDep;

    private BigDecimal totalNbv;

    private Integer totalNum;

    /**
     * 下面是给资产详情界面第三个tabs的汇总页面用的 NR: 没有注销，HR：已经注销，AL所有的
     */
    private BigDecimal totalNRassetNav;
    private BigDecimal totalNRdep;
    private BigDecimal totalNRnbv;
    private Integer totalNRnum;

    private BigDecimal totalHRassetNav;
    private BigDecimal totalHRdep;
    private BigDecimal totalHRnbv;
    private Integer totalHRnum;

    private BigDecimal totalALassetNav;
    private BigDecimal totalALdep;
    private BigDecimal totalALnbv;
    private Integer totalALnum;

    /**
     * 是否为财政补助资金
     */
    private Boolean isFinaSubsidy;

    /**
     * 资金性质
     */
    private String fundingNature;

    /**
     * 快照月份
     */
    private String monthlySnapshot;

}
