package com.jp.med.ams.modules.property.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.property.dto.AmsNfAssetAdminDeptDto;

/**
 * 科室固定资产归口科室表
 * 
 * <AUTHOR>
 * @email -
 * @date 2025-03-13 11:48:41
 */
public interface AmsNfAssetAdminDeptWriteService extends IService<AmsNfAssetAdminDeptDto> {

    boolean save(AmsNfAssetAdminDeptDto dto);

    boolean update(AmsNfAssetAdminDeptDto dto);
}
