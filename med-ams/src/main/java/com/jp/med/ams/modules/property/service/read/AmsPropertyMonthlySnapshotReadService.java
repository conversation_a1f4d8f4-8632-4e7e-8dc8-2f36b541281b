package com.jp.med.ams.modules.property.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.property.dto.AmsPropertyMonthlySnapshotDto;

import java.util.HashMap;
import java.util.List;

/**
 * 资产详情
 *
 * <AUTHOR>
 * @email -
 * @date 2023-08-29 17:33:47
 */
public interface AmsPropertyMonthlySnapshotReadService extends IService<AmsPropertyMonthlySnapshotDto> {
    List<HashMap<String, Object>> queryMonthlyDepreciationDetails(AmsPropertyMonthlySnapshotDto dto);

    /*
     * 查询固定资产
     */

}
