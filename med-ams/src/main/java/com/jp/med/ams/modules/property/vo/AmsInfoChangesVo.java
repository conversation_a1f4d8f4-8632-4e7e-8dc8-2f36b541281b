package com.jp.med.ams.modules.property.vo;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 资产信息变更记录表
 * <AUTHOR>
 * @email -
 * @date 2023-09-06 09:52:16
 */
@Data
public class AmsInfoChangesVo {

	/** ID */
	private Integer id;

	/** 固定资产码 */
	private String faCode;

	/** 变更字段编码 */
	private String chgCode;

	private String chgCodeName;

	/** 变更字段名称 */
	private String chgName;

	/** 变更前 */
	private String chgBefore;

	/** 变更后 */
	private String chgAfter;

	/** 变更前 */
	private String chgBeforeName;

	/** 变更后 */
	private String chgAfterName;

	/** 变更时间 */
	private String chgDate;

	/** 变更人 */
	private String chger;

	/** 审核人 */
	private String chker;


	/** 变更人 */
	private String chgerName;

	/** 审核人 */
	private String chkerName;

	/** 资产编码 */
	private String assetCode;

	/** 资产名称 */
	private String assetName;

	/** 业务状态 */
	private String status;


	@JsonIgnore
	private String propertyJsonData;

	private Object propertyData;

	public Object getPropertyData() {
		return JSONUtil.parse(propertyJsonData).getByPath("property");
	}

}
