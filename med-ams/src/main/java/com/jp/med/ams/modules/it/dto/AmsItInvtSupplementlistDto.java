package com.jp.med.ams.modules.it.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email -
 * @date 2023-12-14 17:42:02
 */
@Data
@TableName("ams_it_invt_supplementlist" )
public class AmsItInvtSupplementlistDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 耗材名称 */
    @Excel(name = "耗材名称")
    @TableField("name2")
    private String name2;

    /** 耗材型号 */
    @Excel(name = "耗材型号")
    @TableField("model2")
    private String model2;

    /** 耗材数量 */
    @Excel(name = "要货数量")
    @TableField("number")
    private Integer number;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    private String remark;

    /** 状态1为删除，0为未删除，null未删除 */
    @TableField("active_flag")
    private Integer activeFlag;

    /** 医院id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 创建人 */
    @TableField("cter")
    private String cter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 修改人员 */
    @TableField("upder")
    private String upder;

    /** 修改时间 */
    @TableField("update_time")
    private String updateTime;

    /** 删除人员 */
    @TableField("delter")
    private String delter;

    /** 删除时间 */
    @TableField("delete_time")
    private String deleteTime;

    /** 删除时间 */
    @TableField(exist = false)
    private Integer[] ids;

    /** 批次号：相同代表同一批次的要货记录 */
    @TableField("batch_number")
    private String batchNumber;


    /** 修改详情-文件 */
    @TableField(exist = false)
    private List<ModifyFileDto> modifyFileDetails;
}
