package com.jp.med.ams.modules.property.service.read;

import java.util.HashMap;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.dto.AmsScrapApplyDto;
import com.jp.med.ams.modules.property.dto.AmsScrapDto;
import com.jp.med.ams.modules.property.dto.AmsScrapRecordsDto;
import com.jp.med.ams.modules.property.vo.AmsScrapApplyVo;
import com.jp.med.ams.modules.property.vo.AmsScrapVo;

/**
 * 资产报废
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-09-04 21:48:57
 */
public interface AmsScrapReadService extends IService<AmsScrapDto> {

    /**
     * 查询列表
     * 
     * @param dto
     * @return
     */
    List<AmsScrapVo> queryList(AmsScrapDto dto);

    /**
     * 查询申请
     * 
     * @param dto
     * @return
     */
    List<AmsScrapApplyVo> queryApply(AmsScrapApplyDto dto);

    /**
     * 查询当前人员待鉴定报废资产列表
     *
     * @param dto
     * @return
     */
    List<AmsScrapApplyVo> queryScrapIdentificationList(AmsScrapApplyDto dto);

    /*
     * 查询待报废数量
     */
    Integer queryScrapIdentificationCount(AmsScrapApplyDto dto);

    /**
     * 查询待审批数量
     *
     * @param dto
     * @return
     */
    Integer queryScrapPendingCount(AmsScrapApplyDto dto);

    HashMap<String, String> queryScrapIdentificationUser(AmsPropertyDto dto);

    Object queryAnnualSummary(AmsScrapDto dto);

    byte[] exportAnnualSummary(AmsScrapDto dto);

    AmsScrapRecordsDto queryScrapRecords(AmsScrapRecordsDto dto);

    /**
     * 查询已鉴定的资产列表
     * 普通用户只能查询自己已鉴定的资产，管理员可查询所有已鉴定资产
     *
     * @param dto 查询条件
     * @return 已鉴定的资产列表
     */
    List<AmsScrapVo> queryIdentifiedAssets(AmsScrapDto dto);
}
