package com.jp.med.ams.modules.property.strategy.impl;

import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.mapper.read.AmsPropertyReadMapper;
import com.jp.med.ams.modules.property.strategy.AmsPropertyInboundStrategy;
import com.jp.med.common.exception.AppException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * 子项资产入库策略
 * 
 * 处理子项资产（type=3）的入库逻辑
 * 
 * <AUTHOR>
 */
@Component
public class AmsPropertySubAssetInboundStrategy implements AmsPropertyInboundStrategy {

    @Autowired
    private AmsPropertyReadMapper amsPropertyReadMapper;

    /**
     * 子项资产UDI长度 -1
     */
    private static final int UDI_LENGTH_2 = 11;

    /**
     * 子项资产编码开头(二维码)
     */
    private static final String TYPE_3_START = "2";

    @Override
    public String getSupportedAssetType() {
        return "3";
    }

    @Override
    public void validateAssetData(AmsPropertyDto dto) {
        // 子项资产必须有父项资产码
        if (StringUtils.isBlank(dto.getSaCode())) {
            throw new AppException("子项资产必须指定父项资产码");
        }

        // 验证父项资产是否存在
        AmsPropertyDto parent = amsPropertyReadMapper.queryParent(dto);
        if (parent == null) {
            throw new AppException("未找到父项资产，请检查父项资产码是否正确");
        }

        // 验证父项资产编码
        if (StringUtils.isEmpty(parent.getAssetCode())) {
            throw new AppException("资产父项无AssetCode请检查");
        }
    }

    @Override
    public void generateAssetCodes(AmsPropertyDto dto) {
        DecimalFormat decimalFormat = new DecimalFormat("000");

        // 获取父项资产信息
        AmsPropertyDto parent = amsPropertyReadMapper.queryParent(dto);
        if (parent == null) {
            throw new AppException("未找到父项资产");
        }

        // 获取最大子项编码
        long maxSonFaCode = amsPropertyReadMapper.queryMaxSonFaCode(dto) + 1;
        String maxSonFaCodeIncremented = decimalFormat.format(maxSonFaCode);

        // 生成二维码
        String uid = parent.getUid() + maxSonFaCodeIncremented;

        // 去掉前面的0直到长度为11位
        while (uid.length() > UDI_LENGTH_2) {
            uid = uid.substring(1);
        }

        // 继承父项 faCode +00x UDI
        String faCode = dto.getSaCode() + "-" + maxSonFaCodeIncremented;
        String assetCode = parent.getAssetCode() + "-" + maxSonFaCodeIncremented;

        dto.setFaCode(faCode);
        dto.setUid(TYPE_3_START + uid);
        dto.setAssetCode(assetCode);
    }

    @Override
    public void setAssetSpecificProperties(AmsPropertyDto dto) {
        // 获取父项资产信息并继承属性
        AmsPropertyDto parent = amsPropertyReadMapper.queryParent(dto);
        if (parent != null) {
            // 设置子项资产类型
            dto.setAssetTypeN(parent.getAssetTypeN());
            dto.setAssetType(parent.getAssetType());

            // 设置资产大类
            if (dto.getAssetType() != null && !dto.getAssetType().isEmpty()) {
                dto.setAssetClassification(dto.getAssetType().substring(0, 1));
            }

        }

        // 设置注销标志为0（未注销）
        dto.setIsCanc("0");

        // 注意：净值、残值、累计折旧等基础属性在 checkAndCompleteInfo 中统一设置
    }

    @Override
    public void preProcessInbound(AmsPropertyDto dto, Boolean isConfirmAdd) {
        // 设置入库日期
        if (Objects.isNull(dto.getInptDate()) || dto.getInptDate().isEmpty()) {
            dto.setInptDate(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        }
    }

    @Override
    public void postProcessInbound(AmsPropertyDto dto, Boolean isConfirmAdd) {
        // 子项资产入库后处理逻辑
        // 可以在这里添加子项资产特有的后处理逻辑
    }

    @Override
    public boolean requiresPostingLockCheck() {
        return false;
    }

    @Override
    public String getStrategyDescription() {
        return "子项资产入库策略 - 处理子项资产的编码生成、验证和入库逻辑，依赖父项资产";
    }
}
