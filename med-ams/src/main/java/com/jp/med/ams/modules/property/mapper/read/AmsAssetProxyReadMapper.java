package com.jp.med.ams.modules.property.mapper.read;

import com.jp.med.ams.modules.property.dto.AmsAssetProxyDto;
import com.jp.med.ams.modules.property.vo.AmsAssetProxyVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 资产人员科室分类代理管理表
 *
 * <AUTHOR>
 * @email -
 * @date 2024-11-05 11:43:44
 */
@Mapper
public interface AmsAssetProxyReadMapper extends BaseMapper<AmsAssetProxyDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<AmsAssetProxyVo> queryList(AmsAssetProxyDto dto);
}
