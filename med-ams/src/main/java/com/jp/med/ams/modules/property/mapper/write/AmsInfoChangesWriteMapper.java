package com.jp.med.ams.modules.property.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ams.modules.property.dto.AmsInfoChangesDto;
import org.apache.ibatis.annotations.Mapper;

/**
 * 资产信息变更记录表
 * <AUTHOR>
 * @email -
 * @date 2023-09-06 09:52:16
 */
@Mapper
public interface AmsInfoChangesWriteMapper extends BaseMapper<AmsInfoChangesDto> {
    /**
     * 写入
     * @param dto
     */
    void insertBatch(AmsInfoChangesDto dto);


    /**
     * 修改资产信息变更记录业务状态
     * @param dto
     */
    void changeInfoStatus(AmsInfoChangesDto dto);
}
