package com.jp.med.ams.modules.it.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 耗材申请详情
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Data
public class AmsItApplyDetailVo {


    private Integer id;

	/** 申请id */
	private Integer applyId;

	/** 名称 */
	private String name;

	/** 耗材名称 */
	private String name2;

	/** 耗材型号 */
	private String model2;

	/** 入库物品类型(0:设备,1:耗材) */
	private String type;

	/** 型号 */
	private String model;

	/** 申请数量 */
	private Integer num;

	/** 历史申请数量 */
	private Integer hisNum;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 更新人 */
	private String updtr;

	/** 更新时间 */
	private String updateTime;

	/** 删除人 */
	private String delter;

	/** 删除时间 */
	private String deleteTime;

	/** 有效标志(1:删除) */
	private String activeFlag;

	/** 医疗机构id */
	private String hospitalId;

	/**
	*当前型号耗材库存数量
	*
	*/
	private Integer invtNum;

}
