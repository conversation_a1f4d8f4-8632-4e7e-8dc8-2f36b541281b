package com.jp.med.ams.modules.property.service.write.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.config.dto.AmsBasicCfgDto;
import com.jp.med.ams.modules.config.mapper.read.AmsBasicCfgReadMapper;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.dto.AmsSourceAmountSplitDto;
import com.jp.med.ams.modules.property.mapper.read.AmsSourceAmountSplitReadMapper;
import com.jp.med.ams.modules.property.mapper.write.AmsSourceAmountSplitWriteMapper;
import com.jp.med.ams.modules.property.service.write.AmsSourceAmountSplitWriteService;
import com.jp.med.ams.modules.property.vo.AmsSourceAmountSplitVo;
import com.jp.med.common.exception.AppException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 资产金额拆分
 *
 * <AUTHOR>
 * @email -
 * @date 2025-02-11 21:04:01
 */
@Service
@Transactional(readOnly = false)
public class AmsSourceAmountSplitWriteServiceImpl
        extends ServiceImpl<AmsSourceAmountSplitWriteMapper, AmsSourceAmountSplitDto>
        implements AmsSourceAmountSplitWriteService {

    @Autowired
    private AmsSourceAmountSplitWriteMapper amsSourceAmountSplitWriteMapper;

    @Autowired
    private AmsBasicCfgReadMapper amsBasicCfgReadMapper;
    @Autowired
    private AmsSourceAmountSplitReadMapper amsSourceAmountSplitReadMapper;

    /**
     * 同步资金来源拆分记录
     *
     * @param dto
     */
    public void syncSplitAmount(AmsPropertyDto dto) {
        if (dto.getDontSyncSplitAmount() != null && dto.getDontSyncSplitAmount()) {
            return;
        }
        // 更新资金来源拆分记录
        if (dto.getSource() == null) {
            return;
        }

        // 查询资金来源配置
        AmsBasicCfgDto amsBasicCfgDto = amsBasicCfgReadMapper.querySourceByCode(dto.getSource());
        if (amsBasicCfgDto == null) {
            throw new AppException("资金来源不存在");
        }

        // 查询现有的拆分记录
        AmsSourceAmountSplitDto splitAmountQueryDto = new AmsSourceAmountSplitDto();
        List<AmsSourceAmountSplitVo> existingSplits = new ArrayList<>();
        if (StrUtil.isNotBlank(dto.getFaCode())) {
            splitAmountQueryDto.setFaCode(dto.getFaCode());
            existingSplits = amsSourceAmountSplitReadMapper.queryList(splitAmountQueryDto);
        }

        // 判断是否为混合资金来源(包含"/"分隔符)
        if (amsBasicCfgDto.getName().contains("/")) {

            // 根据"/"分隔符拆分资金来源名称
            String[] sourceNames = amsBasicCfgDto.getName().split("/");

            String[] sourceCodes = Arrays.stream(sourceNames).map(item -> {
                return amsBasicCfgReadMapper.querySourceByName(item).getCode();
            }).toArray(String[]::new);
            // 根据拆分数量设置对应的金额字段(默认为0)

            // 如果是混合来源但没有拆分记录,则创建新记录
            if (existingSplits.isEmpty()) {
                AmsSourceAmountSplitDto splitRecord = new AmsSourceAmountSplitDto();
                splitRecord.setSourceType(dto.getSource());
                splitRecord.setFaCode(dto.getFaCode());

                if (sourceNames.length >= 1) {
                    splitRecord.setSplitAmount1(BigDecimal.ZERO);
                    splitRecord.setSplitType1(sourceCodes[0]);
                }
                if (sourceNames.length >= 2) {
                    splitRecord.setSplitAmount2(BigDecimal.ZERO);
                    splitRecord.setSplitType2(sourceCodes[1]);
                }
                if (sourceNames.length >= 3) {
                    splitRecord.setSplitAmount3(BigDecimal.ZERO);
                    splitRecord.setSplitType3(sourceCodes[2]);
                }
                // 插入拆分记录
                amsSourceAmountSplitWriteMapper.insert(splitRecord);
            }

            // 如果是混合来源 有拆分记录 则更新拆分记录
            if (!existingSplits.isEmpty()) {
                for (AmsSourceAmountSplitVo splitRecord : existingSplits) {

                    if (sourceNames.length >= 1) {

                        splitRecord.setSplitType1(sourceCodes[0]);
                    }
                    if (sourceNames.length >= 2) {

                        splitRecord.setSplitType2(sourceCodes[1]);
                    }
                    if (sourceNames.length >= 3) {

                        splitRecord.setSplitType3(sourceCodes[2]);
                    }
                    AmsSourceAmountSplitDto updateDto = new AmsSourceAmountSplitDto();
                    BeanUtils.copyProperties(splitRecord, updateDto);
                    amsSourceAmountSplitWriteMapper.updateById(updateDto);
                }
            }
        } else {
            // 如果不是混合来源,删除所有拆分记录
            if (!existingSplits.isEmpty()) {
                for (AmsSourceAmountSplitVo splitRecord : existingSplits) {
                    amsSourceAmountSplitWriteMapper.deleteById(splitRecord.getId());
                }
            }
        }
    }

}
