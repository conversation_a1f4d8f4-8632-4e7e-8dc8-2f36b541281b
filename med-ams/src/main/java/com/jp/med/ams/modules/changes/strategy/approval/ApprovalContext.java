package com.jp.med.ams.modules.changes.strategy.approval;

import com.jp.med.ams.modules.property.service.write.impl.AmsPropertyWriteServiceImpl;

/**
 * 审批处理上下文
 * 包含审批处理过程中需要的服务和参数
 */
public class ApprovalContext {
    private final AmsPropertyWriteServiceImpl amsPropertyWriteService;
    
    public ApprovalContext(AmsPropertyWriteServiceImpl amsPropertyWriteService) {
        this.amsPropertyWriteService = amsPropertyWriteService;
    }
    
    public AmsPropertyWriteServiceImpl getAmsPropertyWriteService() {
        return amsPropertyWriteService;
    }
}
