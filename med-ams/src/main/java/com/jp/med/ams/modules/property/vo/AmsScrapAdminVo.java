package com.jp.med.ams.modules.property.vo;

import java.util.List;

import lombok.Data;

/**
 * 资产报废人员管理
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-03-28 09:39:22
 */
@Data
public class AmsScrapAdminVo {

	/** ID */
	private Integer id;

	/** 操作人 */
	private String opter;
    private String opterDept;

	/** 资产类型 */
	private String assetType;

	/** 资产类型(新) */
	private String assetTypeN;

	/** 科室 */
	private String dept;

	/** 有效标志 */
	private String activeFlag;

	/** 医疗机构编码 */
	private String hospitalId;

	/** 用户名 */
	private String empName;

	/** 资产类型 */
	private List<String> assetTypes;

	/** 资产类型(新) */
	private List<String> assetTypeNs;

	/** 科室(集合) */
	private List<String> depts;
	private List<String> storageAreaList;

    /**
     * 资产类型
     */
    private String assetTypeNames;

    /**
     * 资产类型(新)
     */
    private String assetTypeNNames;

    /**
     * 科室(集合)
     */
    private String deptNames;

	private String storageAreaNames;

	private String storageAreas;

    /**
     * rank
     */
    private int rank;

}
