package com.jp.med.ams.modules.property.strategy.impl;

import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsOutStockAuditDto;
import com.jp.med.ams.modules.amsPropertyInAndOut.mapper.write.AmsOutStockAuditWriteMapper;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.strategy.AmsPropertyInboundStrategy;
import com.jp.med.common.entity.user.SysUser;
import com.jp.med.common.exception.AppException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 预入库资产入库策略
 * 
 * 处理预入库资产（type=4）的入库逻辑
 * 
 * <AUTHOR>
 */
@Component
public class AmsPropertyPreWarehouseInboundStrategy implements AmsPropertyInboundStrategy {

    @Autowired
    private AmsOutStockAuditWriteMapper amsOutStockAuditWriteMapper;

    /**
     * 预入库标志
     */
    private static final String TYPE_1_START = "1";

    @Override
    public String getSupportedAssetType() {
        return "4";
    }

    @Override
    public void validateAssetData(AmsPropertyDto dto) {
        // 预入库资产基本验证
        if (StringUtils.isBlank(dto.getAssetName())) {
            throw new AppException("预入库资产名称不能为空");
        }

        // 验证入库信息
        SysUser sysUser = dto.getSysUser();
        if (sysUser == null &&
                (StringUtils.isBlank(dto.getThirdPartyInDept())
                        || StringUtils.isBlank(dto.getThirdPartyInUserName()))) {
            throw new AppException("入库信息缺失");
        }

        if (sysUser == null && StringUtils.isBlank(dto.getQrId())) {
            throw new AppException("入库信息缺失");
        }
    }

    @Override
    public void generateAssetCodes(AmsPropertyDto dto) {
        // 预入库资产不需要生成特殊编码，使用默认逻辑
        // 编码生成可以延后到正式入库时进行
    }

    @Override
    public void setAssetSpecificProperties(AmsPropertyDto dto) {
        // 设置预入库标志
        dto.setPreWarehousing(TYPE_1_START);

        // 设置出库日期
        dto.setStooutDate(new SimpleDateFormat("yyyyMMdd").format(new Date()));

        // 设置资产大类
        if (dto.getAssetType() != null && !dto.getAssetType().isEmpty()) {
            dto.setAssetClassification(dto.getAssetType().substring(0, 1));
        }

        // 设置注销标志为0（未注销）
        dto.setIsCanc("0");

        // 注意：净值、残值、累计折旧等基础属性在 checkAndCompleteInfo 中统一设置
    }

    @Override
    public void preProcessInbound(AmsPropertyDto dto, Boolean isConfirmAdd) {
        // 预入库资产入库前处理
        // 可以在这里添加预入库特有的前处理逻辑
    }

    @Override
    public void postProcessInbound(AmsPropertyDto dto, Boolean isConfirmAdd) {
        // 创建并插入出库审核记录
        AmsOutStockAuditDto amsOutStockAuditDto = createOutStockAuditDto(dto);
        amsOutStockAuditWriteMapper.insert(amsOutStockAuditDto);
    }

    @Override
    public boolean requiresPostingLockCheck() {
        return false;
    }

    @Override
    public String getStrategyDescription() {
        return "预入库资产入库策略 - 处理预入库资产的特殊逻辑和出库审核记录";
    }

    /**
     * 创建出库审核DTO
     * 
     * @param dto 资产DTO
     * @return 出库审核DTO
     */
    private AmsOutStockAuditDto createOutStockAuditDto(AmsPropertyDto dto) {
        AmsOutStockAuditDto amsOutStockAuditDto = new AmsOutStockAuditDto();
        amsOutStockAuditDto.setAssetName(dto.getAssetName());

        SysUser sysUser = dto.getSysUser();
        String hrmOrgId;
        String empCode;

        if (sysUser != null) {
            hrmOrgId = sysUser.getHrmUser().getHrmOrgId();
            empCode = sysUser.getHrmUser().getEmpCode();
        } else {
            hrmOrgId = dto.getThirdPartyInDept();
            empCode = dto.getThirdPartyInUserName();
            String qrId = dto.getQrId();
            if (StringUtils.isBlank(qrId)) {
                throw new AppException("入库信息缺失");
            }
            dto.setQrId(qrId);
        }

        amsOutStockAuditDto.setInDept(hrmOrgId);
        amsOutStockAuditDto.setInUser(empCode);
        amsOutStockAuditDto.setInTime(new Date());
        amsOutStockAuditDto.setPropertyId(dto.getId());
        amsOutStockAuditDto.setQrId(dto.getQrId());
        amsOutStockAuditDto.setProsstas("0"); // 0 待发起
        amsOutStockAuditDto.setHospitalId(dto.getHospitalId());

        return amsOutStockAuditDto;
    }
}
