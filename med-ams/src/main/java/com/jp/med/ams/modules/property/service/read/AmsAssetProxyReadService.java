package com.jp.med.ams.modules.property.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.property.dto.AmsAssetProxyDto;
import com.jp.med.ams.modules.property.vo.AmsAssetProxyVo;

import java.util.List;

/**
 * 资产人员科室分类代理管理表
 *
 * <AUTHOR>
 * @email -
 * @date 2024-11-05 11:43:44
 */
public interface AmsAssetProxyReadService extends IService<AmsAssetProxyDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<AmsAssetProxyVo> queryList(AmsAssetProxyDto dto);

    /**
     * 分页查询列表
     *
     * @param dto
     * @return
     */
    List<AmsAssetProxyVo> queryPageList(AmsAssetProxyDto dto);
}

