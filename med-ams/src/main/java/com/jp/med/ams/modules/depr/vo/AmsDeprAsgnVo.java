package com.jp.med.ams.modules.depr.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 资产折旧分配
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 22:13:41
 */
@Data
public class AmsDeprAsgnVo {

	/** ID */
	private Integer id;

	/** 固定资产码 */
	private String faCode;

	/** 资产名称 */
	private String assetName;

	/** 科室 */
	private String orgId;

	/** 科室名称 */
	private String orgName;

	/** 比例 */
	private BigDecimal prop;

	/** 更新时间 */
	private String updateTime;

	/** 操作人 */
	private String opter;

	/** 修改原因 */
	private String modiRea;

	/**
	 * 有效标志 未使用
	 */
	private String activeFlag;

	/** 科室 */
	private String allocation;

	/** 分配情况 */
	private String allocationId;

}
