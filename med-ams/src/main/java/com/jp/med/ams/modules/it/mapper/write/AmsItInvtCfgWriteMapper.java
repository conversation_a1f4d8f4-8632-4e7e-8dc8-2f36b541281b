package com.jp.med.ams.modules.it.mapper.write;

import com.jp.med.ams.modules.it.dto.AmsItInvtCfgDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 信息科库房耗材配置
 *
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Mapper
public interface AmsItInvtCfgWriteMapper extends BaseMapper<AmsItInvtCfgDto> {
    void updateCfgById(AmsItInvtCfgDto dto);

    void deleteCfgById(AmsItInvtCfgDto dto);


    void updateCfgByCode(AmsItInvtCfgDto dto);
}
