<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.read.EcsTravelAccomStandardCfgReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.ecs.modules.config.vo.EcsTravelAccomStandardCfgVo" id="travelAccomStandardCfgMap">
        <result property="id" column="id"/>
        <result property="evectionAddr" column="evection_addr"/>
        <result property="standardPrice" column="standard_price"/>
        <result property="peakSeason" column="peak_season"/>
        <result property="peakSeasonPrice" column="peak_season_price"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.ecs.modules.config.vo.EcsTravelAccomStandardCfgVo">
        select
            id as id,
            evection_addr as evectionAddr,
            standard_price as standardPrice,
            peak_season as peakSeason,
            peak_season_price as peakSeasonPrice
        from ecs_travel_accom_standard_cfg
        <where>
            <if test="id!=null">
                and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="evectionAddr!=null">
                and evection_addr = #{evectionAddr,jdbcType=INTEGER}
            </if>
        </where>
    </select>

</mapper>
