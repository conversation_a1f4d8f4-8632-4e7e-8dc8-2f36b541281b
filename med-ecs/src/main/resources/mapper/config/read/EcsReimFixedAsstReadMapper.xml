<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.read.EcsReimFixedAsstReadMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.common.vo.EcsReimFixedAsstVo" id="reimFixedAsstMap">
        <result property="id" column="id"/>
        <result property="fixedSubCode" column="fixed_sub_code"/>
        <result property="fixedSubName" column="fixed_sub_name"/>
        <result property="fundType" column="fund_type"/>
        <result property="deptType" column="dept_type"/>
        <result property="subType" column="sub_type"/>
        <result property="activeFlag" column="active_flag"/>
        <result property="abst" column="abst"/>
        <result property="sort" column="sort"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.common.vo.EcsReimFixedAsstVo">
        select
            id as id,
            fixed_sub_code as fixedSubCode,
            fixed_sub_name as fixedSubName,
            fund_type as fundType,
            dept_type as deptType,
            sub_type as subType,
            econ_fun_sub_code as econFunSubCode,
            active_flag as activeFlag,
            abst as abst,
            sort as sort,
            crter as crter,
            create_time as createTime
        from ecs_reim_fixed_asst
        <where>
            <if test="fixedSubCode !=null and fixedSubCode != ''">
                and fixed_sub_code = #{fixedSubCode,jdbcType=VARCHAR}
            </if>
            <if test="abst != null and abst != ''">
                and abst like concat('%',#{abst,jdbcType=VARCHAR},'%')
            </if>
        </where>
        order by fixed_sub_code
    </select>

    <!--查询上级编制项下拉树-->
    <select id="queryFixedTree" resultType="com.jp.med.common.vo.EcsReimFixedAsstVo">
        select
            a.id as id,
            a.fixed_sub_code as fixedSubCode,
            a.fixed_sub_name as fixedSubName,
            a.item_type as itemType,
            a.fund_type as fundType,
            a.abst as abst,
            a.fixed_parent_code as fixedParentCode
        <if test="fixedSubCode != '' and fixedSubCode != null">
            ,case when b.id is not null then true else false end as disabled <!--是否可选-->
        </if>
        from
        ecs_reim_fixed_asst a
        <if test="fixedSubCode != '' and fixedSubCode != null">
            left join
            (select
            y.* from
            (with RECURSIVE temp AS (
            select * from ecs_reim_fixed_asst r
            where fixed_sub_code = #{fixedSubCode,jdbcType=VARCHAR}
            UNION ALL
            SELECT b.* from ecs_reim_fixed_asst b, temp t where b.fixed_parent_code = t.fixed_sub_code
            )
            select * from temp) y) b
            on a.id = b.id
        </if>
        where
            a.item_type = '1'
            <if test="activeFlag != '' and activeFlag != null">
                and a.active_flag = #{activeFlag}
            </if>
    </select>

</mapper>
