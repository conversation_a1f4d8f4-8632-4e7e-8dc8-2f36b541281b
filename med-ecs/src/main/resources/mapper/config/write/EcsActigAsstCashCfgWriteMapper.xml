<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.write.EcsActigAsstCashCfgWriteMapper">

    <insert id="saveAsstCashCfg">
        INSERT INTO ecs_actig_asst_cash_cfg (
            sub_code,
            sub_name,
            flow_dire,
            pinyin,
            crter,
            create_time,
            hospital_id,
            active_flag,
            parent_sub_code,
            year
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.subCode,jdbcType=VARCHAR},
                #{item.subName,jdbcType=VARCHAR},
                #{item.flowDire,jdbcType=VARCHAR},
                #{item.pinyin,jdbcType=VARCHAR},
                #{item.crter,jdbcType=VARCHAR},
                to_char(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS'),
                'zjxrmyy',
                '1',
                #{item.parentSubCode,jdbcType=VARCHAR},
                #{item.year,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>
