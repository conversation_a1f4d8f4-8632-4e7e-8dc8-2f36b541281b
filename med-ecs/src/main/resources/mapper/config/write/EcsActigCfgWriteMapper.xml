<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.write.EcsActigCfgWriteMapper">
    <insert id="saveActigCfg">
        INSERT INTO ecs_actig_cfg (
            sub_code,
            sub_name,
            sub_type,
            pinyin,
            remarks,
            asst_info,
            actig_elem,
            actig_sys,
            balc_dirc,
            emp_type,
            parent_code,
            crter,
            create_time,
            hospital_id,
            active_flag,
            year
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.subCode,jdbcType=VARCHAR},
                #{item.subName,jdbcType=VARCHAR},
                #{item.subType,jdbcType=VARCHAR},
                #{item.pinyin,jdbcType=VARCHAR},
                #{item.remarks,jdbcType=VARCHAR},
                #{item.asstInfo,jdbcType=VARCHAR},
                #{item.actigElem,jdbcType=VARCHAR},
                #{item.actigSys,jdbcType=VARCHAR},
                #{item.balcDirc,jdbcType=VARCHAR},
                #{item.empType,jdbcType=VARCHAR},
                #{item.parentCode,jdbcType=VARCHAR},
                #{item.crter,jdbcType=VARCHAR},
                to_char(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS'),
                'zjxrmyy',
                '1',
                #{item.year,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>
