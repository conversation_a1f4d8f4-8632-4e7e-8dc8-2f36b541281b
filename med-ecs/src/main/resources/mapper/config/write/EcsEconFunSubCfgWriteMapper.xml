<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.config.mapper.write.EcsEconFunSubCfgWriteMapper">

    <insert id="saveEcsEconSubCfg">
        INSERT INTO ecs_econ_fun_sub_cfg (
            sub_code,
            parent_sub_code,
            sub_name,
            sub_type,
            pinyin,
            budget_code,
            remarks,
            crter,
            create_time,
            hospital_id,
            active_flag,
            year,
            bgt_summary
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.subCode,jdbcType=VARCHAR},
                #{item.parentSubCode,jdbcType=VARCHAR},
                #{item.subName,jdbcType=VARCHAR},
                #{item.subType,jdbcType=VARCHAR},
                #{item.pinyin,jdbcType=VARCHAR},
                #{item.budgetCode,jdbcType=VARCHAR},
                #{item.remarks,jdbcType=VARCHAR},
                #{item.crter,jdbcType=VARCHAR},
                to_char(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS'),
                'zjxrmyy',
                '1',
                #{item.year,jdbcType=VARCHAR},
                #{item.bgtSummary,jdbcType=VARCHAR}
                <!--EXTRACT(YEAR FROM CURRENT_DATE)-->
            )
        </foreach>
    </insert>
</mapper>
