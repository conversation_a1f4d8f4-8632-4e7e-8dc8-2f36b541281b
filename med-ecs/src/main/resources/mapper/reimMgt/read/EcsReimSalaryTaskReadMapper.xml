<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimSalaryTaskReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.ecs.modules.reimMgt.vo.EcsReimSalaryTaskVo" id="reimSalaryTaskMap">
        <result property="id" column="id"/>
        <result property="ffMth" column="ff_mth"/>
        <result property="num" column="num"/>
        <result property="shouldPay" column="should_pay"/>
        <result property="reducePay" column="reduce_pay"/>
        <result property="realPay" column="real_pay"/>
        <result property="remark" column="remark"/>
        <result property="crter" column="crter"/>
        <result property="crteTime" column="crte_time"/>
        <result property="reimFlag" column="reim_flag"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsReimSalaryTaskVo">
        select
            id as id,
            ff_mth as ffMth,
            num as num,
            should_pay as shouldPay,
            reduce_pay as reducePay,
            real_pay as realPay,
            remark as remark,
            crter as crter,
            crte_time as crteTime,
            reim_flag as reimFlag,
            salary_id as salaryId,
            reim_id as reimId,
            type as salaryType
        from ecs_reim_salary_task
        <where>
            <if test="reimFlag != null and reimFlag != ''">
                and reim_flag = #{reimFlag,jdbcType=VARCHAR}
            </if>
            <if test="id != null">
                and id = #{id,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="queryListNew" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsReimSalaryTaskVo">
        select
        a.id as id,
        a.ff_mth as ffMth,
        a.num as num,
        a.should_pay as shouldPay,
        a.reduce_pay as reducePay,
        a.real_pay as realPay,
        a.remark as remark,
        a.crter as crter,
        a.crte_time as crteTime,
        a.reim_flag as reimFlag,
        a.salary_id as salaryId,
        a.reim_id as reimId,
        a.type as salaryType,
        b.process_instance_id as processInstanceId
        from ecs_reim_salary_task a
        left join ecs_reim_detail b
        on a.id = b.travel_appr_id and b.type = '5'
        <where>
            b.audit_bchno is null
            <if test="reimFlag != null and reimFlag != ''">
                and a.reim_flag = #{reimFlag,jdbcType=VARCHAR}
            </if>
            <if test="id != null">
                and a.id = #{id,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="querySalaryTaskDetail" resultType="com.jp.med.ecs.modules.reimMgt.vo.EcsReimSalaryTaskDetailVo">
        SELECT
            A.ID AS ID,
            A.task_id AS taskId,
            A.org_id AS orgId,
            c.org_name as orgName,
            A.reim_type AS reimType,
            A.reim_amt AS reimAmt,
            A.reim_desc AS reimDesc,
            A.type AS type,
            A.reim_name AS reimName,
            A.emp_code AS empCode,
            A.emp_type as empType,
            A.emp_count as empCount,
            B.budget_code AS budgetCode,
            b.bgt_summary as bgtSummary
        FROM
        ecs_reim_salary_task_detail A
        LEFT JOIN ecs_econ_fun_sub_cfg B
        ON A.reim_type = B.sub_code and b.year = EXTRACT(YEAR FROM CURRENT_DATE) ::TEXT
        AND B.active_flag = '1'
        AND B.sub_type = '2'
        LEFT JOIN hrm_org c on c.org_id = a.org_id
        WHERE
            a.reim_amt > 0
        <choose>
            <when test="id != null">
               and task_id = #{id,jdbcType=INTEGER}
            </when>
            <otherwise>
               and task_id in
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id,jdbcType=INTEGER}
                </foreach>
            </otherwise>
        </choose>
    </select>

</mapper>
