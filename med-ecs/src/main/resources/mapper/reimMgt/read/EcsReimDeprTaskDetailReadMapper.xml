<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimDeprTaskDetailReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.common.vo.EcsReimDeprTaskDetailVo" id="reimDeprTaskDetailMap">
        <result property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="deptCode" column="dept_code"/>
        <result property="deptName" column="dept_name"/>
        <result property="assetTypeCode" column="asset_type_code"/>
        <result property="assetTypeName" column="asset_type_name"/>
        <result property="sourceCode" column="source_code"/>
        <result property="sourceName" column="source_name"/>
        <result property="openYear" column="open_year"/>
        <result property="openDate" column="open_date"/>
        <result property="amt" column="amt"/>
        <result property="crter" column="crter"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.common.vo.EcsReimDeprTaskDetailVo">
        select
            id as id,
            task_id as taskId,
            dept_code as deptCode,
            dept_name as deptName,
            asset_type_code as assetTypeCode,
            asset_type_name as assetTypeName,
            source_code as sourceCode,
            source_name as sourceName,
            open_year as openYear,
            open_date as openDate,
            amt as amt,
            crter as crter,
            create_time as createTime
        from ecs_reim_depr_task_detail
        <where>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

</mapper>
