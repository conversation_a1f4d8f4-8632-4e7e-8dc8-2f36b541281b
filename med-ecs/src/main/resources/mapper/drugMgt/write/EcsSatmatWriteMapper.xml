<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.drugMgt.mapper.write.EcsSatmatWriteMapper">

    <insert id="saveSatmat">
        INSERT INTO ecs_satmat(
            stoin_num,
            stoin_date,
            satmat_code,
            satmat_name,
            specs,
            pattern,
            unit,
            batch_num,
            prod_date,
            valid_date,
            satmat_num,
            unit_price,
            sumamt,
            factory,
            spler,
            consignor,
            sync_date,
            hospital_id,
            satmat_reim_detail_id,
            att,
            att_name,
            reim_flag
        ) VALUES (
            #{stoinNum,jdbcType=INTEGER},
            #{stoinDate,jdbcType=VARCHAR},
            #{satmatCode,jdbcType=VARCHAR},
            #{satmatName,jdbcType=VARCHAR},
            #{specs,jdbcType=VARCHAR},
            #{pattern,jdbcType=VARCHAR},
            #{unit,jdbcType=VARCHAR},
            #{batchNum,jdbcType=VARCHAR},
            #{prodDate,jdbcType=VARCHAR},
            #{validDate,jdbcType=VARCHAR},
            #{satmatNum,jdbcType=DOUBLE},
            #{unitPrice,jdbcType=DOUBLE},
            #{sumamt,jdbcType=DOUBLE},
            #{factory,jdbcType=VARCHAR},
            #{spler,jdbcType=VARCHAR},
            #{consignor,jdbcType=VARCHAR},
            to_char(CURRENT_TIMESTAMP,'YYYY-MM-DD HH24:HI:SS'),
            'zjxrmyy',
            #{satmatReimDetailId,jdbcType=INTEGER},
            #{att,jdbcType=VARCHAR},
            #{attName,jdbcType=VARCHAR},
            '0'
        )
    </insert>

    <update id="updateReimState">
        UPDATE ecs_satmat
        SET reim_flag = '1',
        satmat_reim_detail_id = #{id,jdbcType=INTEGER}
        WHERE stoin_num IN
        <foreach collection="stoinNumList" item="num" open="(" close=")" separator=",">
            #{num,jdbcType=INTEGER}
        </foreach>
    </update>
</mapper>
