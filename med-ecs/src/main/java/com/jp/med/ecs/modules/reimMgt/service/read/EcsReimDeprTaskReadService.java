package com.jp.med.ecs.modules.reimMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.EcsReimDeprTaskDto;
import com.jp.med.common.vo.EcsReimDeprTaskVo;

import java.util.List;

/**
 * 费用报销-折旧凭证
 * <AUTHOR>
 * @email -
 * @date 2025-02-07 16:08:24
 */
public interface EcsReimDeprTaskReadService extends IService<EcsReimDeprTaskDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimDeprTaskVo> queryList(EcsReimDeprTaskDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<EcsReimDeprTaskVo> queryPageList(EcsReimDeprTaskDto dto);
}

