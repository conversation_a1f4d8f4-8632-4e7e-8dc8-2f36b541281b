package com.jp.med.ecs.modules.reimMgt.feign.purms;

import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.ecs.modules.reimMgt.entity.EcsPurmsReimIdEntity;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 */
@RefreshScope
@FeignClient(name = "PurmsFeignService", url = "${custom.gateway.med-purms-service-uri}")
public interface PurmsFeignService {

    /**
     * 报销完成更新采购申请
     * @param entity
     * @return
     */
    @PostMapping("/purmsPurcReq/updateItemReimId")
    CommonFeignResult updatePurcTaskReimId(@RequestBody EcsPurmsReimIdEntity entity);

}

