package com.jp.med.ecs.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.ecs.modules.config.mapper.read.EcsTravelAccomStandardCfgReadMapper;
import com.jp.med.ecs.modules.config.dto.EcsTravelAccomStandardCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsTravelAccomStandardCfgVo;
import com.jp.med.ecs.modules.config.service.read.EcsTravelAccomStandardCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsTravelAccomStandardCfgReadServiceImpl extends ServiceImpl<EcsTravelAccomStandardCfgReadMapper, EcsTravelAccomStandardCfgDto> implements EcsTravelAccomStandardCfgReadService {

    @Autowired
    private EcsTravelAccomStandardCfgReadMapper ecsTravelAccomStandardCfgReadMapper;

    @Override
    public List<EcsTravelAccomStandardCfgVo> queryList(EcsTravelAccomStandardCfgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsTravelAccomStandardCfgReadMapper.queryList(dto);
    }

    @Override
    public List<EcsTravelAccomStandardCfgVo> queryNoPageList(EcsTravelAccomStandardCfgDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsTravelAccomStandardCfgReadMapper.queryList(dto);
    }


}
