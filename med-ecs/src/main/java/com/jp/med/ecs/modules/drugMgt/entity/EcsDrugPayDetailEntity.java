package com.jp.med.ecs.modules.drugMgt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 药品报销付款详情
 * <AUTHOR>
 * @email -
 * @date 2024-11-25 12:03:17
 */
@Data
@TableName("ecs_drug_pay_detail")
public class EcsDrugPayDetailEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 文件记录表编码:模块编码+随机字符串 */
	@TableField("att_code")
	private String attCode;

	/** 创建人 */
	@TableField("crter")
	private String crter;

	/** 创建时间 */
	@TableField("create_time")
	private String createTime;

}
