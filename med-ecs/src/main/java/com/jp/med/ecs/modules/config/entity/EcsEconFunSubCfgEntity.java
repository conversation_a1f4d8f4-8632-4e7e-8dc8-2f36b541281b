package com.jp.med.ecs.modules.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 经济和功能科目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 10:53:01
 */
@Data
@TableName("ecs_econ_fun_sub_cfg")
public class EcsEconFunSubCfgEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 科目代码 */
	@TableField("sub_code")
	private String subCode;

	/** 科目名称 */
	@TableField("sub_name")
	private String subName;

	/** 科目类型 */
	@TableField("sub_type")
	private String subType;

	/** 拼音助记码 */
	@TableField("pinyin")
	private String pinyin;

	/** 备注 */
	@TableField("remarks")
	private String remarks;

	/** 创建人 */
	@TableField("crter")
	private String crter;

	/** 创建时间 */
	@TableField("create_time")
	private String createTime;

	/** 修改时间 */
	@TableField("modi_time")
	private String modiTime;

	/** 医疗机构id */
	@TableField("hospital_id")
	private String hospitalId;

	/** 启用标志 */
	@TableField("active_flag")
	private String activeFlag;

}
