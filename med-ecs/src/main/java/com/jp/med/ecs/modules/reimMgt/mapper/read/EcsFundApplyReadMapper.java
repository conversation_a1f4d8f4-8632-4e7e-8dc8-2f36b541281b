package com.jp.med.ecs.modules.reimMgt.mapper.read;

import com.jp.med.ecs.modules.reimMgt.dto.EcsFundApplyDto;
import com.jp.med.ecs.modules.reimMgt.vo.EcsFundApplyVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 经费申请
 * <AUTHOR>
 * @email -
 * @date 2023-12-28 17:58:39
 */
@Mapper
public interface EcsFundApplyReadMapper extends BaseMapper<EcsFundApplyDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsFundApplyVo> queryList(EcsFundApplyDto dto);
}
