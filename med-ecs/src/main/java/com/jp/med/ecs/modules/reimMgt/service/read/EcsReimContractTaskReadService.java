package com.jp.med.ecs.modules.reimMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.EcsReimContractTask;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimContractTaskDetailVo;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimContractTaskVo;

import java.util.List;

/**
 * 合同报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 17:13:38
 */
public interface EcsReimContractTaskReadService extends IService<EcsReimContractTask> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimContractTaskVo> queryList(EcsReimContractTask dto);

    /**
     * 分页查询列表
     * @param dto
     * @return
    */
    List<EcsReimContractTaskVo> queryPageList(EcsReimContractTask dto);

    /**
     * 查询合同任务明细
     * @param dto
     * @return
     */
    List<EcsReimContractTaskDetailVo> queryContractTaskDetail(EcsReimContractTask dto);
}

