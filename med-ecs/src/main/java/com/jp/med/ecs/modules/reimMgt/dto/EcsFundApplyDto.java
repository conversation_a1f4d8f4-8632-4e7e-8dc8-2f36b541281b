package com.jp.med.ecs.modules.reimMgt.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;
import java.util.List;

import com.jp.med.common.entity.audit.AuditDetail;
import lombok.Data;

/**
 * 经费申请
 * <AUTHOR>
 * @email -
 * @date 2023-12-28 17:58:39
 */
@Data
@TableName("ecs_fund_apply" )
public class EcsFundApplyDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 申请人 */
    @TableField("appyer")
    private String appyer;

    /** 申请科室 */
    @TableField("appyer_dept")
    private String appyerDept;

    /** 申请时间 */
    @TableField("appyer_time")
    private String appyerTime;

    /** 申请事由 */
    @TableField("appyer_rea")
    private String appyerRea;

    /** 合计金额小写(元) */
    @TableField("sum")
    private BigDecimal sum;

    /** 合计金额大写 */
    @TableField("cap_sum")
    private String capSum;

    /** 开户银行 */
    @TableField("bank")
    private String bank;

    /** 户名 */
    @TableField("acctname")
    private String acctname;

    /** 银行账(卡)号 */
    @TableField("bankcode")
    private String bankcode;

    /** 审核批次号 */
    @TableField("audit_bchno")
    private String auditBchno;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

}
