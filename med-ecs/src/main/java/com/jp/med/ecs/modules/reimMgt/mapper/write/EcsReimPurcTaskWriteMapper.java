package com.jp.med.ecs.modules.reimMgt.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.EcsReimPurcTask;
import com.jp.med.common.dto.ecs.EcsReimPurcTaskDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 零星采购报销任务
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 14:45:03
 */
@Mapper
public interface EcsReimPurcTaskWriteMapper extends BaseMapper<EcsReimPurcTask> {

    /**
     * 保存零星采购任务明细
     *
     * @param detail
     */
    void insertPurcTaskDetail(EcsReimPurcTaskDetail detail);

    /**
     * 更新报销详情任务状态
     *
     * @param ids      零星采购任务明细id
     * @param reimId   报销id
     * @param reimFlag 报销状态
     */
    void updatePurcTaskDetails(@Param("ids") List<Integer> ids, @Param("reimId") int reimId,
                               @Param("reimFlag") String reimFlag);

    /**
     * 撤销报销申请
     *
     * @param reimId 报销id
     */
    void updatePurcTaskDetailsByReimId(@Param("reimId") int reimId);
}
