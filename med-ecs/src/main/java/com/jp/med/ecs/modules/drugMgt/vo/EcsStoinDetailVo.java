package com.jp.med.ecs.modules.drugMgt.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class EcsStoinDetailVo {

    /**  id **/
    private Integer id;

    /** 入库单序号 **/
    private Integer zdXh;

    /** 药品名称 **/
    private String drugName;

    /** 批号 **/
    private String batchNum;

    /** 单位 **/
    private String unit;

    /** 药品数量 **/
    private Double drugNum;

    /** 购入价（单价） **/
    private BigDecimal purcPrice;

    /** 购入金额 **/
    private BigDecimal purcpricAmt;

    /** 零售价 **/
    private BigDecimal rtalPrice;

    /** 零售金额 **/
    private BigDecimal rtalpricAmt;

    /** 是否退货 **/
    private String isBack;
}
