package com.jp.med.ecs.modules.reimMgt.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimTravelApprDto;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimTravelApprVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报销差旅审批
 * <AUTHOR>
 * @email -
 * @date 2023-12-13 13:48:41
 */
@Mapper
public interface EcsReimTravelApprReadMapper extends BaseMapper<EcsReimTravelApprDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimTravelApprVo> queryList(EcsReimTravelApprDto dto);

    /**
     * 查询列表
     * @param dto
     * @return
     */
    List<EcsReimTravelApprVo> queryListNew(EcsReimTravelApprDto dto);

    /**
     * 获取审核流程的第一顺序审核
     * @param dto
     * @return
     */
    AuditDetail getFirstAudit(EcsReimTravelApprDto dto);

    /**
     * 查询当前节点之后是否有已审核的节点 (包括当前节点)
     * @param auditBchno
     * @param chker
     * @return
     */
    List<AuditDetail> queryNextAuditedNode(@Param("auditBchno") String auditBchno,@Param("chker") String chker);

    /**
     * 差旅申请批次号查询对应报销
     * @param dto
     * @return
     */
    Integer queryReimWithAppr(EcsReimTravelApprDto dto);

}
