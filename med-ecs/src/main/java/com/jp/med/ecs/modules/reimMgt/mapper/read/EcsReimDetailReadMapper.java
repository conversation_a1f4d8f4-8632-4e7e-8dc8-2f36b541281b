package com.jp.med.ecs.modules.reimMgt.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimDetailDto;
import com.jp.med.ecs.modules.reimMgt.entity.*;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimDetailVo;
import com.jp.med.ecs.modules.reimMgt.vo.HomeMsgNoteVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 报销明细
 * <AUTHOR>
 * @email -
 * @date 2023-12-04 22:01:14
 */
@Mapper
public interface EcsReimDetailReadMapper extends BaseMapper<EcsReimDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimDetailVo> queryList(EcsReimDetailDto dto);

    /**
     * 查询列表
     * @param dto
     * @return
     */
    List<EcsReimDetailVo> queryListNew(EcsReimDetailDto dto);

    /**
     * 查询项目详情
     * @param dto
     * @return
     */
    List<EcsReimItemDetail> queryItemDetail(EcsReimDetailDto dto);

    /**
     * 查询补助项目详情
     * @param dto
     * @return
     */
    List<EcsReimSubsItemDetail> querySubsItemDetail(EcsReimDetailDto dto);

    /**
     * 查询报销人员详情
     * @param dto
     * @return
     */
    List<EcsReimPsnDetail> queryPsnDetail(EcsReimDetailDto dto);

    /**
     * 查询科室已报销金额
     * @param dto
     * @return
     */
    List<EcsReimPsnDetail> queryDeptAmt(EcsReimDetailDto dto);

    /**
     * 查询辅项信息
     * @param dto
     * @return
     */
    List<EcsReimAsstDetail> queryReimAsstDetail(EcsReimItemDetail dto);

    /**
     * 查询报销第一审核节点
     * @param dto
     * @return
     */
    AuditDetail getFirstAudit(EcsReimDetailDto dto);

    /**
     * 查询差旅、培训类型发票
     * @return
     */
    List<String> queryBusInvoIds(EcsReimDetailDto dto);

    /** 查询报销明细发票id **/
    List<String> queryItemDetailsInvoIds(EcsReimDetailDto dto);

    List<EcsReimApprManage> queryTravelPsnInfo(EcsReimDetailDto dto);

    /** 查询费用报销home消息通知 **/
    List<HomeMsgNoteVo> msgNote(EcsReimDetailDto dto);

    List<EcsReimDetailVo> queryReimDetailOfMonth(EcsReimDetailDto dto);

    List<EcsReimItemDetail> queryReimItemDetails(EcsReimDetailDto dto);

    List<EcsReimPsnDetail> queryZCFDeptAmt(EcsReimDetailDto dto);
}
