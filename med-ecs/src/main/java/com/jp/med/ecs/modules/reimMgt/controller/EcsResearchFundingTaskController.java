package com.jp.med.ecs.modules.reimMgt.controller;

import com.jp.med.common.dto.ecs.EcsResearchFundingTaskDto;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsResearchFundingTaskReadService;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsResearchFundingTaskWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 科研经费报销任务表
 * <AUTHOR>
 * @email -
 * @date 2024-11-26 05:30:48
 */
@Api(value = "科研经费报销任务表", tags = "科研经费报销任务表")
@RestController
@RequestMapping("ecsResearchFundingTask")
public class EcsResearchFundingTaskController {

    @Autowired
    private EcsResearchFundingTaskReadService ecsResearchFundingTaskReadService;

    @Autowired
    private EcsResearchFundingTaskWriteService ecsResearchFundingTaskWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询科研经费报销任务表")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody EcsResearchFundingTaskDto dto){
        return CommonResult.paging(ecsResearchFundingTaskReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询科研经费报销任务表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsResearchFundingTaskDto dto){
        return CommonResult.success(ecsResearchFundingTaskReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增科研经费报销任务表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsResearchFundingTaskDto dto){
        ecsResearchFundingTaskWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 保存
     */
    @ApiOperation("新增科研经费报销任务表")
    @PostMapping("/saveFundingTask")
    public CommonResult<?> saveFundingTask(@RequestBody EcsResearchFundingTaskDto dto){
        return CommonResult.success(ecsResearchFundingTaskWriteService.saveFundingTask(dto));
    }

    /**
     * 修改
     */
    @ApiOperation("修改科研经费报销任务表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsResearchFundingTaskDto dto){
        ecsResearchFundingTaskWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除科研经费报销任务表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsResearchFundingTaskDto dto){
        ecsResearchFundingTaskWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 查询合同任务明细
     * @param dto
     * @return
     */
    @ApiOperation("查询合同任务明细")
    @PostMapping("/queryResearchTaskDetail")
    public CommonResult<?> queryResearchTaskDetail(@RequestBody EcsResearchFundingTaskDto dto) {
        return CommonResult.success(ecsResearchFundingTaskReadService.queryResearchTaskDetail(dto));
    }

    /**
     * 查询科研项目预算
     * @param dto
     * @return
     */
    @ApiOperation("查询合同任务明细")
    @PostMapping("/queryResearchFundingBudget")
    public CommonResult<?> queryResearchFundingBudget(@RequestBody EcsResearchFundingTaskDto dto) {
        return CommonResult.success(ecsResearchFundingTaskReadService.queryResearchFundingBudget(dto));
    }
}
