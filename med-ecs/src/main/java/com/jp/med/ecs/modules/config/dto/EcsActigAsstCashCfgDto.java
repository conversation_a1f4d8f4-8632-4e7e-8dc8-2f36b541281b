package com.jp.med.ecs.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 现金流量配置
 * <AUTHOR>
 * @email -
 * @date 2023-12-08 14:03:00
 */
@Data
@TableName("ecs_actig_asst_cash_cfg" )
public class EcsActigAsstCashCfgDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 科目代码 */
    @TableField("sub_code")
    private String subCode;

    /** 科目名称 */
    @TableField("sub_name")
    private String subName;

    /** 流向性质 */
    @TableField("flow_dire")
    private String flowDire;

    /** 拼音助记码 */
    @TableField("pinyin")
    private String pinyin;

    /** 备注 */
    @TableField("remarks")
    private String remarks;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 修改时间 */
    @TableField("modi_time")
    private String modiTime;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 启用标志 */
    @TableField("active_flag")
    private String activeFlag;

    /** 年度 **/
    @TableField("year")
    private String year;

    /** 父编码 **/
    @TableField("parent_sub_code")
    private String parentSubCode;

    /** 查询字符串 */
    @TableField(exist = false)
    private String qs;

}
