package com.jp.med.ecs.modules.config.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.EcsReimFixedAsstDto;
import com.jp.med.common.vo.EcsReimFixedAsstVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 18:14:02
 */
@Mapper
public interface EcsReimFixedAsstReadMapper extends BaseMapper<EcsReimFixedAsstDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimFixedAsstVo> queryList(EcsReimFixedAsstDto dto);

    /**
     * 查询固定上级
     * @param dto
     * @return
     */
    List<EcsReimFixedAsstVo> queryFixedTree(EcsReimFixedAsstDto dto);
}
