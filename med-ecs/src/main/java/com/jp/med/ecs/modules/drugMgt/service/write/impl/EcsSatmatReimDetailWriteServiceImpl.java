package com.jp.med.ecs.modules.drugMgt.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.app.AppMsgSup;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.payload.AuditPayload;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.AuditFeignService;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.FeignExecuteUtil;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.common.util.ULIDUtil;
import com.jp.med.ecs.modules.drugMgt.dto.EcsSatmatDto;
import com.jp.med.ecs.modules.drugMgt.dto.EcsSatmatReimDetailDto;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsSatmatReadMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.write.EcsSatmatReimDetailWriteMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.write.EcsSatmatWriteMapper;
import com.jp.med.ecs.modules.drugMgt.service.write.EcsSatmatReimDetailWriteService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 卫材报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-08-02 09:28:49
 */
@Service
@Transactional(readOnly = false)
public class EcsSatmatReimDetailWriteServiceImpl extends ServiceImpl<EcsSatmatReimDetailWriteMapper, EcsSatmatReimDetailDto> implements EcsSatmatReimDetailWriteService {

    @Autowired
    private EcsSatmatReimDetailWriteMapper ecsSatmatReimDetailWriteMapper;

    @Autowired
    private EcsSatmatReadMapper ecsSatmatReadMapper;

    @Autowired
    private EcsSatmatWriteMapper ecsSatmatWriteMapper;

    @Autowired
    private AuditFeignService auditFeignService;

    @Override
    public void batchSave(EcsSatmatReimDetailDto dto) {
        if (CollectionUtil.isEmpty(dto.getAuditDetails())) {
            throw new AppException("无报销详情记录");
        }
        if (StringUtils.isEmpty(dto.getSysUser().getHrmUser().getEmpCode())) {
            throw new AppException("非医院用户无法报销");
        }
        if (CollectionUtil.isNotEmpty(dto.getBatchData())) {
            HrmUser hrmUser = dto.getSysUser().getHrmUser();
            List<String> batchNumList = new ArrayList<>();
            StringBuilder desc = new StringBuilder();
            desc.append("共[");
            desc.append(dto.getBatchData().size());
            desc.append("]家供货单位，总计支付药品金额[");
            BigDecimal summary = new BigDecimal(0);
            // 总审核批次号
            String parentBatchNum = AuditConst.ECS_DRUG_FKTZ + ULIDUtil.generate();
            for (EcsSatmatReimDetailDto ecsSatmatReimDetaiDto : dto.getBatchData()) {
                if (CollectionUtil.isNotEmpty(ecsSatmatReimDetaiDto.getStoinNumList())) {
                    String batchNum = AuditConst.ECS_DRUG_FKTZ + ULIDUtil.generate();
                    ecsSatmatReimDetaiDto.setAuditBchno(batchNum);
                    ecsSatmatReimDetaiDto.setCrter(hrmUser.getEmpCode());
                    ecsSatmatReimDetaiDto.setCreateTime(DateUtil.getCurrentTime(null));
                    ecsSatmatReimDetaiDto.setHospitalId(dto.getHospitalId());
                    ecsSatmatReimDetaiDto.setAuditDetails(dto.getAuditDetails());
                    ecsSatmatReimDetaiDto.setSysUser(dto.getSysUser());
                    ecsSatmatReimDetaiDto.setIssue(dto.getIssue());
                    ecsSatmatReimDetaiDto.setParentAuditBchno(parentBatchNum);
                    ecsSatmatReimDetailWriteMapper.insert(ecsSatmatReimDetaiDto);
                    // 更新入库单表记录
                    ecsSatmatWriteMapper.updateReimState(ecsSatmatReimDetaiDto);
                    // 更新入库单发票
                    ecsSatmatReimDetaiDto.getStoinNumList().forEach(s -> {
                        List<MultipartFile> multipartFiles = ecsSatmatReimDetaiDto.getStoinNumMap().get(s);
                        if (multipartFiles != null && !multipartFiles.isEmpty()) {
                            EcsSatmatDto ecsSatmatDto = new EcsSatmatDto();
                            List<List<String>> ossPaths = OSSUtil.getOSSPaths(multipartFiles, OSSConst.BUCKET_ECS, "reim/satmat/");
                            ecsSatmatDto.setAtt(String.join(",", ossPaths.get(0)));
                            ecsSatmatDto.setAttName(String.join(",", ossPaths.get(1)));
                            // 更新入库单对应的发票
                            UpdateWrapper<EcsSatmatDto> ecsSatmatnDtoUpdateWrapper = Wrappers.update();
                            ecsSatmatnDtoUpdateWrapper.eq("stoin_num", s);
                            ecsSatmatReadMapper.update(ecsSatmatDto, ecsSatmatnDtoUpdateWrapper);
                        }
                    });
                    // 推送消息
//                    push(ecsDrugReimDetaiDto, hrmUser, batchNum, hrmUser.getEmpCode(), "药品付款申请");
                    // 只加入表中记录审核流程，不推送消息
                    auditFeignService.notPushSaveAuditDetail(new AuditDetail(batchNum, ecsSatmatReimDetaiDto.getAuditDetails(), hrmUser.getEmpCode(), OSSConst.BUCKET_ECS));
                    batchNumList.add(batchNum);
                    summary = summary.add(ecsSatmatReimDetaiDto.getSum());
                }
            }
            desc.append(summary);
            desc.append("]");
            dto.setPushContent(desc.toString());
            dto.setSum(summary);
            dto.setSplerNum(dto.getBatchData().size());

            // 更新当前批次号的上级批次号
            auditFeignService.updateParentBecho(new AuditDetail(parentBatchNum, batchNumList, OSSConst.BUCKET_ECS));
            push(dto, hrmUser, parentBatchNum, hrmUser.getEmpCode(), "卫材付款申请");
        }
    }

    public void push(EcsSatmatReimDetailDto dto, HrmUser hrmUser, String batchNum, String appyer, String title){
        // 审核流程
        if (CollectionUtil.isNotEmpty(dto.getAuditDetails())) {
            AppMsgSup appMsgSup = new AppMsgSup();
            appMsgSup.setTitle(title);
            String appyerName = StringUtils.isNotEmpty(hrmUser.getEmpName()) ? hrmUser.getEmpName() :dto.getSysUser().getNickname();
            appMsgSup.setAppyer(appyer);
            appMsgSup.setAppyerName(appyerName);
            appMsgSup.setContent(dto.getPushContent());
            AuditPayload auditPayload = new AuditPayload();
            auditPayload.setAppyer(appyerName);
            auditPayload.setAppyerDept(dto.getSysUser().getHrmUser().getHrmOrgName());
            auditPayload.setAuditBchno(batchNum);
            auditPayload.setTableTitle("待供货商详情");
            auditPayload.setTableHeader("供货单位,支付金额,报销说明");
            auditPayload.setTableContent("spler,sum,payIstr");
            auditPayload.setDetailUrl("/ecs/ecsSatmatReimDetail/appAuditDetail");
            auditPayload.setActionUrl("/ecs/ecsAudit/batchUpdateAuditDetail");
            auditPayload.setTableSelection(MedConst.TYPE_1);
            auditPayload.setBatchAudit(MedConst.TYPE_1);
            Map<String,Object> map = new HashMap<>();
            auditPayload.setAdditionItems(JSON.toJSONString(List.of(new HashMap<>() {
                {
                    put("title", "合计金额");
                    put("value", dto.getSum().setScale(2, RoundingMode.CEILING));
                }
            }, new HashMap<>(){
                {
                    put("title", "报销期号");
                    put("value", dto.getIssue());
                }
            })));
            auditPayload.setDisplayItem(map);
            FeignExecuteUtil.execute(auditFeignService.saveAuditDetail(new AuditDetail(batchNum, dto.getAuditDetails(), appMsgSup, auditPayload, OSSConst.BUCKET_ECS)));
        }
    }
}
