package com.jp.med.ecs.modules.drugMgt.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.dto.ecs.drug.EcsDrugPayDetailDto;
import com.jp.med.ecs.modules.drugMgt.mapper.write.EcsDrugPayDetailWriteMapper;
import com.jp.med.ecs.modules.drugMgt.service.write.EcsDrugPayDetailWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 药品报销付款详情
 * <AUTHOR>
 * @email -
 * @date 2024-11-25 12:03:17
 */
@Service
@Transactional(readOnly = false)
public class EcsDrugPayDetailWriteServiceImpl extends ServiceImpl<EcsDrugPayDetailWriteMapper, EcsDrugPayDetailDto> implements EcsDrugPayDetailWriteService {
}
