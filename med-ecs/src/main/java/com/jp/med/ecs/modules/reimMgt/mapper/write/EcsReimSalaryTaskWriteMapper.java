package com.jp.med.ecs.modules.reimMgt.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.EcsReimSalaryTask;
import com.jp.med.common.dto.ecs.EcsReimSalaryTaskDetail;
import org.apache.ibatis.annotations.Mapper;

/**
 * 应发工资报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 14:45:03
 */
@Mapper
public interface EcsReimSalaryTaskWriteMapper extends BaseMapper<EcsReimSalaryTask> {

    void insertSalaryTaskDetail(EcsReimSalaryTaskDetail detail);
}
