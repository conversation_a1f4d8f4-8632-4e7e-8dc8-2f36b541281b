package com.jp.med.ecs.modules.reimMgt.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.dto.ecs.EcsReimContractTask;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimContractTaskReadMapper;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimContractTaskReadService;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimContractTaskDetailVo;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimContractTaskVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class EcsReimContractTaskReadServiceImpl extends ServiceImpl<EcsReimContractTaskReadMapper, EcsReimContractTask> implements EcsReimContractTaskReadService {

    @Autowired
    private EcsReimContractTaskReadMapper ecsReimContractTaskReadMapper;

    @Override
    public List<EcsReimContractTaskVo> queryList(EcsReimContractTask dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsReimContractTaskReadMapper.queryList(dto);
    }

    @Override
    public List<EcsReimContractTaskVo> queryPageList(EcsReimContractTask dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsReimContractTaskReadMapper.queryList(dto);
    }

    @Override
    public List<EcsReimContractTaskDetailVo> queryContractTaskDetail(EcsReimContractTask dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return ecsReimContractTaskReadMapper.queryContractTaskDetail(dto);
    }
}
