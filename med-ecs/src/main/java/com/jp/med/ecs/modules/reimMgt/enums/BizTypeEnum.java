package com.jp.med.ecs.modules.reimMgt.enums;


import io.seata.common.util.StringUtils;

/**
 * 费用报销类型
 */
public enum BizTypeEnum {

    TRAVAL("1","差旅","ECS"),
    TRAINING("2","培训","ECS");


    private String type;

    private String name;

    private String prefix;

    BizTypeEnum(String type,String name,String prefix){
        this.type = type;
        this.name = name;
        this.prefix = prefix;
    }

    public String getType(){
        return type;
    }

    public String getName(){
        return name;
    }

    public String getPrefix(){
        return prefix;
    }

    public static BizTypeEnum getByType(String type) {
        for (BizTypeEnum biz : BizTypeEnum.values()) {
            if (StringUtils.equals(biz.getType(),type)) {
                return biz;
            }
        }
        return null;
    }
}
