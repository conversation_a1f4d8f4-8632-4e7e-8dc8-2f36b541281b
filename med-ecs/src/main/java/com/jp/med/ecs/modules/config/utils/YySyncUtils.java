package com.jp.med.ecs.modules.config.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.HttpRequestUtil;

import java.util.List;
import java.util.Map;

public class YySyncUtils {

    public static <T> List<T> doSync(String url, Map<String,Object> params, Class<T> responseType) {

        String result = HttpRequestUtil.post(params, url);

        result = result.substring(1, result.length() - 1)
                .replaceAll("\\\\", "");
        JSONObject jsonObject = JSON.parseObject(result);
        Integer code = jsonObject.getInteger("code");
        String msg = jsonObject.getString("message");
        if (code != 200) {
            throw new AppException("获取数据失败: " + msg);
        }
        return JSON.parseArray(jsonObject.getString("data"), responseType);
    }
}
