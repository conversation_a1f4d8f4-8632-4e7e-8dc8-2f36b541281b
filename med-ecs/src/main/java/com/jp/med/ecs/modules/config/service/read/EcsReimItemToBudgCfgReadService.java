package com.jp.med.ecs.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.EcsReimItemToBudgCfgDto;
import com.jp.med.common.vo.ecs.EcsReimItemToBudgCfgVo;

import java.util.List;

/**
 * 报销项目对应预算项目
 * <AUTHOR>
 * @email -
 * @date 2023-12-12 09:43:44
 */
public interface EcsReimItemToBudgCfgReadService extends IService<EcsReimItemToBudgCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimItemToBudgCfgVo> queryList(EcsReimItemToBudgCfgDto dto);
}

