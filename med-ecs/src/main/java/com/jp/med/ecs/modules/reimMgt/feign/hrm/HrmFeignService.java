package com.jp.med.ecs.modules.reimMgt.feign.hrm;

import com.jp.med.common.entity.common.CommonFeignResult;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * <AUTHOR>
 */
@RefreshScope
@FeignClient(name = "HrmFeignService", url = "${custom.gateway.med-hrm-service-uri}")
public interface HrmFeignService {

    /**
     * 报销完成写入工资任务表
     *
     * @param salaryTaskId 工资任务Id
     * @param reimId 报销任务Id
     * @return
     */
    @PostMapping("/employeeSalary/updateTaskReimId")
    CommonFeignResult updateSalaryTaskReimId(@RequestParam("salaryTaskId")Integer salaryTaskId,@RequestParam("reimId")Integer reimId);

}

