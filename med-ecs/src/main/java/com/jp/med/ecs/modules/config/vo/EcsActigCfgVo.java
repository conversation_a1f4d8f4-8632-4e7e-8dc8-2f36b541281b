package com.jp.med.ecs.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import com.jp.med.common.interceptors.BaseTree;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 会计科目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 10:53:01
 */
@Data
public class EcsActigCfgVo implements BaseTree<String,EcsActigCfgVo> {

	/** id */
	private Integer id;

	/** 科目代码 */
	private String subCode;

	/** 科目名称 */
	private String subName;

	/** 科目类型 */
	private String subType;

	/** 备注 */
	private String remarks;

	/** 拼音助记码 */
	private String pinyin;

	/** 辅助信息 */
	private String asstInfo;

	/** 会计要素 */
	private String actigElem;

	/** 会计体系 */
	private String actigSys;

	/** 余额方向 */
	private String balcDirc;

	/** 单位类型 */
	private String empType;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 修改时间 */
	private String modiTime;

	/** 医疗机构id */
	private String hospitalId;

	/** 启用标志 */
	private String activeFlag;

	/** 上级科目编码 */
	private String parentCode;

	/** 单位类型数组 */
	private String[] empTypeArr;

	/** 辅助信息数组 */
	private String[] asstInfoArr;

	private List<EcsActigCfgVo> children;

	@Override
	public String getCode() {
		return this.subCode;
	}

	@Override
	public void setCode(String code) {
		this.subCode = code;
	}

	@Override
	public String getPid() {
		return this.parentCode;
	}

	@Override
	public void setPid(String pid) {
		this.parentCode = pid;
	}

	@Override
	public List<EcsActigCfgVo> getChildren() {
		return this.children;
	}

	@Override
	public void addChild(EcsActigCfgVo node) {
		if (Objects.isNull(this.children)){
			this.children = new ArrayList<>();
		}
		this.children.add(node);
	}
}
