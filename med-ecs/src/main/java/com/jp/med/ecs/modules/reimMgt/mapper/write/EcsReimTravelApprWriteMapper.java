package com.jp.med.ecs.modules.reimMgt.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimTravelApprDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 报销差旅审批
 * <AUTHOR>
 * @email -
 * @date 2023-12-13 13:48:41
 */
@Mapper
public interface EcsReimTravelApprWriteMapper extends BaseMapper<EcsReimTravelApprDto> {

    /**
     * 重置当前差旅审核节点状态未未审核
     * @param apprId
     */
    void resetTravelApprAuditNode(@Param("apprId") Integer apprId);

    /**
     * 重置当前差旅审核结果状态
     * @param auditBchno
     */
    void resetTravelApprAuditRes(@Param("auditBchno")String auditBchno);
}
