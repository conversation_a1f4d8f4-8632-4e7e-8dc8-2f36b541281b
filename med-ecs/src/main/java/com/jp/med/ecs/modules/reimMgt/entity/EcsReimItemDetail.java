package com.jp.med.ecs.modules.reimMgt.entity;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/7 11:02
 * @description: 报销项目
 */
@Data
public class EcsReimItemDetail {
    /** id **/
    private Integer id;

    /** 报销详情id */
    private Long reimDetailId;

    /** 报销科室 */
    private String deptCode;

    private String deptName;

    /** 项目 */
    private String item;

    /** 票据张数 */
    private Integer docNum;

    /** 天数/公里 */
    private BigDecimal daysOrKilor;

    /** 标准 */
    private BigDecimal std;

    /** 金额 */
    private BigDecimal amt;

    /** 住宿费(单男单女补贴费) **/
    private BigDecimal extraAmt;

    /** 附件 */
    private String att;

    /** 附件名称 */
    private String attName;

    /** 报销摘要 */
    private String reimAbst;

    /** 类型 */
    private String type;

    /** 类型名称 */
    private String typeName;

    /** 预算编制项编码 */
    private String budgetCode;

    /** 发票id */
    private String invoId;

    /** 科目名称 */
    private String subName;

    /** 员工编号 针对个人扣减 **/
    private String empCode;

    /** 报销项目 针对工资 **/
    private String reimName;

    /** 附件 */
    private List<MultipartFile> attFiles;

    /** 当前项目辅助项 */
    private List<EcsReimAsstDetail> reimAsstDetails;

    /** 报销类型 1：费用报销 2：药品报销 **/
    private String supType;


    /** 会计科目 */
    private String actig;

    /** 会计科目名称 */
    private String actigName;


    /** 往来单位 */
    private String relCo;

    /** 往来单位名称 */
    private String relCoName;
    /** 临时试验经费id */
    private Long researcherFundingApplyId;


}
