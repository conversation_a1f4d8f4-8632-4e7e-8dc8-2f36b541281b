package com.jp.med.ecs.modules.drugMgt.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.drug.EcsDrugReimDetaiDto;
import com.jp.med.common.vo.ecs.drug.EcsDrugReimDetaiVo;

import java.util.List;
import java.util.Map;

/**
 * 药品报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:45
 */
public interface EcsDrugReimDetaiWriteService extends IService<EcsDrugReimDetaiDto> {

    /**
     * 批量新增
     * @param dto
     */
    void batchSave(EcsDrugReimDetaiDto dto);

    void updEcsDrugPayRcpt(EcsDrugReimDetaiDto dto);

    Map<String,String> printRcptDoc(EcsDrugReimDetaiDto dto);

    List<EcsDrugReimDetaiVo> queryEcsDrugReims(EcsDrugReimDetaiDto dto);

    void updIssue(EcsDrugReimDetaiDto dto);

    void batchSaveNew(EcsDrugReimDetaiDto dto);

    void delDrugReimNoChoose(EcsDrugReimDetaiDto dto);
}

