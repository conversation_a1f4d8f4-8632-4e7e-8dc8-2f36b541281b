package com.jp.med.ecs.modules.drugMgt.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.common.dto.ecs.drug.EcsDrugPayDetailDto;
import com.jp.med.ecs.modules.drugMgt.service.read.EcsDrugPayDetailReadService;
import com.jp.med.ecs.modules.drugMgt.service.write.EcsDrugPayDetailWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 药品报销付款详情
 * <AUTHOR>
 * @email -
 * @date 2024-11-25 12:03:17
 */
@Api(value = "药品报销付款详情", tags = "药品报销付款详情")
@RestController
@RequestMapping("ecsDrugPayDetail")
public class EcsDrugPayDetailController {

    @Autowired
    private EcsDrugPayDetailReadService ecsDrugPayDetailReadService;

    @Autowired
    private EcsDrugPayDetailWriteService ecsDrugPayDetailWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询药品报销付款详情")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody EcsDrugPayDetailDto dto){
        return CommonResult.paging(ecsDrugPayDetailReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询药品报销付款详情")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsDrugPayDetailDto dto){
        return CommonResult.success(ecsDrugPayDetailReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增药品报销付款详情")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsDrugPayDetailDto dto){
        ecsDrugPayDetailWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改药品报销付款详情")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsDrugPayDetailDto dto){
        ecsDrugPayDetailWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除药品报销付款详情")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsDrugPayDetailDto dto){
        ecsDrugPayDetailWriteService.removeById(dto);
        return CommonResult.success();
    }

}
