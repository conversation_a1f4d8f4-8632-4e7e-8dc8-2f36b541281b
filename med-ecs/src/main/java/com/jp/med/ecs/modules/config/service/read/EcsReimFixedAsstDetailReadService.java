package com.jp.med.ecs.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.EcsReimFixedAsstDetailDto;
import com.jp.med.common.vo.EcsReimFixedAsstDetailVo;

import java.util.List;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-03-13 18:14:02
 */
public interface EcsReimFixedAsstDetailReadService extends IService<EcsReimFixedAsstDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimFixedAsstDetailVo> queryList(EcsReimFixedAsstDetailDto dto);
}

