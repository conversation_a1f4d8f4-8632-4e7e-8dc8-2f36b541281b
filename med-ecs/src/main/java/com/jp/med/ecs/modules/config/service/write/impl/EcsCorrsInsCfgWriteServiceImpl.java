package com.jp.med.ecs.modules.config.service.write.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ecs.modules.config.dto.EcsCorrsInsCfgDto;
import com.jp.med.ecs.modules.config.mapper.write.EcsCorrsInsCfgWriteMapper;
import com.jp.med.ecs.modules.config.service.write.EcsCorrsInsCfgWriteService;
import com.jp.med.ecs.modules.config.service.write.EcsEconFunSubCfgWriteService;
import com.jp.med.ecs.modules.config.utils.YySyncUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 往来单位配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-30 19:37:36
 */
@Service
@Transactional(readOnly = false)
public class EcsCorrsInsCfgWriteServiceImpl extends ServiceImpl<EcsCorrsInsCfgWriteMapper, EcsCorrsInsCfgDto> implements EcsCorrsInsCfgWriteService {

    @Autowired
    private EcsCorrsInsCfgWriteMapper ecsCorrsInsCfgWriteMapper;

    @Autowired
    private EcsEconFunSubCfgWriteService ecsEconFunSubCfgWriteService;

    @Value("${urls.mid.yy.corrs_ins_sync}")
    private String corrsInsSyncUrl;

    private static final int BATCH_SIZE = 500; //批处理大小

    @Override
    public void sync(EcsCorrsInsCfgDto dto) {
        int start = 1;
        int end = start + BATCH_SIZE;
        Map<String,Object> params = new HashMap<>();
        params.put("year",dto.getYear());

        /*params.put("start",start);
        params.put("end",end);
        List<EcsEconFunSubCfgDto> efcDtos = doSync(econFunSyncUrl, params, EcsEconFunSubCfgDto.class);
        //处理parentCode
        efcDtos.forEach(item -> {
            if (item.getSubCode().length()> 3) {
                item.setParentCode(item.getSubCode().substring(0,item.getSubCode().length()-2));
            }
        });
        ecsEconFunSubCfgWriteMapper.saveEcsEconSubCfg(efcDtos);*/

        //删除某年度往来单位
        LambdaQueryWrapper<EcsCorrsInsCfgDto> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EcsCorrsInsCfgDto::getYear,dto.getYear());
        ecsCorrsInsCfgWriteMapper.delete(wrapper);

        List<EcsCorrsInsCfgDto> efcDtos;
        do{
            params.put("start",start);
            params.put("end",end);

            efcDtos = YySyncUtils.doSync(corrsInsSyncUrl, params, EcsCorrsInsCfgDto.class);
            if (!efcDtos.isEmpty()) {
                String crter = dto.getSysUser().getHrmUser().getEmpCode();
                //处理parentCode
                efcDtos.forEach(item -> {
                    item.setCrter(crter);
                });

                //插入往来单位
                ecsCorrsInsCfgWriteMapper.saveCorrsInsCfg(efcDtos);

                start +=BATCH_SIZE;
                end +=BATCH_SIZE;
            }
        }while(!efcDtos.isEmpty());
    }
}
