package com.jp.med.ecs.modules.config.mapper.read;

import com.jp.med.ecs.modules.config.dto.EcsItemCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsItemCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 财务科目辅助项目项目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-30 19:37:36
 */
@Mapper
public interface EcsItemCfgReadMapper extends BaseMapper<EcsItemCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsItemCfgVo> queryList(EcsItemCfgDto dto);
}
