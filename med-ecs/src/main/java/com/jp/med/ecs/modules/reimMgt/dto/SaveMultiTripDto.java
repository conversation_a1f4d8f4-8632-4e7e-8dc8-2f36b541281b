package com.jp.med.ecs.modules.reimMgt.dto;

import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class SaveMultiTripDto extends CommonQueryDto {

    /**
     * 数据表单
     */
    private String dataForms;

    /** 文件 */
    private List<MultipartFile> attFiles;

    /** 页面图片文件 */
    private MultipartFile pageImageFile;

    /**
     * BPM 参数
     */
    private Map<String,String> bpmParams;

}
