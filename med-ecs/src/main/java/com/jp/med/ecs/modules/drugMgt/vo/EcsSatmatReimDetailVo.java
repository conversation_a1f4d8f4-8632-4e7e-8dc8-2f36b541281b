package com.jp.med.ecs.modules.drugMgt.vo;

import com.jp.med.common.entity.audit.AuditCommonRes;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 卫材报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-08-02 09:28:49
 */
@Data
public class EcsSatmatReimDetailVo extends AuditCommonRes {

	/** id */
	private Integer id;

	/** 供货单位 */
	private String spler;

	/** 合计金额小写(元) */
	private BigDecimal sum;

	/** 合计金额大写 */
	private String capSum;

	/** 付款说明 */
	private String payIstr;

	/** 审核批次号 */
	private String auditBchno;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 审核状态，1：审核完成且通过，2：审核失败，3：审核中（全部未开始或开始部分） */
	private String auditState;

	/** 创建者 */
	private String crterName;

	/** 医疗机构id */
	private String hospitalId;

	/** 期号(报销月份) */
	private String issue;

	/** 审核标识 */
	private String auditFlag;

	/** 上级审批号 */
	private String parentAuditBchno;

}
