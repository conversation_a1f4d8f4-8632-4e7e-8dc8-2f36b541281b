package com.jp.med.ecs.modules.reimMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.EcsResearchFundingTaskDto;
import com.jp.med.ecs.modules.reimMgt.vo.EcsResearchFundingTaskDetailVo;
import com.jp.med.ecs.modules.reimMgt.vo.EcsResearchFundingTaskVo;

import java.util.List;

/**
 * 科研经费报销任务表
 * <AUTHOR>
 * @email -
 * @date 2024-11-26 05:30:48
 */
public interface EcsResearchFundingTaskReadService extends IService<EcsResearchFundingTaskDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsResearchFundingTaskVo> queryList(EcsResearchFundingTaskDto dto);

    /**
     * 分页查询列表
     * @param dto
     * @return
    */
    List<EcsResearchFundingTaskVo> queryPageList(EcsResearchFundingTaskDto dto);

    List<EcsResearchFundingTaskDetailVo> queryResearchTaskDetail(EcsResearchFundingTaskDto dto);

    List<EcsResearchFundingTaskVo> queryResearchFundingBudget(EcsResearchFundingTaskDto dto);
}

