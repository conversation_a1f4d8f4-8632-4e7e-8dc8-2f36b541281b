package com.jp.med.ecs.modules.config.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.config.dto.EcsItemCfgDto;
import com.jp.med.ecs.modules.config.service.read.EcsItemCfgReadService;
import com.jp.med.ecs.modules.config.service.write.EcsItemCfgWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 财务科目辅助项目项目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-30 19:37:36
 */
@Api(value = "财务科目辅助项目项目配置", tags = "财务科目辅助项目项目配置")
@RestController
@RequestMapping("ecsItemCfg")
public class EcsItemCfgController {

    @Autowired
    private EcsItemCfgReadService ecsItemCfgReadService;

    @Autowired
    private EcsItemCfgWriteService ecsItemCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询财务科目辅助项目项目配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsItemCfgDto dto){
        return CommonResult.success(ecsItemCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增财务科目辅助项目项目配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsItemCfgDto dto){
        ecsItemCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改财务科目辅助项目项目配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsItemCfgDto dto){
        ecsItemCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除财务科目辅助项目项目配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsItemCfgDto dto){
        ecsItemCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 同步
     */
    @ApiOperation("查询经济和功能科目配置")
    @PostMapping("/sync")
    public CommonResult<?> sync(@RequestBody EcsItemCfgDto dto){
        ecsItemCfgWriteService.sync(dto);
        return CommonResult.success();
    }

}
