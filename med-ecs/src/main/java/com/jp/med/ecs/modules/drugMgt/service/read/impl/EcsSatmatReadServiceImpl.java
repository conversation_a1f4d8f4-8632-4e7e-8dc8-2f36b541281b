package com.jp.med.ecs.modules.drugMgt.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.ecs.modules.drugMgt.dto.EcsSatmatDto;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsSatmatReadMapper;
import com.jp.med.ecs.modules.drugMgt.service.read.EcsSatmatReadService;
import com.jp.med.ecs.modules.drugMgt.vo.EcsSatmatVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Transactional(readOnly = true)
@Service
public class EcsSatmatReadServiceImpl extends ServiceImpl<EcsSatmatReadMapper, EcsSatmatDto> implements EcsSatmatReadService {

    @Autowired
    private EcsSatmatReadMapper ecsSatmatReadMapper;

    @Override
    public List<EcsSatmatVo> queryList(EcsSatmatDto dto) {
        if (dto.getPaging() == null || dto.getPaging()) {
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        }
        return ecsSatmatReadMapper.queryList(dto);
    }

    @Override
    public List<EcsSatmatVo> queryPageList(EcsSatmatDto dto) {
        if (dto.getPaging() == null || dto.getPaging()) {
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        }
        return ecsSatmatReadMapper.queryList(dto);
    }


    @Override
    public List<Map<String, Integer>> monthNum(EcsSatmatDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        if (StringUtils.isNotEmpty(dto.getIssue()) && dto.getIssue().length() > 4) {
            dto.setYear(dto.getIssue().substring(0,4));
            dto.setIssue(null);
        }
        return ecsSatmatReadMapper.monthNum(dto);
    }
}
