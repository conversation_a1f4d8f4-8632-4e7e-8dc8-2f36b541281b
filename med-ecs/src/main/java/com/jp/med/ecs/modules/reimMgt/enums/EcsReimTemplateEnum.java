package com.jp.med.ecs.modules.reimMgt.enums;

import io.seata.common.util.StringUtils;

public enum EcsReimTemplateEnum {

    ECS_REIM_TRAVEL_TEMPLATE("1","template/差旅报销单文档模板.docx","差旅费审批表"),
    ECS_REIM_TRAINING_TEMPLATE("2","template/差旅报销单文档模板.docx","培训费审批表"),
    ECS_REIM_TEMPLATE("3","template/其他费用报销单文档模板.docx","其他费用审批表"),

    ECS_REIM_SHARE("4","template/费用报销分摊费用文档模板.docx","分摊费用审批表"),

    ECS_REIM_SALARY("5","template/工资报销单文档模板.docx","工资费用审批表"),

    ECS_REIM_CONTRACT("6","template/其他费用报销单文档模板.docx","合同费用审批表"),

    // 暂时没有折旧类的报销
    ECS_REIM_ZHEJIU("7","template/费用报销折旧费用文档模板.docx","折旧费用审批表"),

    ECS_REIM_PURC("8","template/采购报销单文档模板.docx","零星采购费用审批表"),

    ECS_REIM_RESEARCH("9","template/其他费用报销单文档模板.docx","科研费用报销审批表"),

    ECS_REIM_WZ_PURC("10","template/采购报销单文档模板.docx","物资采购费用审批表"),

    ECS_REIM_LABOR_TEMPLATE("11","template/其他费用报销单文档模板.docx","其他费用报销(无发票)审批表"),

    ECS_INTERCHANGE_PAYMENT_TEMPLATE("12","template/往来支付报销单文档模板.docx","往来支付审批表"),
    ECS_LOAN_REIM_TEMPLATE("13","template/借款报销单文档模板.docx","其他费用报销(无发票)审批表"),
    ECS_REIM_HRM_RESEARCHER_FUNDING("14","template/其他费用报销单文档模板.docx","其他费用审批表");


    private final String type;

    private final String templatePath;

    private final String fileBaseName;

    EcsReimTemplateEnum(String type, String templatePath,String fileBaseName) {
        this.type = type;
        this.templatePath = templatePath;
        this.fileBaseName = fileBaseName;
    }

    public String getType() {
        return type;
    }

    public String getTemplatePath() {
        return templatePath;
    }

    public String getFileBaseName() {
        return fileBaseName;
    }

    public static EcsReimTemplateEnum getByType(String type){
        for (EcsReimTemplateEnum status : EcsReimTemplateEnum.values()) {
            if (StringUtils.equals(status.getType(),type)) {
                return status;
            }
        }
        return null;
    }
}
