package com.jp.med.ecs.modules.config.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 差旅住宿费标准
 * <AUTHOR>
 * @email -
 * @date 2024-05-23 11:12:00
 */
@Data
public class EcsTravelAccomStandardCfgVo {

	/** id */
	private Integer id;

	/** 出差地区 */
	private Integer evectionAddr;

	/** 住宿费标准价格 */
	private BigDecimal standardPrice;

	/** 旺季期间 */
	private String peakSeason;

	/** 旺季期间价格 */
	private BigDecimal peakSeasonPrice;

}
