package com.jp.med.ecs.modules.drugMgt.service.write.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.app.AppMsgSup;
import com.jp.med.common.dto.bpm.BpmProcessInstanceCreateReqDTO;
import com.jp.med.common.dto.ecs.EcsReimFileRecordDto;
import com.jp.med.common.dto.ecs.drug.EcsDrugPayDetailDto;
import com.jp.med.common.dto.ecs.drug.EcsDrugReimDetaiDto;
import com.jp.med.common.dto.ecs.drug.EcsStoinDto;
import com.jp.med.common.dto.user.UserSignDto;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.payload.AuditPayload;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.AuditFeignService;
import com.jp.med.common.feign.BpmProcessInstanceFeignApi;
import com.jp.med.common.feign.UserFeignService;
import com.jp.med.common.util.*;
import com.jp.med.common.vo.ecs.drug.EcsDrugReimDetaiVo;
import com.jp.med.common.vo.ecs.drug.EcsStoinVo;
import com.jp.med.ecs.modules.config.service.write.EcsDrugAccountPeriodWriteService;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsDrugPayDetailReadMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsDrugReimDetaiReadMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsStoinReadMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.write.EcsDrugPayDetailWriteMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.write.EcsDrugReimDetaiWriteMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.write.EcsStoinWriteMapper;
import com.jp.med.ecs.modules.drugMgt.service.write.EcsDrugReimDetaiWriteService;
import com.jp.med.ecs.modules.reimMgt.constant.EcsConst;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsInvoRcdWriteMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimFileRecordWriteMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.imaging.Imaging;
import org.apache.commons.imaging.common.ImageMetadata;
import org.apache.commons.imaging.formats.jpeg.JpegImageMetadata;
import org.apache.commons.imaging.formats.tiff.TiffField;
import org.apache.commons.imaging.formats.tiff.TiffImageMetadata;
import org.apache.commons.imaging.formats.tiff.constants.TiffTagConstants;
import org.apache.commons.lang.StringUtils;
import org.apache.pdfbox.multipdf.LayerUtility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.form.PDFormXObject;
import org.apache.pdfbox.pdmodel.graphics.image.LosslessFactory;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;
import org.apache.pdfbox.util.Matrix;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 药品报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:45
 */
@Service
@Transactional(readOnly = false)
@Slf4j
public class EcsDrugReimDetaiWriteServiceImpl extends ServiceImpl<EcsDrugReimDetaiWriteMapper, EcsDrugReimDetaiDto> implements EcsDrugReimDetaiWriteService {

    @Autowired
    private EcsDrugReimDetaiWriteMapper ecsDrugReimDetaiWriteService;

    @Autowired
    private EcsDrugReimDetaiReadMapper ecsDrugReimDetaiReadMapper;

    @Autowired
    private EcsStoinReadMapper ecsStoinReadMapper;

    @Autowired
    private EcsStoinWriteMapper ecsStoinWriteMapper;

    @Autowired
    private AuditFeignService auditFeignService;

    @Autowired
    private EcsDrugPayDetailWriteMapper ecsDrugPayDetailWriteMapper;

    @Autowired
    private EcsDrugPayDetailReadMapper ecsDrugPayDetailReadMapper;

    @Autowired
    private EcsInvoRcdWriteMapper ecsInvoRcdWriteMapper;

    @Autowired
    private EcsDrugAccountPeriodWriteService ecsDrugAccountPeriodWriteService;

    @Autowired
    private EcsDrugReimDetaiWriteMapper ecsDrugReimDetaiWriteMapper;

    @Autowired
    private UserFeignService userFeignService;

    @Autowired
    private BpmProcessInstanceFeignApi bpmProcessInstanceFeignApi;

    @Override
    public void batchSave(EcsDrugReimDetaiDto dto) {
        if (CollectionUtil.isEmpty(dto.getAuditDetails())) {
            throw new AppException("无报销详情记录");
        }
        if (StringUtils.isEmpty(dto.getSysUser().getHrmUser().getEmpCode())) {
            throw new AppException("非医院用户无法报销");
        }
        if (CollectionUtil.isNotEmpty(dto.getBatchData())) {
            /*//如果当前为扎帐
            if (StringUtils.equals(dto.getIsCloseAccount(),MedConst.TYPE_1)) {
                EcsDrugAccountPeriodDto param = new EcsDrugAccountPeriodDto();
                param.setSysUser(dto.getSysUser());
                ecsDrugAccountPeriodWriteService.closeAccount(param);
            }*/

            HrmUser hrmUser = dto.getSysUser().getHrmUser();
            List<String> batchNumList = new ArrayList<>();
            StringBuilder desc = new StringBuilder();
            desc.append("共[");
            desc.append(dto.getBatchData().size());
            desc.append("]家供货单位，总计支付药品金额[");
            BigDecimal summary = new BigDecimal(0);
            // 总审核批次号
            String parentBatchNum = AuditConst.ECS_DRUG_FKTZ + ULIDUtil.generate();
            for (EcsDrugReimDetaiDto ecsDrugReimDetaiDto : dto.getBatchData()) {
                if (CollectionUtil.isNotEmpty(ecsDrugReimDetaiDto.getStoinNumList())) {
                    String batchNum = AuditConst.ECS_DRUG_FKTZ + ULIDUtil.generate();
                    ecsDrugReimDetaiDto.setAuditBchno(batchNum);
                    ecsDrugReimDetaiDto.setCrter(hrmUser.getEmpCode());
                    ecsDrugReimDetaiDto.setCraeteTime(DateUtil.getCurrentTime(null));
                    ecsDrugReimDetaiDto.setHospitalId(dto.getHospitalId());
                    ecsDrugReimDetaiDto.setAuditDetails(dto.getAuditDetails());
                    ecsDrugReimDetaiDto.setSysUser(dto.getSysUser());
                    ecsDrugReimDetaiDto.setIssue(dto.getIssue());
                    ecsDrugReimDetaiDto.setParentAuditBchno(parentBatchNum);
                    ecsDrugReimDetaiDto.setStatus(EcsConst.DRUG_REIM_STATUS_AUDITING);
                    ecsDrugReimDetaiWriteService.insert(ecsDrugReimDetaiDto);

                    // 更新入库单发票
                    List<Long> invoIds = new ArrayList<>();
                    List<Integer> stoinIds = new ArrayList<>();
                    ecsDrugReimDetaiDto.getStoinDtos().forEach(s -> {
                        stoinIds.add(s.getId());
                        if (StringUtils.isNotEmpty(s.getInvoId())) {
                            invoIds.addAll(Arrays.stream(s.getInvoId()
                                            .split(","))
                                    .map(Long:: valueOf)
                                    .collect(Collectors.toList()));
                        }
                        LambdaUpdateWrapper<EcsStoinDto> updateWrapper = Wrappers.lambdaUpdate();
                        updateWrapper.set(EcsStoinDto::getAtt,s.getAtt())
                                .set(EcsStoinDto::getAttName,s.getAttName())
                                .set(EcsStoinDto::getInvoId,s.getInvoId())
                                .eq(EcsStoinDto::getId,s.getId());                  //使用id更新
                        ecsStoinReadMapper.update(null,updateWrapper);
                    });
                    // 更新入库单表记录
                    //设置为选择的入库单id
                    EcsDrugReimDetaiDto param = new EcsDrugReimDetaiDto();
                    param.setId(ecsDrugReimDetaiDto.getId());
                    param.setStoinIds(stoinIds);
                    ecsStoinReadMapper.updateReimState(param);
                    //更新发票状态为审核中
                    if (CollectionUtil.isNotEmpty(invoIds)) {
                        // 4：审核中
                        ecsInvoRcdWriteMapper.updateStateByIds(invoIds,MedConst.TYPE_4,null);
                    }
                    /*ecsDrugReimDetaiDto.getStoinNumList().forEach(s -> {
                        List<MultipartFile> multipartFiles = ecsDrugReimDetaiDto.getStoinNumMap().get(s);
                        if (multipartFiles != null && !multipartFiles.isEmpty()) {
                            EcsStoinDto ecsStoinDto = new EcsStoinDto();
                            List<List<String>> ossPaths = OSSUtil.getOSSPaths(multipartFiles, OSSConst.BUCKET_ECS, "reim/drug/");
                            ecsStoinDto.setAtt(String.join(",", ossPaths.get(0)));
                            ecsStoinDto.setAttName(String.join(",", ossPaths.get(1)));
                            // 更新入库单对应的发票
                            UpdateWrapper<EcsStoinDto> ecsStoinDtoUpdateWrapper = Wrappers.update();
                            ecsStoinDtoUpdateWrapper.eq("stoin_num", s);
                            ecsStoinReadMapper.update(ecsStoinDto, ecsStoinDtoUpdateWrapper);
                        }
                    });*/
                    // 推送消息
//                    push(ecsDrugReimDetaiDto, hrmUser, batchNum, hrmUser.getEmpCode(), "药品付款申请");
                    // 只加入表中记录审核流程，不推送消息
                    auditFeignService.notPushSaveAuditDetail(new AuditDetail(batchNum, ecsDrugReimDetaiDto.getAuditDetails(), hrmUser.getEmpCode(), OSSConst.BUCKET_ECS));
                    batchNumList.add(batchNum);
                    summary = summary.add(ecsDrugReimDetaiDto.getSum());
                }
            }
            desc.append(summary);
            desc.append("]");
            dto.setPushContent(desc.toString());
            dto.setSum(summary);
            dto.setSplerNum(dto.getBatchData().size());

            // 更新当前批次号的上级批次号
            auditFeignService.updateParentBecho(new AuditDetail(parentBatchNum, batchNumList, OSSConst.BUCKET_ECS));
            push(dto, hrmUser, parentBatchNum, hrmUser.getEmpCode(), "药品付款申请");
        }
    }

//    public void push(EcsDrugReimDetaiDto dto, HrmUser hrmUser, String batchNum, String appyer, String title){
//        // 审核流程
//        if (CollectionUtil.isNotEmpty(dto.getAuditDetails())) {
//            AppMsgSup appMsgSup = new AppMsgSup();
//            appMsgSup.setTitle(title);
//            String appyerName = StringUtils.isNotEmpty(hrmUser.getEmpName()) ? hrmUser.getEmpName() :dto.getSysUser().getNickname();
//            appMsgSup.setAppyer(appyer);
//            appMsgSup.setAppyerName(appyerName);
//            appMsgSup.setContent("供货单位["+dto.getSpler()+"],支付金额["+dto.getSum()+"]");
//            AuditPayload auditPayload = new AuditPayload();
//            auditPayload.setAppyer(appyerName);
//            auditPayload.setAppyerDept(dto.getSysUser().getHrmUser().getHrmOrgName());
//            auditPayload.setAuditBchno(batchNum);
//            auditPayload.setTableTitle("入库详情");
//            auditPayload.setTableHeader("入库单号,入库日期,数量合计,进价金额");
//            auditPayload.setTableContent("stoinNum,stoinDate,totlcnt,purcpricAmt");
//            auditPayload.setDetailUrl("/ecs/ecsDrugReimDetai/appAuditDetail");
//            Map<String,Object> map = new HashMap<>();
//            map.put("sum", "合计金额小写");
//            map.put("capSum", "合计金额大写");
//            map.put("spler", "供货单位");
//            map.put("payIstr", "付款说明");
//            auditPayload.setDisplayItem(map);
//            ecsAuditWriteService.saveAuditDetail(batchNum, dto.getAuditDetails(),appMsgSup,auditPayload);
//        }
//    }
    public void push(EcsDrugReimDetaiDto dto, HrmUser hrmUser, String batchNum, String appyer, String title){
        // 审核流程
        if (CollectionUtil.isNotEmpty(dto.getAuditDetails())) {
            AppMsgSup appMsgSup = new AppMsgSup();
            appMsgSup.setTitle(title);
            String appyerName = StringUtils.isNotEmpty(hrmUser.getEmpName()) ? hrmUser.getEmpName() :dto.getSysUser().getNickname();
            appMsgSup.setAppyer(appyer);
            appMsgSup.setAppyerName(appyerName);
            appMsgSup.setContent(dto.getPushContent());
            AuditPayload auditPayload = new AuditPayload();
            auditPayload.setAppyer(appyerName);
            auditPayload.setAppyerDept(dto.getSysUser().getHrmUser().getHrmOrgName());
            auditPayload.setAuditBchno(batchNum);
            auditPayload.setTableTitle("待供货商详情");
            auditPayload.setTableHeader("供货单位,支付金额,报销说明");
            auditPayload.setTableContent("spler,sum,payIstr");
            auditPayload.setDetailUrl("/ecs/ecsDrugReimDetai/appAuditDetail");
            auditPayload.setActionUrl("/ecs/ecsAudit/batchUpdateAuditDetail");
            auditPayload.setTableSelection(MedConst.TYPE_1);
            auditPayload.setBatchAudit(MedConst.TYPE_1);
            Map<String,Object> map = new HashMap<>();
            auditPayload.setAdditionItems(JSON.toJSONString(List.of(new HashMap<>() {
                {
                    put("title", "合计金额");
                    put("value", dto.getSum().setScale(2, RoundingMode.CEILING));
                }
            }, new HashMap<>(){
                {
                    put("title", "报销期号");
                    put("value", dto.getIssue());
                }
            })));
            auditPayload.setDisplayItem(map);
            FeignExecuteUtil.execute(auditFeignService.saveAuditDetail(new AuditDetail(batchNum, dto.getAuditDetails(), appMsgSup, auditPayload, OSSConst.BUCKET_ECS)));
        }
    }

    /**
     * 药品报销审批(新审批)
     * @param dto
     */
    @Override
    public void batchSaveNew(EcsDrugReimDetaiDto dto) {
        if (StringUtils.isEmpty(dto.getSysUser().getHrmUser().getEmpCode())) {
            throw new AppException("非医院用户无法报销");
        }

        if (CollectionUtil.isNotEmpty(dto.getBatchData())) {
            HrmUser hrmUser = dto.getSysUser().getHrmUser();
            List<String> batchNumList = new ArrayList<>();
            StringBuilder desc = new StringBuilder();
            desc.append("共[");
            desc.append(dto.getBatchData().size());
            desc.append("]家供货单位，总计支付药品金额[");
            BigDecimal summary = new BigDecimal(0);
            //保存所有供应商药品报销记录
            for (EcsDrugReimDetaiDto ecsDrugReimDetaiDto : dto.getBatchData()) {
                if (CollectionUtil.isNotEmpty(ecsDrugReimDetaiDto.getStoinNumList())) {
                    ecsDrugReimDetaiDto.setCrter(hrmUser.getEmpCode());
                    ecsDrugReimDetaiDto.setCraeteTime(DateUtil.getCurrentTime(null));
                    ecsDrugReimDetaiDto.setHospitalId(dto.getHospitalId());
                    ecsDrugReimDetaiDto.setAuditDetails(dto.getAuditDetails());
                    ecsDrugReimDetaiDto.setSysUser(dto.getSysUser());
                    ecsDrugReimDetaiDto.setIssue(dto.getIssue());
                    ecsDrugReimDetaiDto.setStatus(EcsConst.DRUG_REIM_STATUS_AUDITING);
                    ecsDrugReimDetaiWriteService.insert(ecsDrugReimDetaiDto);

                    // 更新入库单发票
                    List<Long> invoIds = new ArrayList<>();
                    List<Integer> stoinIds = new ArrayList<>();
                    ecsDrugReimDetaiDto.getStoinDtos().forEach(s -> {
                        stoinIds.add(s.getId());
                        if (StringUtils.isNotEmpty(s.getInvoId())) {
                            invoIds.addAll(Arrays.stream(s.getInvoId()
                                            .split(","))
                                    .map(Long:: valueOf)
                                    .collect(Collectors.toList()));
                        }
                        LambdaUpdateWrapper<EcsStoinDto> updateWrapper = Wrappers.lambdaUpdate();
                        updateWrapper.set(EcsStoinDto::getAtt,s.getAtt())
                                .set(EcsStoinDto::getAttName,s.getAttName())
                                .set(EcsStoinDto::getInvoId,s.getInvoId())
                                .eq(EcsStoinDto::getId,s.getId());                  //使用id更新
                        ecsStoinReadMapper.update(null,updateWrapper);
                    });
                    // 更新入库单表记录
                    //设置为选择的入库单id
                    EcsDrugReimDetaiDto param = new EcsDrugReimDetaiDto();
                    param.setId(ecsDrugReimDetaiDto.getId());
                    param.setStoinIds(stoinIds);
                    ecsStoinReadMapper.updateReimState(param);
                    //更新发票状态为审核中
                    if (CollectionUtil.isNotEmpty(invoIds)) {
                        // 4：审核中
                        ecsInvoRcdWriteMapper.updateStateByIds(invoIds,MedConst.TYPE_4,null);
                    }
                    // 只加入表中记录审核流程，不推送消息
//                    auditFeignService.notPushSaveAuditDetail(new AuditDetail(batchNum, ecsDrugReimDetaiDto.getAuditDetails(), hrmUser.getEmpCode(), OSSConst.BUCKET_ECS));
//                    batchNumList.add(batchNum);
                    summary = summary.add(ecsDrugReimDetaiDto.getSum());
                }
            }

            //发起BPM流程
            Map<String,Object> processInstanceVariables = new HashMap<>();
            //获取参数 库房类型  中/西药库  消毒用品库
            dto.getBpmParams().forEach((key,value) -> {
                processInstanceVariables.put(key,value);
            });
            //添加附件  暂无
            processInstanceVariables.put(OSSConst.APP_ATT_PATHS, "");
            processInstanceVariables.put(OSSConst.APP_ATT_NAMES, "");
            processInstanceVariables.put(OSSConst.APP_ATT_BUCKET_NAME, OSSConst.BUCKET_ECS);
            //获取审核流程  stoinType:  1(中药库库)  2：西药库  3：消毒用品库
            String PROCESS_KEY = StringUtils.equals(dto.getBpmParams().get("stoinType"),MedConst.TYPE_3)? "ECS_DRUG_REIM_XDYP":"ECS_DRUG_REIM_ZXY";
//            String PROCESS_KEY = StringUtils.equals(dto.getBpmParams().get("stoinType"),MedConst.TYPE_3)? "ECS_DRUG_REIM_ZXY":"ECS_DRUG_REIM_ZXY";
            try {
                List<Long> drugDetailIds = dto.getBatchData()
                        .stream()
                        .map(EcsDrugReimDetaiDto::getId).collect(Collectors.toList());
                val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
                bpmProcessInstanceCreateReqDTO
                        .setUserId(hrmUser.getEmpCode())
                        .setProcessDefinitionKey(PROCESS_KEY)
                        .setVariables(processInstanceVariables)
                        .setBusinessKey(drugDetailIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
                // .setStartUserSelectAssignees(dto.getStartUserSelectAssignees());
                CommonFeignResult processInstance = bpmProcessInstanceFeignApi
                        .createProcessInstance(bpmProcessInstanceCreateReqDTO);
                if (!StringUtils.equals(processInstance.get("code").toString(), "200")) {
                    throw new AppException("生成BPM流程异常");
                }
                String processInstanceId = processInstance.get("data").toString();
                // 将工作流编号，更新到报销中
                LambdaUpdateWrapper<EcsDrugReimDetaiDto> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(EcsDrugReimDetaiDto::getProcessInstanceId, processInstanceId)
                        .in(EcsDrugReimDetaiDto::getId, drugDetailIds);
                ecsDrugReimDetaiWriteMapper.update(null, updateWrapper);
            } catch (Exception e) {
                log.error("药品报销发起BPM流程异常", e);
                throw new AppException("药品报销发起BPM流程异常");
            }
        }
    }

    @Override
    public void updEcsDrugPayRcpt(EcsDrugReimDetaiDto dto) {
        if (CollectionUtil.isEmpty(dto.getIds())) {
            throw new AppException("报销的药品不能为空");
        }
        //查询药品报销是否存在
        List<EcsDrugReimDetaiDto> ecsDrugPayDetailDtos = ecsDrugReimDetaiReadMapper.selectBatchIds(dto.getIds());
        if (ecsDrugPayDetailDtos.size()!=dto.getIds().size()) {
            throw new AppException("存在药品报销不存在的记录");
        }

        //更新药品报销状态为已付款，更新说明
        LambdaUpdateWrapper<EcsDrugReimDetaiDto> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(EcsDrugReimDetaiDto::getStatus,EcsConst.DRUG_REIM_STATUS_PAID)
                .set(EcsDrugReimDetaiDto::getDrugPayType,dto.getDrugPayType())
                .set(EcsDrugReimDetaiDto::getPayMethod,dto.getPayMethod())
                .set(EcsDrugReimDetaiDto::getPayIstr2,dto.getPayIstr2())
                .in(EcsDrugReimDetaiDto::getId,dto.getIds());
        //集采报销和现金支付都不上传付款单   非集采报销 1，  非现金支付 0
        if (StringUtils.equals(dto.getDrugPayType(),MedConst.TYPE_1) && StringUtils.equals(dto.getPayMethod(),MedConst.TYPE_0)) {
            //保存药品付款证明详情
            if (CollectionUtil.isEmpty(dto.getPayRcptFiles())) {
                throw new AppException("药品付款证明文件不能为空");
            }

            //保存付款证明文件
            String attCode = ULIDUtil.generate();
            List<List<String>> ossPaths = OSSUtil.getOSSPaths(dto.getPayRcptFiles(),OSSConst.BUCKET_ECS,"reim/item/");
            List<EcsReimFileRecordDto> rcds = new ArrayList<>();
            for (int i = 0;i<ossPaths.get(0).size();i++) {
                EcsReimFileRecordDto rcd = new EcsReimFileRecordDto();
                rcd.setAttCode(attCode);
                rcd.setAtt(ossPaths.get(0).get(i));
                rcd.setAttName(ossPaths.get(1).get(i));
                rcd.setType(EcsConst.FILE_TYPE_PAY);
                rcds.add(rcd);
            }
            BatchUtil.batch("insertFileRecord",rcds, EcsReimFileRecordWriteMapper.class);

            //生成药品上传付款文件批次
            EcsDrugPayDetailDto payDto = new EcsDrugPayDetailDto();
            payDto.setAttCode(attCode);
            payDto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
            payDto.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            ecsDrugPayDetailWriteMapper.insert(payDto);

            //设置drugPayId
            updateWrapper.set(EcsDrugReimDetaiDto::getDrugPayId,payDto.getId());
        }

        ecsDrugReimDetaiWriteService.update(null,updateWrapper);
    }

    @Override
    public Map<String, String> printRcptDoc(EcsDrugReimDetaiDto dto) {
        // Validate input
        if (CollectionUtil.isEmpty(dto.getIds())) {
            throw new AppException("药品报销id不能为空");
        }

        List<String> filePaths = new ArrayList<>();
        List<String> fileNames = new ArrayList<>();
        Map<String, String> fileInfo = new HashMap<>();

        Map<Integer,String> reimIdPaths = new HashMap<>();               //key 报销id,value 路径

        // Fetch drug reimbursement details
        LambdaQueryWrapper<EcsDrugReimDetaiDto> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EcsDrugReimDetaiDto::getId, dto.getIds());
        List<EcsDrugReimDetaiDto> drDtos = ecsDrugReimDetaiReadMapper.selectList(wrapper);
        if (CollectionUtil.isEmpty(drDtos)) {
            throw new AppException("打印的报销单不存在");
        }

        // Collect all auditBchno values
        Set<String> auditBchnos = drDtos.stream()
                .flatMap(drDto -> Stream.of(drDto.getAuditBchno(), drDto.getParentAuditBchno()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // Fetch audit details concurrently
        Map<String, List<AuditDetail>> auditDetailsMap = fetchAuditDetailsConcurrently(auditBchnos);

        // Generate PDFs concurrently
        ExecutorService executorService = Executors.newFixedThreadPool(10); // Adjust the thread pool size as needed
        List<Future<Void>> futures = new ArrayList<>();

        try {
            for (EcsDrugReimDetaiDto drDto : drDtos) {
                //查询制表人信息
                UserSignDto usParam = new UserSignDto();
                usParam.setUsername(drDto.getCrter());
                usParam.setActiveFlag(MedConst.TYPE_1);
                CommonFeignResult commonFeignResult = userFeignService.userSign(usParam);
                HashMap<String,String> objMap = (HashMap<String, String>) commonFeignResult.get(CommonFeignResult.DATA_KEY);
                String signAtt = objMap.get("att");
                String userSignUrl = processSignUrl(OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_CORE, signAtt));
                PictureRenderData picRender = Pictures.ofUrl(userSignUrl,PictureType.suggestFileType(signAtt)).size(70,30).create();

                Future<Void> future = executorService.submit(() -> {
                    try {
                        Map<String, Object> params = buildParams(drDto, auditDetailsMap);
                        params.put("crterSign",picRender);
                        String filePath = generatePdf(params);                      //只是文件名   xxx.pdf
                        synchronized (reimIdPaths) {
                            reimIdPaths.put(drDto.getId().intValue(),""+filePath);
//                        filePaths.add(filePath);
                            fileNames.add("药品付款通知单.pdf");
                        }
                    } catch (Exception e) {
                        log.error(String.format("生成申请审批表失败, drugReimId: {}", drDto.getId()), e);
                    }
                    return null;
                });
                futures.add(future);
            }

            // Wait for all tasks to complete
            for (Future<Void> future : futures) {
                try {
                    future.get();
                } catch (InterruptedException | ExecutionException e) {
                    log.error("任务执行失败", e);
                }
            }

            //将报销对应发票写入到PDF中
            List<Future<String>> pdfFutures = new ArrayList<>();
            for (Integer key : reimIdPaths.keySet()) {
                String path = reimIdPaths.get(key);
                //查询入库单
                LambdaQueryWrapper<EcsStoinDto> stoinWrapper = Wrappers.lambdaQuery(EcsStoinDto.class);
                stoinWrapper.eq(EcsStoinDto::getDrugReimDetailId,key);
                List<EcsStoinDto> ecsStoinDtos = ecsStoinReadMapper.selectList(stoinWrapper);
                Set<String> stoinPaths = new HashSet<>();
                //获取所有发票，去重
                ecsStoinDtos.stream().forEach(es -> {
                    String att = es.getAtt();
                    if (StringUtils.isNotEmpty(att)) {
                        String[] split = att.split(",");
                        for (String s : split) {
                            stoinPaths.add(s);
                        }
                    }
                });
                Future<String> pdfFuture = executorService.submit(() -> mergePdfAndUpload("temp", path, new ArrayList<>(stoinPaths), executorService));
                pdfFutures.add(pdfFuture);
            }

            for (Future<String> pdfFuture : pdfFutures) {
                try {
                    filePaths.add(pdfFuture.get());
                } catch (InterruptedException | ExecutionException e) {
                    log.error("合成药品报销单及发票PDF失败",e);
                    throw new AppException("合成药品报销单及发票PDF失败");
                }
            }

            String resPath;
            if (filePaths.size() == 1) {
                resPath = filePaths.get(0);
            } else {
                PDDocument mergedDoc = new PDDocument();
                List<PDDocument> sourceDocuments = new ArrayList<>();
                try {
                    List<Future<byte[]>> finalPdfFutures = new ArrayList<>();
                    for (int i = 0; i < filePaths.size(); i++) {
                        String filePath = filePaths.get(i);
                        finalPdfFutures.add(executorService.submit(() -> getObjectBytes("temp",filePath)));
                    }
                    //等待所有PDF数据获取完成
                    List<byte[]> finalPdfBytesList = new ArrayList<>();
                    for (Future<byte[]> finalPdfFuture : finalPdfFutures) {
                        finalPdfBytesList.add(finalPdfFuture.get());
                    }

                    for (byte[] bytes : finalPdfBytesList) {
                        PDDocument docIn = PDDocument.load(bytes);
                        sourceDocuments.add(docIn);
                    }
                    for (PDDocument sourceDocument : sourceDocuments) {
                        for (PDPage page : sourceDocument.getPages()) {
                            mergedDoc.addPage(page);
                        }
                    }

                    //将PDF文档保存到一个字节数组输出流中
                    try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                        mergedDoc.save(outputStream);
                        byte[] mergedPdfBytes = outputStream.toByteArray();

                        // 上传生成的PDF文件到MinIO
                        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(mergedPdfBytes)) {
                            resPath =  OSSUtil.uploadFileByInputStream("temp", "", OSSUtil.genFilePath("", filePaths.get(0)), inputStream);
                        }
                    } catch (Exception e) {
                        log.error("保存药品报销单组合文件时错误",e);
                        throw new AppException("保存药品报销单组合文件时错误");
                    }
                }  catch (ExecutionException e) {
                    throw new RuntimeException(e);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                } catch (Exception e) {
                    log.error("合并 PDF 文件时出错： ",e);
                    throw new AppException("合并 PDF 文件时出错： ");
                } finally {
                    //關閉
                    mergedDoc.close();
                    for (PDDocument sourceDocument : sourceDocuments) {
                        sourceDocument.close();
                    }
                }
            }

            fileInfo.put("filePath",resPath);
            fileInfo.put("fileName","药品付款通知单.pdf");

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new AppException(e.getMessage());
        } finally {
            //关闭executorService
            executorService.shutdown();
        }
        return fileInfo;
    }

    /**
     * 将MinIO中的PDF文件和图片文件合并为一个新的PDF文件，并上传到MinIO
     * @param bucketName 存储桶名称
     * @param pdfObjectName PDF文件的ObjectName
     * @param imageObjectNames 图片文件的ObjectName列表
     * @return 上传文件的完整路径
     * @throws Exception 如果发生错误
     */
    @SneakyThrows(Exception.class)
    public String mergePdfAndUpload(String bucketName, String pdfObjectName, List<String> imageObjectNames, ExecutorService executorService) {
        // 获取原始PDF文件
        byte[] pdfBytes = getObjectBytes(bucketName, pdfObjectName);

        // 创建一个新的PDF文档
        try (PDDocument document = PDDocument.load(new ByteArrayInputStream(pdfBytes))) {

            // 区分PDF文件和图片文件
            List<String> pdfObjectNames = new ArrayList<>();
            List<String> imageObjectNamesFiltered = new ArrayList<>();

            for (String objectName : imageObjectNames) {
                if (objectName.toLowerCase().endsWith(".pdf")) {
                    pdfObjectNames.add(objectName);
                } else {
                    imageObjectNamesFiltered.add(objectName);
                }
            }
            // 追加PDF文件
            List<Future<byte[]>> pdfFutures = new ArrayList<>();
            for (String objectName : pdfObjectNames) {
                pdfFutures.add(executorService.submit(() -> getObjectBytes("ecs", objectName)));
            }

            //等待所有PDF数据获取完成
            List<byte[]> pdfBytesList = new ArrayList<>();
            for (Future<byte[]> pdfFuture : pdfFutures) {
                pdfBytesList.add(pdfFuture.get());
            }
            //添加pdf每一页作为新的pdf页面
            List<PDDocument> documents = new ArrayList<>();

            List<PageInfo> allPages = new ArrayList<>();
            try {
                for (byte[] bytes : pdfBytesList) {
                    PDDocument load = PDDocument.load(new ByteArrayInputStream(bytes));
                    documents.add(load);
                    for (int i = 0; i < load.getNumberOfPages(); i++) {
                        allPages.add(new PageInfo(load,i));
                    }
                }
            } catch(IOException e) {
                for (PDDocument dd : documents) {
                    dd.close();
                }
                log.error("加载PDF失败",e);
                throw e;
            }


            LayerUtility layerUtility = new LayerUtility(document);

            //主doc 处理的页数

            for (int i = 0; i < allPages.size(); i+=2) {
                //创建新页面
                PDPage newPage = new PDPage(PDRectangle.A4);
                document.addPage(newPage);

                try(PDPageContentStream contentStream = new PDPageContentStream(document,newPage,PDPageContentStream.AppendMode.APPEND,true,true)) {

                    // 处理第一页 上半
                    processPage(layerUtility,contentStream,allPages.get(i),PDRectangle.A4.getHeight()/2);
                    // 处理第二页 下半
                    if (i + 1 < allPages.size()) {
                        processPage(layerUtility,contentStream,allPages.get(i+1),0);
                    }
                }
            }

            // 并发获取图片数据
            List<Future<byte[]>> imageFutures = new ArrayList<>();
            for (String imageObjectName : imageObjectNamesFiltered) {
                imageFutures.add(executorService.submit(() -> getObjectBytes("ecs", imageObjectName)));
            }

            // 等待所有图片数据获取完成
            List<byte[]> imageBytesList = new ArrayList<>();
            for (Future<byte[]> future : imageFutures) {
                imageBytesList.add(future.get());
            }

            // 每两张图片创建一个PDF页面
            for (int i = 0; i < imageBytesList.size(); i += 2) {
                PDPage page = new PDPage();
                document.addPage(page);

                try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                    // 页面尺寸
                    float pageWidth = page.getMediaBox().getWidth();
                    float pageHeight = page.getMediaBox().getHeight();

                    // 处理第一张图片（上半部分）
                    if (i < imageBytesList.size()) {
                        processImage(contentStream, document, imageBytesList.get(i),
                                pageWidth, pageHeight, true);
                    }

                    // 处理第二张图片（下半部分）
                    if (i+1 < imageBytesList.size()) {
                        processImage(contentStream, document, imageBytesList.get(i+1),
                                pageWidth, pageHeight, false);
                    }
                }
            }

            // 将PDF文档保存到一个字节数组输出流中
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                document.save(outputStream);
                byte[] mergedPdfBytes = outputStream.toByteArray();

                // 上传生成的PDF文件到MinIO
                try (ByteArrayInputStream inputStream = new ByteArrayInputStream(mergedPdfBytes)) {
                    return OSSUtil.uploadFileByInputStream(bucketName, "", OSSUtil.genFilePath("", pdfObjectName), inputStream);
                }


            } catch (Exception e) {
                log.error("保存药品报销单组合文件时错误",e);
                throw new AppException("保存药品报销单组合文件时错误");
            } finally {
                //关闭documents 和 document
                for (PDDocument document1 : documents) {
                    document1.close();
                }
            }
        }
    }

    private static class PageInfo {
        final PDDocument sourceDoc;
        final int pageIndex;

        PageInfo(PDDocument sourceDoc, int pageIndex) {
            this.sourceDoc = sourceDoc;
            this.pageIndex = pageIndex;
        }
    }

    private static void processPage(LayerUtility layerUtility,
                                    PDPageContentStream cs,
                                    PageInfo pageInfo,
                                    float baseYOffset) throws IOException {
        PDPage sourcePage = pageInfo.sourceDoc.getPage(pageInfo.pageIndex);
        PDRectangle mediaBox = sourcePage.getMediaBox();

        // 创建表单对象
        PDFormXObject form = layerUtility.importPageAsForm(pageInfo.sourceDoc, pageInfo.pageIndex);

        // 目标区域（A4半页）
        float targetWidth = PDRectangle.A4.getWidth();
        float targetHeight = (PDRectangle.A4.getHeight() - 40) / 2; // 保留边距

        // 检测文本方向
        TextDirectionDetector detector = new TextDirectionDetector();
        detector.processPage(sourcePage);
        int detectedRotation = detector.getDetectedRotation();

        // 原始尺寸（考虑检测到的旋转）
        float originalWidth = mediaBox.getWidth();
        float originalHeight = mediaBox.getHeight();

        float contentWidth, contentHeight;
        if (detectedRotation == 90 || detectedRotation == 270) {
            contentWidth = originalHeight;
            contentHeight = originalWidth;
        } else {
            contentWidth = originalWidth;
            contentHeight = originalHeight;
        }

        // 最佳缩放比例
        float scale = Math.min(targetWidth / contentWidth, targetHeight / contentHeight);
        float scaledWidth = contentWidth * scale;
        float scaledHeight = contentHeight * scale;

        cs.saveGraphicsState();
        try {
            // 1. 移动到目标区域左上角
            float translateX = (targetWidth - scaledWidth) / 2;
            float translateY = baseYOffset + targetHeight - scaledHeight;

            // 2. 构建复合变换矩阵
            Matrix matrix = new Matrix();

            // 基础平移
            matrix.translate(translateX, translateY);

            // 处理检测到的旋转
            if (detectedRotation != 0) {
                // 移动到旋转中心
                matrix.translate(scaledWidth / 2, scaledHeight / 2);

                // 应用旋转补偿
                matrix.rotate(Math.toRadians(detectedRotation));

                // 移回原点
                matrix.translate(-contentWidth / 2, -contentHeight / 2);
            }

            // 应用复合变换
            cs.transform(matrix);

            // 3. 应用缩放并绘制
            cs.transform(Matrix.getScaleInstance(scale, scale));
            cs.drawForm(form);

        } finally {
            cs.restoreGraphicsState();
        }

        // 文本方向检测
    }

    private static class TextDirectionDetector extends PDFTextStripper {
        private final List<Float> orientations = new ArrayList<>();

        public TextDirectionDetector() throws IOException {
            super();
            setSortByPosition(true);
        }

        @Override
        protected void processTextPosition(TextPosition text) {
            Matrix textMatrix = text.getTextMatrix();
            float angle = (float) Math.toDegrees(Math.atan2(textMatrix.getShearY(), textMatrix.getScaleY()));
            orientations.add(angle % 360);
        }

        public int getDetectedRotation() {
            if (orientations.isEmpty()) return 0;

            // 统计主要方向
            Map<Integer, Integer> counts = new HashMap<>();
            for (Float angle : orientations) {
                int rounded = Math.round(angle / 90) * 90;
                counts.put(rounded, counts.getOrDefault(rounded, 0) + 1);
            }

            // 取出现次数最多的有效角度
            return counts.entrySet().stream()
                    .filter(e -> e.getKey() != 0)
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse(0);
        }
    }

    /*private static void processPage(LayerUtility layerUtility,
                                    PDPageContentStream cs,
                                    PageInfo pageInfo,
                                    float yOffset) throws IOException {
        PDDocument sourceDoc = pageInfo.sourceDoc;
        PDPage sourcePage = sourceDoc.getPage(pageInfo.pageIndex);

        // 获取原始页面尺寸
        PDRectangle mediaBox = sourcePage.getMediaBox();
        float originalWidth = mediaBox.getWidth();
        float originalHeight = mediaBox.getHeight();

        // 自动旋转检测：原始页面是否为纵向
        boolean isPortrait = originalHeight > originalWidth;

        // 计算缩放比例（以A4半高区域为基准）
        float targetWidth = PDRectangle.A4.getWidth();
        float targetHeight = (PDRectangle.A4.getHeight() - 10) / 2;

        // 有效尺寸（旋转后的实际尺寸）
        float effectiveWidth = isPortrait ? originalHeight : originalWidth;
        float effectiveHeight = isPortrait ? originalWidth : originalHeight;

        // 计算缩放比例（保持宽高比）
        float scale = Math.min(
                targetWidth / effectiveWidth,
                targetHeight / effectiveHeight
        );

        float yOffsetA = yOffset + (yOffset > 0 ? 10: 0);

        // 导入原始页面为表单对象
        PDFormXObject form = layerUtility.importPageAsForm(sourceDoc, pageInfo.pageIndex);

        // 绘制内容到目标位置
        cs.saveGraphicsState();
        {
            // 移动到指定垂直位置
            cs.transform(Matrix.getTranslateInstance(0, yOffsetA));

            // 处理纵向页旋转
            if (isPortrait) {
                // 旋转90度（绕左上角）
                cs.transform(Matrix.getRotateInstance(Math.toRadians(90), 0, 0));
                // 调整位置（将旋转后的内容移到可视区域内）
                cs.transform(Matrix.getTranslateInstance(0, -originalHeight));
            }

            // 应用缩放
            cs.transform(Matrix.getScaleInstance(scale, scale));

            // 绘制表单
            cs.drawForm(form);
        }
        cs.restoreGraphicsState();
    }*/


    // 图片处理通用方法
    private void processImage(PDPageContentStream contentStream, PDDocument document,
                              byte[] imageBytes, float pageWidth, float pageHeight,
                              boolean isTop) throws IOException {
        final float margin = 10f;

        // 使用Apache Commons Imaging读取图片
        BufferedImage bufferedImage;
        try (InputStream input = new ByteArrayInputStream(imageBytes)) {
            bufferedImage = Imaging.getBufferedImage(input); // 替换为更可靠的图片读取方式
        } catch (Exception e) {
            throw new IOException("无法读取图片数据", e);
        }

        // 处理透明通道：将图像转换为ARGB格式
        BufferedImage convertedImage = new BufferedImage(
                bufferedImage.getWidth(),
                bufferedImage.getHeight(),
                BufferedImage.TYPE_INT_ARGB
        );
        convertedImage.getGraphics().drawImage(bufferedImage, 0, 0, null);

        // 创建PDF图片对象
        PDImageXObject pdImage;
        if (convertedImage.getColorModel().hasAlpha()) {
            // 使用LosslessFactory处理透明通道
            pdImage = LosslessFactory.createFromImage(document, convertedImage);
        } else {
            pdImage = PDImageXObject.createFromByteArray(document, imageBytes, null);
        }

        // 智能方向判断（增加EXIF方向检测）
        boolean needRotate = shouldRotate(imageBytes); // 新增方向判断方法

        // 计算显示尺寸
        float displayWidth = needRotate ? convertedImage.getHeight() : convertedImage.getWidth();
        float displayHeight = needRotate ? convertedImage.getWidth() : convertedImage.getHeight();

        // 计算缩放比例
        float targetWidth = pageWidth - 2 * margin;
        float targetHeight = (pageHeight/2) - 2 * margin;
        float scale = Math.min(targetWidth / displayWidth, targetHeight / displayHeight);

        // 调整缩放逻辑（保持整数像素）
        scale = (float) Math.floor(scale * 100) / 100; // 保留两位小数避免抗锯齿

        // 计算绘制位置
        float scaledWidth = displayWidth * scale;
        float scaledHeight = displayHeight * scale;
        float x = margin + (targetWidth - scaledWidth)/2;
        float y = isTop
                ? (pageHeight - margin - scaledHeight)
                : (pageHeight/2 - scaledHeight + margin);

        // 优化旋转绘制逻辑
        if (needRotate) {
            drawRotatedImage(contentStream, pdImage, x, y, scaledWidth, scaledHeight);
        } else {
            contentStream.drawImage(pdImage, x, y, scaledWidth, scaledHeight);
        }
    }

    // 新增方向判断方法（包含EXIF检测）
    private boolean shouldRotate(byte[] imageBytes) {
        try {
            ImageMetadata metadata = Imaging.getMetadata(imageBytes);
            if (metadata instanceof JpegImageMetadata) {
                JpegImageMetadata jpegMetadata = (JpegImageMetadata) metadata;

                // 关键：通过 TIFF 标签获取方向
                TiffImageMetadata exif = jpegMetadata.getExif();
                if (exif != null) {
                    // EXIF 方向标签为 274 (0x0112)
                    TiffField orientationField = exif.findField(TiffTagConstants.TIFF_TAG_ORIENTATION);
                    if (orientationField != null) {
                        int orientation = orientationField.getIntValue();
                        return orientation >= 5 && orientation <= 8;
                    }
                }
            }
        } catch (Exception e) {
            // 异常处理...
            throw new AppException("转换失败");
        }

        // 回退到宽高比判断
        try (InputStream input = new ByteArrayInputStream(imageBytes)) {
            BufferedImage image = Imaging.getBufferedImage(input);
            return image.getHeight() > image.getWidth();
        } catch (Exception ex) {
            return false;
        }
    }

    // 优化后的旋转绘制方法
    private void drawRotatedImage(PDPageContentStream cs, PDImageXObject image,
                                  float baseX, float baseY,
                                  float imgWidth, float imgHeight) throws IOException {
        // PDF坐标系原点在左下角，Y轴向上
        cs.saveGraphicsState();

        // 1. 计算旋转中心点（基于目标位置中心）
        float centerX = baseX + imgWidth/2;
        float centerY = baseY + imgHeight/2;

        // 2. 构建变换矩阵（按正确顺序）
        Matrix matrix = new Matrix();
        matrix.translate(centerX, centerY);    // 移动至中心点
        matrix.rotate(Math.toRadians(90));     // 执行旋转
//        matrix.scale(1, -1);                   // Y轴翻转
        matrix.translate(-imgHeight/2, -imgWidth/2); // 调整旋转后偏移量

        // 3. 应用矩阵变换
        cs.transform(matrix);

        // 4. 绘制图像（使用原始尺寸）
        cs.drawImage(image, 0, 0, imgHeight, imgWidth); // 交换宽高

        cs.restoreGraphicsState();
    }
    /*private void processImage(PDPageContentStream contentStream, PDDocument document,
                              byte[] imageBytes, float pageWidth, float pageHeight,
                              boolean isTop) throws IOException {
        // 边距设置
        float margin = 10;
        PDImageXObject pdImage = PDImageXObject.createFromByteArray(document, imageBytes, "image");
        BufferedImage bufferedImage = pdImage.getImage();

        // 原始尺寸
        float originalWidth = bufferedImage.getWidth();
        float originalHeight = bufferedImage.getHeight();

        // 自动旋转检测（纵向图片需要旋转90度）
        boolean needRotate = originalHeight > originalWidth;
        float displayWidth = needRotate ? originalHeight : originalWidth;
        float displayHeight = needRotate ? originalWidth : originalHeight;

        // 目标区域尺寸（A4横向的半页）
        float targetWidth = PDRectangle.A4.getWidth();
        float targetHeight = (PDRectangle.A4.getHeight() - margin) / 2;

        // 计算宽高比，决定缩放模式
        float imageAspect = displayWidth / displayHeight;
        float targetAspect = targetWidth / targetHeight;
        float scale = (imageAspect > targetAspect) ?
                targetWidth / displayWidth :  // 按宽度缩放
                targetHeight / displayHeight;  // 按高度缩放

        // 计算缩放后尺寸
        float scaledWidth = displayWidth * scale;
        float scaledHeight = displayHeight * scale;

        // 计算居中坐标
        float x = (targetWidth - scaledWidth) / 2;
        float y = isTop ?
//                PDRectangle.A4.getHeight() - scaledHeight - margin / 2 :
                (PDRectangle.A4.getHeight() - targetHeight) + (targetHeight - scaledHeight) / 2 :
                (targetHeight - scaledHeight) / 2 + margin/2;

        // 绘制逻辑
        if (needRotate) {
            contentStream.saveGraphicsState();
            Matrix matrix = new Matrix();
            // 计算旋转后的定位点
            matrix.translate(x + scaledHeight, y + scaledWidth);
            matrix.rotate(Math.toRadians(90));
            contentStream.transform(matrix);
            contentStream.drawImage(pdImage, 0, 0, scaledHeight, scaledWidth);
            contentStream.restoreGraphicsState();
        } else {
            contentStream.drawImage(pdImage, x, y, scaledWidth, scaledHeight);
        }
    }*/

    /**
     * 获取文件流并转换为字节数组
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 对象的字节数组
     * @throws Exception 如果发生错误
     */
    @SneakyThrows(Exception.class)
    private byte[] getObjectBytes(String bucketName, String objectName) {
        try (InputStream inputStream = OSSUtil.getObject(bucketName, objectName);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            return outputStream.toByteArray();
        }
    }

    private Map<String, Object> buildParams(EcsDrugReimDetaiDto drDto, Map<String, List<AuditDetail>> auditDetailsMap) {
        Map<String, Object> params = new HashMap<>();
        params.put("createTime", drDto.getCraeteTime());
        params.put("spler", drDto.getSpler());
        params.put("capSum", drDto.getCapSum());
        params.put("sum", drDto.getSum());
        params.put("payIstr", drDto.getPayIstr());
        params.put("no",drDto.getId());
        params.put("payType","转账");

        //查询报销对应入库单的最大月份
        LambdaQueryWrapper<EcsStoinDto> stoinWrapper = Wrappers.lambdaQuery();
        stoinWrapper.eq(EcsStoinDto::getDrugReimDetailId,drDto.getId())
                .orderByAsc(EcsStoinDto::getStoinDate)
                .last("LIMIT 1");
        EcsStoinDto ecsStoinDto = ecsStoinReadMapper.selectOne(stoinWrapper);
        if (ObjectUtils.isNotNull(ecsStoinDto)){
            params.put("getYear","    ");
            params.put("getMonth",StringUtils.substring(ecsStoinDto.getStoinDate(),5,7));
            params.put("getDay","  ");
        }

        params.put("payYear","    ");
        params.put("payMonth","  ");
        params.put("payDay","  ");
        if (!Objects.isNull(drDto.getDrugPayId())) {
            //查询付款单的时间
            LambdaQueryWrapper<EcsDrugPayDetailDto> drugPayDtoWrapper = Wrappers.lambdaQuery();
            drugPayDtoWrapper.eq(EcsDrugPayDetailDto::getId,Integer.parseInt(drDto.getDrugPayId()))
                    .orderByDesc(EcsDrugPayDetailDto::getCreateTime)
                    .last("LIMIT 1");
            EcsDrugPayDetailDto ecsDrugPayDetailDto = ecsDrugPayDetailReadMapper.selectOne(drugPayDtoWrapper);
            params.put("payYear",StringUtils.substring(ecsDrugPayDetailDto.getCreateTime(),0,4));
            params.put("payMonth",StringUtils.substring(ecsDrugPayDetailDto.getCreateTime(),5,7));
            params.put("payDay",StringUtils.substring(ecsDrugPayDetailDto.getCreateTime(),8,10));
        }

        if (StringUtils.isEmpty(drDto.getProcessInstanceId())) {        //老审批审核流程数据
            // Fetch audit details from the map
            List<AuditDetail> auditDetails = auditDetailsMap.getOrDefault(drDto.getAuditBchno(), Collections.emptyList());
            List<AuditDetail> pAuditDetails = auditDetailsMap.getOrDefault(drDto.getParentAuditBchno(), Collections.emptyList());

            // Sort audit details by chkSeq
            auditDetails.sort(Comparator.comparingInt(AuditDetail::getChkSeq));
            pAuditDetails.sort(Comparator.comparingInt(AuditDetail::getChkSeq));

            // Populate params with audit details
            for (int i = 0; i < auditDetails.size() && i < pAuditDetails.size(); i++) {
                AuditDetail auditDetail = auditDetails.get(i);
                AuditDetail pAuditDetail = pAuditDetails.get(i);
                params.put("auditTitle" + i, auditDetail.getDscr());
                if (StringUtils.equals(auditDetail.getChkState(), MedConst.TYPE_1)) {
                    if (StringUtils.isNotEmpty(pAuditDetail.getSign())) {
                        String signUrl = processSignUrl(pAuditDetail.getSign());
                        String signuri = signUrl.split("\\?")[0];
                        PictureRenderData picRender = Pictures.ofUrl(signUrl, PictureType.suggestFileType(signuri))
                                .size(70, 30).create();
                        params.put("sign" + i, picRender);
                    }
                    params.put("chkTime" + i, StringUtils.substring(pAuditDetail.getChkTime(), 0, 10));
                    params.put("remark" + i, pAuditDetail.getChkRemarks());
                }
            }
        } else {                                                                //新审批审核数据
            CommonFeignResult processRes = bpmProcessInstanceFeignApi.getTaskListByProcessInstanceId(drDto.getProcessInstanceId());
            if (!StringUtils.equals(processRes.get("code").toString(), "0")) {          //0 为成功
                log.error("获取审核流程失败");
                throw new AppException("获取审核流程失败");
            }
            List<Object> list = (List<Object>)processRes.get("data");
            //处理审核流程数据 筛选、排序
            List<Map<String, Object>> maps = processAuditData(list);

            for (int i = 0; i < maps.size(); i++) {
                Map<String, Object> stepStr = maps.get(i);
//                Map<String, Object> stepStr = BeanUtil.beanToMap(step);
                params.put("auditTitle"+ i, stepStr.get("name").toString());
                //是否有签名
                boolean isSignNull = Objects.isNull(stepStr.get("signUrl"));
                //是否有审核时间
                boolean isTimeNull = Objects.isNull(stepStr.get("auditTime")) && Objects.isNull(stepStr.get("endTime"));
                //是否有审批意见
                boolean isReasonNull = Objects.isNull(stepStr.get("reason"));
                if (!isSignNull) {
                    String signUrl = stepStr.get("signUrl").toString();
                    String signUri = signUrl.split("\\?")[0];
                    if (signUrl.contains("hrp.zjxrmyy.cn:18090")) {
                        //替换为文件服务器真实地址
                        signUrl = signUrl.replace("https","http");
                        signUrl = signUrl.replace("hrp.zjxrmyy.cn:18090","10.2.233.10:4556");
                        //去掉oss
                        int ossIdx = signUrl.indexOf("oss");
                        if (ossIdx != -1) {
                            StringBuilder sb = new StringBuilder(signUrl);
                            sb.delete(ossIdx,ossIdx+4);
                            signUrl = sb.toString();
                        }
                    }
                    PictureRenderData pictureRenderData = Pictures.ofUrl(signUrl
                            , PictureType.suggestFileType(signUri)).size(70, 30).altMeta("").create();
                    params.put("sign"+ i, pictureRenderData);
                }
                if (!isTimeNull) {
                    String chkTime = Objects.isNull(stepStr.get("auditTime"))?stepStr.get("endTime").toString():stepStr.get("auditTime").toString();
                    Instant instant = Instant.ofEpochMilli(Long.parseLong(chkTime));
                    LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
                    params.put("chkTime"+ i, localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                }
                if (!isReasonNull) {
                    params.put("remark" + i, stepStr.get("reason").toString());
                }
            }
        }
        // 打印 params 内容
        log.error("构建的报销参数 params 内容：",JSON.toJSONString(params));
        return params;
    }

    public List<Map<String, Object>> processAuditData(List<Object> list) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }

        // 1. 筛选 status != "5"
        List<Map<String, Object>> filtered = list.stream()
                .map(obj -> BeanUtil.beanToMap(obj))
                .filter(map -> !"5".equals(String.valueOf(map.get("status"))))
                .collect(Collectors.toList());

        // 2. 按 createTime 倒序排序
        filtered.sort((o1, o2) -> {
            Long time1 = parseTimestamp(o1.get("createTime"));
            Long time2 = parseTimestamp(o2.get("createTime"));
            return time2.compareTo(time1);
        });

        // 3. 根据 taskDefinitionKey 去重，保留最新的
        Map<String, Map<String, Object>> uniqueMap = new LinkedHashMap<>();
        for (Map<String, Object> item : filtered) {
            String key = (String) item.get("taskDefinitionKey");
            if (key != null) {
                uniqueMap.put(key, item);
            }
        }
        List<Map<String, Object>> deduplicated = new ArrayList<>(uniqueMap.values());

        // 4. 再按 createTime 正序排序
        deduplicated.sort(Comparator.comparing(
                map -> parseTimestamp(map.get("createTime"))
        ));

        return deduplicated;
    }

    private static Long parseTimestamp(Object obj) {
        if (obj instanceof Number) {
            return ((Number) obj).longValue();
        } else if (obj instanceof String) {
            try {
                return Long.parseLong((String) obj);
            } catch (NumberFormatException ignored) {
            }
        }
        return 0L; // 默认值
    }

    private Map<String, List<AuditDetail>> fetchAuditDetailsConcurrently(Set<String> auditBchnos) {
        if (CollectionUtil.isEmpty(auditBchnos)) {
            return Collections.emptyMap();
        }

        ExecutorService executorService = Executors.newFixedThreadPool(10); // Adjust the thread pool size as needed
        Map<String, Future<List<AuditDetail>>> futures = new HashMap<>();

        // Submit tasks to fetch audit details concurrently
        for (String auditBchno : auditBchnos) {
            AuditDetail audit = new AuditDetail();
            audit.setSys("ecs");
            audit.setAuditBchno(auditBchno);
            Future<List<AuditDetail>> future = executorService.submit(() -> auditFeignService.getAuditDetails(audit));
            futures.put(auditBchno, future);
        }

        Map<String, List<AuditDetail>> auditDetailsMap = new HashMap<>();

        // Collect results from futures
        for (Map.Entry<String, Future<List<AuditDetail>>> entry : futures.entrySet()) {
            String auditBchno = entry.getKey();
            Future<List<AuditDetail>> future = entry.getValue();
            try {
                List<AuditDetail> auditDetails = future.get();
                auditDetailsMap.put(auditBchno, auditDetails);
            } catch (InterruptedException | ExecutionException e) {
                log.error(String.format("Failed to fetch audit details for auditBchno: {}", auditBchno), e);
            }
        }

        executorService.shutdown();
        return auditDetailsMap;
    }

    private String processSignUrl(String signUrl) {
        if (signUrl.contains("hrp.zjxrmyy.cn:18090")) {
            signUrl = signUrl.replace("https", "http")
                    .replace("hrp.zjxrmyy.cn:18090", "10.2.233.10:4556");
            int ossIdx = signUrl.indexOf("oss");
            if (ossIdx != -1) {
                signUrl = signUrl.substring(0, ossIdx) + signUrl.substring(ossIdx + 4);
            }
        }
        return signUrl;
    }

    private String generatePdf(Map<String, Object> params) throws Exception {
        return PdfUtil.readOssRender(OSSConst.BUCKET_ECS, "template/药品付款通知单模板.docx", params);
    }

    @Override
    public List<EcsDrugReimDetaiVo> queryEcsDrugReims(EcsDrugReimDetaiDto dto) {
        List<Long> drugIds = Arrays.stream(dto.getDrugIdsStr().split(",")).map(Long::parseLong).collect(Collectors.toList());
        //查询所有药品报销
        EcsDrugReimDetaiDto param = new EcsDrugReimDetaiDto();
        param.setIds(drugIds.stream().map(Long::intValue).collect(Collectors.toList()));

        List<EcsDrugReimDetaiVo> ecsDrugReimDetaiVos = ecsDrugReimDetaiReadMapper.queryList(param);
        //获取所有报销id，查询对应入库单信息
        List<Long> reimIds = ecsDrugReimDetaiVos.stream().map(e -> e.getId()).collect(Collectors.toList());
        //查询所有入库单信息
        LambdaQueryWrapper<EcsStoinDto> stoinWrapper = Wrappers.lambdaQuery(EcsStoinDto.class);
        stoinWrapper.in(EcsStoinDto::getDrugReimDetailId,reimIds);

        EcsStoinDto stoinDto = new EcsStoinDto();
        stoinDto.setDrugReimDetailIds(reimIds);
        List<EcsStoinVo> ecsStoinVos = ecsStoinReadMapper.queryList(stoinDto);
        //报销找到对应入库单信息，放入报销中
        List<EcsDrugReimDetaiVo> reimDetailVos = new ArrayList<>();
        ecsDrugReimDetaiVos.stream().forEach(e -> {
            List<EcsStoinVo> reimStoins = ecsStoinVos.stream()
                    .filter(s -> s.getDrugReimDetailId().equals(e.getId())).collect(Collectors.toList());
            e.setStoins(reimStoins);
        });
        /*LambdaQueryWrapper<EcsDrugReimDetaiDto> wrapper = Wrappers.lambdaQuery(EcsDrugReimDetaiDto.class);
        wrapper.eq(EcsDrugReimDetaiDto::getParentAuditBchno,dto.getParentAuditBchno());
        //父审批号下所有报销
        List<EcsDrugReimDetaiDto> ecsDrugReimDetaiDtos = ecsDrugReimDetaiReadMapper.selectList(wrapper);
        //获取所有报销id，查询对应入库单信息
        List<Long> reimIds = ecsDrugReimDetaiDtos.stream().map(e -> e.getId()).collect(Collectors.toList());
        //查询所有入库单信息
        LambdaQueryWrapper<EcsStoinDto> stoinWrapper = Wrappers.lambdaQuery(EcsStoinDto.class);
        stoinWrapper.in(EcsStoinDto::getDrugReimDetailId,reimIds);
//        List<EcsStoinDto> ecsStoinDtos = ecsStoinReadMapper.selectList(stoinWrapper);
        EcsStoinDto stoinDto = new EcsStoinDto();
        stoinDto.setDrugReimDetailIds(reimIds);
        List<EcsStoinVo> ecsStoinVos = ecsStoinReadMapper.queryList(stoinDto);
        //报销找到对应入库单信息，放入报销中
        List<EcsDrugReimDetaiVo> reimDetailVos = new ArrayList<>();
        ecsDrugReimDetaiDtos.stream().forEach(e -> {
            EcsDrugReimDetaiVo vo = new EcsDrugReimDetaiVo();
            BeanUtils.copyProperties(e,vo);
            List<EcsStoinVo> reimStoins = ecsStoinVos.stream()
                    .filter(s -> s.getDrugReimDetailId().equals(e.getId())).collect(Collectors.toList());
            vo.setStoins(reimStoins);
            reimDetailVos.add(vo);
        });*/
        return ecsDrugReimDetaiVos;
    }

    @Override
    public void updIssue(EcsDrugReimDetaiDto dto) {
        LambdaUpdateWrapper<EcsDrugReimDetaiDto> updWrapper = Wrappers.lambdaUpdate(EcsDrugReimDetaiDto.class);
        updWrapper.set(EcsDrugReimDetaiDto::getIssue,dto.getIssue())
                .in(EcsDrugReimDetaiDto::getId,dto.getIds());
        ecsDrugReimDetaiWriteMapper.update(null,updWrapper);
    }

    /**
     * 删除未选择的药品报销记录
     * @param dto
     */
    @Override
    public void delDrugReimNoChoose(EcsDrugReimDetaiDto dto) {
        //获取入库单
        LambdaQueryWrapper<EcsStoinDto> stoinWrapper = Wrappers.lambdaQuery();
        stoinWrapper.in(EcsStoinDto::getDrugReimDetailId,dto.getIds());
        List<EcsStoinDto> stoins = ecsStoinReadMapper.selectList(stoinWrapper);
        //更新入库单状态
        List<Integer> stoinIds = stoins.stream().map(EcsStoinDto::getId).collect(Collectors.toList());
        LambdaUpdateWrapper<EcsStoinDto> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(EcsStoinDto::getReimFlag,MedConst.TYPE_0)
                .set(EcsStoinDto::getDrugReimDetailId,null)
                .set(EcsStoinDto::getAtt,null)
                .set(EcsStoinDto::getAttName,null)
                .set(EcsStoinDto::getInvoId,null)
                .in(EcsStoinDto::getId,stoinIds);
        ecsStoinWriteMapper.update(null,updateWrapper);
        //删除未通过的报销id
        LambdaQueryWrapper<EcsDrugReimDetaiDto> deleteWrapper = Wrappers.lambdaQuery(EcsDrugReimDetaiDto.class);
        deleteWrapper.in(EcsDrugReimDetaiDto::getId,dto.getIds());
        ecsDrugReimDetaiWriteMapper.delete(deleteWrapper);
        //获取发票并更新发票状态
        Set<Long> invoIds = new HashSet<>();
        stoins.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getInvoId())) {
                Arrays.stream(item.getInvoId().split(","))
                        .map(Long::valueOf)
                        .forEach(invoIds::add);
            }
        });
        ecsInvoRcdWriteMapper.updateStateByIds(new ArrayList<>(invoIds), MedConst.TYPE_1,"");

    }
}
