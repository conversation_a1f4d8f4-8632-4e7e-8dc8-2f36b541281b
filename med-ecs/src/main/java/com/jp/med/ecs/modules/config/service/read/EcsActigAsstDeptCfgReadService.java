package com.jp.med.ecs.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.config.dto.EcsActigAsstDeptCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsActigAsstDeptCfgVo;

import java.util.List;

/**
 * 会计科目科室辅助信息配置
 * <AUTHOR>
 * @email -
 * @date 2024-02-01 16:36:57
 */
public interface EcsActigAsstDeptCfgReadService extends IService<EcsActigAsstDeptCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsActigAsstDeptCfgVo> queryList(EcsActigAsstDeptCfgDto dto);
}

