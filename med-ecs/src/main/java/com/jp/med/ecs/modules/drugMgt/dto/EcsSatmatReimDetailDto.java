package com.jp.med.ecs.modules.drugMgt.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.common.entity.audit.AuditDetail;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 卫材报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-08-02 09:28:49
 */
@Data
@TableName("ecs_satmat_reim_detail" )
public class EcsSatmatReimDetailDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 供货单位 */
    @TableField("spler")
    private String spler;

    /** 合计金额小写(元) */
    @TableField("sum")
    private BigDecimal sum;

    /** 合计金额大写 */
    @TableField("cap_sum")
    private String capSum;

    /** 付款说明 */
    @TableField("pay_istr")
    private String payIstr;

    /** 审核批次号 */
    @TableField("audit_bchno")
    private String auditBchno;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 期号(报销月份) */
    @TableField("issue")
    private String issue;

    /** 上级审批号 */
    @TableField("parent_audit_bchno")
    private String parentAuditBchno;

    /** 推送消息内容 */
    @TableField(exist = false)
    private String pushContent;

    /** 供货单位个数 */
    @TableField(exist = false)
    private Integer splerNum;

    /** 入库单号 */
    @TableField(exist = false)
    private Integer stoinNum;

    /** 是否批量审核 */
    @TableField(exist = false)
    private String batchAudit;

    /** 审核状态，1：成功，2：失败，3审核中 */
    @TableField(exist = false)
    private String auditState;

    /** 入库单号 */
    @TableField(exist = false)
    private List<Integer> stoinNumList;

    /** 批量新增时使用 */
    @TableField(exist = false)
    private List<EcsSatmatReimDetailDto> batchData;

    /** 子集 */
    @TableField(exist = false)
    private List<AuditDetail> childrenDetails;

    /** 入库单对应发票 */
    @TableField(exist = false)
    private Map<Integer, List<MultipartFile>> stoinNumMap;

}
