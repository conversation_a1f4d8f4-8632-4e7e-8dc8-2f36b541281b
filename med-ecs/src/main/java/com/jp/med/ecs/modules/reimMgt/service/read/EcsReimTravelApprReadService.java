package com.jp.med.ecs.modules.reimMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimTravelApprDto;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimTravelApprVo;

import java.util.List;
import java.util.Map;

/**
 * 报销差旅审批
 * <AUTHOR>
 * @email -
 * @date 2023-12-13 13:48:41
 */
public interface EcsReimTravelApprReadService extends IService<EcsReimTravelApprDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimTravelApprVo> queryList(EcsReimTravelApprDto dto);

    /**
     * 查询列表
     * @param dto
     * @return
     */
    List<EcsReimTravelApprVo> queryListNew(EcsReimTravelApprDto dto);

    /**
     * 查询app审核详情页面数据
     * @param dto
     * @return
     */
    EcsReimTravelApprVo queryAppAuditDetail(EcsReimTravelApprDto dto);

    /**
     * 查询申请审批详情
     * @param dto
     * @return
     */
    Map<String,Object> queryApprDetail(EcsReimTravelApprDto dto);

    /**
     * 查询临床医技科室差旅、培训申请数量
     * @param dto
     * @return
     */
    Integer queryTrainingApplyWarnNum(EcsReimTravelApprDto dto);

    /**
     * 查询临床医技科室差旅、培训报销数量
     * @param dto
     * @return
     */
    Integer queryTrainingToReimWarnNum(EcsReimTravelApprDto dto);

    /**
     * 查询职能科室差旅、培训申请数量
     * @param dto
     * @return
     */
    Integer queryTravelApplyWarnNum(EcsReimTravelApprDto dto);

    /**
     * 查询职能科室差旅、培训报销数量
     * @param dto
     * @return
     */
    Integer queryTravelToReimWarnNum(EcsReimTravelApprDto dto);

    Integer queryTrainingAuditWarnNum(EcsReimTravelApprDto dto);

    Map<String,String> queryTravelApprDoc(Map<String, Object> params);

    List<EcsReimTravelApprVo> queryListMultiTrip(EcsReimTravelApprDto dto);

    Map<String,String>  queryTravelApprDocMultiTrip(List<Map<String, Object>> params);
}

