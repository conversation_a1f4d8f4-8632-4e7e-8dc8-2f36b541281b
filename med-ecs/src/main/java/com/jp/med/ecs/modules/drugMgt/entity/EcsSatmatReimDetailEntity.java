package com.jp.med.ecs.modules.drugMgt.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 卫材报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-08-02 09:28:49
 */
@Data
@TableName("ecs_satmat_reim_detail")
public class EcsSatmatReimDetailEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 供货单位 */
	@TableField("spler")
	private String spler;

	/** 合计金额小写(元) */
	@TableField("sum")
	private BigDecimal sum;

	/** 合计金额大写 */
	@TableField("cap_sum")
	private String capSum;

	/** 付款说明 */
	@TableField("pay_istr")
	private String payIstr;

	/** 审核批次号 */
	@TableField("audit_bchno")
	private String auditBchno;

	/** 创建人 */
	@TableField("crter")
	private String crter;

	/** 创建时间 */
	@TableField("create_time")
	private String createTime;

	/** 医疗机构id */
	@TableField("hospital_id")
	private String hospitalId;

	/** 期号(报销月份) */
	@TableField("issue")
	private String issue;

	/** 上级审批号 */
	@TableField("parent_audit_bchno")
	private String parentAuditBchno;

}
