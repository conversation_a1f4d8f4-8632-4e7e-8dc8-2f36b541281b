package com.jp.med.ecs.modules.reimMgt.controller;

import com.jp.med.common.dto.ecs.EcsReimContractTask;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsReimContractTaskReadService;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsReimContractTaskWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 合同报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 17:13:38
 */
@Api(value = "合同报销任务", tags = "合同报销任务")
@RestController
@RequestMapping("ecsReimContractTask")
public class EcsReimContractTaskController {

    @Autowired
    private EcsReimContractTaskReadService ecsReimContractTaskReadService;

    @Autowired
    private EcsReimContractTaskWriteService ecsReimContractTaskWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询合同报销任务")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody EcsReimContractTask dto){
        return CommonResult.paging(ecsReimContractTaskReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询合同报销任务")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsReimContractTask dto){
        return CommonResult.success(ecsReimContractTaskReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增合同报销任务")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsReimContractTask dto){
        ecsReimContractTaskWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改合同报销任务")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsReimContractTask dto){
        ecsReimContractTaskWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除合同报销任务")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsReimContractTask dto){
        ecsReimContractTaskWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 保存合同任务
     * @param dto
     * @return
     */
    @ApiOperation("保存合同任务")
    @PostMapping("/saveContractTask")
    public CommonFeignResult saveContractTask(@RequestBody EcsReimContractTask dto) {
        ecsReimContractTaskWriteService.saveContractTask(dto);
        return CommonFeignResult.build();
    }

    /**
     * 查询合同任务明细
     * @param dto
     * @return
     */
    @ApiOperation("查询合同任务明细")
    @PostMapping("/queryContractTaskDetail")
    public CommonResult<?> queryContractTaskDetail(@RequestBody EcsReimContractTask dto) {
        return CommonResult.success(ecsReimContractTaskReadService.queryContractTaskDetail(dto));
    }

}
