package com.jp.med.ecs.modules.config.entity;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("ecs_reim_share_detail")
@ExcelIgnoreUnannotated
public class EcsReimShareEntity {

    /**
     * id
     */
    @TableField("id")
    private Integer id;

    /**
     * 报销明细id
     */
    @TableField("reim_detail_id")
    private Integer reimDetailId;

    /**
     * 分摊类型
     */
    @TableField("share_type")
    private String shareType;

    /**
     * 科室编码
     */
    @TableField("dept_code")
    @ExcelProperty(index = 2)
    private String deptCode;

    /**
     * 科室名称
     */
    @TableField("dept_name")
    @ExcelProperty(index = 1)
    private String deptName;

    /**
     * 金额
     */
    @TableField("amt")
    private BigDecimal amt;

    /**
     * 摘要
     */
    @TableField("abs")
    @ExcelProperty(index = 0)
    private String abs;

    @TableField(exist = false)
    @ExcelProperty(index = 3)
    private Double base;
}
