package com.jp.med.ecs.modules.reimMgt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

import java.util.List;

/**
 * 发票校验记录
 * <AUTHOR>
 * @email -
 * @date 2024-05-28 14:34:49
 */
@Data
@TableName("ecs_invo_chk_rcd")
public class EcsInvoChkRcdEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 卖方单位/个人 */
	@TableField("seller_unit_or_individual")
	private String sellerUnitOrIndividual;

	/** 发票种类 */
	@TableField("invoice_type")
	private String invoiceType;

	/** 销方税号 */
	@TableField("saler_taxpayer_number")
	private String salerTaxpayerNumber;

	/** 销方地址、电话 */
	@TableField("saler_address_or_phone")
	private String salerAddressOrPhone;

	/** 销方开户账号 */
	@TableField("saler_bank_account")
	private String salerBankAccount;

	/** 购方名称 */
	@TableField("purchaser_name")
	private String purchaserName;

	/** 校验码 */
	@TableField("check_code")
	private String checkCode;

	/** 验真结果 */
	@TableField("cyjgxx")
	private String cyjgxx;

	/** 备注 */
	@TableField("note")
	private String note;

	/** 销方名称 */
	@TableField("saler_name")
	private String salerName;

	/** 发票金额 */
	@TableField("invoice_money")
	private String invoiceMoney;

	/** 作废标志 */
	@TableField("invalid_mark")
	private String invalidMark;

	/** 发票号码 */
	@TableField("invoice_number")
	private String invoiceNumber;

	/** 发票税额 */
	@TableField("all_tax")
	private String allTax;

	/** 价税合计 */
	@TableField("all_valorem_tax")
	private String allValoremTax;

	/** 开票日期-格式-yyyymmdd */
	@TableField("invoice_date")
	private String invoiceDate;

	/** 销方开户行及账号 */
	@TableField("saler_bank_and_number")
	private String salerBankAndNumber;

	/** 购方税号 */
	@TableField("purchaser_taxpayer_number")
	private String purchaserTaxpayerNumber;

	/** 发票记录id */
	@TableField("invo_rcd_id")
	private Integer invoRcdId;

	/** 完税凭证号码 */
	@TableField("after_tax_code")
	private String afterTaxCode;

	/** 蓝票发票代码 */
	@TableField("blue_invoice_code")
	private String blueInvoiceCode;

	/** 蓝票发票号码 */
	@TableField("blue_invoice_no")
	private String blueInvoiceNo;

	/** 厂牌型号 */
	@TableField("brand_version")
	private String brandVersion;

	/** 经营、拍卖单位 */
	@TableField("business_unit")
	private String businessUnit;

	/** 经营、拍卖单位纳税人识别号 */
	@TableField("business_unit_tax_no")
	private String businessUnitTaxNo;

	/** 经营、拍卖单位地址 */
	@TableField("busmess_unit_address")
	private String busmessUnitAddress;

	/** 开户银行及账号 */
	@TableField("busmess_unit_bank_and_account")
	private String busmessUnitBankAndAccount;

	/** 经营、拍卖单位电话 */
	@TableField("busmess_unit_phone")
	private String busmessUnitPhone;

	/** 车价合计 */
	@TableField("car_price")
	private String carPrice;

	/** 车辆类型 */
	@TableField("car_type")
	private String carType;

	/** 车种车号 */
	@TableField("car_type_and_number")
	private String carTypeAndNumber;

	/** 车辆识别代号/车架号码 */
	@TableField("carframe_code")
	private String carframeCode;

	/** 承运人名称 */
	@TableField("carrier_name")
	private String carrierName;

	/** 承运人识别号 */
	@TableField("carrier_tax_no")
	private String carrierTaxNo;

	/** 发票校验状态码(常用001:成功) */
	@TableField("code")
	private String code;

	/** 发货人名称 */
	@TableField("consignor_name")
	private String consignorName;

	/** 发货人识别号 */
	@TableField("consignor_tax_no")
	private String consignorTaxNo;

	/** 受票方名称 */
	@TableField("drawee_name")
	private String draweeName;

	/** 受票方识别号 */
	@TableField("drawee_tax_no")
	private String draweeTaxNo;

	/** 发动机号 */
	@TableField("engine_code")
	private String engineCode;

	/** 购方身份证号/组织机构代码 */
	@TableField("id_card")
	private String idCard;

	/** 进口证明书号 */
	@TableField("import_license")
	private String importLicense;

	/** 查验次数 */
	@TableField("inspection_amount")
	private String inspectionAmount;

	/** 商检单号 */
	@TableField("inspection_number")
	private String inspectionNumber;

	/** 发票代码 */
	@TableField("invoice_code")
	private String invoiceCode;

	/** 二手车市场 */
	@TableField("lemon_market")
	private String lemonMarket;

	/** 二手车市场地址 */
	@TableField("lemon_market_address")
	private String lemonMarketAddress;

	/** 二手车市场开户银行及账号 */
	@TableField("lemon_market_bank_and_account")
	private String lemonMarketBankAndAccount;

	/** 二手车市场电话 */
	@TableField("lemon_market_phone")
	private String lemonMarketPhone;

	/** 二手车市场纳税人识别号 */
	@TableField("lemon_market_tax_no")
	private String lemonMarketTaxNo;

	/** 合格证号 */
	@TableField("license_code")
	private String licenseCode;

	/** 车牌照号 */
	@TableField("license_plate")
	private String licensePlate;

	/** 限乘人数 */
	@TableField("limit_amount")
	private String limitAmount;

	/** 机器编号 */
	@TableField("machine_code")
	private String machineCode;

	/** 产地 */
	@TableField("producing_area")
	private String producingArea;

	/** 购方地址、电话 */
	@TableField("purchaser_address_or_phone")
	private String purchaserAddressOrPhone;

	/** 购方开户行及账号 */
	@TableField("purchaser_bank_and_number")
	private String purchaserBankAndNumber;

	/** 买方电话 */
	@TableField("purchaser_phone")
	private String purchaserPhone;

	/** 买方单位/个人 */
	@TableField("purchaser_unit_or_individual")
	private String purchaserUnitOrIndividual;

	/** 买方单位/个人住址 */
	@TableField("purchaser_unit_or_individual_address")
	private String purchaserUnitOrIndividualAddress;

	/** 买方单位代码/身份证号 */
	@TableField("purchaser_unitcode_or_idno")
	private String purchaserUnitcodeOrIdno;

	/** 收货人名称 */
	@TableField("receive_name")
	private String receiveName;

	/** 收货人识别号 */
	@TableField("receive_tax_no")
	private String receiveTaxNo;

	/** 登记证号 */
	@TableField("registration_no")
	private String registrationNo;

	/** 销方地址 */
	@TableField("saler_address")
	private String salerAddress;

	/** 销方开户银行 */
	@TableField("saler_bank_name")
	private String salerBankName;

	/** 销方电话 */
	@TableField("saler_phone")
	private String salerPhone;

	/** 卖方电话 */
	@TableField("seller_phone")
	private String sellerPhone;

	/** 卖方单位代码/身份证号 */
	@TableField("seller_unit_code_or_idno")
	private String sellerUnitCodeOrIdno;

	/** 卖方单位/个人住址 */
	@TableField("seller_unit_or_individual_address")
	private String sellerUnitOrIndividualAddress;

	/** 税控盘号 */
	@TableField("tax_disk_number")
	private String taxDiskNumber;

	/** 税率 */
	@TableField("tax_rate")
	private String taxRate;

	/** 主管税务机关代码 */
	@TableField("tax_unit_code")
	private String taxUnitCode;

	/** 主管税务机关名称 */
	@TableField("tax_unit_name")
	private String taxUnitName;

	/** 起运地、经由、到达地 */
	@TableField("through_address")
	private String throughAddress;

	/** 通行费标准 */
	@TableField("traffic_fee_flag")
	private String trafficFeeFlag;

	/** 转入地车辆车管所名称 */
	@TableField("transferred_vehicle_office")
	private String transferredVehicleOffice;

	/** 运输货物信息 */
	@TableField("transport_goods_info")
	private String transportGoodsInfo;

	/** 吨位 */
	@TableField("unit")
	private String unit;

	/** 车船吨位 */
	@TableField("vehicle_tonnage")
	private String vehicleTonnage;

	/** 零税率标识 */
	@TableField("zero_tax_rate_flag")
	private String zeroTaxRateFlag;

	@TableField(exist = false)
	private List<EcsInvoChkRcdDetailEntity> detailList;

}
