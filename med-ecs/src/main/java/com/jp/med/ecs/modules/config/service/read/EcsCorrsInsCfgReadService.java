package com.jp.med.ecs.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ecs.modules.config.dto.EcsCorrsInsCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsCorrsInsCfgVo;

import java.util.List;

/**
 * 往来单位配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-30 19:37:36
 */
public interface EcsCorrsInsCfgReadService extends IService<EcsCorrsInsCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsCorrsInsCfgVo> queryList(EcsCorrsInsCfgDto dto);
}

