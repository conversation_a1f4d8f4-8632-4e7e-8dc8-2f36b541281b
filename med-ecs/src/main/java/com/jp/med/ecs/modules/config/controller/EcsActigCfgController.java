package com.jp.med.ecs.modules.config.controller;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.util.ChineseCharToEnUtil;
import com.jp.med.common.util.DateUtil;
import com.jp.med.ecs.modules.config.dto.EcsActigCfgDto;
import com.jp.med.ecs.modules.config.service.read.EcsActigCfgReadService;
import com.jp.med.ecs.modules.config.service.write.EcsActigCfgWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;


/**
 * 会计科目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 10:53:01
 */
@Api(value = "会计科目配置", tags = "会计科目配置")
@RestController
@RequestMapping("ecsActigCfg")
public class EcsActigCfgController {

    @Autowired
    private EcsActigCfgReadService ecsActigCfgReadService;

    @Autowired
    private EcsActigCfgWriteService ecsActigCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询会计科目配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsActigCfgDto dto){
        return CommonResult.success(ecsActigCfgReadService.queryList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询会计科目配置")
    @PostMapping("/listLayFlat")
    public CommonResult<?> listBy(@RequestBody EcsActigCfgDto dto){
        return CommonResult.success(ecsActigCfgReadService.queryListLayFlat(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询会计科目配置")
    @PostMapping("/auxItem")
    public CommonResult<?> auxItem(@RequestBody EcsActigCfgDto dto){
        return CommonResult.success(ecsActigCfgReadService.queryAuxItem(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增会计科目配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsActigCfgDto dto){
        setEmpType(dto);
        if (StringUtils.isNotEmpty(dto.getStatus())) {
            // 财务会计科目
            if (MedConst.TYPE_3.equals(dto.getStatus())) {
                dto.setActigSys(MedConst.TYPE_1);
            } else {
                // 预算会计科目
                dto.setActigSys(MedConst.TYPE_2);
            }
        }
        dto.setCrter(dto.getSysUser().getUsername());
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        dto.setPinyin(ChineseCharToEnUtil.getAllFirstLetter(dto.getSubName()).toUpperCase());
        ecsActigCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改会计科目配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsActigCfgDto dto){
        setEmpType(dto);
        dto.setModiTime(DateUtil.getCurrentTime(null));
        ecsActigCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    private void setEmpType(EcsActigCfgDto dto){
        if (dto.getEmpTypeArr() != null) {
            dto.setEmpType(Arrays.toString(dto.getEmpTypeArr()).replaceAll("\\[|\\]", "").replace(", ", ","));
        }
        if (dto.getAsstInfoArr() != null) {
            dto.setAsstInfo(Arrays.toString(dto.getAsstInfoArr()).replaceAll("\\[|\\]", "").replace(", ", ","));
        }
    }

    /**
     * 删除
     */
    @ApiOperation("删除会计科目配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsActigCfgDto dto){
        ecsActigCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 同步
     */
    @ApiOperation("查询会计科目配置")
    @PostMapping("/sync")
    public CommonResult<?> sync(@RequestBody EcsActigCfgDto dto){
        ecsActigCfgWriteService.sync(dto);
        return CommonResult.success();
    }
}
