package com.jp.med.ecs.modules.reimMgt.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 应发工资报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 14:45:03
 */
@Data
public class EcsReimSalaryTaskVo {

	/** id */
	private Integer id;

	/** 工资任务发放月份 */
	private String ffMth;

	/** 工资条数 */
	private Integer num;

	/** 应发合计汇总 */
	private BigDecimal shouldPay;

	/** 扣款合计汇总 */
	private BigDecimal reducePay;

	/** 实发合计汇总 */
	private BigDecimal realPay;

	/** 备注 */
	private String remark;

	/** 制表人 */
	private String crter;

	/** 制表时间 */
	private String crteTime;

	/** 是否报销 */
	private String reimFlag;

	/** 报销id **/
	private Integer reimId;

	/** 工资任务id **/
	private Integer salaryId;

	/** 工资类型 **/
	private String salaryType;

	/**
	 * 流程实例id
	 */
	private String processInstanceId;

}
