package com.jp.med.ecs.modules.drugMgt.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.ecs.modules.drugMgt.dto.EcsStoinDetailDto;
import com.jp.med.common.dto.ecs.drug.EcsStoinDto;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsStoinReadMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.write.EcsStoinWriteMapper;
import com.jp.med.ecs.modules.drugMgt.service.write.EcsStoinWriteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Reader;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 入库单
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:44
 */
@Service
@Transactional(readOnly = false)
@Slf4j
public class EcsStoinWriteServiceImpl extends ServiceImpl<EcsStoinWriteMapper, EcsStoinDto> implements EcsStoinWriteService {

    @Autowired
    private EcsStoinReadMapper ecsStoinReadMapper;

    @Autowired
    private EcsStoinWriteMapper ecsStoinWriteMapper;

    private static final int BATCH_SIZE = 500; // 每次批处理的大小

    @Override
    public void drugSync(EcsStoinDto dto) {


        //获取当前入库单最大序号
        int floor = ecsStoinReadMapper.queryMaxXh(MedConst.TYPE_0);
        log.info("--------当前floor-------"+ floor);
        //获取当前入库单退货最大序号
        int backFloor = ecsStoinReadMapper.queryMaxXh(MedConst.TYPE_1);
        log.info("--------当前backFloor-------"+ backFloor);
        //查询大于序号的 批量数为batch 入库单集合

        String resource = "winning-mybatis-config.xml";
        Reader reader;

        SqlSessionFactory sqlSessionFactorySource = null;
        try{
            reader = Resources.getResourceAsReader(resource);
            SqlSessionFactoryBuilder builder = new SqlSessionFactoryBuilder();
            sqlSessionFactorySource = builder.build(reader,"winning");
        } catch(Exception e) {
            log.error("读取数据失败");
            throw new AppException("创建连接失败");
        }

        try(SqlSession sessionSource = sqlSessionFactorySource.openSession()){

            EcsStoinReadMapper sourceMapper = sessionSource.getMapper(EcsStoinReadMapper.class);

            int offset = 0;
            List<EcsStoinDto> stoins;
            List<EcsStoinDto> stoinBacks;

            //同步入库单信息
            do{

                stoins = sourceMapper.list(floor, offset, BATCH_SIZE);

                if (!stoins.isEmpty()) {
                    List<Integer> stoinIds = stoins.stream().map(EcsStoinDto::getXh).collect(Collectors.toList());
                    //查询入库单明细
                    List<EcsStoinDetailDto> stoinDetails = sourceMapper.listStoinDetails(stoinIds);

                    //，插入明细数据
                    ecsStoinWriteMapper.saveDrugStoinDetail(stoinDetails);

                    //插入入库单数据
                    ecsStoinWriteMapper.saveDrugStoin(stoins);

                    offset += BATCH_SIZE;
                }
            }while(!stoins.isEmpty());

            offset = 0;
            //同步退货信息
            do {
                stoinBacks = sourceMapper.listStoinBack(backFloor, offset, BATCH_SIZE);
                if (!stoinBacks.isEmpty()) {
                    List<Integer> stoinBackIds = stoinBacks.stream().map(EcsStoinDto::getXh).collect(Collectors.toList());
                    //查询入库退货明细
                    List<EcsStoinDetailDto> stoinBackDetails = sourceMapper.listStoinBackDetails(stoinBackIds);

                    //插入明细数据
//                    ecsStoinBackWriteMapper.saveDrugStoinBackDetail(stoinBackDetails);
                    ecsStoinWriteMapper.saveDrugStoinDetail(stoinBackDetails);

                    //插入入库单退货数据
//                    ecsStoinBackWriteMapper.saveDrugStoinBack(stoinBacks);
                    ecsStoinWriteMapper.saveDrugStoin(stoinBacks);

                    offset += BATCH_SIZE;
                }
            } while(!stoinBacks.isEmpty());
        } catch(Exception e) {
            log.error("同步失败",e);
            throw new AppException("同步数据失败");
        }
    }
}
