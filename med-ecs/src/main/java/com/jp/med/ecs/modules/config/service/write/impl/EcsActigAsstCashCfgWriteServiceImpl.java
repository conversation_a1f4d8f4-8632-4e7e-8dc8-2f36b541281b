package com.jp.med.ecs.modules.config.service.write.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ecs.modules.config.dto.EcsActigAsstCashCfgDto;
import com.jp.med.ecs.modules.config.mapper.write.EcsActigAsstCashCfgWriteMapper;
import com.jp.med.ecs.modules.config.service.write.EcsActigAsstCashCfgWriteService;
import com.jp.med.ecs.modules.config.utils.YySyncUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 现金流量配置
 * <AUTHOR>
 * @email -
 * @date 2023-12-08 14:03:00
 */
@Service
@Transactional(readOnly = false)
public class EcsActigAsstCashCfgWriteServiceImpl extends ServiceImpl<EcsActigAsstCashCfgWriteMapper, EcsActigAsstCashCfgDto> implements EcsActigAsstCashCfgWriteService {

    @Autowired
    private EcsActigAsstCashCfgWriteMapper ecsActigAsstCashCfgWriteMapper;

    @Value("${urls.mid.yy.asst-cash-sync}")
    private String asstCashSyncUrl;

    private static final int BATCH_SIZE = 500;  //批处理大小

    @Override
    public void sync(EcsActigAsstCashCfgDto dto) {


        int start = 1;
        int end = start + BATCH_SIZE;
//        int end = 2;
        Map<String,Object> params = new HashMap<>();
        params.put("year",dto.getYear());

        /*params.put("start",start);
        params.put("end",end);
        List<EcsEconFunSubCfgDto> efcDtos = doSync(econFunSyncUrl, params, EcsEconFunSubCfgDto.class);
        //处理parentCode
        efcDtos.forEach(item -> {
            if (item.getSubCode().length()> 3) {
                item.setParentCode(item.getSubCode().substring(0,item.getSubCode().length()-2));
            }
        });
        ecsEconFunSubCfgWriteMapper.saveEcsEconSubCfg(efcDtos);*/

        //删除某年度现金流量科目
        LambdaQueryWrapper<EcsActigAsstCashCfgDto> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EcsActigAsstCashCfgDto::getYear,dto.getYear());
        ecsActigAsstCashCfgWriteMapper.delete(wrapper);

        List<EcsActigAsstCashCfgDto> efcDtos;
        do{
            params.put("start",start);
            params.put("end",end);

            efcDtos = YySyncUtils.doSync(asstCashSyncUrl, params, EcsActigAsstCashCfgDto.class);
            if (!efcDtos.isEmpty()) {
                String crter = dto.getSysUser().getHrmUser().getEmpCode();
                //处理parentCode
                efcDtos.forEach(item -> {
                    item.setCrter(crter);
                    /*if (StringUtils.isNotEmpty(item.getFlowDire())) {
                        //现金流向 1：入 2：出
                        item.setFlowDire(StringUtils.equals(item.getFlowDire(),"入")? MedConst.TYPE_1:MedConst.TYPE_2);
                    }*/
                    if (item.getSubCode().length()> 2) {
                        item.setParentSubCode(item.getSubCode().substring(0,item.getSubCode().length()-2));
                    }
                });

                //插入现金流量
                ecsActigAsstCashCfgWriteMapper.saveAsstCashCfg(efcDtos);

                start +=BATCH_SIZE;
                end +=BATCH_SIZE;
            }
        }while(!efcDtos.isEmpty());
    }
}
