package com.jp.med.ecs.modules.reimMgt.service.read.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.MedConst;
import com.jp.med.ecs.modules.reimMgt.dto.EcsInvoRcdDto;
import com.jp.med.ecs.modules.reimMgt.entity.EcsInvoChkRcdDetailEntity;
import com.jp.med.ecs.modules.reimMgt.entity.EcsInvoChkRcdEntity;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsInvoRcdReadMapper;
import com.jp.med.ecs.modules.reimMgt.service.read.EcsInvoRcdReadService;
import com.jp.med.ecs.modules.reimMgt.vo.EcsInvoRcdVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Transactional(readOnly = true)
@Service
public class EcsInvoRcdReadServiceImpl extends ServiceImpl<EcsInvoRcdReadMapper, EcsInvoRcdDto> implements EcsInvoRcdReadService {

    @Autowired
    private EcsInvoRcdReadMapper ecsInvoRcdReadMapper;

    @Override
    public List<EcsInvoRcdVo> queryList(EcsInvoRcdDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return ecsInvoRcdReadMapper.queryList(dto);
    }

    @Override
    public Map<String, Object> queryEcsInvoRcdChk(EcsInvoRcdDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        List<EcsInvoChkRcdEntity> chkRcdList = ecsInvoRcdReadMapper.queryChkRcdByInvoId(dto);

        List<EcsInvoChkRcdDetailEntity> chkRcdDetailList = ecsInvoRcdReadMapper.queryChkRcdDetailByInvoId(dto);

        return new HashMap<>(){
            {
                put("chkRcds",chkRcdList);
                put("chkRcdDetails",chkRcdDetailList);
            }
        };
    }

    @Override
    public Integer queryInvoAuditWarnNum(EcsInvoRcdDto dto) {
        dto.setStatus(MedConst.TYPE_1);
        dto.setPageNum(1);
        dto.setPageSize(99999);
        return queryList(dto).size();
    }

    @Override
    public List<EcsInvoRcdVo> queryEcsInvoRcdRecogn(EcsInvoRcdDto dto) {
        //发票ids去重
        Set<Integer> set = new HashSet<>(dto.getIds());
        LambdaQueryWrapper<EcsInvoRcdDto> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EcsInvoRcdDto::getId,set);
        //查询parentInvo
        List<EcsInvoRcdDto> ecsInvoRcdDtos = ecsInvoRcdReadMapper.selectList(wrapper);
        //提取subInvo
        Set<Integer> subInvos = new HashSet<>();
        ecsInvoRcdDtos.stream().forEach(e -> {
            String[] split = StringUtils.split(e.getSubInvoIds(), ",");
            for (String s : split) {
                subInvos.add(Integer.parseInt(s));
            }
        });
        //查询子发票信息
        LambdaQueryWrapper<EcsInvoRcdDto> subWrapper = Wrappers.lambdaQuery();
        subWrapper.in(EcsInvoRcdDto::getId,subInvos);
        List<EcsInvoRcdDto> subInvoDtos = ecsInvoRcdReadMapper.selectList(subWrapper);
        List<EcsInvoRcdVo> result = new ArrayList<>();
        subInvoDtos.stream().forEach(e -> {
            EcsInvoRcdVo a = new EcsInvoRcdVo();
            BeanUtils.copyProperties(e,a);
            result.add(a);
        });
        return result;
    }
}
