<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.emp.mapper.write.EmpEmployeeInfoWriteMapper">
    <insert id="saveEmployeeInfo">
        insert into hrm_employee_info (emp_code,
                                       emp_name,
                                       hospital_id,
                                       org_id,
                                       job,
                                       icd_card,
                                       birthday,
                                       age,
                                       phone,
                                       status,
                                       icd_card_type,
                                       sex)
        values (#{empCode},
                #{empName},
                #{hospitalId},
                #{orgId},
                #{job},
                #{icdCard},
                #{birthday},
                #{age},
                #{phone},
                #{status},
                #{icdCardType},
                #{sex})
    </insert>

    <!-- 新增操作日志 -->
    <insert id="addOperateLog">
        INSERT INTO hrm_emp_modify_log(emp_id,
                                       modi_type,
                                       modi_modu,
                                       modi_tab_name,
                                       modi_field_code,
                                       modi_field_name,
                                       old_val,
                                       new_val,
                                       old_display_val,
                                       new_display_val,
                                       opter,
                                       create_time,
                                       hospital_id,
                                       is_file,
                                       asoc_tab_id,
                                       stas,
                                       exe_date)
        VALUES (#{empId,jdbcType=INTEGER},
                #{type,jdbcType=VARCHAR},
                #{module,jdbcType=VARCHAR},
                #{tableName,jdbcType=VARCHAR},
                #{key,jdbcType=VARCHAR},
                #{title,jdbcType=VARCHAR},
                #{originValue,jdbcType=VARCHAR},
                #{presentValue,jdbcType=VARCHAR},
                #{displayOriginValue,jdbcType=VARCHAR},
                #{displayPresentValue,jdbcType=VARCHAR},
                #{opter,jdbcType=VARCHAR},
                #{createTime,jdbcType=VARCHAR},
                #{hospitalId,jdbcType=VARCHAR},
                #{isFile,jdbcType=VARCHAR},
                #{id,jdbcType=INTEGER},
                #{stas,jdbcType=VARCHAR},
                #{infoExeDate})
    </insert>

    <update id="updateEmployeeInfo">
        update hrm_employee_info
        <set>
            <include refid="commonUpdate">
            </include>
            <if test="icdCardType != null and icdCardType != ''">
                icd_card_type = #{icdCardType,jdbcType=VARCHAR},
            </if>
            <if test="sex != null and sex != ''">
                sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="endTime != null and endTime != ''">
                end_time = #{endTime,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null and startTime != ''">
                start_time = #{startTime,jdbcType=VARCHAR},
            </if>
            <if test="joinJobTime != null and joinJobTime != ''">
                join_job_time = #{joinJobTime,jdbcType=VARCHAR},
            </if>
            <if test="bloodType != null and bloodType != ''">
                blood_type = #{bloodType,jdbcType=VARCHAR},
            </if>
            <if test="email != null and email != ''">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="marriageCondition != null and marriageCondition != ''">
                marriage_condition = #{marriageCondition,jdbcType=VARCHAR},
            </if>
            <if test="mz != null and mz != ''">
                mz = #{mz,jdbcType=VARCHAR},
            </if>
            <if test="registered != null and registered != ''">
                registered = #{registered,jdbcType=VARCHAR},
            </if>
            <if test="nativePlace != null and nativePlace != ''">
                native_place = #{nativePlace,jdbcType=VARCHAR},
            </if>
            <if test="censusRegister != null and censusRegister != ''">
                census_register = #{censusRegister,jdbcType=VARCHAR},
            </if>
            <if test="birthPlace != null and birthPlace != ''">
                birth_place = #{birthPlace,jdbcType=VARCHAR},
            </if>
            <if test="communicationAddress != null and communicationAddress != ''">
                communication_address = #{communicationAddress,jdbcType=VARCHAR},
            </if>
            <if test="hobby != null and hobby != ''">
                hobby = #{hobby,jdbcType=VARCHAR},
            </if>
            <if test="healthCondition != null and healthCondition != ''">
                health_condition = #{healthCondition,jdbcType=VARCHAR},
            </if>
            <if test="politicsStatus != null and politicsStatus != ''">
                politics_status = #{politicsStatus,jdbcType=VARCHAR},
            </if>
            <if test="jobType != null and jobType != ''">
                job_type = #{jobType,jdbcType=VARCHAR},
            </if>
            <if test="enterChannel != null and enterChannel != ''">
                enter_channel = #{enterChannel,jdbcType=VARCHAR},
            </if>
            <if test="toCompanyTime != null and toCompanyTime != ''">
                to_company_time = #{toCompanyTime,jdbcType=VARCHAR},
            </if>
            <if test="blacklist != null and blacklist != ''">
                blacklist = #{blacklist,jdbcType=VARCHAR},
            </if>
            <if test="portraitPath != null and portraitPath != ''">
                portrait_path = #{portraitPath,jdbcType=VARCHAR}
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <!-- 修改值 -->
    <update id="update2">
        UPDATE ${tableName}
        <set>
            <foreach collection="list" item="item" separator=",">
                ${item.key} = #{item.presentValue}
            </foreach>
        </set>
        WHERE id = #{id}
    </update>
    <update id="update3">
        UPDATE ${tableName}
        SET ${fieldName}     = #{filePath,jdbcType=VARCHAR},
            ${fileFieldName} = #{fileName,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 逻辑删除 -->
    <update id="update4">
        UPDATE ${tableName}
        SET is_deleted = #{isDelete,jdbcType=INTEGER}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 修改审核状态 -->
    <update id="updateLogState">
        UPDATE hrm_emp_modify_log
        SET stas = #{stas,jdbcType=VARCHAR},
        chker = #{empCode,jdbcType=VARCHAR},
        chk_time = #{chkTime,jdbcType=VARCHAR},
        chk_remarks = #{remark,jdbcType=VARCHAR}
        WHERE id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <!-- 回退数据 -->
    <update id="rollbackData">
        UPDATE ${tableName}
        <set>
            <foreach collection="data" item="item" separator=",">
                ${item.key} = #{item.value}
            </foreach>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 修改管理员值 -->
    <update id="updateManagerValue">
        UPDATE hrm_emp_modify_log
        SET mgt_val = #{value,jdbcType=VARCHAR}
        WHERE id = cast(#{id,jdbcType=INTEGER} as INT)
    </update>

    <sql id="commonUpdate">
        <if test="empCode != null and empCode != ''">
            emp_code = #{empCode,jdbcType=VARCHAR},
        </if>
        <if test="empName != null and empName != ''">
            emp_name = #{empName,jdbcType=VARCHAR},
        </if>
        <if test="hospitalId != null and hospitalId != ''">
            hospital_id = #{hospitalId,jdbcType=VARCHAR},
        </if>
        <if test="orgId != null and orgId != ''">
            org_id = #{orgId,jdbcType=VARCHAR},
        </if>
        <if test="job != null and job != ''">
            job = #{job,jdbcType=VARCHAR},
        </if>
        <if test="icdCard != null and icdCard != ''">
            icd_card = #{icdCard,jdbcType=VARCHAR},
        </if>
        <if test="birthday != null and birthday != ''">
            birthday = #{birthday,jdbcType=VARCHAR},
        </if>
        <if test="age != null and age != ''">
            age = #{age,jdbcType=VARCHAR},
        </if>
        <if test="phone != null and phone != ''">
            phone = #{phone,jdbcType=VARCHAR},
        </if>
        <if test="status != null and status != ''">
            status = #{status,jdbcType=VARCHAR},
        </if>
    </sql>

    <update id="updateManagerFile">
        UPDATE hrm_emp_modify_log
        SET mgt_val      = #{filesPath,jdbcType=VARCHAR},
            mgt_val_name = #{filesName,jdbcType=VARCHAR}
        WHERE id = cast(#{id,jdbcType=INTEGER} as INT)
    </update>

    <!-- 劳务合同，合同登记弹框填入试用期时间（如果非试用，暂不填转正时间） -->
    <update id="updateTrailTimeById">
        update hrm_employee_info
        set start_time = #{startTime},
            end_time   = #{endTime}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="logicalDeleteEmp">
        update hrm_employee_info
        set is_deleted= 1
        where id = #{empId,jdbcType=INTEGER}
    </update>

    <!--修改职工 科室-->
    <update id="updateEmpInfo">
        update hrm_employee_info
        set ${fieldName} = #{orgId,jdbcType=VARCHAR}
        where id = #{empId,jdbcType=INTEGER}
    </update>
</mapper>
