<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.emp.mapper.read.EmpEmployeeTypeReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.hrm.modules.emp.vo.EmpEmployeeTypeVo" id="employeeTypeMap">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="certificateNo" column="certificate_no"/>
        <result property="changeTime" column="change_time"/>
        <result property="file" column="file"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="empId" column="emp_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeTypeVo">
        select
            id as id,
            type as type,
            certificate_no as certificateNo,
            change_time as changeTime,
            file as file,
            remark as remark,
            status as status,
            is_deleted as isDeleted,
            hospital_id as hospitalId,
            emp_id as empId
        from hrm_employee_type
    </select>

</mapper>
