<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.emp.mapper.read.EmpWorkExperienceReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.hrm.modules.emp.vo.EmpWorkExperienceVo" id="workExperienceMap">
        <result property="id" column="id"/>
        <result property="workStartTime" column="work_start_time"/>
        <result property="workEndTime" column="work_end_time"/>
        <result property="workUnit" column="work_unit"/>
        <result property="orgId" column="org_id"/>
        <result property="post" column="post"/>
        <result property="position" column="position"/>
        <result property="type" column="type"/>
        <result property="mainExperience" column="main_experience"/>
        <result property="proveUsername" column="prove_username"/>
        <result property="workAge" column="work_age"/>
        <result property="file" column="file"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="empId" column="emp_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.hrm.modules.emp.vo.EmpWorkExperienceVo">
        select
            id as id,
            work_start_time as workStartTime,
            work_end_time as workEndTime,
            work_unit as workUnit,
            org_id as orgId,
            post as post,
            position as position,
            type as type,
            main_experience as mainExperience,
            prove_username as proveUsername,
            work_age as workAge,
            file as file,
            remark as remark,
            status as status,
            is_deleted as isDeleted,
            hospital_id as hospitalId,
            emp_id as empId
        from hrm_work_experience
    </select>

</mapper>
