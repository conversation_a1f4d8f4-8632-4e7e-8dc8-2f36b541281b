<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.emp.mapper.read.EmpScientificItemReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.hrm.modules.emp.vo.EmpScientificItemVo" id="scientificItemMap">
        <result property="id" column="id"/>
        <result property="endTime" column="end_time"/>
        <result property="itemName" column="item_name"/>
        <result property="aprvEmp" column="aprv_emp"/>
        <result property="itemResper" column="item_resper"/>
        <result property="myRank" column="my_rank"/>
        <result property="remark" column="remark"/>
        <result property="att" column="att"/>
        <result property="attName" column="att_name"/>
        <result property="empId" column="emp_id"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.hrm.modules.emp.vo.EmpScientificItemVo">
        select
            id as id,
            end_time as endTime,
            item_name as itemName,
            aprv_emp as aprvEmp,
            item_resper as itemResper,
            my_rank as myRank,
            remark as remark,
            att as att,
            att_name as attName,
            emp_id as empId,
            is_deleted as isDeleted,
            hospital_id as hospitalId
        from hrm_scientific_item
    </select>

</mapper>
