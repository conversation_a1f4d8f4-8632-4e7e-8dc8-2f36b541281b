<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.emp.mapper.read.EmpEmployeeInfoReadMapper">
    <!--首页显示员工基本信息-->
    <sql id="employeeField">
        a.id                 as id,
        a.emp_code           as empCode,
        a.emp_name           as empName,
        a.hospital_id        as hospitalId,
        a.org_id             as orgId,
        a.job                as job,
        a.icd_card           as icdCard,
        a.birthday           as birthday,
        a.politics_status    as politicsStatus,
        a.age                as age,
        a.phone              as phone,
        a.status             as status,
        a.emp_type           as empType,
        a.icd_card_type      as icdCardType,
        a.transferred_time   as transferredTime,
        a.sex                as sex,
        a.portrait_path      as portraitPath,
        a.portrait_path_name as portraitPathName,
        a.confirmation_date  as confirmationDate
    </sql>

    <!--详情页员工基本信息-->
    <sql id="employeeField2">
        id,
        emp_code               as empCode,
        emp_name               as empName,
        emp_type               as empType,
        hospital_id            as hospitalId,
        org_id                 as orgId,
        job                    as job,
        icd_card               as icdCard,
        birthday               as birthday,
        phone                  as phone,
        status                 as status,
        status_chg_date        as statusChgDate,
        status_chg_dscr        as statusChgDscr,
        icd_card_type          as icdCardType,
        sex                    as sex,
        end_time               as endTime,
        start_time             as startTime,
        join_job_time          as joinJobTime,
        blood_type             as bloodType,
        email                  as email,
        marriage_condition     as marriageCondition,
        mz                     as mz,
        registered             as registered,
        native_place           as nativePlace,
        census_register        as censusRegister,
        birth_place            as birthPlace,
        communication_address  as communicationAddress,
        hobby                  as hobby,
        health_condition       as healthCondition,
        politics_status        as politicsStatus,
        job_type               as jobType,
        enter_channel          as enterChannel,
        to_company_time        as toCompanyTime,
        blacklist              as blacklist,
        portrait_path          as portraitPath,
        portrait_path_name     as portraitPathName,
        archive_num            as archiveNum,
        archive_location       as archiveLocation,
        person_height          as personHeight,
        person_weight          as personWeight,
        icd_validity_date      as icdValidityDate,
        funds_nature           as fundsNature,
        employ_nature          as employNature,
        phone2                 as phone2,
        sort_phone             as sortPhone,
        religious_identity     as religiousIdentity,
        confirmation_date      as confirmationDate,
        prebationary_result    as prebationaryResult,
        prebationary_condition as prebationaryCondition,
        icd_file               as icdFile,
        remark                 as remark,
        archive_birthday       as archiveBirthday,
        compile_time           as compileTime,
        transferred_time       as transferredTime,
        seniority_sure_time    as senioritySureTime,
        is_deleted             as isDeleted,
        ajt_org_ids            as ajtOrgIdsStr,
        confirmation_remark    as confirmationRemark,
        leave_date             as leaveDate,
        leave_remark           as leaveRemark,
        exit_date              as exitDate,
        exit_remark            as exitRemark,
        retire_date            as retireDate,
        retire_ramark          as retireRamark
    </sql>

    <!--劳务合同显示员工基本信息-->
    <sql id="employeeContractField">
        a.id                 as id,
        a.emp_code           as empCode,
        a.emp_name           as empName,
        a.emp_type           as empType,
        a.hospital_id        as hospitalId,
        a.org_id             as orgId,
        a.job                as job,
        a.icd_card           as icdCard,
        a.birthday           as birthday,
        a.politics_status    as politicsStatus,
        a.age                as age,
        a.phone              as phone,
        a.status             as status,
        a.icd_card_type      as icdCardType,
        a.sex                as sex,
        a.portrait_path      as portraitPath,
        a.portrait_path_name as portraitPathName
    </sql>

    <!--查询员工基本信息集合-->
    <select id="queryList" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeInfoVo">
        select x.*,
               y.org_parent_id as orgParentId,
               y.org_name      as orgName
        from
        (
        select
        <include refid="employeeField">
        </include>
        from hrm_employee_info a
        <where>
            <if test="empCode != null and empCode != ''">
                and a.emp_code = #{empCode}
            </if>
            and a.is_deleted != 1
        </where>
        )x
        <choose>
            <when test="orgId != null and orgId != ''">
                inner join (select *
                            from (WITH RECURSIVE temp AS (SELECT *
                                                          FROM hrm_org r
                                                          where org_id = #{orgId}
                                                          union all
                                                          select b.*
                                                          from hrm_org b,
                                                               temp t
                                                          where b.org_parent_id = t.org_id)
                                  SELECT *
                                  FROM temp) z) y
            </when>
            <when test="orgIds != null and orgIds.size > 0">
                inner join (
                select * from (
                WITH RECURSIVE temp AS(
                SELECT *
                FROM hrm_org r
                where
                org_id in
                <foreach collection="orgIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                union all
                select b.*
                from hrm_org b,
                     temp t
                where b.org_parent_id = t.org_id
                )
                SELECT *
                FROM temp
                )z
                )y
            </when>
            <otherwise>
                left join hrm_org y
            </otherwise>
        </choose>
        on x.orgId = y.org_id
        <where>
            <if test="empType != null and empType != ''">
                x.empType = #{empType}
            </if>
        </where>
        order by cast(x.empCode as INTEGER)
    </select>

    <!--根据员工编号查询基本信息（新增员工判重）-->
    <select id="queryByEmpCode" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeInfoVo">
        SELECT a.id,
               a.emp_code      AS empCode,
               a.emp_name      AS empName,
               a.org_id        as orgId,
               a.portrait_path as portraitPath,
               a.ajt_org_ids   as ajtOrgIds,
               a.ajt_org_ids   as ajtOrgIdsStr,
               b.org_name      AS orgName,
               c.ori_org       AS oriOrg
        FROM hrm_employee_info a
                 LEFT JOIN hrm_org b
                           ON a.org_id = b.org_id
                 LEFT JOIN hrm_org_map c
                           on a.org_id = c.org_id
        WHERE A.emp_code = #{empCode}
          and (is_deleted is NULL or is_deleted != 1)
    </select>

    <!--查询员工基本信息（详情界面）-->
    <select id="queryBaseData" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeInfoVo">
        select a.*, b.org_parent_id as orgParentId, b.org_name as orgName
        from (
        select
        <include refid="employeeField2">
        </include>
        from hrm_employee_info
        <where>
            <if test="empId != null and empId != ''">
                and id = #{empId}
            </if>
            <if test="empCode != null and empCode != ''">
                and emp_code = #{empCode}
            </if>
            <if test="empName != null and empName != ''">
                and emp_name = #{empName}
            </if>
            and (is_deleted is NULL or is_deleted != 1)
        </where>
        ) as a
            left join hrm_org as b
                      ON a.orgId = b.org_id
    </select>

    <!-- 查询下拉 -->
    <select id="querySelection" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeInfoVo">
        select x.*
        from
        (
        select b.org_parent_id as orgParentId,
               b.org_name      as orgName,
               c.code_value    as empTypeValue,
               c.code_lable    as empTypeName,
               D.code_value    as practicingType,
               D.code_lable    as practicingTypeName,
        <include refid="employeeField"/>
        from hrm_employee_info a
            left join hrm_org b on a.org_id = b.org_id
            left join hrm_employee_dict c on a.emp_type::INTEGER = c.id
        <!--执业资格-->
        left join (SELECT b.code_value,
                          B.code_lable,
                          A.*
                   FROM (SELECT *
                         from (SELECT A.*,
                                      MAX(A.ID) OVER ( PARTITION BY A.emp_id ) AS max_id2
                               FROM (SELECT MAX
                                                (A.give_time) OVER ( PARTITION BY A.emp_id ) AS max_time,
                                            MAX(A.ID) OVER ( PARTITION BY A.emp_id )         AS max_id,
                                            A.*
                                     FROM hrm_practicing_requirement A
                                     WHERE A.is_deleted IS NULL
                                        OR A.is_deleted != 1) A
                               WHERE (A.give_time = A.max_time)
                                  OR (A.max_time IS NULL AND A.ID = A.max_id)) A
                         where A.id = A.max_id2) A
                            LEFT JOIN hrm_employee_dict b ON CAST(A.practicing_type AS INTEGER) = b.ID) D
                  on A.id = D.emp_id
        where A.is_deleted != 1
        <if test="empCodeOrEmpName != null and empCodeOrEmpName != ''">
            and (emp_name like concat('%', #{empCodeOrEmpName}, '%') or emp_code like
                                                                        concat('%', #{empCodeOrEmpName}, '%')
                or b.org_id in
                   (select org_id
                    from (WITH RECURSIVE temp AS (SELECT *
                                                  FROM hrm_org r
                                                  where org_name like
                                                        concat('%', concat(#{empCodeOrEmpName}, '%'))
                                                  union all
                                                  select b.*
                                                  from hrm_org b,
                                                       temp t
                                                  where b.org_parent_id = t.org_id)
                          SELECT *
                          FROM temp) z)
                )
        </if>
        <if test="restrictedEmpType != null and restrictedEmpType != ''">
            and exists (select *
                        from hrm_administrative_rank b
                        where a.id = b.emp_id
                          and end_time is null
                          and position(rank in #{restrictedEmpType}) > 0)
        </if>
        <if test="restrictedEmps != null and restrictedEmps != ''">
            and position(a.emp_code in #{restrictedEmps}) > 0
        </if>
        <if test="empCodeList != null and empCodeList.size() != 0">
            and a.emp_code in

            <foreach collection="empCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) x
        <if test="orgId != null and orgId != ''">
            inner join
            (select *
             from (WITH RECURSIVE temp AS (SELECT *
                                           FROM hrm_org r
                                           where org_id = #{orgId}
                                           union all
                                           select b.*
                                           from hrm_org b,
                                                temp t
                                           where b.org_parent_id = t.org_id)
                   SELECT *
                   FROM temp) z) y
            on x.orgId = y.org_id
        </if>
        <if test="orgIds != null and orgIds.size() > 0">
            inner join
            (
            select * from (
            WITH RECURSIVE temp AS (
            SELECT *
            FROM hrm_org r
            where
            org_id in
            <foreach collection="orgIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            union all
            select b.*
            from hrm_org b,
                 temp t
            where b.org_parent_id = t.org_id
            )
            SELECT *
            FROM temp
            ) z
            ) m
            on x.orgId = m.org_id
        </if>
    </select>

    <!-- 查询操作日志 -->
    <select id="queryLog" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeModifyLogVo">
        SELECT a.id,
               a.emp_id          as empId,
               a.modi_type       as modiType,
               a.modi_modu       as modiModu,
               a.modi_tab_name   as modiTabName,
               a.modi_field_code as modiFieldCode,
               a.modi_field_name as modiFieldName,
               a.old_val         as oldVal,
               a.new_val         as newVal,
               a.old_display_val as oldDisplayVal,
               a.new_display_val as newDisplayVal,
               a.opter,
               a.create_time     as createTime,
               a.hospital_id     as hospitalId,
               a.is_file         as isFile,
               a.asoc_tab_id     as asocTabId,
               a.stas,
               a.mgt_val         as mgtVal,
               a.mgt_val_Name    as mgtValName,
               a.exe_date        as infoExeDate,
               b.emp_name        as opterName,
               c.emp_name        AS empName
        FROM hrm_emp_modify_log a
                 LEFT JOIN hrm_employee_info b ON a.opter = b.emp_code
                 INNER JOIN hrm_employee_info c ON a.emp_id = c.id
        <where>
            <if test="empCode != null and empCode != ''">
                AND c.emp_code = #{empCode}
            </if>
            <if test="empId != null and empId != ''">
                AND a.emp_id = #{empId,jdbcType=INTEGER}
            </if>
            <if test="moduleName != null and moduleName != ''">
                AND a.modi_modu = #{moduleName}
            </if>
            <if test="stas != null and stas != ''">
                AND a.stas = #{stas}
            </if>
            <if test="startTime != null and startTime != ''">
                AND a.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="ids != null and ids.size() > 0">
                AND a.id IN
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <!-- 查询审核数据 -->
    <select id="queryAuditLog"
            resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeAuditModifyLogVo">
        select b.*,
               c.emp_code as empCode,
               c.emp_name as empName,
               d.org_name as orgName
        from (select a.emp_id                                    as empId,
                     string_agg(distinct a.modi_modu, ',')       as modiModules,
                     string_agg(distinct a.modi_field_name, ',') as modiFields
              from hrm_emp_modify_log a
              where a.stas = '1'
              group by a.emp_id) b
                 left join hrm_employee_info c
                           ON b.empId = c.id
                 left join hrm_org d
                           on c.org_id = d.org_id
    </select>


    <!--获取新的职工工号-->
    <select id="queryNewEmpCode" resultType="java.lang.Integer">
        SELECT CAST
               (emp_code AS INTEGER) + 1 AS empCode
        FROM hrm_employee_info
        WHERE emp_code NOT IN ('9001', '9004', '9005', '9006', '9007')
        ORDER BY emp_code DESC
        LIMIT 1
    </select>

    <!--获取新的职工档案号-->
    <select id="queryNewArchiveNum" resultType="java.lang.Integer">
        select regexp_replace(archive_num, '^[a-zA-Z]+', '') ::INTEGER + 1 as archive_num
        from hrm_employee_info
        where archive_num like concat('', #{prefix}, '%')
        ORDER BY regexp_replace(archive_num, '^[a-zA-Z]+', '')::INTEGER desc
        limit 1
    </select>

    <!--查询档案号是否存在-->
    <select id="queryByArchiveNum" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeInfoVo">
        SELECT a.id,
               a.emp_code      AS empCode,
               a.org_id        as orgId,
               a.portrait_path as portraitPath,
               a.ajt_org_ids   as ajtOrgIds,
               a.archive_num   as archiveNum
        FROM hrm_employee_info a
        WHERE A.archive_num = #{archiveNum}
    </select>

    <!--查询同科室职工 工号-->
    <select id="selectEmpCodeByOrg" resultType="java.lang.String">
        select string_agg(distinct a.emp_code, ',')
        from hrm_employee_info a
        where
        a.org_id IN
        <foreach collection="orgIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and a.emp_code is not null
    </select>

    <!--查询 正式和试用 的职工基本信息-->
    <select id="queryLeaveInfoList" resultType="com.jp.med.hrm.modules.attendanceMgt.vo.HrmLeaveStatVo">
        with empStatus as (select * from hrm_employee_dict where code_type = 'EMPLOYEE_TYPE' AND code_value = '1')
        SELECT EXTRACT(YEAR FROM
                       age(now(), date_trunc('month', a.join_job_time::date) + INTERVAL '1 month')) AS currWorkAge,
               CASE
                   WHEN EXTRACT(YEAR FROM
                                age(now(), date_trunc('month', a.join_job_time::date) + INTERVAL '1 month')) &gt;= 1 and
                        EXTRACT(YEAR FROM age(now(), a.join_job_time::date)) &lt; 10 THEN 5
                   WHEN EXTRACT(YEAR FROM age(now(), date_trunc('month', a.join_job_time::date) +
                                                     INTERVAL '1 month')) BETWEEN 10 AND 19 THEN 10
                   WHEN EXTRACT(YEAR FROM
                                age(now(), date_trunc('month', a.join_job_time::date) + INTERVAL '1 month')) &gt;= 20
                       THEN 15
                   ELSE 0
                   END                                                                              AS currYearVacDays,
               CASE
                   WHEN EXTRACT(YEAR FROM
                                age(date_trunc('year', now()) - INTERVAL '1 day', a.join_job_time::date)) &gt;= 1 and
                        EXTRACT(YEAR FROM age(now(), a.join_job_time::date)) &lt; 10 THEN 5
                   WHEN EXTRACT(YEAR FROM age(date_trunc('year', now()) - INTERVAL '1 day',
                                              a.join_job_time::date)) BETWEEN 10 AND 19 THEN 10
                   WHEN EXTRACT(YEAR FROM
                                age(date_trunc('year', now()) - INTERVAL '1 day', a.join_job_time::date)) &gt;= 20
                       THEN 15
                   ELSE 0
                   END                                                                              AS prevYearVacDays,
               a.emp_code                                                                           as empCode,
               a.emp_name                                                                           as empName,
               a.org_id                                                                             as orgId,
               a.hospital_id                                                                        as hospitalId,
               c.org_name                                                                           as orgName,
               a.emp_type                                                                           as empType,
               dict.code_lable                                                                      as empTypeLabel,
               a.status                                                                             as status,
               dict2.code_lable                                                                     as statusLabel,
               d.practicing_type                                                                    as practicingType,
               d.code_lable                                                                         as practicingTypeLabel,
               case when e.engage_rank is not null then e.engage_rank else '无职称' end             as engageRank
        FROM hrm_employee_info a
                 left join hrm_org c
                           on a.org_id = c.org_id
                 left join hrm_employee_dict dict on a.emp_type = dict.id::varchar
                 left join hrm_employee_dict dict2 on a.status = dict2.id::varchar
                 left join (SELECT b.code_lable,
                                   A.*
                            FROM (SELECT *
                                  from (SELECT A.*,
                                               MAX(A.ID) OVER ( PARTITION BY A.emp_id ) AS max_id2
                                        FROM (SELECT MAX
                                                         (A.give_time) OVER ( PARTITION BY A.emp_id ) AS max_time,
                                                     MAX(A.ID) OVER ( PARTITION BY A.emp_id )         AS max_id,
                                                     A.*
                                              FROM hrm_practicing_requirement A
                                              WHERE A.is_deleted IS NULL
                                                 OR A.is_deleted != 1) A
                                        WHERE (A.give_time = A.max_time)
                                           OR (A.max_time IS NULL AND A.ID = A.max_id)) A
                                  where A.id = A.max_id2) A
                                     LEFT JOIN hrm_employee_dict b ON CAST(A.practicing_type AS INTEGER) = b.ID) d
                           on a.id = d.emp_id
                 left join (SELECT *
                            FROM (SELECT A.*,
                                         MAX(A.ID) OVER ( PARTITION BY A.emp_id ) AS max_id2
                                  FROM (SELECT MAX
                                                   (A.start_time) OVER ( PARTITION BY A.emp_id ) AS max_time,
                                               MAX(A.id) OVER ( PARTITION BY A.emp_id )          AS max_id,
                                               A.*
                                        FROM hrm_engage_level A
                                        WHERE A.is_deleted IS NULL
                                           OR A.is_deleted != 1) A
                                  WHERE A.start_time = A.max_time
                                     OR (A.max_time IS NULL AND A.ID = A.max_id)) A
                            where A.id = A.max_id2) e on a.id = e.emp_id
        <where>
            <if test="empCodes != null and empCodes.size() > 0">
                and a.emp_code in
                <foreach collection="empCodes" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="empStatus != null and empStatus.size() > 0">
                and a.status in
                <foreach collection="empStatus" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="toCompanyTime != null and toCompanyTime.size() > 0">
                and a.to_company_time between #{toCompanyTime[0]} and #{toCompanyTime[1]}
            </if>
            <if test="statusChgDate != null and statusChgDate.size() > 0">
                and a.status_chg_date between #{statusChgDate[0]} and #{statusChgDate[1]}
            </if>
            <if test="orgIds != null and orgIds.size() > 0">
                and a.org_id in
                <foreach collection="orgIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="empType != null and empType.size() > 0">
                and a.emp_type in
                <foreach collection="empType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="practicingType != null and practicingType.size() > 0">
                and d.practicing_type in
                <foreach collection="practicingType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="engageRank != null and engageRank != ''">
                and e.engage_rank LIKE CONCAT('%', #{engageRank}, '%')
            </if>

            and (a.is_deleted is NULL or a.is_deleted != 1)
            <if test="statYear != null and statYear != ''">
                and (a.status in (select id::varchar from empStatus) or
                     (a.status_chg_date::date &gt; to_date(#{statYear}, 'yyyy')))
                and (a.to_company_time::date &lt;=
                     date_trunc('year', to_date(#{statYear}, 'yyyy')) + INTERVAL '1 YEAR - 1 DAY')
            </if>
            <if test="yearMonth != null and yearMonth != ''">
                and (a.status in (select id::varchar from empStatus) or a.status_chg_date is null or
                     a.status_chg_date::date &gt; to_date(#{yearMonth}, 'yyyy-MM'))
                and (a.to_company_time is null or a.to_company_time::date &lt;=
                                                  date_trunc('month', to_date(#{yearMonth}, 'yyyy-MM')) +
                                                  INTERVAL '1 MONTH - 1 DAY')
            </if>
        </where>
        order by a.emp_code
    </select>
    <select id="empStatusNum" resultType="java.util.Map">
        select status    as name,
               count(id) as num
        from hrm_employee_info
        where (is_deleted is null or is_deleted != 1)
        group by status
    </select>

    <!--科室合并 查询管辖的职工-->
    <select id="queryMyEmps" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeInfoVo">
        select distinct a.emp_code    as empCode,
                        a.emp_name    as empName,
                        a.org_id      as orgId,
                        a.hospital_id as hospitalId
        from hrm_employee_info a
                 left join hrm_org b
                           on a.org_id = b.org_id
        <where>
            <if test="orgIds != null and orgIds.size() > 0">
                and b.org_id in
                <foreach collection="orgIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orgId != null and orgId != ''">
                and b.org_id = #{orgId}
            </if>
            and (a.is_deleted is NULL or a.is_deleted != 1)
        </where>
    </select>

    <select id="querySimpleInfoByEmp" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeInfoVo">
        SELECT a.id,
               a.emp_code      AS empCode,
               a.emp_name      AS empName,
               a.phone,
               a.org_id        as orgId,
               a.portrait_path as portraitPath,
               a.ajt_org_ids   as ajtOrgIds,
               a.ajt_org_ids   as ajtOrgIdsStr,
               a.hospital_id   as hospitalId,
               b.org_name      AS orgName,
               c.ori_org       AS oriOrg
        FROM hrm_employee_info a
                 LEFT JOIN hrm_org b
                           ON a.org_id = b.org_id
                 LEFT JOIN hrm_org_map c
                           on a.org_id = c.org_id
        <where>
            <if test="orgId != null and orgId != ''">
                and b.org_id = #{orgId}
            </if>
            <if test="empCode != null and empCode != ''">
                and a.emp_code = #{empCode}
            </if>
            <if test="empName != null and empName != ''">
                and a.emp_name = #{empName}
            </if>
            and (is_deleted is NULL or is_deleted != 1)
        </where>
    </select>

    <select id="querySpecialEmpInfo" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeInfoVo">
        select A.emp_code                                as empCode,
               A.emp_name                                as empName,
               coalesce(A.to_company_time, '1924-01-01') as toCompanyTime,
               coalesce(a.status_chg_date, '3024-01-01') as statusChgDate,
               A.status                                  as status

        from hrm_employee_info A
        <where>
            <if test="orgId != null and orgId != ''">
                and A.org_id = #{orgId}
            </if>
            and (
                (coalesce(A.to_company_time, '1924-01-01')::date &gt; to_date(#{minDate}, 'yyyy-MM-dd') and
                 coalesce(A.to_company_time, '1924-01-01')::date &lt; to_date(#{maxDate}, 'yyyy-MM-dd'))
                    or
                (a.status not in ('397', '398') and
                 coalesce(a.status_chg_date, '3024-01-01')::date &gt; to_date(#{minDate}, 'yyyy-MM-dd') and
                 coalesce(a.status_chg_date, '3024-01-01')::date &lt;= to_date(#{maxDate}, 'yyyy-MM-dd'))
                )
        </where>
    </select>

    <select id="getHrmHomeEmpStatus" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeVo">
        <!--员工状态-->
        select status as key, count(id) as num
        from hrm_employee_info
        group by status
        union all
        <!--职工类型-->
        select emp_type as key, count(id) as num
        from hrm_employee_info
        group by emp_type
        <!--执业资格-->
        union all
        select practicing_type as key, count(id) as num
        from (SELECT *
              from (SELECT A.*,
                           MAX(A.ID) OVER ( PARTITION BY A.emp_id ) AS max_id2
                    FROM (SELECT MAX
                                     (A.give_time) OVER ( PARTITION BY A.emp_id ) AS max_time,
                                 MAX(A.ID) OVER ( PARTITION BY A.emp_id )         AS max_id,
                                 A.*
                          FROM hrm_practicing_requirement A
                          WHERE A.is_deleted IS NULL
                             OR A.is_deleted != 1) A
                    WHERE (A.give_time = A.max_time)
                       OR (A.max_time IS NULL AND A.ID = A.max_id)) A
              where A.id = A.max_id2) A
        group by practicing_type
        <!--行政职务-->
        union all
        select rank as key, count(id) as num
        from (SELECT *
              from (SELECT A.*,
                           MAX(A.ID) OVER ( PARTITION BY A.emp_id ) AS max_id2
                    FROM (SELECT MAX
                                     (A.start_time) OVER ( PARTITION BY A.emp_id ) AS max_time,
                                 MAX(A.ID) OVER ( PARTITION BY A.emp_id )          AS max_id,
                                 A.*
                          FROM hrm_administrative_rank A
                          WHERE A.is_deleted IS NULL
                             OR A.is_deleted != 1) A
                    WHERE (A.start_time = A.max_time)
                       OR (A.max_time IS NULL AND A.ID = A.max_id)) A
              where A.id = A.max_id2) A
        group by rank
    </select>

    <select id="selectHrmHomeBasicInfo" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeInfoVo">
        select a.emp_code                as empCode,
               a.emp_name                as empName,
               a.sex                     as sex,
               a.portrait_path           as portraitPath,
               coalesce(a.phone, phone2) as phone,
        <!--年休假总天数-->
        CEIL(CASE
            WHEN EXTRACT(YEAR FROM a.to_company_time::date) &gt;= EXTRACT(YEAR FROM now())
                THEN 0
        <!--计算当前年份的年休假天数-->
        WHEN EXTRACT(YEAR FROM age(now(), a.join_job_time::date)) &lt; 1
            THEN 0
        WHEN EXTRACT(YEAR FROM age(now(), a.join_job_time::date)) &lt; 10
            THEN  5 *
        <!-- 计算去年工作的月份数-->
        CASE
            WHEN EXTRACT(YEAR FROM a.to_company_time::date) = EXTRACT(YEAR FROM now()) - 1
                THEN EXTRACT(MONTH FROM age(DATE_TRUNC('year', now()) - INTERVAL '1 day', a.to_company_time::date)) + 1
            ELSE 12 <!-- 在公司全年的员工-->
        END / 12.0
            WHEN EXTRACT(YEAR FROM age(now(), a.join_job_time::date)) BETWEEN 10 AND 19 THEN
                10 *
                CASE
                    WHEN EXTRACT(YEAR FROM a.to_company_time::date) = EXTRACT(YEAR FROM now()) - 1 THEN
                        EXTRACT(MONTH FROM
                                age(DATE_TRUNC('year', now()) - INTERVAL '1 day', a.to_company_time::date)) + 1
                    ELSE 12
                    END / 12.0
            WHEN EXTRACT(YEAR FROM age(now(), a.join_job_time::date)) &gt;= 20 THEN
                15 *
                CASE
                    WHEN EXTRACT(YEAR FROM a.to_company_time::date) = EXTRACT(YEAR FROM now()) - 1 THEN
                        EXTRACT(MONTH FROM
                                age(DATE_TRUNC('year', now()) - INTERVAL '1 day', a.to_company_time::date)) + 1
                    ELSE 12
                    END / 12.0
            ELSE 0
            END * 2)/ 2   AS "annualLeaveTotal",
            b.result      as lastYearAssessment,
            c.rank        as job

        from hrm_employee_info a
                 left join(select *
                           from hrm_year_assess
                           where is_deleted != '1'
                             and CAST(year AS INTEGER) = EXTRACT(YEAR FROM NOW()) - 1
                           order by result desc) b
                          on a.id = b.emp_id
                 left join (select *
                            from (SELECT *
                                  from (SELECT A.*,
                                               MAX(A.ID) OVER ( PARTITION BY A.emp_id ) AS max_id2
                                        FROM (SELECT MAX
                                                         (A.start_time) OVER ( PARTITION BY A.emp_id ) AS max_time,
                                                     MAX(A.ID) OVER ( PARTITION BY A.emp_id )          AS max_id,
                                                     A.*
                                              FROM hrm_administrative_rank A
                                              WHERE A.is_deleted IS NULL
                                                 OR A.is_deleted != 1) A
                                        WHERE (A.start_time = A.max_time)
                                           OR (A.max_time IS NULL AND A.ID = A.max_id)) A
                                  where A.id = A.max_id2) A) c on a.id = c.emp_id
        <!--年度已休年休假天数-->

        where a.emp_code = #{empCode}
        limit 1
    </select>

    <select id="queryEmpNamesByEmpCodes" resultType="java.lang.String">
        select STRING_AGG(emp_name, ',')
        from hrm_employee_info
        where emp_code in
        <foreach collection="empCodes" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryLeaders" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeInfoVo">
        WITH filtered_ranks AS (SELECT *
                                FROM hrm_administrative_rank
                                WHERE (is_deleted IS NULL OR is_deleted != 1)
                                  AND rank is not null),
             latest_records AS (SELECT *,
                                       ROW_NUMBER() OVER (
                                           PARTITION BY emp_id
                                           ORDER BY start_time DESC NULLS LAST, id DESC
                                           ) as rn
                                FROM filtered_ranks)
        SELECT distinct b.emp_code as "empCode",
                        b.emp_name as "empName",
                        a.org_id   as "orgId",
                        c.org_name as "orgName",
                        c.org_type as "orgType",
                        a.rank     as "rank"
        FROM latest_records a
                 inner join hrm_employee_info b on a.emp_id = b.id
                 inner join hrm_org c on a.org_id = c.org_id
        where a.rn = 1
          and a.rank in ('513', '518')
        <!--  <where>
              a.rn = 1
                AND
              (
              a.rank = '513'
              <if test="orgId != null and orgId != ''">
                  OR (
                         (a.org_id = #{orgId} or #{orgId} = ANY (string_to_array(b.ajt_org_ids, ',')))
                             AND a.rank = '518'
                         )
              </if>
              )
          </where>-->
    </select>


    <select id="queryLeadersCode" resultType="java.lang.String">
        WITH filtered_ranks AS (SELECT *
        FROM hrm_administrative_rank
        WHERE (is_deleted IS NULL OR is_deleted != 1)
        AND rank is not null),
        latest_records AS (SELECT *,
        ROW_NUMBER() OVER (
        PARTITION BY emp_id
        ORDER BY start_time DESC NULLS LAST, id DESC
        ) as rn
        FROM filtered_ranks)
        SELECT distinct b.emp_code as "empCode"
        FROM latest_records a
        inner join hrm_employee_info b on a.emp_id = b.id
        <where>
           ( a.rn = 1
            and a.rank in ('513', '518'))
            <if test="orgId != null">
                or (a.rank = '514' and b.org_id = #{orgId}) or (a.rank = '515' and b.org_id = #{orgId})
            </if>

        </where>
    </select>

    <select id="queryLeadersByOrgIds" resultType="java.lang.String">
        WITH filtered_ranks AS (SELECT *
        FROM hrm_administrative_rank
        WHERE (is_deleted IS NULL OR is_deleted != 1)
        AND rank is not null),
        latest_records AS (SELECT *,
        ROW_NUMBER() OVER (
        PARTITION BY emp_id
        ORDER BY start_time DESC NULLS LAST, id DESC
        ) as rn
        FROM filtered_ranks)
        SELECT distinct b.emp_code as "empCode"
        FROM latest_records a
        inner join hrm_employee_info b on a.emp_id = b.id
        <where>
            a.rn = 1
            <if test="orgIds != null and orgIds.size() >0 ">
                and b.org_id in
                <foreach collection="orgIds" item="orgId" open="(" close=")" separator=",">
                    #{orgId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectEmpInfoById" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeInfoVo">
        SELECT a.id,
               a.emp_code      AS empCode,
               a.emp_name      AS empName,
               a.status        AS status,
               a.org_id        as orgId,
               a.hospital_id   as hospitalId,
               b.org_name      AS orgName,
               dict.code_lable as statusName
        FROM hrm_employee_info a
                 LEFT JOIN hrm_org b
                           ON a.org_id = b.org_id
                 left join hrm_employee_dict dict on a.status = dict.id::varchar
        where a.id = #{empId}
    </select>

    <select id="queryListByCondition" resultType="com.jp.med.hrm.modules.emp.vo.EmpEmployeeInfoVo">
        select a.id,
               a.emp_code          AS empCode,
               a.emp_name          AS empName,
               a.emp_type          AS empType,
               a.status            AS status,
               a.org_id            as orgId,
               a.sex               as sex,
               a.birthday          as birthday,
               a.politics_status   as politicsStatus,
               a.mz                as mz,
               a.phone             as phone,
               a.phone2            as phone2,
               a.sort_phone        as sortPhone,
               a.status_chg_date   as statusChgDate,
               a.to_company_time   as toCompanyTime,
               a.start_time        as startTime,
               a.end_time          as endTime,
               a.confirmation_date as confirmationDate,
               a.leave_date        as leaveDate,
               a.retire_date       as retireDate,
               a.exit_date         as exitDate,
               a.hospital_id       as hospitalId
        from hrm_employee_info a
        <where>
            <if test="startMonth != null and startMonth != '' and endMonth != null and endMonth != ''">
                and left(a.status_chg_date, 7) between #{startMonth} and #{endMonth}
            </if>
            <if test="empCode != null and empCode != ''">
                and a.emp_code = #{empCode}
            </if>
            <if test="orgId != null and orgId != ''">
                and a.org_id = #{orgId}
            </if>
            <if test="empType != null and empType != ''">
                and a.emp_type = #{empType}
            </if>
            <if test="status != null and status != ''">
                and a.status = #{status}
            </if>
            <if test="curEmpCode != null and curEmpCode != ''">
                and a.emp_code != #{curEmpCode}
            </if>
            <if test="curSysOrgId != null and curSysOrgId != ''">
                and a.org_id = #{curSysOrgId}
            </if>

        </where>
        order by a.emp_code desc
    </select>

    <select id="selectModifyEmpInfoByDate" resultType="com.jp.med.hrm.modules.emp.dto.EmpEmployeeInfoDto">
        select * from hrm_employee_info where id in
                                              (select emp_id from hrm_emp_modify_log where create_time like CONCAT('%', #{date}, '%'))
        <if test="empIds !=null and empIds.size()>0">
            and id not in
                                        <foreach collection="empIds" item="empId" open="(" close=")" separator=",">
                                            #{empId}
                                        </foreach>
        </if>

    </select>
    <select id="getUserStatus" resultType="java.lang.String">
            select is_lock from sys_user where username = #{empCode}
    </select>
</mapper>
