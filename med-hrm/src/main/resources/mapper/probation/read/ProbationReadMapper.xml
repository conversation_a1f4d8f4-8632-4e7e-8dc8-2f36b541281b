<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.probation.mapper.read.ProbationReadMapper">
    <!--查询员工试用期信息-->
        <select  id="queryProbationData" resultType="com.jp.med.hrm.modules.probation.vo.ProbationVo">
            SELECT a.id,
                   a.emp_code      as empCode,
                   a.emp_name      as empName,
                   b.org_name      as orgName,
                   a.age,
                   a.mz,
                   a.sex           as sex,
                   a.native_place  as homeTown,
                   a.phone         as phone,
                   a.icd_card      as icdCard,
                   a.icd_card_type as icdCardType,
                   a.birthday      as birthday,
                   a.start_time    as startTime,
                   a.end_time      as endTime,
                   a.status        as status,
                   a.hospital_id   as hospitalId,
                   b.org_id        as orgId
            from hrm_employee_info a
                     left join hrm_org b on a.org_id = b.org_id and a.hospital_id = b.hospital_id
            <where>
                <if test="empCode != null and empCode != ''">
                    AND a.emp_code = #{empCode,jdbcType=VARCHAR}
                </if>
                <if test="status != null and status != ''">
                    AND a.status = #{status,jdbcType=VARCHAR}
                </if>
                <if test="orgId != null and orgId != ''">
                    AND a.org_id = #{orgId,jdbcType=VARCHAR}
                </if>
                AND is_deleted != 1
            </where>
            order by emp_code
        </select>

</mapper>
