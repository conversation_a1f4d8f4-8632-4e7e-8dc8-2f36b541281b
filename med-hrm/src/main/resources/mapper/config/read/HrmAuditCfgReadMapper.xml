<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.config.mapper.read.HrmAuditCfgReadMapper">

    <select id="queryList" resultType="com.jp.med.hrm.modules.config.vo.HrmAuditCfgVo">
        select
            id as id,
            flow_name as flowName,
            "desc" as "desc",
            create_time as createTime,
            hospital_id as hospitalId,
            active_flag as activeFlag
        from hrm_audit_cfg
    </select>

    <!-- 查询详情 -->
    <select id="queryDetails" resultType="com.jp.med.common.entity.audit.AuditDetail">
        select a.id,
               a.audit_cfg_id as auditCfgId,
               a.chker as chker,
               a.chk_dept as chkDept,
               a.chk_time as chkTime,
               a.chk_remarks as chkRemarks,
               a.chk_sign as chkSign,
               a.chk_att as chkAtt,
               a.chk_seq as chkSeq,
               a.chk_state as chkState,
               a.chk_sign_path as chkSignPath,
               a.chk_att_path as chkAttPath,
               b.org_name as chkDeptName,
               c.chkerName
        from hrm_audit_detail_cfg a
        left join hrm_org b
        on a.chk_dept = b.org_id
        left join (
            select a.id, string_agg(c.emp_name, ',') as chkerName
            from hrm_audit_detail_cfg a
            left join hrm_employee_info c
            on c.emp_code = ANY(STRING_TO_ARRAY(a.chker, ',')::VARCHAR[])
            group by a.id
        ) c
        on a.id = c.id
        where a.audit_cfg_id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

</mapper>
