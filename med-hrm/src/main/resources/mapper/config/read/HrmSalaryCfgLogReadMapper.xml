<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.config.mapper.read.HrmSalaryCfgLogReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.hrm.modules.config.vo.HrmSalaryCfgLogVo" id="salaryCfgLogMap">
        <result property="id" column="id"/>
        <result property="asocTabId" column="asoc_tab_id"/>
        <result property="modiType" column="modi_type"/>
        <result property="empCode" column="emp_code"/>
        <result property="modiFieldCode" column="modi_field_code"/>
        <result property="modiFieldName" column="modi_field_name"/>
        <result property="oldVal" column="old_val"/>
        <result property="newVal" column="new_val"/>
        <result property="oldDisplayName" column="old_display_name"/>
        <result property="newDisplayName" column="new_display_name"/>
        <result property="crter" column="crter"/>
        <result property="crteTime" column="crte_time"/>
    </resultMap>
    <sql id="cfgLogFields">
        a.id as id,
            a.asoc_tab_id as asocTabId,
            a.modi_type as modiType,
            a.emp_code as empCode,
            a.modi_field_code as modiFieldCode,
            a.modi_field_name as modiFieldName,
            a.old_val as oldVal,
            a.new_val as newVal,
            a.old_display_name as oldDisplayName,
            a.new_display_name as newDisplayName,
            a.crter as crter,
            a.crte_time as crteTime,
            a.active_flag as activeFlag,
            a.hospital_id as hospitalId
    </sql>
    <select id="queryList" resultType="com.jp.med.hrm.modules.config.vo.HrmSalaryCfgLogVo">
        select
        <include refid="cfgLogFields"/>,
        b.emp_name as crterName
        from hrm_salary_cfg_log a
        left join hrm_employee_info b on a.crter = b.emp_code
        <where>
            <if test="startTime != null and startTime != ''">
                and A.crte_time between #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR}
            </if>
            <if test="asocTabId != null">
                and a.asoc_tab_id = #{asocTabId,jdbcType=INTEGER}
            </if>
        </where>
        order by a.crte_time desc
    </select>

</mapper>
