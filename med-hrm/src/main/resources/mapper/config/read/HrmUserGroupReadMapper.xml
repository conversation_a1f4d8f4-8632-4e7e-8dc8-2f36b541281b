<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.config.mapper.read.HrmUserGroupReadMapper">

    <select id="queryList" resultType="com.jp.med.hrm.modules.config.vo.HrmUserGroupVo">
        with empStatus as (select * from hrm_employee_dict where code_type = 'EMPLOYEE_TYPE' AND code_value = '1')
        select a.id               as id,
               a.emp_code         as empCode,
               b.emp_name         as empName,
               c.org_name         as orgName,
               b.hospital_id      as hospitalId,
               a.group_code       as groupCode,
               d.group_name       as groupName,
               a.active_flag      as activeFlag,
               case
                   when a.emp_code is not null and a.group_code != #{groupCode,jdbcType=VARCHAR}
                       then true
                   else false end as disabled
        from hrm_user_group a
            inner join hrm_employee_info b on a.emp_code = b.emp_code
            left join hrm_org c on b.org_id = c.org_id
        <choose>
            <when test="groupCode != null and groupCode != ''">
                inner join (WITH RECURSIVE temp AS
                                               (SELECT *
                                                FROM hrm_group_cfg r
                                                where r.group_code = #{groupCode,jdbcType=VARCHAR}
                                                UNION
                                                    ALL
                                                SELECT b.*
                                                FROM hrm_group_cfg b,
                                                     temp t
                                                WHERE b.parent_id = t.group_code)
                            SELECT *
                            FROM temp) d on a.group_code = d.group_code
            </when>
            <otherwise>
                left join hrm_group_cfg d on a.group_code = d.group_code
            </otherwise>
        </choose>
        inner join hrm_group_cfg e on a.group_code = e.group_code
        <where>
            <if test="groupCodes != null and groupCodes.size > 0">
                a.group_code in
                <foreach collection="groupCodes" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <choose>
                <when test="sysUser.hrmUser.orlOrgIds != null and sysUser.hrmUser.orlOrgIds.size > 0">
                    and e.org_id in
                    <foreach collection="sysUser.hrmUser.orlOrgIds" item="item" open="(" close=")" separator=",">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
                <otherwise>
                    <if test="curSysOrgId != '' and curSysOrgId != null">
                        and e.org_id = #{curSysOrgId,jdbcType=VARCHAR}
                    </if>
                </otherwise>
            </choose>
            and b.status in (select id::varchar from empStatus)
        </where>
        order by c.org_id, b.emp_name
    </select>

    <select id="queryUser" resultType="com.jp.med.hrm.modules.config.vo.HrmUserGroupVo">
        with empStatus as (select * from hrm_employee_dict where code_type = 'EMPLOYEE_TYPE' AND code_value = '1')
        select
            a.id as id,
            a.emp_code as empCode,
            a.emp_name as empName,
            c.org_name as orgName,
            a.hospital_id as hospitalId,
            b.group_code as groupCode,
            d.group_name as groupName,
            case when b.emp_code is not null and b.group_code != #{groupCode,jdbcType=VARCHAR}
                    <choose>
                        <when test="sysUser.hrmUser.orlOrgIds != null and sysUser.hrmUser.orlOrgIds.size >0">
                            and d.org_id in
                            <foreach collection="sysUser.hrmUser.orlOrgIds" item="item" open="(" close=")" separator=",">
                                #{item,jdbcType=VARCHAR}
                            </foreach>
                        </when>
                        <otherwise>
                            <if test="curSysOrgId != '' and curSysOrgId != null">
                                and d.org_id = #{curSysOrgId,jdbcType=VARCHAR}
                            </if>
                        </otherwise>
                    </choose>
                    then true else false end as disabled,
            b.active_flag as activeFlag
        from hrm_employee_info a
            left join hrm_user_group b on a.emp_code = b.emp_code
            left join hrm_org c on a.org_id = c.org_id
        left join hrm_group_cfg d on b.group_code = d.group_code
        <where>
            <choose>
                <when test="sysUser.hrmUser.orlOrgIds != null and sysUser.hrmUser.orlOrgIds.size >0">
                    and a.org_id in
                    <foreach collection="sysUser.hrmUser.orlOrgIds" item="item" open="(" close=")" separator=",">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
                <otherwise>
                    <if test="curSysOrgId != '' and curSysOrgId != null">
                        and a.org_id = #{curSysOrgId,jdbcType=VARCHAR}
                    </if>
                </otherwise>
            </choose>
            and a.status in (select id::varchar from empStatus)
        </where>
        order by c.org_id,a.emp_name
    </select>

</mapper>
