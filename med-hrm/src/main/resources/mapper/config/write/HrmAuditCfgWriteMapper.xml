<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.config.mapper.write.HrmAuditCfgWriteMapper">

    <!-- 保存详情 -->
    <insert id="saveDetail">
        INSERT INTO hrm_audit_detail_cfg(
                audit_cfg_id,
                chker,
                chk_dept,
                chk_time,
                chk_remarks,
                chk_sign,
                chk_att,
                chk_seq,
                chk_state
        )
        VALUES(
               #{auditCfgId,jdbcType=INTEGER},
               #{chker,jdbcType=VARCHAR},
               #{chkDept,jdbcType=VARCHAR},
               #{chkTime,jdbcType=VARCHAR},
               #{chkRemarks,jdbcType=VARCHAR},
               #{chkSign,jdbcType=VARCHAR},
               #{chkAtt,jdbcType=VARCHAR},
               #{chkSeq,jdbcType=INTEGER},
               #{chkState,jdbcType=VARCHAR}
      )
    </insert>

    <!-- 通过id删除详情 -->
    <delete id="deleteDetailsById">
        DELETE FROM hrm_audit_detail_cfg
        WHERE audit_cfg_id = #{id,jdbcType=INTEGER}
    </delete>

</mapper>
