<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.postManagement.mapper.read.SalaryStandardReadMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.hrm.modules.postManagement.vo.SalaryStandardVo" id="salaryStandardMap">
        <result property="id" column="id"/>
        <result property="levelName" column="level_name"/>
        <result property="performanceId" column="performance_id"/>
        <result property="levelSalary" column="level_salary"/>
        <result property="sort" column="sort"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.hrm.modules.postManagement.vo.SalaryStandardVo">
        select id               as id,
               person_type      as personType,
               post_or_salary   as postOrSalary,
               level_name       as levelName,
               performance_id as performanceId,
               level_salary     as levelSalary,
               sort             as sort,
               create_by        as createBy,
               create_time      as createTime,
               last_update_by   as lastUpdateBy,
               last_update_time as lastUpdateTime,
               hospital_id      as hospitalId,
               del_flag         as delFlag
        from hrm_salary_standard
    </select>

    <select id="querySalaryStandard" resultType="com.jp.med.hrm.modules.postManagement.vo.SalaryStandardVo">
        select
        id as id,
        person_type as personType,
        post_or_salary as postOrSalary,
        level_name as levelName,
        performance_id as performanceId,
        level_salary as levelSalary,
        sort as sort,
        hospital_id as hospitalId
        from hrm_salary_standard
        <where>
            <if test="personType!=null and personType !=''">
                and person_type = #{personType,jdbcType=VARCHAR}
            </if>
            <if test="postOrSalary!=null and postOrSalary !=''">
                and post_or_salary = #{postOrSalary,jdbcType=VARCHAR}
            </if>
            and (del_flag != -1 or del_flag is NULL)
        </where>
        order by sort
    </select>


    <select id="selectByName" resultType="java.lang.String">
        select level_name as levelName
        from hrm_salary_standard
        <where>
            <if test="levelName !=null and levelName !=''">
                and level_name = #{levelName,jdbcType=VARCHAR}
            </if>
            <if test="personType !=null and personType !=''">
                and person_type = #{personType,jdbcType=VARCHAR}
            </if>
            <if test="postOrSalary !=null and postOrSalary !=''">
                and post_or_salary = #{postOrSalary,jdbcType=VARCHAR}
            </if>
            <if test="id !=null and id !=''">
                and id != #{id,jdbcType=INTEGER}
            </if>
            and (del_flag != -1 or del_flag is NULL)
        </where>
    </select>
</mapper>
