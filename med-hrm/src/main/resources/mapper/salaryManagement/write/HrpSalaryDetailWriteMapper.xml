<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.salaryManagement.mapper.write.EmployeeSalaryWriteMapper">
  <insert id="saveYyData">
    <foreach item="item" index="index" collection="result" separator=";">
      INSERT INTO "public"."hrm_salary_yy" ("gsdm",
                                            "gsmc",
                                            "ffnd",
                                            "lbdm",
                                            "lbmc",
                                            "bmdm",
                                            "bmmc",
                                            "lxdm",
                                            "lxmc",
                                            "zydm",
                                            "zyxm",
                                            "jsff",
                                            "yhdm",
                                            "grzh",
                                            "ffDate",
                                            "ffcs",
                                            "jbgz",
                                            "gwgz",
                                            "glgz",
                                            "flf",
                                            "fsbt",
                                            "jb",
                                            "jj",
                                            "sjts",
                                            "sjkk",
                                            "bjts",
                                            "bjkk",
                                            "gwjt",
                                            "xlf",
                                            "jbf",
                                            "shbz",
                                            "yfhj",
                                            "kkhj",
                                            "sfhj",
                                            "bykl",
                                            "sycl",
                                            "grsds",
                                            "sfzh",
                                            "zc",
                                            "yskmdm",
                                            "yskmmc",
                                            "zw",
                                            "DZKDM",
                                            "DZKMC",
                                            "ksx",
                                            "klbcgs",
                                            "havesh",
                                            "havepf",
                                            "deptord",
                                            "yhdm1",
                                            "grzh1",
                                            "yhdm2",
                                            "grzh2",
                                            "ksbfs",
                                            "yhmc",
                                            "pcxh",
                                            "ffyf",
                                            "calc",
                                            "gz6",
                                            "gz7",
                                            "gz8",
                                            "gz9",
                                            "gz2",
                                            "gz5",
                                            "gz1",
                                            "gz3",
                                            "gz4",
                                            "gz32",
                                            "gz31",
                                            "gz26",
                                            "gz29",
                                            "gz30",
                                            "gz33",
                                            "gz34",
                                            "gz35",
                                            "gz18",
                                            "gz36",
                                            "gz25",
                                            "gz37",
                                            "gz38",
                                            "gz39",
                                            "gz22",
                                            "gz10",
                                            "gz40",
                                            "gz12",
                                            "gz23",
                                            "gz11",
                                            "gz41",
                                            "gz27",
                                            "gz19",
                                            "gz24",
                                            "gz14",
                                            "gz42",
                                            "gz15",
                                            "gz13",
                                            "gz16",
                                            "gz43",
                                            "gz28",
                                            "gz21",
                                            "gz20",
                                            "gz17",
                                            "gz44",
                                            "gz45",
                                            "gz46",
                                            "gz47",
                                            "gz48",
                                            "gz49",
                                            "gz50",
                                            "gz51",
                                            "gz52",
                                            "gz53",
                                            "gz54",
                                            "gz55",
                                            "gz56",
                                            "gz57",
                                            "gz58",
                                            "gz59",
                                            "gz60",
                                            "gz61",
                                            "gz62",
                                            "gz63",
                                            "gz64",
                                            "gz65",
                                            "gz66",
                                            "gz67",
                                            "gz68",
                                            "gz69",
                                            "gz70",
                                            "gz71",
                                            "gz72",
                                            "PXH",
                                            "SHRID",
                                            "SHR",
                                            "SHRQ",
                                            "gz73",
                                            "gz74",
                                            "gz75",
                                            "gz76",
                                            "gz77",
                                            "gz78",
                                            "gz79",
                                            "gz80",
                                            "gz81",
                                            "gz82",
                                            "gz83",
                                            "gz84",
                                            "gz85",
                                            "gz86",
                                            "gz87",
                                            "gz88",
                                            "gz89",
                                            "gz90",
                                            "gz91",
                                            "gz92",
                                            "gz93",
                                            "gz94",
                                            "gz95",
                                            "gz96",
                                            "gz97",
                                            "gz98",
                                            "gz99",
                                            "gz100",
                                            "gz101",
                                            "gz102",
                                            "gz103",
                                            "gz104",
                                            "gz105",
                                            "gz106",
                                            "gz107",
                                            "gz108",
                                            "gz109",
                                            "gz110",
                                            "gz111",
                                            "gz112",
                                            "gz113",
                                            "gz114",
                                            "gz115",
                                            "gz116",
                                            "gz117",
                                            "gz118",
                                            "gz119",
                                            "gz120",
                                            "gz121",
                                            "gz122",
                                            "gz123",
                                            "gz124",
                                            "gz125",
                                            "gz126",
                                            "gz127",
                                            "gz128",
                                            "gz129",
                                            "gz130",
                                            "gz131",
                                            "gz132",
                                            "gz133",
                                            "gz134",
                                            "gz135",
                                            "gz136",
                                            "gz137",
                                            "gz138",
                                            "gz139",
                                            "gz140",
                                            "gz141",
                                            "gz142",
                                            "gz143",
                                            "gz144",
                                            "gz145",
                                            "gz146",
                                            "gz147",
                                            "gz148",
                                            "gz149",
                                            "gz150",
                                            "gz151",
                                            "gz152",
                                            "gz153",
                                            "gz154",
                                            "gz155",
                                            "gz156",
                                            "gz157",
                                            "gz158",
                                            "gz159",
                                            "gz160",
                                            "gz161",
                                            "gz162",
                                            "gz163",
                                            "gz164",
                                            "gz165",
                                            "gz166",
                                            "gz167",
                                            "gz168",
                                            "gz169",
                                            "gz170",
                                            "gz171",
                                            "gz172",
                                            "gz173",
                                            "gz174",
                                            "gz175",
                                            "gz176",
                                            "gz177",
                                            "gz178",
                                            "gz179",
                                            "gz180",
                                            "gz181",
                                            "gz182",
                                            "gz183",
                                            "gz184",
                                            "gz185",
                                            "gz186",
                                            "gz187",
                                            "gz188",
                                            "gz189",
                                            "gz190",
                                            "gz191",
                                            "gz192",
                                            "gz193",
                                            "gz194",
                                            "gz195",
                                            "gz196",
                                            "gz197",
                                            "ZJLX",
                                            "ZNJY",
                                            "JXJY",
                                            "ZFDKLX",
                                            "ZFZJ",
                                            "SYLR",
                                            "QYZYNJ",
                                            "SYJKBX",
                                            "SYYLBX",
                                            "YCXJJ",
                                            "YCXJJGS",
                                            "gz198",
                                            "gz199",
                                            "gz200",
                                            "gz201",
                                            "gz202",
                                            "gz203",
                                            "gz204",
                                            "gz205",
                                            "gz206",
                                            "gz207",
                                            "gz208",
                                            "gz209",
                                            "gz210",
                                            "gz211",
                                            "gz212",
                                            "gz213",
                                            "gz214",
                                            "gz215",
                                            "gz216",
                                            "gz217",
                                            "gz218",
                                            "gz219",
                                            "gz220",
                                            "gz221",
                                            "gz222",
                                            "gz223",
                                            "gz224",
                                            "gz225",
                                            "gz226",
                                            "gz227",
                                            "gz228",
                                            "gz229",
                                            "gz230",
                                            "gz231",
                                            "gz232",
                                            "gz233",
                                            "gz234",
                                            "gz235",
                                            "gz236",
                                            "gz237",
                                            "gz238",
                                            "gz239",
                                            "gz240",
                                            "gz241",
                                            "gz242")
      VALUES (#{item.gsdm,jdbcType=VARCHAR},
              #{item.gsmc,jdbcType=VARCHAR},
              #{item.ffnd,jdbcType=VARCHAR}, #{item.lbdm,jdbcType=VARCHAR},
              #{item.lbmc,jdbcType=VARCHAR}, #{item.bmdm,jdbcType=VARCHAR},
              #{item.bmmc,jdbcType=VARCHAR}, #{item.lxdm,jdbcType=VARCHAR},
              #{item.lxmc,jdbcType=VARCHAR}, #{item.zydm,jdbcType=VARCHAR},
              #{item.zyxm,jdbcType=VARCHAR}, #{item.jsff,jdbcType=VARCHAR},
              #{item.yhdm,jdbcType=VARCHAR}, #{item.grzh,jdbcType=VARCHAR},
              #{item.ffDate,jdbcType=VARCHAR}, #{item.ffcs,jdbcType=VARCHAR},
              #{item.jbgz,jdbcType=VARCHAR}, #{item.gwgz,jdbcType=VARCHAR},
              #{item.glgz,jdbcType=VARCHAR}, #{item.flf,jdbcType=VARCHAR},
              #{item.fsbt,jdbcType=VARCHAR}, #{item.jb,jdbcType=VARCHAR},
              #{item.jj,jdbcType=VARCHAR}, #{item.sjts,jdbcType=VARCHAR},
              #{item.sjkk,jdbcType=VARCHAR}, #{item.bjts,jdbcType=VARCHAR},
              #{item.bjkk,jdbcType=VARCHAR}, #{item.gwjt,jdbcType=VARCHAR},
              #{item.xlf,jdbcType=VARCHAR}, #{item.jbf,jdbcType=VARCHAR},
              #{item.shbz,jdbcType=VARCHAR}, #{item.yfhj,jdbcType=VARCHAR},
              #{item.kkhj,jdbcType=VARCHAR}, #{item.sfhj,jdbcType=VARCHAR},
              #{item.bykl,jdbcType=VARCHAR}, #{item.sycl,jdbcType=VARCHAR},
              #{item.grsds,jdbcType=VARCHAR}, #{item.sfzh,jdbcType=VARCHAR},
              #{item.zc,jdbcType=VARCHAR}, #{item.yskmdm,jdbcType=VARCHAR},
              #{item.yskmmc,jdbcType=VARCHAR}, #{item.zw,jdbcType=VARCHAR},
              #{item.DZKDM,jdbcType=VARCHAR}, #{item.DZKMC,jdbcType=VARCHAR},
              #{item.ksx,jdbcType=VARCHAR}, #{item.klbcgs,jdbcType=VARCHAR},
              #{item.havesh,jdbcType=VARCHAR}, #{item.havepf,jdbcType=VARCHAR},
              #{item.deptord,jdbcType=VARCHAR}, #{item.yhdm1,jdbcType=VARCHAR},
              #{item.grzh1,jdbcType=VARCHAR}, #{item.yhdm2,jdbcType=VARCHAR},
              #{item.grzh2,jdbcType=VARCHAR}, #{item.ksbfs,jdbcType=VARCHAR},
              #{item.yhmc,jdbcType=VARCHAR}, #{item.pcxh,jdbcType=VARCHAR},
              #{item.ffyf,jdbcType=VARCHAR}, #{item.calc,jdbcType=VARCHAR},
              #{item.gz6,jdbcType=VARCHAR}, #{item.gz7,jdbcType=VARCHAR},
              #{item.gz8,jdbcType=VARCHAR}, #{item.gz9,jdbcType=VARCHAR},
              #{item.gz2,jdbcType=VARCHAR}, #{item.gz5,jdbcType=VARCHAR},
              #{item.gz1,jdbcType=VARCHAR}, #{item.gz3,jdbcType=VARCHAR},
              #{item.gz4,jdbcType=VARCHAR}, #{item.gz32,jdbcType=VARCHAR},
              #{item.gz31,jdbcType=VARCHAR}, #{item.gz26,jdbcType=VARCHAR},
              #{item.gz29,jdbcType=VARCHAR}, #{item.gz30,jdbcType=VARCHAR},
              #{item.gz33,jdbcType=VARCHAR}, #{item.gz34,jdbcType=VARCHAR},
              #{item.gz35,jdbcType=VARCHAR}, #{item.gz18,jdbcType=VARCHAR},
              #{item.gz36,jdbcType=VARCHAR}, #{item.gz25,jdbcType=VARCHAR},
              #{item.gz37,jdbcType=VARCHAR}, #{item.gz38,jdbcType=VARCHAR},
              #{item.gz39,jdbcType=VARCHAR}, #{item.gz22,jdbcType=VARCHAR},
              #{item.gz10,jdbcType=VARCHAR}, #{item.gz40,jdbcType=VARCHAR},
              #{item.gz12,jdbcType=VARCHAR}, #{item.gz23,jdbcType=VARCHAR},
              #{item.gz11,jdbcType=VARCHAR}, #{item.gz41,jdbcType=VARCHAR},
              #{item.gz27,jdbcType=VARCHAR}, #{item.gz19,jdbcType=VARCHAR},
              #{item.gz24,jdbcType=VARCHAR}, #{item.gz14,jdbcType=VARCHAR},
              #{item.gz42,jdbcType=VARCHAR}, #{item.gz15,jdbcType=VARCHAR},
              #{item.gz13,jdbcType=VARCHAR}, #{item.gz16,jdbcType=VARCHAR},
              #{item.gz43,jdbcType=VARCHAR}, #{item.gz28,jdbcType=VARCHAR},
              #{item.gz21,jdbcType=VARCHAR}, #{item.gz20,jdbcType=VARCHAR},
              #{item.gz17,jdbcType=VARCHAR}, #{item.gz44,jdbcType=VARCHAR},
              #{item.gz45,jdbcType=VARCHAR}, #{item.gz46,jdbcType=VARCHAR},
              #{item.gz47,jdbcType=VARCHAR}, #{item.gz48,jdbcType=VARCHAR},
              #{item.gz49,jdbcType=VARCHAR}, #{item.gz50,jdbcType=VARCHAR},
              #{item.gz51,jdbcType=VARCHAR}, #{item.gz52,jdbcType=VARCHAR},
              #{item.gz53,jdbcType=VARCHAR}, #{item.gz54,jdbcType=VARCHAR},
              #{item.gz55,jdbcType=VARCHAR}, #{item.gz56,jdbcType=VARCHAR},
              #{item.gz57,jdbcType=VARCHAR}, #{item.gz58,jdbcType=VARCHAR},
              #{item.gz59,jdbcType=VARCHAR}, #{item.gz60,jdbcType=VARCHAR},
              #{item.gz61,jdbcType=VARCHAR}, #{item.gz62,jdbcType=VARCHAR},
              #{item.gz63,jdbcType=VARCHAR}, #{item.gz64,jdbcType=VARCHAR},
              #{item.gz65,jdbcType=VARCHAR}, #{item.gz66,jdbcType=VARCHAR},
              #{item.gz67,jdbcType=VARCHAR}, #{item.gz68,jdbcType=VARCHAR},
              #{item.gz69,jdbcType=VARCHAR}, #{item.gz70,jdbcType=VARCHAR},
              #{item.gz71,jdbcType=VARCHAR}, #{item.gz72,jdbcType=VARCHAR},
              #{item.PXH,jdbcType=VARCHAR}, #{item.SHRID,jdbcType=VARCHAR},
              #{item.SHR,jdbcType=VARCHAR}, #{item.SHRQ,jdbcType=VARCHAR},
              #{item.gz73,jdbcType=VARCHAR}, #{item.gz74,jdbcType=VARCHAR},
              #{item.gz75,jdbcType=VARCHAR}, #{item.gz76,jdbcType=VARCHAR},
              #{item.gz77,jdbcType=VARCHAR}, #{item.gz78,jdbcType=VARCHAR},
              #{item.gz79,jdbcType=VARCHAR}, #{item.gz80,jdbcType=VARCHAR},
              #{item.gz81,jdbcType=VARCHAR}, #{item.gz82,jdbcType=VARCHAR},
              #{item.gz83,jdbcType=VARCHAR}, #{item.gz84,jdbcType=VARCHAR},
              #{item.gz85,jdbcType=VARCHAR}, #{item.gz86,jdbcType=VARCHAR},
              #{item.gz87,jdbcType=VARCHAR}, #{item.gz88,jdbcType=VARCHAR},
              #{item.gz89,jdbcType=VARCHAR}, #{item.gz90,jdbcType=VARCHAR},
              #{item.gz91,jdbcType=VARCHAR}, #{item.gz92,jdbcType=VARCHAR},
              #{item.gz93,jdbcType=VARCHAR}, #{item.gz94,jdbcType=VARCHAR},
              #{item.gz95,jdbcType=VARCHAR}, #{item.gz96,jdbcType=VARCHAR},
              #{item.gz97,jdbcType=VARCHAR}, #{item.gz98,jdbcType=VARCHAR},
              #{item.gz99,jdbcType=VARCHAR}, #{item.gz100,jdbcType=VARCHAR},
              #{item.gz101,jdbcType=VARCHAR}, #{item.gz102,jdbcType=VARCHAR},
              #{item.gz103,jdbcType=VARCHAR}, #{item.gz104,jdbcType=VARCHAR},
              #{item.gz105,jdbcType=VARCHAR}, #{item.gz106,jdbcType=VARCHAR},
              #{item.gz107,jdbcType=VARCHAR}, #{item.gz108,jdbcType=VARCHAR},
              #{item.gz109,jdbcType=VARCHAR}, #{item.gz110,jdbcType=VARCHAR},
              #{item.gz111,jdbcType=VARCHAR}, #{item.gz112,jdbcType=VARCHAR},
              #{item.gz113,jdbcType=VARCHAR}, #{item.gz114,jdbcType=VARCHAR},
              #{item.gz115,jdbcType=VARCHAR}, #{item.gz116,jdbcType=VARCHAR},
              #{item.gz117,jdbcType=VARCHAR}, #{item.gz118,jdbcType=VARCHAR},
              #{item.gz119,jdbcType=VARCHAR}, #{item.gz120,jdbcType=VARCHAR},
              #{item.gz121,jdbcType=VARCHAR}, #{item.gz122,jdbcType=VARCHAR},
              #{item.gz123,jdbcType=VARCHAR}, #{item.gz124,jdbcType=VARCHAR},
              #{item.gz125,jdbcType=VARCHAR},
              #{item.gz126,jdbcType=VARCHAR}, #{item.gz127,jdbcType=VARCHAR},
              #{item.gz128,jdbcType=VARCHAR}, #{item.gz129,jdbcType=VARCHAR},
              #{item.gz130,jdbcType=VARCHAR}, #{item.gz131,jdbcType=VARCHAR},
              #{item.gz132,jdbcType=VARCHAR}, #{item.gz133,jdbcType=VARCHAR},
              #{item.gz134,jdbcType=VARCHAR}, #{item.gz135,jdbcType=VARCHAR},
              #{item.gz136,jdbcType=VARCHAR}, #{item.gz137,jdbcType=VARCHAR},
              #{item.gz138,jdbcType=VARCHAR}, #{item.gz139,jdbcType=VARCHAR},
              #{item.gz140,jdbcType=VARCHAR}, #{item.gz141,jdbcType=VARCHAR},
              #{item.gz142,jdbcType=VARCHAR}, #{item.gz143,jdbcType=VARCHAR},
              #{item.gz144,jdbcType=VARCHAR}, #{item.gz145,jdbcType=VARCHAR},
              #{item.gz146,jdbcType=VARCHAR}, #{item.gz147,jdbcType=VARCHAR},
              #{item.gz148,jdbcType=VARCHAR}, #{item.gz149,jdbcType=VARCHAR},
              #{item.gz150,jdbcType=VARCHAR}, #{item.gz151,jdbcType=VARCHAR},
              #{item.gz152,jdbcType=VARCHAR}, #{item.gz153,jdbcType=VARCHAR},
              #{item.gz154,jdbcType=VARCHAR}, #{item.gz155,jdbcType=VARCHAR},
              #{item.gz156,jdbcType=VARCHAR}, #{item.gz157,jdbcType=VARCHAR},
              #{item.gz158,jdbcType=VARCHAR}, #{item.gz159,jdbcType=VARCHAR},
              #{item.gz160,jdbcType=VARCHAR}, #{item.gz161,jdbcType=VARCHAR},
              #{item.gz162,jdbcType=VARCHAR}, #{item.gz163,jdbcType=VARCHAR},
              #{item.gz164,jdbcType=VARCHAR}, #{item.gz165,jdbcType=VARCHAR},
              #{item.gz166,jdbcType=VARCHAR}, #{item.gz167,jdbcType=VARCHAR},
              #{item.gz168,jdbcType=VARCHAR}, #{item.gz169,jdbcType=VARCHAR},
              #{item.gz170,jdbcType=VARCHAR}, #{item.gz171,jdbcType=VARCHAR},
              #{item.gz172,jdbcType=VARCHAR}, #{item.gz173,jdbcType=VARCHAR},
              #{item.gz174,jdbcType=VARCHAR}, #{item.gz175,jdbcType=VARCHAR},
              #{item.gz176,jdbcType=VARCHAR}, #{item.gz177,jdbcType=VARCHAR},
              #{item.gz178,jdbcType=VARCHAR}, #{item.gz179,jdbcType=VARCHAR},
              #{item.gz180,jdbcType=VARCHAR}, #{item.gz181,jdbcType=VARCHAR},
              #{item.gz182,jdbcType=VARCHAR}, #{item.gz183,jdbcType=VARCHAR},
              #{item.gz184,jdbcType=VARCHAR}, #{item.gz185,jdbcType=VARCHAR},
              #{item.gz186,jdbcType=VARCHAR}, #{item.gz187,jdbcType=VARCHAR},
              #{item.gz188,jdbcType=VARCHAR}, #{item.gz189,jdbcType=VARCHAR},
              #{item.gz190,jdbcType=VARCHAR}, #{item.gz191,jdbcType=VARCHAR},
              #{item.gz192,jdbcType=VARCHAR}, #{item.gz193,jdbcType=VARCHAR},
              #{item.gz194,jdbcType=VARCHAR}, #{item.gz195,jdbcType=VARCHAR},
              #{item.gz196,jdbcType=VARCHAR}, #{item.gz197,jdbcType=VARCHAR},
              #{item.ZJLX,jdbcType=VARCHAR}, #{item.ZNJY,jdbcType=VARCHAR},
              #{item.JXJY,jdbcType=VARCHAR}, #{item.ZFDKLX,jdbcType=VARCHAR},
              #{item.ZFZJ,jdbcType=VARCHAR}, #{item.SYLR,jdbcType=VARCHAR},
              #{item.QYZYNJ,jdbcType=VARCHAR}, #{item.SYJKBX,jdbcType=VARCHAR},
              #{item.SYYLBX,jdbcType=VARCHAR}, #{item.YCXJJ,jdbcType=VARCHAR},
              #{item.YCXJJGS,jdbcType=VARCHAR}, #{item.gz198,jdbcType=VARCHAR},
              #{item.gz199,jdbcType=VARCHAR}, #{item.gz200,jdbcType=VARCHAR},
              #{item.gz201,jdbcType=VARCHAR}, #{item.gz202,jdbcType=VARCHAR},
              #{item.gz203,jdbcType=VARCHAR}, #{item.gz204,jdbcType=VARCHAR},
              #{item.gz205,jdbcType=VARCHAR}, #{item.gz206,jdbcType=VARCHAR},
              #{item.gz207,jdbcType=VARCHAR}, #{item.gz208,jdbcType=VARCHAR},
              #{item.gz209,jdbcType=VARCHAR}, #{item.gz210,jdbcType=VARCHAR},
              #{item.gz211,jdbcType=VARCHAR}, #{item.gz212,jdbcType=VARCHAR},
              #{item.gz213,jdbcType=VARCHAR}, #{item.gz214,jdbcType=VARCHAR},
              #{item.gz215,jdbcType=VARCHAR}, #{item.gz216,jdbcType=VARCHAR},
              #{item.gz217,jdbcType=VARCHAR}, #{item.gz218,jdbcType=VARCHAR},
              #{item.gz219,jdbcType=VARCHAR}, #{item.gz220,jdbcType=VARCHAR},
              #{item.gz221,jdbcType=VARCHAR}, #{item.gz222,jdbcType=VARCHAR},
              #{item.gz223,jdbcType=VARCHAR}, #{item.gz224,jdbcType=VARCHAR},
              #{item.gz225,jdbcType=VARCHAR}, #{item.gz226,jdbcType=VARCHAR},
              #{item.gz227,jdbcType=VARCHAR}, #{item.gz228,jdbcType=VARCHAR},
              #{item.gz229,jdbcType=VARCHAR}, #{item.gz230,jdbcType=VARCHAR},
              #{item.gz231,jdbcType=VARCHAR}, #{item.gz232,jdbcType=VARCHAR},
              #{item.gz233,jdbcType=VARCHAR}, #{item.gz234,jdbcType=VARCHAR},
              #{item.gz235,jdbcType=VARCHAR}, #{item.gz236,jdbcType=VARCHAR},
              #{item.gz237,jdbcType=VARCHAR}, #{item.gz238,jdbcType=VARCHAR},
              #{item.gz239,jdbcType=VARCHAR}, #{item.gz240,jdbcType=VARCHAR},
              #{item.gz241,jdbcType=VARCHAR}, #{item.gz242,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <!--重置hrm_salary_yy 表数据-->
  <update id="deleteYySalary">
    delete
    from hrm_salary_yy
    where concat(ffnd,'-',ffyf) = #{salaryMonth}
  </update>

  <!--新增工资预测任务申请-->
  <insert id="insertTask" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO hrm_salary_task("status",
                                "bchno",
                                "ff_mth",
                                "num",
                                "should_pay",
                                "reduce_pay",
                                "real_pay",
                                "temp_add",
                                "temp_reduce",
                                "remark",
                                "sign",
                                "att",
                                "att_name",
                                "crter",
                                "crte_time",
                                "active_flag",
                                "hospital_id")
    VALUES (#{status},
            #{bchno},
            #{salaryMonth},
            #{num},
            #{shouldPay},
            #{reducePay},
            #{realPay},
            #{tempAdd},
            #{tempReduce},
            #{remark},
            #{sign},
            #{att},
            #{attName},
            #{crter},
            #{crteTime},
            #{activeFlag},
            #{hospitalId});
  </insert>

  <!--逻辑删除工资预测任务申请-->
  <update id="deleteTaskApply">
      update hrm_salary_task
      set active_flag = '0'
      where id = #{id};
      update hrm_salary_hrp
      set active_flag='0'
      where parent_id = #{id};
  </update>

  <update id="updateReimId">
    UPDATE hrm_salary_really_task
    SET reim_id = CONCAT(COALESCE(reim_id, ''), #{reimId},',')
    WHERE id = #{salaryTaskId}
  </update>

  <insert id="insertReallySalaryTask" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
    INSERT INTO "public"."hrm_salary_really_task" ("task_id",
                                                   "status",
                                                   "ff_mth",
                                                   "num",
                                                   "temp_add",
                                                   "temp_reduce",
                                                   "active_flag",
                                                   "hospital_id")
    VALUES (#{id},
            #{status},
            #{salaryMonth},
            #{num},
            #{tempAdd},
            #{tempReduce},
            #{activeFlag},
            #{hospitalId});
  </insert>

  <update id="updateReallySalaryStatus">
      update hrm_salary_really_task
      set status      = #{status},
          num         = #{num},
          should_pay  = #{shouldPay},
          reduce_pay  = #{reducePay},
          real_pay    = #{realPay},
          temp_add    = #{tempAdd},
          temp_reduce = #{tempReduce},
          crter       = #{crter},
          crte_time   = #{crteTime},
          remark      = #{cfmRemark},
          sign        = #{sign},
          att         = #{att},
          att_name    = #{attName},
          hospital_id = #{hospitalId}
      where id = #{id};

      <if test="status != null and status != '' and status == 2">
          update hrm_salary_task
          set status = '2'
          where id = #{taskId};
          update hrm_salary_hrp
          set active_flag='0'
          where parent_id = #{taskId};
      </if>
  </update>

  <update id="updateReallySalaryTask">
      update hrm_salary_really_task
      set should_pay  = #{shouldPay},
          reduce_pay  = #{reducePay},
          real_pay    = #{realPay},
          entp_pay    = #{entpPay},
          labor_union = #{laborUnion}
      where id = #{id}
  </update>
</mapper>
