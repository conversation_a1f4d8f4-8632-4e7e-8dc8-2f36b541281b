<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.workforceMgt.mapper.read.EmpMyScduTagReadMapper">

    <sql id="fields">

    </sql>

    <select id="queryList" resultType="com.jp.med.hrm.modules.workforceMgt.vo.EmpMyScduTagVo">
        select id as id,
               tag_name as tagName,
               tag_type as tagType,
               "desc" as "desc",
               crter as crter,
               create_time as createTime,
               hospital_id as hospitalId,
               active_flag as activeFlag
        from hrm_my_scdu_tag
        where active_flag = '1'
        and org_id = #{orgId,jdbcType=VARCHAR}
    </select>


    <select id="verifyTag" resultType="java.lang.Integer">
        select count(0)
        from hrm_my_scdu_tag
        where tag_name = #{tagName,jdbcType=VARCHAR}
          and org_id = #{orgId,jdbcType=VARCHAR}
    </select>
</mapper>
