<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.workforceMgt.mapper.write.EmpMyScduWriteMapper">
    <!-- 通过日期删除数据 -->
    <delete id="removeByDate">
        update hrm_my_scdu
        set active_flag='0'
        WHERE emp_code IN
        <foreach collection="scduList" item="item" open="(" close=")" separator=",">
            #{item.empCode,jdbcType=VARCHAR}
        </foreach>
        AND scdu_date IN
        <foreach collection="scduList" item="item" open="(" close=")" separator=",">
            #{item.scduDate,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 通过员工删除数据 -->
    <update id="removeByEmp">
        update hrm_my_scdu
        set active_flag='0'
        WHERE emp_code = #{empCode,jdbcType=VARCHAR}
          AND scdu_date = #{scduDate,jdbcType=VARCHAR}
    </update>

    <update id="removeByEmps">
        <foreach collection="dtos" item="item" separator=";">
            update hrm_my_scdu
            set active_flag='0'
            WHERE emp_code = #{item.empCode,jdbcType=VARCHAR}
            AND scdu_date = #{item.scduDate,jdbcType=VARCHAR}
        </foreach>
    </update>

    <!--审核拒绝，清除leave_id-->
    <update id="cleanLeaveId">
        update hrm_my_scdu
        set leave_id = NULL
        where leave_id = (select id
                          from hrm_leave
                          where bchno = #{bchno,jdbcType=VARCHAR})
    </update>

    <!--主动销假 清除排班leaveId(清除当前日期之后的排班leaveId)-->
    <update id="cleanLeaveId2">
        update hrm_my_scdu a
        set leave_id = NULL
        from hrm_leave_end b
        where b.bchno = #{bchno,jdbcType=VARCHAR}
          and a.leave_id = b.leave_id
          and (to_timestamp(a.scdu_date, 'yyyy-mm-dd') + interval '12 hours' &gt; to_timestamp(b.actual_end_time, 'yyyy-mm-dd hh24:mi:ss'))
    </update>

    <update id="backConfirmOrg">
        UPDATE hrm_my_scdu
        SET confirm = '0'
        WHERE emp_code IN (SELECT A.emp_code FROM hrm_employee_info A WHERE A.org_id = #{orgId,jdbcType=VARCHAR})
          and to_date(scdu_date, 'yyyy-MM-dd') BETWEEN to_date(#{startDate,jdbcType=VARCHAR}, 'yyyy-MM-dd') and to_date(#{endDate,jdbcType=VARCHAR}, 'yyyy-MM-dd')
          AND active_flag = '1'
    </update>


    <update id="upsertScduSort">
        insert into hrm_my_scdu_sort (emp_code, sort, nurse_level, group_name, hospital_id)
        values (#{empCode}, #{sort}, #{nurseLevel}, #{groupName}, #{hospitalId})
        on conflict (emp_code) do update set sort=excluded.sort,nurse_level=excluded.nurse_level,group_name=excluded.group_name
    </update>

    <update id="backConfirmEmp">
        UPDATE hrm_my_scdu
        SET confirm = '0'
        WHERE emp_code IN
        <foreach collection="empCodes" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and to_date(scdu_date, 'yyyy-MM-dd') BETWEEN to_date(#{startDate,jdbcType=VARCHAR}
        , 'yyyy-MM-dd')
        and to_date(#{endDate,jdbcType=VARCHAR}
        , 'yyyy-MM-dd')
        AND active_flag = '1'
    </update>
</mapper>
