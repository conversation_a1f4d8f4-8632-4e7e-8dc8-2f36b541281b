package com.jp.med.hrm.modules.orgMap.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.hrm.modules.orgMap.mapper.read.HrmOrgAgencyMapReadMapper;
import com.jp.med.hrm.modules.orgMap.dto.HrmOrgAgencyMapDto;
import com.jp.med.hrm.modules.orgMap.vo.HrmOrgAgencyMapVo;
import com.jp.med.hrm.modules.orgMap.service.read.HrmOrgAgencyMapReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class HrmOrgAgencyMapReadServiceImpl extends ServiceImpl<HrmOrgAgencyMapReadMapper, HrmOrgAgencyMapDto> implements HrmOrgAgencyMapReadService {

    @Autowired
    private HrmOrgAgencyMapReadMapper hrmOrgAgencyMapReadMapper;

    @Override
    public List<HrmOrgAgencyMapVo> queryList(HrmOrgAgencyMapDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        dto.setSqlAutowiredHospitalCondition(true);
        return hrmOrgAgencyMapReadMapper.queryList(dto);
    }

    @Override
    public List<HrmOrgAgencyMapVo> queryNoPageList(HrmOrgAgencyMapDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return hrmOrgAgencyMapReadMapper.queryList(dto);
    }


}
