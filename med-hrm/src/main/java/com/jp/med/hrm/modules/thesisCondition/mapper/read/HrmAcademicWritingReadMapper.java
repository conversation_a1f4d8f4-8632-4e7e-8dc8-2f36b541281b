package com.jp.med.hrm.modules.thesisCondition.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.hrm.modules.thesisCondition.dto.HrmAcademicWritingDto;
import com.jp.med.hrm.modules.thesisCondition.vo.HrmAcademicWritingVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface HrmAcademicWritingReadMapper extends BaseMapper<HrmAcademicWritingDto> {

    List<HrmAcademicWritingVo> queryList(HrmAcademicWritingDto dto);
}
