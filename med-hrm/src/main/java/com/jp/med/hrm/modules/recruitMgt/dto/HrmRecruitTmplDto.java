package com.jp.med.hrm.modules.recruitMgt.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 招聘模板
 * <AUTHOR>
 * @email -
 * @date 2023-11-14 13:54:10
 */
@Data
@TableName("hrm_recruit_tmpl" )
public class HrmRecruitTmplDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 标题 */
    @TableField("ttl")
    private String ttl;

    /** 说明 */
    @TableField("decr")
    private String decr;

    /** 模板 */
    @TableField("tmpl")
    private String tmpl;

    /** 创建时间 */
    @TableField("create_time")
    private String createTime;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 医疗机构ID */
    @TableField("hospital_id")
    private String hospitalId;

    /** 有效标志 */
    @TableField("active_flag")
    private String activeFlag;

    /** 填写页面的url */
    @TableField(exist = false)
    private String url;
}
