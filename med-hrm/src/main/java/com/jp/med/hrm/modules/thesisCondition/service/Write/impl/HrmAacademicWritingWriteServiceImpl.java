package com.jp.med.hrm.modules.thesisCondition.service.Write.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.bpm.BpmProcessInstanceCreateReqDTO;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.BpmProcessInstanceFeignApi;
import com.jp.med.common.util.FeignExecuteUtil;
import com.jp.med.hrm.modules.emp.mapper.read.EmpEmployeeInfoReadMapper;
import com.jp.med.hrm.modules.org.mapper.read.HrmOrgReadMapper;
import com.jp.med.hrm.modules.thesisCondition.dto.HrmAcademicWritingDto;
import com.jp.med.hrm.modules.thesisCondition.mapper.read.HrmAcademicWritingReadMapper;
import com.jp.med.hrm.modules.thesisCondition.mapper.write.HrmAcademicWritingWriteMapper;
import com.jp.med.hrm.modules.thesisCondition.service.Write.HrmAacademicWritingWriteService;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Service
public class HrmAacademicWritingWriteServiceImpl
        extends ServiceImpl<HrmAcademicWritingReadMapper, HrmAcademicWritingDto>
        implements HrmAacademicWritingWriteService {

    @Autowired
    HrmAcademicWritingWriteMapper hrmAcademicWritingWriteMapper;
    @Autowired
    private BpmProcessInstanceFeignApi bpmProcessInstanceFeignApi;
    @Autowired
    private HrmOrgReadMapper hrmOrgReadMapper;
    @Autowired
    private EmpEmployeeInfoReadMapper empEmployeeInfoReadMapper;

    @Override
    public void saveThesis(HrmAcademicWritingDto dto) {
        if (StrUtil.isNotEmpty(dto.getDtoJson())) {
            HrmAcademicWritingDto hrmAcademicWritingDto = JSONObject.parseObject(dto.getDtoJson(),
                    HrmAcademicWritingDto.class);
            hrmAcademicWritingDto.setSysUser(dto.getSysUser());
            hrmAcademicWritingDto.setHospitalId(dto.getHospitalId());
            dto = hrmAcademicWritingDto;
        }

        //插入申请表
        insertTask(dto);

        //发起BPM流程
        String processInstanceCode = createProcessInstance(dto);

        //将流程实例编号，更新到申请记录中
        UpdateWrapper<HrmAcademicWritingDto> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", dto.getId()).set("process_instance_code", processInstanceCode);
        hrmAcademicWritingWriteMapper.update(null, updateWrapper);


    }


    private void insertTask(HrmAcademicWritingDto dto) {

        //创建人相关信息
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String creatorCode = StrUtil.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
        dto.setId(null);
        dto.setChkState(MedConst.TYPE_0);
        dto.setCrter(creatorCode);
        dto.setCrteTime(DateUtil.now());
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        dto.setHospitalId(dto.getHospitalId());

        baseMapper.insert(dto);

//        hrmReaserchThesisConditionWriteMapper.insert(dto);
    }

    /**
     * 发起BPM流程
     *
     * @param dto
     */
    private String createProcessInstance(HrmAcademicWritingDto dto) {
        if (dto == null || StrUtil.isEmpty(dto.getProcessDefinitionKey())) {
            return null;
        }


        String[] fields = {
                "id",                       // 主键
                "processInstanceCode",      // 流程实例编码
                "thesisName",              // 项目名称
                "department",               // 所属部门
                "mainResearcher",           // 主要研究者
                "education",            //学历
                "editorRankZhu",               //主编排位
                "editorRankFu",
                "editorRankCan",
                "publictionType",
                "mainChapter",
                "wordCount",
                "publisherName",
                "publisherType",
                "contentIntroduction"
        };


        // 实例参数封装
        Map<String, Object> processInstanceVariables = new HashMap<>();
        Map<String, Object> map = BeanUtil.beanToMap(dto, new HashMap<>(), false, true);
        for (String field : fields) {
            if (map.containsKey(field)) {
                switch (field) {
                    case "department":
                        processInstanceVariables.put("department", hrmOrgReadMapper.selectOrgById(dto.getDepartment()).getOrgName());
                        break;
                    case "mainResearcher":
                        processInstanceVariables.put("mainResearcher", empEmployeeInfoReadMapper.queryEmpNamesByEmpCodes(Arrays.asList(dto.getMainResearcher().split(","))));
                        break;
                    default:
                        processInstanceVariables.put(field, map.get(field));
                        break;
                }
            }
        }


        processInstanceVariables.put("nextApprover", dto.getNextApprover());


        val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
        bpmProcessInstanceCreateReqDTO
                .setUserId(dto.getCrter())
                .setProcessDefinitionKey(dto.getProcessDefinitionKey())
                .setVariables(processInstanceVariables)
                .setBusinessKey(String.valueOf(dto.getId()));

        //发起BPM流程
        CommonFeignResult processInstance = FeignExecuteUtil
                .execute(bpmProcessInstanceFeignApi.createProcessInstance(bpmProcessInstanceCreateReqDTO));
        if (!StrUtil.equals(processInstance.get("code").toString(), "200")) {
            throw new AppException("生成BPM流程异常");
        }
        return processInstance.get("data").toString();
    }
}
