package com.jp.med.hrm.modules.thesisCondition.controller;


import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.hrm.modules.thesisCondition.dto.HrmPaperSubmissionDto;
import com.jp.med.hrm.modules.thesisCondition.service.Write.HrmPaperSubmissionWriteService;
import com.jp.med.hrm.modules.thesisCondition.service.read.HrmPaperSubmissionReadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(value = "论文投稿备案" ,tags = "论文投稿备案")
@RestController
@RequestMapping("paperSubmission")
public class HrmPaperSubmissionController {

    @Autowired
    HrmPaperSubmissionWriteService hrmPaperSubmissionWriteService;
    @Autowired
    HrmPaperSubmissionReadService hrmPaperSubmissionReadService;


    @ApiOperation("新增论文投稿备案")
    @PostMapping("save")
    public CommonResult<?> save(HrmPaperSubmissionDto dto){
        hrmPaperSubmissionWriteService.saveThesis(dto);
        return CommonResult.success();
    }


    @ApiOperation("查询论文投稿申请")
    @PostMapping("/pageQueryThesis")
    public CommonResult<?> list(@RequestBody HrmPaperSubmissionDto dto) {
        return CommonResult.paging(hrmPaperSubmissionReadService.queryPageList(dto));
    }

    @ApiOperation("根据id查询(详情)")
    @GetMapping("/getBpmDetailById")
    public CommonResult<?> getBpmDetailById(@RequestParam Integer id) {
        return CommonResult.success(hrmPaperSubmissionReadService.getBpmDetailById(id));
    }

    /**
     * 列表
     */
    @ApiOperation("查询申请表")
    @PostMapping("/list")
    public CommonResult<?> listThesis(@RequestBody HrmPaperSubmissionDto dto) {
        return CommonResult.success(hrmPaperSubmissionReadService.queryPageList(dto));
    }

    /**
     * 获取申请表pdf外链地址
     */
    @ApiOperation("获取申请表pdf外链地址")
    @GetMapping("/getPdfUrl")
    public CommonResult<String> getPdfUrl() {
        return CommonResult.success(hrmPaperSubmissionReadService.getPdfUrl());
    }
}
