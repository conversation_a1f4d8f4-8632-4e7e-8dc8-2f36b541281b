package com.jp.med.hrm.modules.researcherFunding.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 临床试验研究者经费发放申请表
 * <AUTHOR>
 * @email -
 * @date 2025-02-11 09:08:59
 */
@Data
@TableName("hrm_researcher_funding_apply")
public class HrmResearcherFundingApplyEntity {

	/** 主键 */
	@TableId("id")
	private Integer id;

	/** 流程实例编码 */
	@TableField("process_instance_code")
	private String processInstanceCode;

	/** 项目名称 */
	@TableField("project_name")
	private String projectName;

	/** 所属部门 */
	@TableField("department")
	private String department;

	/** 主要研究者 */
	@TableField("main_researcher")
	private String mainResearcher;

	/** 项目赞助方 */
	@TableField("sponsor")
	private String sponsor;

	/** 工作周期开始日期 */
	@TableField("work_period_start")
	private String workPeriodStart;

	/** 工作周期结束日期 */
	@TableField("work_period_end")
	private String workPeriodEnd;

	/** 合同例数 */
	@TableField("contract_count")
	private Integer contractCount;

	/** 入组例数 */
	@TableField("enrolled_count")
	private Integer enrolledCount;

	/** 合同金额 */
	@TableField("contract_amount")
	private BigDecimal contractAmount;

	/** 已收金额 */
	@TableField("received_amount")
	private BigDecimal receivedAmount;

	/** 质量控制 */
	@TableField("quality_control")
	private String qualityControl;

	/** 申请节点 */
	@TableField("apply_node")
	private String applyNode;

	/** 申请次数 */
	@TableField("apply_times")
	private Integer applyTimes;

	/** 申请部门 */
	@TableField("apply_department")
	private String applyDepartment;

	/** 申请金额 */
	@TableField("apply_amount")
	private BigDecimal applyAmount;

	/** 附件ID数组 */
	@TableField("att")
	private String att;

	/** 附件名称数组 */
	@TableField("att_name")
	private String attName;

	/** 备注 */
	@TableField("remark")
	private String remark;

	/** 审核状态（0:待审，1:通过，2:未通过，3：已取消） */
	@TableField("chk_state")
	private String chkState;

	/** 创建人 */
	@TableField("crter")
	private String crter;

	/** 创建时间 */
	@TableField("crte_time")
	private String crteTime;

	/** 有效标志 */
	@TableField("active_flag")
	private String activeFlag;

	/** 医疗机构id */
	@TableField("hospital_id")
	private String hospitalId;

}
