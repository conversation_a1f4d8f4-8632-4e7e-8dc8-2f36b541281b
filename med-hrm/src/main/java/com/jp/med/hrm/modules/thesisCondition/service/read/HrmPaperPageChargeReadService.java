package com.jp.med.hrm.modules.thesisCondition.service.read;

import com.jp.med.hrm.modules.thesisCondition.dto.HrmPaperPageChargeDto;
import com.jp.med.hrm.modules.thesisCondition.vo.HrmPaperPageChargeVo;

import java.util.List;

public interface HrmPaperPageChargeReadService {

    /**
     * 分页查询论文版面费申请
     * @param dto
     * @return
     */
    List<HrmPaperPageChargeVo> queryPageList(HrmPaperPageChargeDto dto);

    HrmPaperPageChargeDto getBpmDetailById(Integer id);
}
