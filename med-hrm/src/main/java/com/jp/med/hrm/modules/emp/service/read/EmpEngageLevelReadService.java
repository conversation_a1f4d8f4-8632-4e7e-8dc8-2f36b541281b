package com.jp.med.hrm.modules.emp.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.hrm.modules.emp.dto.EmpEngageLevelDto;
import com.jp.med.hrm.modules.emp.vo.EmpEngageLevelVo;

import java.util.List;

/**
 * 聘任岗位职级
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 11:12:37
 */
public interface EmpEngageLevelReadService extends IService<EmpEngageLevelDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EmpEngageLevelVo> queryList(EmpEngageLevelDto dto);
}

