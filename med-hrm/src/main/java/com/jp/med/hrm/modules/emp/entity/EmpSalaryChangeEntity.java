package com.jp.med.hrm.modules.emp.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 薪级变动
 * <AUTHOR>
 * @email -
 * @date 2023-09-15 14:18:22
 */
@Data
@TableName("hrm_salary_change")
public class EmpSalaryChangeEntity {


    @TableId("id")
	private Integer id;

	/** 薪级 */
	@TableField("salary_level")
	private String salaryLevel;

	/** 变动时间 */
	@TableField("change_time")
	private String changeTime;

	/** 备注 */
	@TableField("remark")
	private String remark;

	/** 附件 */
	@TableField("file")
	private String file;


    @TableField("status")
	private Integer status;

	/** 是否逻辑删除 */
	@TableField("is_deleted")
	private Integer isDeleted;


    @TableField("hospital_id")
	private String hospitalId;


    @TableField("emp_id")
	private Integer empId;

}
