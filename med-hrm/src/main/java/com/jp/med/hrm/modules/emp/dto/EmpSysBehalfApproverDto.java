package com.jp.med.hrm.modules.emp.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_behalf_approver")
public class EmpSysBehalfApproverDto extends CommonQueryDto {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 部门 逗号分割
     */
    private String depts;
    /**
     * 审批人
     */
    private String approver;

    @TableField(exist = false)
    private boolean sqlAutowiredHospitalCondition = true;

}
