package com.jp.med.hrm.modules.emp.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.hrm.modules.emp.mapper.read.EmpJobChangeReadMapper;
import com.jp.med.hrm.modules.emp.dto.EmpJobChangeDto;
import com.jp.med.hrm.modules.emp.vo.EmpJobChangeVo;
import com.jp.med.hrm.modules.emp.service.read.EmpJobChangeReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EmpJobChangeReadServiceImpl extends ServiceImpl<EmpJobChangeReadMapper, EmpJobChangeDto> implements EmpJobChangeReadService {

    @Autowired
    private EmpJobChangeReadMapper empJobChangeReadMapper;

    @Override
    public List<EmpJobChangeVo> queryList(EmpJobChangeDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return empJobChangeReadMapper.queryList(dto);
    }

}
