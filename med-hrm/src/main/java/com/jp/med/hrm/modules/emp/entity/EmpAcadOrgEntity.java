package com.jp.med.hrm.modules.emp.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 学术团体
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 11:14:30
 */
@Data
@TableName("hrm_acad_org")
public class EmpAcadOrgEntity {


    @TableId("id")
	private Long id;

	/** 是否参加 */
	@TableField("is_join")
	private String isJoin;

	/** 参加时间 */
	@TableField("join_time")
	private String joinTime;

	/** 学术团体 */
	@TableField("acad_org")
	private String acadOrg;

	/** 担任职务 */
	@TableField("duty")
	private String duty;

	/** 备注 */
	@TableField("ramark")
	private String ramark;

	/** 附件 */
	@TableField("att")
	private String att;

	/** 附件名称 */
	@TableField("att_name")
	private String attName;

	/** 职工id */
	@TableField("emp_id")
	private Long empId;

	/** 是否逻辑删除 */
	@TableField("is_deleted")
	private Long isDeleted;

	/** 医疗机构id */
	@TableField("hospital_id")
	private String hospitalId;

}
