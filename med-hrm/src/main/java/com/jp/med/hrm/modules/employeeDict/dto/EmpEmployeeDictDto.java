package com.jp.med.hrm.modules.employeeDict.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.Date;

/**
 * 员工字典信息
 * <AUTHOR>
 * @email -
 * @date 2023-09-20 19:10:33
 */
@Data
@TableName("hrm_employee_dict" )
public class EmpEmployeeDictDto extends CommonQueryDto {


    @TableId("id")
    private Integer id;

    /** 数据值 */
    @TableField("code_value")
    private String codeValue;

    /** 标签名 */
    @TableField("code_lable")
    private String codeLable;

    /** 类型 */
    @TableField("code_type")
    private String codeType;

    /** 排序 */
    @TableField("code_sort")
    private Integer codeSort;

    /** 父ID */
    @TableField("parent_id")
    private Integer parentId;

    /** 备注 */
    @TableField("code_remark")
    private String codeRemark;

    /** 创建人 */
    @TableField("create_by")
    private String createBy;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 更新人 */
    @TableField("last_update_by")
    private String lastUpdateBy;

    /** 上次更新时间 */
    @TableField("last_update_time")
    private Date lastUpdateTime;


    @TableField("hospital_id")
    private String hospitalId;

    /** 是否删除 */
    @TableField("del_flag")
    private Integer delFlag;

    /** 刷新查询 */
    @TableField(exist = false)
    private Boolean genChildren;
}
