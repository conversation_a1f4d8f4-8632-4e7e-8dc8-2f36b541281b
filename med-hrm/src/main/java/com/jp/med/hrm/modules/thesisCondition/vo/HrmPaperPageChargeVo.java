package com.jp.med.hrm.modules.thesisCondition.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.hrm.modules.thesisCondition.dto.PaperRecordDto;
import lombok.Data;

import java.util.List;

@Data
public class HrmPaperPageChargeVo {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 流程实例编码
     */
    private String processInstanceCode;

    /**
     * 论文名称
     */
    private String thesisName;

    /**
     * 所属部门
     */
    private String department;

    /**
     * 所属部门Name
     */
    private String departmentName;

    /**
     * 主要研究者
     */
    private String mainResearcher;

    private String mainResearcherName;


    /**
     * 审核状态（0:待审，1:通过，2:未通过，3：已取消）
     */
    private String chkState;

    /**
     * 创建人
     */
    private String crter;

    /**
     * 创建人名称
     */
    private String crterName;

    /**
     * 创建时间
     */
    private String crteTime;

    /**
     * 有效标志
     */
    private String activeFlag;

    /**
     * 医疗机构id
     */
    private String hospitalId;


    /**
     * 期刊名称
     */
    private String journalName;

    /**
     * 申请金额
     */
    private String applyCost;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 户名
     */
    private String accountName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 报销状态
     */
    private String status;

    /**
     * 签名url
     */
    private String signFile;

    /**
     * 附件
     */
    private String att;
    private String attName;

    private List<PaperRecordDto> paperRecords;
}
