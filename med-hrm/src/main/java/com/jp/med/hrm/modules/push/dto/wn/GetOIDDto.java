package com.jp.med.hrm.modules.push.dto.wn;

import com.jp.med.hrm.modules.push.dto.wn.tag.DataItem;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 数据项 DTO
 *
 * <AUTHOR>
 * @date 2025/05/12
 */
@Data
@Accessors(chain = true)
public class GetOIDDto implements DataItem {
    /**
     * VALUE_SET
     */
    private String modelCode;
    /**
     * 型号 oid
     */
    private String modelOID;
    /**
     * 医院 oid
     */
    private String hospitalOid;
    /**
     * 条数，最大200条
     */
    private Integer size;
}
