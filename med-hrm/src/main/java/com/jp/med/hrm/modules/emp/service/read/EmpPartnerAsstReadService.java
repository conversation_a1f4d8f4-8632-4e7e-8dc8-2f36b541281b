package com.jp.med.hrm.modules.emp.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.hrm.modules.emp.dto.EmpPartnerAsstDto;
import com.jp.med.hrm.modules.emp.vo.EmpPartnerAsstVo;

import java.util.List;

/**
 * 对口支援
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 11:14:30
 */
public interface EmpPartnerAsstReadService extends IService<EmpPartnerAsstDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EmpPartnerAsstVo> queryList(EmpPartnerAsstDto dto);
}

