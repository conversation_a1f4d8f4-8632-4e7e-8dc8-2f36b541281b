package com.jp.med.hrm.modules.thesisCondition.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.hrm.modules.thesisCondition.dto.PaperRecordDto;
import lombok.Data;

import java.util.List;

@Data
public class HrmResearcherThesisConditionVo {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 流程实例编码
     */
    private String processInstanceCode;

    /**
     * 论文名称
     */
    private String thesisName;

    /**
     * 所属部门
     */
    private String department;

    /**
     * 所属部门Name
     */
    private String departmentName;

    /**
     * 主要研究者
     */
    private String mainResearcher;

    private String mainResearcherName;


    /**
     * 学术会议
     */
    private String academicConference;

    /**
     * 优秀科技论文
     */
    private String excellentSciencePapers;


    /**
     * 审核状态（0:待审，1:通过，2:未通过，3：已取消）
     */
    private String chkState;

    /**
     * 创建人
     */
    private String crter;

    /**
     * 创建人名称
     */
    private String crterName;

    /**
     * 创建时间
     */
    private String crteTime;

    /**
     * 有效标志
     */
    private String activeFlag;

    /**
     * 医疗机构id
     */
    private String hospitalId;

    /**
     * 大会宣读
     */
    private String conferenceRead;

    /**
     * 壁报交流
     */
    private String posterExchange;

    /**
     * 论文汇编
     */
    private String paperCompilation;

    /**
     * 国家级
     */
    private String nationalLevel;

    /**
     * 省部级
     */
    private String provincialLevel;

    /**
     * 市厅级
     */
    private String municipalLevel;

    /**
     * 区县级
     */
    private String countyLevel;

    /**
     * 签名
     */
    private String signFile;

    /**
     * 附件
     */
    @TableField("att")
    private String att;
    @TableField("att_name")
    private String attName;

    /**
     * 论文公共信息
     */
    private List<PaperRecordDto> paperRecords;

}
