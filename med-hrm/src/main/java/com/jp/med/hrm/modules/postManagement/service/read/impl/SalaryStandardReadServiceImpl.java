package com.jp.med.hrm.modules.postManagement.service.read.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.ConvertUtil;
import com.jp.med.common.util.RedisUtil;
import com.jp.med.common.util.TreeUtil;
import com.jp.med.hrm.modules.employeeDict.dto.EmpEmployeeDictDto;
import com.jp.med.hrm.modules.employeeDict.mapper.read.EmpEmployeeDictReadMapper;
import com.jp.med.hrm.modules.employeeDict.vo.EmpEmployeeDictVo;
import com.jp.med.hrm.modules.postManagement.dto.PerformanceStandardDto;
import com.jp.med.hrm.modules.postManagement.dto.SalaryStandardDto;
import com.jp.med.hrm.modules.postManagement.mapper.read.PerformanceStandardReadMapper;
import com.jp.med.hrm.modules.postManagement.mapper.read.SalaryStandardReadMapper;
import com.jp.med.hrm.modules.postManagement.service.read.SalaryStandardReadService;
import com.jp.med.hrm.modules.postManagement.vo.BasicSalaryStandardVo;
import com.jp.med.hrm.modules.postManagement.vo.SalaryStandardVo;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional(readOnly = true)
@Service
public class SalaryStandardReadServiceImpl extends ServiceImpl<SalaryStandardReadMapper, SalaryStandardDto> implements SalaryStandardReadService {

    @Autowired
    private SalaryStandardReadMapper salaryStandardReadMapper;
    @Autowired
    private PerformanceStandardReadMapper performanceStandardReadMapper;
    @Autowired
    private EmpEmployeeDictReadMapper empEmployeeDictReadMapper;

    @Override
    public List<SalaryStandardVo> queryList(SalaryStandardDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return salaryStandardReadMapper.queryList(dto);
    }

    /**
     * 获取管理(personType=1)，技术(personType=2)，工人(personType=3) 的基本工资标准表
     * 获取岗位工资()，薪级工资()
     */
    @Override
    public List<SalaryStandardVo> getSalaryStandard(SalaryStandardDto dto) {
        if (Objects.isNull(dto.getPersonType())) {
            throw new AppException();
        }
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<SalaryStandardVo> postsRank = salaryStandardReadMapper.querySalaryStandard(dto);
        return postsRank;
    }

    /**
     * 获取管理(personType=1)，技术(personType=2)，工人(personType=3) 的基本工资标准表
     */
    @Override
    public BasicSalaryStandardVo getBasicSalariesStandard(SalaryStandardDto dto) {
        if (Objects.isNull(dto.getPersonType())) {
            throw new AppException();
        }

        BasicSalaryStandardVo basicSalaryStandard = new BasicSalaryStandardVo();

        //岗位工资表
        dto.setPostOrSalary("1");
        List<SalaryStandardVo> postsRank = salaryStandardReadMapper.querySalaryStandard(dto);
        basicSalaryStandard.setPostsRank(postsRank);

        //薪级工资表
        dto.setPostOrSalary("2");
        List<SalaryStandardVo> salaryRank = salaryStandardReadMapper.querySalaryStandard(dto);
        basicSalaryStandard.setSalaryRank(salaryRank);

        return basicSalaryStandard;
    }

    @Override
    public Map<String, List<EmpEmployeeDictVo>> getBasicSalaryTreeData() {

        Map<String, List<EmpEmployeeDictVo>> map;
        Object o = RedisUtil.get("basicSalaryTreeData");
        if(Objects.nonNull(o)){
            map = JSON.parseObject(o.toString(), HashMap.class);
        }else {
            map = new HashMap<>();
            //获取职工类型
            EmpEmployeeDictDto postTypeData = new EmpEmployeeDictDto();
            postTypeData.setCodeType("HRM_POST_TYPE");
            List<EmpEmployeeDictVo> empEmployeeDictVos = empEmployeeDictReadMapper.queryList(postTypeData);
            List<EmpEmployeeDictVo> list = TreeUtil.getTreeNode(empEmployeeDictVos, "id", "parentId");
            List<EmpEmployeeDictVo> postTypeList1 = list.get(0).getChildren();
            List<EmpEmployeeDictVo> postTypeList2 = ConvertUtil.copyList2List(postTypeList1, EmpEmployeeDictVo.class);
            List<EmpEmployeeDictVo> postTypeList3 = ConvertUtil.copyList2List(postTypeList1, EmpEmployeeDictVo.class);

            //原始数据
            List<EmpEmployeeDictVo> originData = new ArrayList<>();

            //岗位工资
            List<EmpEmployeeDictVo> postRank = new ArrayList<>();
            int itemNum = 0;
            for (EmpEmployeeDictVo item : postTypeList1) {
                //职工类型对应岗位list
                SalaryStandardDto salaryStandardDto = new SalaryStandardDto();
                salaryStandardDto.setPersonType(item.getId().toString());
                salaryStandardDto.setPostOrSalary("1");
                List<SalaryStandardVo> salaryStandardVos = salaryStandardReadMapper.querySalaryStandard(salaryStandardDto);

                itemNum++;
                item.setId(Integer.MAX_VALUE - itemNum);
                item.setParentId(null);
                //数据封装
                List<EmpEmployeeDictVo> postRankChildren = new ArrayList<>();
                salaryStandardVos.forEach(item1 -> {
                    EmpEmployeeDictVo postRank1 = new EmpEmployeeDictVo();
                    postRank1.setId(item1.getId());
                    postRank1.setParentId(item.getId());
                    postRank1.setCodeLable(item1.getLevelName());
                    postRank1.setCodeType("HRM_POST_RANK");
                    if (item1.getPerformanceId()!= null) {
                        postRank1.setPerformanceId(item1.getPerformanceId().toString());
                    }
                    postRankChildren.add(postRank1);
                    originData.add(postRank1);
                });

                item.setChildren(postRankChildren);
                item.setCodeType("HRM_POST_RANK");
                originData.add(item);
                postRank.add(item);
            }
            map.put("postRankOptions", postRank);

            //薪级工资
            List<EmpEmployeeDictVo> salaryRank = new ArrayList<>();
            int item1Num = 0;
            for (EmpEmployeeDictVo item : postTypeList2) {
                //职工类型对应薪级list
                SalaryStandardDto salaryStandardDto = new SalaryStandardDto();
                salaryStandardDto.setPersonType(item.getId().toString());
                salaryStandardDto.setPostOrSalary("2");
                List<SalaryStandardVo> salaryStandardVos = salaryStandardReadMapper.querySalaryStandard(salaryStandardDto);

                item1Num++;
                item.setId(Integer.MAX_VALUE - item1Num);
                item.setParentId(null);
                //数据封装
                List<EmpEmployeeDictVo> salaryRankChildren = new ArrayList<>();
                salaryStandardVos.forEach(item1 -> {
                    EmpEmployeeDictVo salaryRank1 = new EmpEmployeeDictVo();
                    salaryRank1.setId(item1.getId());
                    salaryRank1.setParentId(item.getId());
                    salaryRank1.setCodeLable(item1.getLevelName());
                    salaryRank1.setCodeType("HRM_SALARY_RANK");
                    salaryRankChildren.add(salaryRank1);
                    originData.add(salaryRank1);
                });

                item.setChildren(salaryRankChildren);
                item.setCodeType("HRM_SALARY_RANK");
                originData.add(item);
                salaryRank.add(item);
            }
            map.put("salaryRankOptions", salaryRank);


            //绩效工资
            List<EmpEmployeeDictVo> perfRank = new ArrayList<>();
            int item2Num = 0;
            for (EmpEmployeeDictVo item : postTypeList3) {
                //职工类型对应绩效list
                QueryWrapper<PerformanceStandardDto> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("person_type",item.getId().toString());
                queryWrapper.orderByAsc("person_type","sort");
                List<PerformanceStandardDto> performanceStandardDtos = performanceStandardReadMapper.selectList(queryWrapper);

                item2Num++;
                item.setId(Integer.MAX_VALUE - item2Num);
                item.setParentId(null);
                //数据封装
                List<EmpEmployeeDictVo> perfRankChildren = new ArrayList<>();
                performanceStandardDtos.forEach(item1 -> {
                    EmpEmployeeDictVo perfRank1 = new EmpEmployeeDictVo();
                    String codeLabel = item1.getLevelName1() + (StringUtils.isNotBlank(item1.getLevelName2())? "--" + item1.getLevelName2() : "");
                    perfRank1.setId(item1.getId());
                    perfRank1.setParentId(item.getId());
                    perfRank1.setCodeLable(codeLabel);
                    perfRank1.setCodeType("HRM_PERF_RANK");
                    perfRankChildren.add(perfRank1);
                    originData.add(perfRank1);
                });

                item.setChildren(perfRankChildren);
                item.setCodeType("HRM_PERF_RANK");
                originData.add(item);
                perfRank.add(item);
            }
            map.put("perfRankOptions", perfRank);


            map.put("originData", originData);

            //在树形数据管理  主动刷新缓存
            RedisUtil.set("basicSalaryTreeData",JSON.toJSONString(map));
        }
        return map;
    }


}
