package com.jp.med.hrm.modules.emp.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.hrm.modules.emp.mapper.write.HrmPaymentInfoWriteMapper;
import com.jp.med.hrm.modules.emp.dto.HrmPaymentInfoDto;
import com.jp.med.hrm.modules.emp.service.write.HrmPaymentInfoWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 个人支付信息
 * <AUTHOR>
 * @email -
 * @date 2024-07-31 15:02:13
 */
@Service
@Transactional(readOnly = false)
public class HrmPaymentInfoWriteServiceImpl extends ServiceImpl<HrmPaymentInfoWriteMapper, HrmPaymentInfoDto> implements HrmPaymentInfoWriteService {
}
