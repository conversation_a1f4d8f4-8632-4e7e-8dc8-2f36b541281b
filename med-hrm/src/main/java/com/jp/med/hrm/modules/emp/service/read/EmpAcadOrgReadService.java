package com.jp.med.hrm.modules.emp.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.hrm.modules.emp.dto.EmpAcadOrgDto;
import com.jp.med.hrm.modules.emp.vo.EmpAcadOrgVo;

import java.util.List;

/**
 * 学术团体
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 11:14:30
 */
public interface EmpAcadOrgReadService extends IService<EmpAcadOrgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EmpAcadOrgVo> queryList(EmpAcadOrgDto dto);
}

