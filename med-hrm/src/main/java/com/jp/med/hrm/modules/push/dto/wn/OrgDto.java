package com.jp.med.hrm.modules.push.dto.wn;

import com.jp.med.hrm.modules.push.dto.wn.tag.DataItem;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 组织 DTO
 *
 * <AUTHOR>
 * @date 2025/05/12
 */
@Data
@Accessors(chain = true)
public class OrgDto implements DataItem {
    /**
     * 说明：组织标识
     * modelOID:1.2.156.112604.1.7
     * 必填：是
     */
    private String orgOid;

    /**
     * 业务系统自定义标识号，业务交互时使用的。例如：
     * 60HIS：soid,5xHIS:ksdm
     * 必填：是
     */
    private String orgId;

    /**
     * 组织编码
     * 必填：是
     */
    private String orgNo;

    /**
     * 组织名称
     * 必填：是
     */
    private String orgName;

    /**
     * 拼音码
     * 必填：
     */
    private String pinyin;

    /**
     * 五笔码
     * 必填：
     */
    private String wubi;

    /**
     * 助记码
     * 必填：
     */
    private String shortcut;

    /**
     * 组织介绍
     * 必填：
     */
    private String orgDesc;

    /**
     * 组织图片base64
     * 必填：
     */
    private String orgPic;

    /**
     * 组织图片URL
     * 必填：
     */
    private String orgPicUrl;

    /**
     * 科室级别
     * 注：注册时不填,下发时根据上级机构层级计算，
     * 病区无级别
     * 1：一级科室
     * 2：二级科室
     * 3：三级科室
     * 4：四级科室
     * 必填：
     */
    private String orgLevelCode;

    /**
     * 上级组织标识
     * 注：注册时，有上级，必传
     * 必填：
     */
    private String parentOrgOid;

    /**
     * 上级组织编码
     * 注册时不需要传
     * 必填：
     */
    private String parentOrgNo;

    /**
     * 上级组织名称
     * 注册时不需要传
     * 必填：
     */
    private String parentOrgName;

    /**
     * 隶属机构标识(业务系统可能使用此节点判断所属机构，注册时不会落库)
     * 必填：
     */
    private String associatedOrgOid;

    /**
     * 服务院区
     * 必填：
     */
    private String serviceOrgOid;

    /**
     * 组ID，用作集团化
     * 必填：
     */
    private String groupId;

    /**
     * 说明：组织状态
     * 值集：1.2.156.112604.1.2.767
     * 概念域：1.2.156.112604.1.3.765
     * 必填：是
     */
    private String orgStatus;

    /**
     * 押金警示线
     * 必填：
     */
    private String depositWarLine;

    /**
     * 医技确费欠费控制标志(oweCtrlSign)
     * 值集：1.2.156.112604.1.2.750
     * 概念域：1.2.156.112604.1.3.748
     * 必填：
     */
    private String oweCtrlFlag;

    /**
     * 限号数
     * 必填：
     */
    private String limitNo;

    /**
     * 押金停药线
     * 必填：
     */
    private String depositBndonLine;

    /**
     * 自助挂号标志(selfRegisterSign)
     * 值集：1.2.156.112604.1.2.750
     * 概念域：1.2.156.112604.1.3.748
     * 必填：
     */
    private String selfRegisterFlag;

    /**
     * 使用全科医生站标志(generPracStationSign)
     * 值集：1.2.156.112604.1.2.750
     * 概念域：1.2.156.112604.1.3.748
     * 必填：
     */
    private String generPracStationFlag;

    /**
     * 专病专家标志(specialistSign)
     * 值集：1.2.156.112604.1.2.750
     * 概念域：1.2.156.112604.1.3.748
     * 必填：
     */
    private String specialistFlag;

    /**
     * 转诊模式(zzms)
     * 0:允许转出和接收，1:允许转出，2:允许接收，3:不允
     * 值集：1.2.156.112604.1.99.2.2
     * 概念域：1.2.156.112604.1.99.3.2
     * 必填：
     */
    private String referralModel;

    /**
     * 专科标志(specialtySign)
     * 值集：1.2.156.112604.1.2.750
     * 概念域：1.2.156.112604.1.3.748
     * 必填：
     */
    private String specialtyFlag;

    /**
     * 儿科标志(childSign)
     * 值集：1.2.156.112604.1.2.750
     * 概念域：1.2.156.112604.1.3.748
     * 必填：
     */
    private String childFlag;

    /**
     * 中医科室标志(chineseMedSign)
     * 值集：1.2.156.112604.1.2.750
     * 概念域：1.2.156.112604.1.3.748
     * 必填：
     */
    private String chineseMedFlag;

    /**
     * 床位数量
     * 必填：
     */
    private String bedNumber;

    /**
     * 特殊病区标志(specialWardSign)
     * 值集：1.2.156.112604.1.2.750
     * 概念域：1.2.156.112604.1.3.748
     * 必填：
     */
    private String specialWardFlag;

    /**
     * 排序序号(orderno)
     * 科室排序序号、病区排序序号
     * 必填：
     */
    private String orderNo;

    /**
     * 核算科室OID
     * 值集：1.2.156.112604.1.2.116103
     * 概念域：1.2.156.112604.1.2.116103
     * 必填：
     */
    private String accountDeptOid;

    /**
     * 核算科室编码
     * 必填：
     */
    private String accountDeptNo;

    /**
     * 核算科室名称
     * 必填：
     */
    private String accountDeptName;

    /**
     * 医保科室oid
     * (医疗卫生机构业务科室分类与代码概念域)
     * 值集：1.2.156.112604.1.2.94187
     * 概念域：1.2.156.112604.1.3.819
     * 必填：
     */
    private String medDeptOid;

    /**
     * 组织别名
     * 必填：
     */
    private List<String> orgAliasList;

    /**
     * 业务单元类型代码
     * 值集：1.2.156.112604.1.2.576
     * 概念域：1.2.156.112604.1.3.26
     * 注：1、注册时类型给全量 2、不支持药房、药库
     * 必填：是
     */
    private List<String> buTypeCodeList;

    /**
     * 就诊类型代码
     * 值集：1.2.156.112604.1.2.433
     * 概念域：1.2.156.112604.1.3.263
     * 必填：
     */
    private List<String> encounterTypeCodeList;

    /**
     * 说明：组织可提供服务类型
     * 值域：1.2.156.112604.1.2.94383
     * 概念域：1.2.156.112604.1.3.3783
     * 必填：
     */
    private List<String> serviceTypeList;
    /**
     * 组织联系方式
     */
    private List<OrgTelecom> orgTelecomList;

    /**
     * (list数组) 1:N
     * 说明：如果联系方式不为空，则contactCode、contactNo必填
     */
    @Data
    static class OrgTelecom {
        /**
         * 联系方式代码
         * 值集：1.2.156.112604.1.2.678
         * 概念域：1.2.156.112604.1.3.485
         * 必填：
         */
        private String contactCode;

        /**
         * 联系号码
         * 必填：
         */
        private String contactNo;

        /**
         * 联系用途代码
         * 值集：1.2.156.112604.1.2.521
         * 概念域：1.2.156.112604.1.3.484
         * 必填：
         */
        private String contactUsageCode;
    }

    /**
     * 组织证书
     *
     * <AUTHOR>
     * @date 2025/05/12
     * 说明：如果组织证件不为空，则certTypeCode、certNo参数必填
     */
    @Data
    static class OrgCert {
        /**
         * 说明：证件类型代码
         * 值集：1.2.156.112604.1.2.1102
         * 概念域：1.2.156.112604.1.3.974
         * 必填：
         */
        private String certTypeCode;

        /**
         * 证件编号
         * 必填：
         */
        private String certNo;

        /**
         * 证件图片
         * 必填：
         */
        private String certPic;

        /**
         * 证件有效期
         * 格式：YYYY-MM-DD HH:MI:SS
         * 必填：
         */
        private String certExpiryDate;
    }

    /**
     * 果组织证件
     */
    private List<OrgCert> orgCertList;

    /**
     * 组织地址1:N
     *
     * <AUTHOR>
     * @date 2025/05/12
     */
    @Data
    static class OrgAddress {

        /**
         * 说明：地址用途代码
         * 值集：1.2.156.112604.1.2.556
         * 概念域：1.2.156.112604.1.3.212
         * 必填：
         */
        private String addrUsageCode;

        /**
         * 说明：地址类型代码
         * 值集：1.2.156.112604.1.2.522
         * 概念域：1.2.156.112604.1.3.214
         * 必填：
         */
        private String addrCategoryCode;

        /**
         * 说明：地址-国家代码
         * 值集：1.2.156.112604.1.2.8
         * 概念域：1.2.156.112604.1.3.24
         * 必填：
         */
        private String addrCountryCode;

        /**
         * 说明：地址-省代码（自治区、直辖市）
         * 值集：1.2.156.112604.1.2.506
         * 概念域：1.2.156.112604.1.3.36
         * 必填：
         */
        private String addrProvinceCode;

        /**
         * 说明：地址-市代码（地区、州）
         * 值集：1.2.156.112604.1.2.506
         * 概念域：1.2.156.112604.1.3.36
         * 必填：
         */
        private String addrCityCode;

        /**
         * 说明：地址-县代码（区）
         * 值集：1.2.156.112604.1.2.506
         * 概念域：1.2.156.112604.1.3.36
         * 必填：
         */
        private String addrCountyCode;

        /**
         * 说明：地址-乡代码（镇、街道办事处）
         * 值集：1.2.156.112604.1.2.506
         * 概念域：1.2.156.112604.1.3.36
         * 必填：
         */
        private String addrTownCode;

        /**
         * 地址-村（街、路、弄等）
         * 必填：
         */
        private String addrVillage;

        /**
         * 地址-门牌号码
         * 必填：
         */
        private String addrHouseNo;

        /**
         * 详细地址
         * 必填：
         */
        private String addrDetail;
    }

    /**
     * 组织地址
     */
    private List<OrgAddress> orgAddressList;

    @Data
    public static class OrganizationXEmployee {
        /**
         * 组织与员工关系类型代码(科主任、护士长)
         * 值集：1.2.156.112604.1.2.105679
         * 概念域：1.2.156.112604.1.3.6903
         * 必填：是
         */
        private String adminPositionCode;

        /**
         * 员工oid
         * 必填：是
         */
        private String employeeOid;
    }

    private List<OrganizationXEmployee> organizationXEmployeeList;

    /**
     * 贯标数据(0~1)
     *
     * <AUTHOR>
     * @date 2025/05/12
     */
    @Data
    static class MedInstiBasicData {
        /**
         * 地方码编码
         * 必填：
         */
        private String localMedInsurNo;

        /**
         * 地方码名称
         * 必填：
         */
        private String localMedInsurName;

        /**
         * 国家码编码
         * 必填：
         */
        private String countryMedInsurNo;

        /**
         * 国家码名称
         * 必填：
         */
        private String countryMedInsurName;
    }

    /**
     * 贯标数据
     */
    private MedInstiBasicData medInstiBasicDataMapping;

    /**
     * 动态扩展属性列表
     * 数组list(0-n)
     */
    private List<ExtendedAttribute> extendedAttributes;
}
