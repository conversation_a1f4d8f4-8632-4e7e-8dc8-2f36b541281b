package com.jp.med.hrm.modules.thesisCondition.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.exception.AppException;
import com.jp.med.hrm.modules.thesisCondition.dto.HrmResearchThesisConditionDto;
import com.jp.med.hrm.modules.thesisCondition.mapper.read.HrmResearcherThesisConditionReadMapper;
import com.jp.med.hrm.modules.thesisCondition.service.read.HrmResearcherThesisConditionReadService;
import com.jp.med.hrm.modules.thesisCondition.vo.HrmResearcherThesisConditionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional
@Service
public class HrmResearcherThesisConditionReadServiceImpl
        extends ServiceImpl<HrmResearcherThesisConditionReadMapper, HrmResearchThesisConditionDto>
        implements HrmResearcherThesisConditionReadService {

    @Autowired
    private HrmResearcherThesisConditionReadMapper hrmResearcherThesisConditionReadMapper;

    @Override
    public List<HrmResearcherThesisConditionVo> queryPageList(HrmResearchThesisConditionDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return hrmResearcherThesisConditionReadMapper.queryList(dto);
    }

    @Override
    public HrmResearchThesisConditionDto getBpmDetailById(Integer id) {
        if (id == null) {
            throw new AppException("id不能为空");
        }
//        HrmResearchThesisConditionDto hrmResearchThesisConditionDto = baseMapper.selectById(id);
        HrmResearchThesisConditionDto hrmResearchThesisConditionDto = hrmResearcherThesisConditionReadMapper.queryById(id);
        if (hrmResearchThesisConditionDto == null) {
            throw new AppException("论文申请表不存在");
        }
        return hrmResearchThesisConditionDto;
    }

}
