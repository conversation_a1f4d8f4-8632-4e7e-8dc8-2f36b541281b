package com.jp.med.hrm.modules.thesisCondition.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.hrm.modules.thesisCondition.dto.HrmEvectionExpenseDto;
import com.jp.med.hrm.modules.thesisCondition.vo.HrmEvectionExpenseVo;

import java.util.List;

public interface HrmEvectionExpenseReadMapper extends BaseMapper<HrmEvectionExpenseDto> {
    List<HrmEvectionExpenseVo> queryList(HrmEvectionExpenseDto dto);
}
