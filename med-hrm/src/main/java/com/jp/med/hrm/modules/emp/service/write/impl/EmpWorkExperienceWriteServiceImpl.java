package com.jp.med.hrm.modules.emp.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.hrm.modules.emp.mapper.write.EmpWorkExperienceWriteMapper;
import com.jp.med.hrm.modules.emp.dto.EmpWorkExperienceDto;
import com.jp.med.hrm.modules.emp.service.write.EmpWorkExperienceWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 工作履历
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 11:12:36
 */
@Service
@Transactional(readOnly = false)
public class EmpWorkExperienceWriteServiceImpl extends ServiceImpl<EmpWorkExperienceWriteMapper, EmpWorkExperienceDto> implements EmpWorkExperienceWriteService {
}
