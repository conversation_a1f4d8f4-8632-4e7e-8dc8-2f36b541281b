package com.jp.med.hrm.modules.thesisCondition.service.Write.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.bpm.BpmProcessInstanceCreateReqDTO;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.BpmProcessInstanceFeignApi;
import com.jp.med.common.util.FeignExecuteUtil;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.hrm.modules.emp.mapper.read.EmpEmployeeInfoReadMapper;
import com.jp.med.hrm.modules.org.mapper.read.HrmOrgReadMapper;
import com.jp.med.hrm.modules.thesisCondition.dto.HrmOutStudyDto;
import com.jp.med.hrm.modules.thesisCondition.entity.StudyExperienceEntity;
import com.jp.med.hrm.modules.thesisCondition.entity.WorkExperienceEntity;
import com.jp.med.hrm.modules.thesisCondition.mapper.write.HrmOutStudyWriteMapper;
import com.jp.med.hrm.modules.thesisCondition.mapper.write.StudyExperienceWriteMapper;
import com.jp.med.hrm.modules.thesisCondition.mapper.write.WorkExperienceWriteMapper;
import com.jp.med.hrm.modules.thesisCondition.service.Write.HrmOutStudyWritingService;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


@Service
@Transactional(readOnly = false)
public class HrmOutStudyWritingServiceImpl
        extends ServiceImpl<HrmOutStudyWriteMapper, HrmOutStudyDto> implements HrmOutStudyWritingService {

    @Autowired
    private HrmOrgReadMapper hrmOrgReadMapper;
    @Autowired
    private EmpEmployeeInfoReadMapper empEmployeeInfoReadMapper;
    @Autowired
    private StudyExperienceWriteMapper studyExperienceWriteMapper;
    @Autowired
    private WorkExperienceWriteMapper workExperienceWriteMapper;
    @Autowired
    private BpmProcessInstanceFeignApi bpmProcessInstanceFeignApi;
    @Autowired
    private HrmOutStudyWriteMapper hrmOutStudyWriteMapper;

    @Override
    public void saveThesis(HrmOutStudyDto dto) {
        if (StrUtil.isNotEmpty(dto.getDtoJson())) {
            HrmOutStudyDto hrmOutStudyDto = JSONObject.parseObject(dto.getDtoJson(),
                    HrmOutStudyDto.class);
            hrmOutStudyDto.setAttFiles(dto.getAttFiles());
            hrmOutStudyDto.setSysUser(dto.getSysUser());
            hrmOutStudyDto.setHospitalId(dto.getHospitalId());
            dto = hrmOutStudyDto;
        }

        //插入申请表
        insertTask(dto);

        //发起BPM流程
        String processInstanceCode = createProcessInstance(dto);

        //将流程实例编号，更新到申请记录中
        UpdateWrapper<HrmOutStudyDto> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", dto.getId()).set("process_instance_code", processInstanceCode);
        hrmOutStudyWriteMapper.update(null, updateWrapper);

        if(dto.getStudyExperience() != null){
            for(StudyExperienceEntity studyExperience: dto.getStudyExperience()){
                studyExperience.setProcessInstanceCode(processInstanceCode);
                if(studyExperience.getStudyStartEndTime().length > 0){
                    studyExperience.setStudyStartTime(studyExperience.getStudyStartEndTime()[0]);
                    studyExperience.setStudyEndTime(studyExperience.getStudyStartEndTime()[1]);
                }
                studyExperienceWriteMapper.insert(studyExperience);
            }
        }

        if(dto.getWorkExperience() != null){
            for(WorkExperienceEntity workExperienceEntity: dto.getWorkExperience()){
                workExperienceEntity.setProcessInstanceCode(processInstanceCode);
                if(workExperienceEntity.getWorkStartEndTime().length > 0){
                    workExperienceEntity.setWorkStartTime(workExperienceEntity.getWorkStartEndTime()[0]);
                    workExperienceEntity.setWorkEndTime(workExperienceEntity.getWorkStartEndTime()[1]);
                }
                workExperienceWriteMapper.insert(workExperienceEntity);
            }
        }

    }


    private void insertTask(HrmOutStudyDto dto) {

        //附件
        if (dto.getAttFiles() != null && CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            StringBuilder attStr = new StringBuilder();
            StringBuilder attName = new StringBuilder();
            dto.getAttFiles().forEach(file -> {
                String filePath = OSSUtil.uploadFile(OSSConst.BUCKET_HRM, "outStudy/", file);
                attStr.append(filePath);
                attStr.append(",");
                attName.append(file.getOriginalFilename());
                attName.append(",");
            });
            dto.setAtt(attStr.toString());
            dto.setAttName(attName.toString());
        }

        //创建人相关信息
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String creatorCode = StrUtil.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode() : dto.getSysUser().getUsername();
        dto.setId(null);
        dto.setChkState(MedConst.TYPE_0);
        dto.setCrter(creatorCode);
        dto.setCrteTime(DateUtil.now());
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        dto.setHospitalId(dto.getHospitalId());

        dto.setStudyPeriodStart(dto.getStudyPeriod()[0]);
        dto.setStudyPeriodEnd(dto.getStudyPeriod()[1]);

        baseMapper.insert(dto);
    }

    /**
     * 发起BPM流程
     *
     * @param dto
     */
    private String createProcessInstance(HrmOutStudyDto dto) {
        if (dto == null || StrUtil.isEmpty(dto.getProcessDefinitionKey())) {
            return null;
        }


        String[] fields = {
                "id",                       // 主键
                "processInstanceCode",      // 流程实例编码
                "thesisName",              // 项目名称
                "department",               // 所属部门
                "mainResearcher",           // 主要研究者
                "sex",
                "age",
                "politicalStatus",
                "title",
                "position",
                "currentProfessional",
                "workTime",
                "workYears",
                "studyType",
                "studyCity",
                "studyUnit",
                "studyProfessional",
                "studyPeriod",
                "studyPeriodStart",
                "studyPeriodEnd",
                "allMonth",
                "studyStartEndTime",
                "schoolName",
                "education",
                "degree",
                "currentTechnicalAbility",
                "studyPlan",
                "att",
                "attName",
                "signFile",
                "oldId"
        };


        // 实例参数封装
        Map<String, Object> processInstanceVariables = new HashMap<>();
        Map<String, Object> map = BeanUtil.beanToMap(dto, new HashMap<>(), false, true);
        for (String field : fields) {
            if (map.containsKey(field)) {
                switch (field) {
                    case "department":
                        processInstanceVariables.put("department", hrmOrgReadMapper.selectOrgById(dto.getDepartment()).getOrgName());
                        break;
                    case "mainResearcher":
                        processInstanceVariables.put("mainResearcher", empEmployeeInfoReadMapper.queryEmpNamesByEmpCodes(Arrays.asList(dto.getMainResearcher().split(","))));
                        break;
                    default:
                        processInstanceVariables.put(field, map.get(field));
                        break;
                }
            }
        }

//        processInstanceVariables.put("nextApprover", dto.getNextApprover());

        val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
        bpmProcessInstanceCreateReqDTO
                .setUserId(dto.getCrter())
                .setProcessDefinitionKey(dto.getProcessDefinitionKey())
                .setVariables(processInstanceVariables)
                .setBusinessKey(String.valueOf(dto.getId()));

        //发起BPM流程
        CommonFeignResult processInstance = FeignExecuteUtil
                .execute(bpmProcessInstanceFeignApi.createProcessInstance(bpmProcessInstanceCreateReqDTO));
        if (!StrUtil.equals(processInstance.get("code").toString(), "200")) {
            throw new AppException("生成BPM流程异常");
        }
        return processInstance.get("data").toString();
    }
}
