package com.jp.med.hrm.modules.hrmIssue.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.hrm.modules.hrmIssue.dto.HrmIssueMgtDto;
import org.apache.ibatis.annotations.Mapper;

/**
 * 议题申报表
 * <AUTHOR>
 * @email -
 * @date 2024-11-06 16:04:44
 */
@Mapper
public interface HrmIssueMgtWriteMapper extends BaseMapper<HrmIssueMgtDto> {

    /**
     * 根据id更新议题决议
     * @param dto
     */
    void updateIssueById(HrmIssueMgtDto dto);
}
