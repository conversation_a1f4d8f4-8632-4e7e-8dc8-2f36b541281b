package com.jp.med.hrm.modules.thesisCondition.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.exception.AppException;
import com.jp.med.hrm.modules.thesisCondition.dto.HrmPaperPageChargeDto;
import com.jp.med.hrm.modules.thesisCondition.mapper.read.HrmPaperPageChargeReadMapper;
import com.jp.med.hrm.modules.thesisCondition.service.read.HrmPaperPageChargeReadService;
import com.jp.med.hrm.modules.thesisCondition.vo.HrmPaperPageChargeVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class HrmPaperPageChargeReadServiceImpl
        extends ServiceImpl<HrmPaperPageChargeReadMapper, HrmPaperPageChargeDto> implements HrmPaperPageChargeReadService {

    @Autowired
    HrmPaperPageChargeReadMapper hrmPaperPageChargeReadMapper;

    @Override
    public List<HrmPaperPageChargeVo> queryPageList(HrmPaperPageChargeDto dto) {
        PageHelper.startPage(dto.getPageNum(),dto.getPageSize());
        return hrmPaperPageChargeReadMapper.queryList(dto);
    }

    @Override
    public HrmPaperPageChargeDto getBpmDetailById(Integer id) {
        if (id == null) {
            throw new AppException("id不能为空");
        }
//        HrmPaperPageChargeDto hrmPaperPageChargeDto = baseMapper.selectById(id);
        HrmPaperPageChargeDto hrmPaperPageChargeDto = hrmPaperPageChargeReadMapper.queryById(id);
        if (hrmPaperPageChargeDto == null) {
            throw new AppException("申请表不存在");
        }
        return hrmPaperPageChargeDto;
    }
}
