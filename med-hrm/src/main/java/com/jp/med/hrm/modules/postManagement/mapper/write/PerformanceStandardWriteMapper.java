package com.jp.med.hrm.modules.postManagement.mapper.write;

import com.jp.med.hrm.modules.postManagement.dto.PerformanceStandardDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 事业人员绩效工资标准表
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:56:57
 */
@Mapper
public interface PerformanceStandardWriteMapper extends BaseMapper<PerformanceStandardDto> {
    void updateBatchById(List<PerformanceStandardDto> list);

    void deleteBatchIds(List<Integer> list);
}
