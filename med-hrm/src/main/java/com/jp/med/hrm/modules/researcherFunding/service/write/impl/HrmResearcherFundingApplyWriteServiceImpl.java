package com.jp.med.hrm.modules.researcherFunding.service.write.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.dto.bpm.BpmProcessInstanceCreateReqDTO;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.feign.BpmProcessInstanceFeignApi;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.FeignExecuteUtil;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.hrm.modules.emp.mapper.read.EmpEmployeeInfoReadMapper;
import com.jp.med.hrm.modules.org.mapper.read.HrmOrgReadMapper;
import com.jp.med.hrm.modules.researcherFunding.dto.HrmResearcherFundingApplyDto;
import com.jp.med.hrm.modules.researcherFunding.dto.HrmResearcherFundingDistributionDto;
import com.jp.med.hrm.modules.researcherFunding.mapper.write.HrmResearcherFundingApplyWriteMapper;
import com.jp.med.hrm.modules.researcherFunding.mapper.write.HrmResearcherFundingDistributionWriteMapper;
import com.jp.med.hrm.modules.researcherFunding.service.write.HrmResearcherFundingApplyWriteService;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 临床试验研究者经费发放申请表
 *
 * <AUTHOR>
 * @email -
 * @date 2025-02-11 09:08:59
 */
@Service
@Transactional(readOnly = false)
public class HrmResearcherFundingApplyWriteServiceImpl
        extends ServiceImpl<HrmResearcherFundingApplyWriteMapper, HrmResearcherFundingApplyDto>
        implements HrmResearcherFundingApplyWriteService {

    @Autowired
    private BpmProcessInstanceFeignApi bpmProcessInstanceFeignApi;
    @Autowired
    private HrmOrgReadMapper hrmOrgReadMapper;
    @Autowired
    private EmpEmployeeInfoReadMapper empEmployeeInfoReadMapper;

    @Override
    public void saveApply(HrmResearcherFundingApplyDto dto) {
        if (StrUtil.isNotEmpty(dto.getDtoJson())) {
            HrmResearcherFundingApplyDto hrmResearcherFundingApplyDto = JSONObject.parseObject(dto.getDtoJson(),
                    HrmResearcherFundingApplyDto.class);
            hrmResearcherFundingApplyDto.setSignFile(dto.getSignFile());
            hrmResearcherFundingApplyDto.setAttFiles(dto.getAttFiles());
            hrmResearcherFundingApplyDto.setSysUser(dto.getSysUser());
            hrmResearcherFundingApplyDto.setHospitalId(dto.getHospitalId());
            dto = hrmResearcherFundingApplyDto;
        }

        // 1.1 插入申请表
        insertTask(dto);

        // 1.2 插入分配明细
        insertDistribution(dto);

        // 1.3 发起BPM流程
        String processInstanceCode = createProcessInstance(dto);

        // 1.4 将流程实例编号，更新到申请记录中
        UpdateWrapper<HrmResearcherFundingApplyDto> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", dto.getId()).set("process_instance_code", processInstanceCode);
        baseMapper.update(null, updateWrapper);

    }

    /**
     * 插入申请表
     *
     * @param dto
     */
    private void insertTask(HrmResearcherFundingApplyDto dto) {
        // 1.2附件
        if (dto.getAttFiles() != null && CollectionUtil.isNotEmpty(dto.getAttFiles())) {
            StringBuilder attStr = new StringBuilder();
            StringBuilder attName = new StringBuilder();
            dto.getAttFiles().forEach(file -> {
                String filePath = OSSUtil.uploadFile(OSSConst.BUCKET_HRM, "fundingApply/", file);
                attStr.append(filePath);
                attStr.append(",");
                attName.append(file.getOriginalFilename());
                attName.append(",");
            });
            dto.setAtt(attStr.toString());
            dto.setAttName(attName.toString());
        }

        // 1.3签名
        if (dto.getSignFile() != null) {
            String filePath = OSSUtil.uploadFile(OSSConst.BUCKET_HRM, "fundingApply/", dto.getSignFile());
            dto.setSign(filePath);
        }

        // 1.4 创建人相关信息
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String creatorCode = StrUtil.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode()
                : dto.getSysUser().getUsername();

        dto.setId(null);
        dto.setChkState(MedConst.TYPE_0);
        dto.setCrter(creatorCode);
        dto.setCrteTime(DateUtil.now());
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        dto.setHospitalId(dto.getHospitalId());
        baseMapper.insert(dto);
    }

    /**
     * 插入分配明细
     *
     * @param dto
     */
    private void insertDistribution(HrmResearcherFundingApplyDto dto) {
        if (dto == null || CollectionUtil.isEmpty(dto.getDistributions())) {
            return;
        }
        // 1 创建人相关信息
        HrmUser hrmUser = dto.getSysUser().getHrmUser();
        String creatorCode = StrUtil.isNotEmpty(hrmUser.getEmpCode()) ? hrmUser.getEmpCode()
                : dto.getSysUser().getUsername();

        dto.getDistributions().forEach(distribution -> {
            distribution.setId(null);
            distribution.setApplyId(dto.getId());
            distribution.setCrter(creatorCode);
            distribution.setCrteTime(DateUtil.now());
            distribution.setActiveFlag(MedConst.ACTIVE_FLAG_1);
            distribution.setHospitalId(dto.getHospitalId());
        });
        BatchUtil.batch(dto.getDistributions(), HrmResearcherFundingDistributionWriteMapper.class);
    }

    /**
     * 发起BPM流程
     *
     * @param dto
     */
    private String createProcessInstance(HrmResearcherFundingApplyDto dto) {
        if (dto == null || StrUtil.isEmpty(dto.getProcessDefinitionKey())) {
            return null;
        }
        //2.2.0 审核人
        if (StrUtil.isBlank(dto.getNextApprover())) {
            throw new AppException("未指定审核人");
        }

        String[] fields = {
                "id",                       // 主键
                "processInstanceCode",      // 流程实例编码
                "projectName",              // 项目名称
                "department",               // 所属部门
                "mainResearcher",           // 主要研究者
                "sponsor",                  // 项目赞助方
                "workPeriodStart",          // 工作周期开始日期
                "workPeriodEnd",            // 工作周期结束日期
                "contractCount",            // 合同例数
                "enrolledCount",            // 入组例数
                "contractAmount",           // 合同金额
                "receivedAmount",           // 已收金额
                "qualityControl",           // 质量控制
                "applyNode",                // 申请节点
                "applyTimes",               // 申请次数
                "applyDepartment",          // 申请部门
                "applyAmount",              // 申请金额
                "sign",                     // 签名
                "remark",                   // 备注
                "crter",                    // 创建人
                "crteTime",                 // 创建时间
                "distributions",            // 分配明细列表
        };


        // 1.1 实例参数封装
        Map<String, Object> processInstanceVariables = new HashMap<>();
        Map<String, Object> map = BeanUtil.beanToMap(dto, new HashMap<>(), false, true);
        for (String field : fields) {
            if (map.containsKey(field)) {
                switch (field) {
                    case "department":
                        processInstanceVariables.put("department", hrmOrgReadMapper.selectOrgById(dto.getDepartment()).getOrgName());
                        break;
                    case "mainResearcher":
                        processInstanceVariables.put("mainResearcher", empEmployeeInfoReadMapper.queryEmpNamesByEmpCodes(Arrays.asList(dto.getMainResearcher().split(","))));
                        break;
                    case "applyDepartment":
                        processInstanceVariables.put("applyDepartment", hrmOrgReadMapper.selectOrgById(dto.getApplyDepartment()).getOrgName());
                        break;
                    default:
                        processInstanceVariables.put(field, map.get(field));
                        break;
                }
            }
        }
        BigDecimal detailTotal = Optional.ofNullable(dto.getDistributions())
                .orElse(Collections.emptyList())
                .stream()
                .map(HrmResearcherFundingDistributionDto::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        processInstanceVariables.put("detailTotal", detailTotal);
        // 定义 5w 即 50000
        BigDecimal fiveW = new BigDecimal("50000");

        // 判断是否大于 5w
        if (detailTotal.compareTo(fiveW) > 0) {
            processInstanceVariables.put("isHign", "1");
        }else{
            processInstanceVariables.put("isHign", "0");
        }
        processInstanceVariables.put("distributions2", dto.getDistributions());
        processInstanceVariables.put("nextApprover", dto.getNextApprover());
        processInstanceVariables.put(OSSConst.APP_ATT_BUCKET_NAME, OSSConst.BUCKET_HRM);
        processInstanceVariables.put(OSSConst.APP_ATT_PATHS, dto.getAtt());
        processInstanceVariables.put(OSSConst.APP_ATT_NAMES, dto.getAttName());

        // 1.2
        val bpmProcessInstanceCreateReqDTO = new BpmProcessInstanceCreateReqDTO();
        bpmProcessInstanceCreateReqDTO
                .setUserId(dto.getCrter())
                .setProcessDefinitionKey(dto.getProcessDefinitionKey())
                .setVariables(processInstanceVariables)
                .setBusinessKey(String.valueOf(dto.getId()));

        // 1.3 发起BPM流程
        CommonFeignResult processInstance = FeignExecuteUtil
                .execute(bpmProcessInstanceFeignApi.createProcessInstance(bpmProcessInstanceCreateReqDTO));
        if (!StrUtil.equals(processInstance.get("code").toString(), "200")) {
            throw new AppException("生成BPM流程异常");
        }
        return processInstance.get("data").toString();
    }
}
