package com.jp.med.hrm.modules.recruitMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.hrm.modules.recruitMgt.dto.HrmRecruitDetlDto;
import com.jp.med.hrm.modules.recruitMgt.vo.HrmRecruitDetlVo;

import java.util.List;

/**
 * 招聘填报详情
 * <AUTHOR>
 * @email -
 * @date 2023-11-15 20:59:31
 */
public interface HrmRecruitDetlReadService extends IService<HrmRecruitDetlDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<HrmRecruitDetlVo> queryList(HrmRecruitDetlDto dto);
}

