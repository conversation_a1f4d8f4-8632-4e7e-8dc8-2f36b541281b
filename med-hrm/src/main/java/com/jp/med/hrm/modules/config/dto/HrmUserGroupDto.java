package com.jp.med.hrm.modules.config.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * 人力资源用户组
 * <AUTHOR>
 * @email -
 * @date 2024-02-29 19:22:26
 */
@Data
@TableName("hrm_user_group" )
public class HrmUserGroupDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 员工编号 */
    @TableField(value = "emp_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String empCode;

    /** 用户组 */
    @TableField(value = "group_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String groupCode;

    /** 有效标志 */
    @TableField(value = "active_flag",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String activeFlag;

    /** 用户列表 */
    @TableField(exist = false)
    private List<String> userList;

    @TableField(exist = false)
    private List<String> groupCodes;

}
