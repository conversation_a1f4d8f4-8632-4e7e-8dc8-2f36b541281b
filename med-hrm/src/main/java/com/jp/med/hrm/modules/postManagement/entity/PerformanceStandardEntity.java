package com.jp.med.hrm.modules.postManagement.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 事业人员绩效工资标准表
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:56:57
 */
@Data
@TableName("hrm_performance_standard")
public class PerformanceStandardEntity {


    @TableId("id")
	private Integer id;

	/** 人员类型(1:管理 ,2:技术人员,3:工人····) */
	@TableField("person_type")
	private String personType;

	/** 职级名称1 */
	@TableField("level_name1")
	private String levelName1;

	/** 职级名称2 */
	@TableField("level_name2")
	private String levelName2;

	/** 奖励绩效 */
	@TableField("award_perf")
	private String awardPerf;

	/** 基础绩效 */
	@TableField("basic_perf")
	private String basicPerf;

	@TableField("sort")
	private Integer sort;

	/** 创建人 */
	@TableField("create_by")
	private String createBy;

	/** 创建时间 */
	@TableField("create_time")
	private String createTime;

	/** 更新人 */
	@TableField("last_update_by")
	private String lastUpdateBy;

	/** 上次更新时间 */
	@TableField("last_update_time")
	private String lastUpdateTime;


    @TableField("hospital_id")
	private String hospitalId;

	/** : */
	@TableField("del_flag")
	private Integer delFlag;

}
