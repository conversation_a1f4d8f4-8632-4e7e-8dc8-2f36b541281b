package com.jp.med.hrm.modules.postManagement.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.hrm.modules.postManagement.dto.SalaryStandardDto;
import com.jp.med.hrm.modules.postManagement.service.read.SalaryStandardReadService;
import com.jp.med.hrm.modules.postManagement.service.write.SalaryStandardWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;


/**
 * 事业单位基本工资标准表
 * <AUTHOR>
 * @email -
 * @date 2023-09-21 19:08:32 绩效
 */
@Api(value = "事业单位基本工资标准表", tags = "事业单位基本工资标准表")
@RestController
@RequestMapping("salaryStandard")
public class SalaryStandardController {

    @Autowired
    private SalaryStandardReadService salaryStandardReadService;

    @Autowired
    private SalaryStandardWriteService salaryStandardWriteService;

    /**
     *
     * 获取岗位工资和薪级工资树形数据
     */
    @ApiOperation("获取岗位工资和薪级工资树形数据")
    @GetMapping("/basicSalaryTreeData")
    public CommonResult<?> getBasicSalaryTreeData(){
        return CommonResult.success(salaryStandardReadService.getBasicSalaryTreeData());
    }


    /**
     *
     * 查询不同员工类型的岗位工资和薪级工资
     */
    @ApiOperation("查询不同员工类型的岗位工资和薪级工资")
    @PostMapping("/basicSalary")
    public CommonResult<?> getBasicSalary(@RequestBody SalaryStandardDto dto){
        return CommonResult.success(salaryStandardReadService.getBasicSalariesStandard(dto));
    }

    @ApiOperation("新增")
    @PostMapping("/saveSalaryStandard")
    public CommonResult<?> saveSalaryStandard(@RequestBody SalaryStandardDto dto){
        salaryStandardWriteService.addSalaryStandard(dto);
        return CommonResult.success();
    }
    @ApiOperation("修改")
    @PutMapping("/updateSalaryStandard")
    public CommonResult<?> updateSalaryStandard(@RequestBody List<SalaryStandardDto> list){
        salaryStandardWriteService.updateSalaryStandard(list);
        return CommonResult.success();
    }

    @ApiOperation("批量逻辑删除基本工资标准")
    @DeleteMapping("/deletedBasicSalaryStandard")
    public CommonResult<?> deletedSalaryStandard(@RequestBody List<Integer> ids){
        salaryStandardWriteService.deletedSalaryStandard(ids);
        return CommonResult.success();
    }


}
