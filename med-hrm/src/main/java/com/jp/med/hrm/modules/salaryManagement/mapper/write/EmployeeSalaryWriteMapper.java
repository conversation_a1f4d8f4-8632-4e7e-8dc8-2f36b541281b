package com.jp.med.hrm.modules.salaryManagement.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.hrm.modules.salaryManagement.dto.EmployeeSalaryDto;
import com.jp.med.hrm.modules.salaryManagement.vo.HrpSalaryTask;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 工资发放详情
 * <AUTHOR>
 * @email -
 * @date 2023-09-28 11:31:24
 */
@Mapper
public interface EmployeeSalaryWriteMapper extends BaseMapper<EmployeeSalaryDto> {


    /**
     * 写入工资数据到Hrm
     * @param result
     */
    void saveYyData(@Param("result")List<Map<String, Object>> result);

    /**
     * 删除Yy指定月份的工资数据
     * @param dto
     */
    void deleteYySalary(@Param("salaryMonth") String salaryMonth);

    /**
     * 创建应发工资申请任务
     * @param dto
     */
    void insertTask(EmployeeSalaryDto dto);

    /**
     * 删除申请
     * @param dto
     */
    void deleteTaskApply(HrpSalaryTask dto);

    /**
     * 更新工资任务报销Id
     *
     * @param salaryTaskId 工资任务Id
     * @param reimId    报销任务Id
     */
    void updateReimId(@Param("salaryTaskId") Integer salaryTaskId,@Param("reimId") Integer reimId);

    /**
     * 创建实发工资任务
     * @param dto
     */
    void insertReallySalaryTask(HrpSalaryTask dto);

    /**
     * 更新实发工资业务状态
     * @param dto
     */
    void updateReallySalaryStatus(EmployeeSalaryDto dto);

    /**
     * 更新实发工资任务 应发、扣款 、实发合计
     * @param hrpSalaryTask
     */
    void updateReallySalaryTask(HrpSalaryTask hrpSalaryTask);
}
