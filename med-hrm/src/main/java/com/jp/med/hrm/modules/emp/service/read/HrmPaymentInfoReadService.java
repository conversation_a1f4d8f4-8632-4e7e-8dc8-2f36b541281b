package com.jp.med.hrm.modules.emp.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.hrm.modules.emp.dto.HrmPaymentInfoDto;
import com.jp.med.hrm.modules.emp.vo.HrmPaymentInfoVo;

import java.util.List;

/**
 * 个人支付信息
 * <AUTHOR>
 * @email -
 * @date 2024-07-31 15:02:13
 */
public interface HrmPaymentInfoReadService extends IService<HrmPaymentInfoDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<HrmPaymentInfoVo> queryList(HrmPaymentInfoDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<HrmPaymentInfoVo> queryPageList(HrmPaymentInfoDto dto);
}

