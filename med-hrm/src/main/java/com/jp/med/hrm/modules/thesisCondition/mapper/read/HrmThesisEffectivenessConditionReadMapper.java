package com.jp.med.hrm.modules.thesisCondition.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.beust.jcommander.Parameter;
import com.jp.med.hrm.modules.thesisCondition.dto.HrmThesisEffectivenessConditionDto;
import com.jp.med.hrm.modules.thesisCondition.vo.HrmThesisEffectivenessConditionVo;
import org.mapstruct.Mapper;
import org.springframework.data.repository.query.Param;

import java.util.List;

@Mapper
public interface HrmThesisEffectivenessConditionReadMapper extends BaseMapper<HrmThesisEffectivenessConditionDto> {
    /**
     * 论文有效性认定
     * @param dto
     * @return
     */
    List<HrmThesisEffectivenessConditionVo> queryList(HrmThesisEffectivenessConditionDto dto);

    HrmThesisEffectivenessConditionDto queryById(Integer id);
}
