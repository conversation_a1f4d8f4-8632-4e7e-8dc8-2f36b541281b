package com.jp.med.hrm.modules.config.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 用户组配置
 * <AUTHOR>
 * @email -
 * @date 2024-02-29 19:22:26
 */
@Data
@TableName("hrm_group_cfg" )
public class HrmGroupCfgDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 用户组编码 */
    @TableField(value = "group_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String groupCode;

    /** 用户组名称 */
    @TableField(value = "group_name",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String groupName;


    /** 父级编码 */
    @TableField(value = "parent_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String parentId;

    /** 组织编码 */
    @TableField(value = "org_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String orgId;

    /** 医疗机构编码 */
    @TableField(value = "hospital_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String hospitalId;

    /** 有效标志 */
    @TableField(value = "active_flag",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String activeFlag;

}
