package com.jp.med.hrm.modules.emp.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 对口支援
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 11:14:30
 */
@Data
@TableName("hrm_partner_asst" )
public class EmpPartnerAsstDto extends CommonQueryDto {


    @TableId("id")
    private Long id;

    /** 是否对口支援 */
    @TableField("is_asst")
    private String isAsst;

    /** 对口支援类型 */
    @TableField("asst_type")
    private String asstType;

    /** 开始时间 */
    @TableField("start_time")
    private String startTime;

    /** 结束时间 */
    @TableField("end_time")
    private String endTime;

    /** 受援单位 */
    @TableField("asst_emp")
    private String asstEmp;

    /** 备注 */
    @TableField("remark")
    private String remark;

    /** 附件 */
    @TableField("att")
    private String att;

    /** 附件名称 */
    @TableField("att_name")
    private String attName;

    /** 职工id */
    @TableField("emp_id")
    private Long empId;

    /** 是否逻辑删除 */
    @TableField("is_deleted")
    private Long isDeleted;

    /** 医疗机构id */
    @TableField("hospital_id")
    private String hospitalId;

}
