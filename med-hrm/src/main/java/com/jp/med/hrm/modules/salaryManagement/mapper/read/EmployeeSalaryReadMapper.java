package com.jp.med.hrm.modules.salaryManagement.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.common.dto.ecs.EcsReimSalaryTask;
import com.jp.med.hrm.modules.config.dto.HrmSalaryCfgLogDto;
import com.jp.med.hrm.modules.config.vo.HrmSalaryCfgLogVo;
import com.jp.med.hrm.modules.salaryManagement.dto.EmployeeSalaryDto;
import com.jp.med.hrm.modules.salaryManagement.vo.EmployeeReallySalaryVo;
import com.jp.med.hrm.modules.salaryManagement.vo.EmployeeSalaryVo;
import com.jp.med.hrm.modules.salaryManagement.vo.HrpSalaryTask;
import com.jp.med.hrm.modules.salaryManagement.vo.SpecialEmpSalaryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工资发放详情
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-28 11:31:24
 */
@Mapper
public interface EmployeeSalaryReadMapper extends BaseMapper<EmployeeSalaryDto> {


    /**
     * 查询工资发放申请任务
     *
     * @param dto
     * @return
     */
    List<HrpSalaryTask> querySalaryTaskList(EmployeeSalaryDto dto);

    /**
     * 查询hrp应发工资列表
     *
     * @param dto
     * @return
     */
    List<EmployeeSalaryVo> queryHrpList(EmployeeSalaryDto dto);

    /**
     * 查询实发工资任务
     *
     * @param dto
     * @return
     */
    List<HrpSalaryTask> queryReallySalaryTask(HrpSalaryTask dto);

    /**
     * 查询实发工资详情
     *
     * @param dto
     * @return
     */
    List<EmployeeReallySalaryVo> queryReallySalaryDetail(HrpSalaryTask dto);

    /**
     * 查询实发工资详情2
     * @param dto
     * @return
     */
    List<EmployeeSalaryVo> queryReallySalaryDetail2(EmployeeSalaryDto dto);

    /**
     * 查询用友工资列表
     *
     * @param dto
     * @return
     */
    List<EmployeeSalaryVo> queryYyList(EmployeeSalaryDto dto);



    /***
     * 查询工资变动记录
     * @param dto
     * @return
     */
    List<HrmSalaryCfgLogVo> selectSalaryChgLog(HrmSalaryCfgLogDto dto);



    /**
     * 校验应发工资任务是否存在
     *
     * @param dto
     * @return
     */
    boolean validateSalaryMonth(EmployeeSalaryDto dto);

    /**
     * 查询任务月份禁用
     *
     * @param dto
     * @return
     */
    List<String> queryTaskMonthDisabled(HrpSalaryTask dto);

    /**
     * 更新工资任务 业务状态
     *
     * @param bchno
     * @param status
     */
    void updateTaskStatus(@Param("bchno") String bchno, @Param("status") String status);

    /**
     * 申请任务撤销 校验
     *
     * @param dto
     * @return
     */
    boolean deleteTaskCheck(HrpSalaryTask dto);

    /**
     * 通过审核批次号查询工资任务
     *
     * @param bchno 审核批次号
     */
    HrpSalaryTask querySalaryTaskByBchno(@Param("bchno") String bchno);

    /**
     * 查询应发工资报销数据详情（按科室汇总）
     *
     * @param taskId 实发工资任务Id
     */
    List<EmployeeSalaryVo> queryRealySalaryReimDetail(@Param("taskId") Integer taskId);

    /**
     * 查询企业代扣代缴三险两金数据（按科室汇总）
     *
     * @param taskId 实发工资任务Id
     */
    List<EmployeeSalaryVo> queryEnterpriseWithholdingPaymentData(@Param("taskId") Integer taskId);

    /**
     * 查询个人临时扣款数据
     *
     * @param taskId 实发工资任务Id
     */
    List<EmployeeSalaryVo> queryEmpTempReduceData(@Param("taskId") Integer taskId);

    /**
     * 查询企业五险两金缴纳数据
     *
     * @param taskId 实发工资任务Id
     */
    List<EmployeeSalaryVo> queryEnterpriseInsuranceAndFundPaymentData(@Param("taskId") Integer taskId);

    /**
     * 查询企业缴纳工会会费详情
     *
     * @param taskId 实发工资任务Id
     */
    List<EmployeeSalaryVo> queryEnterpriselaborUnionPaymentData(@Param("taskId") Integer taskId);



    /**
     * 审核通过的应发工资任务 撤销校验
     *
     * @return
     */
    boolean deleteReallySalaryTaskCheck(HrpSalaryTask dto);

    /**
     * 人力应发工资预测
     *
     * @param dto
     * @return
     */
    List<EmployeeSalaryVo> fortSalary(EmployeeSalaryDto dto);

    /**
     * 财务实发工资预测
     *
     * @param dto
     * @return
     */
    List<EmployeeReallySalaryVo> fortReallySalary(HrpSalaryTask dto);

    /**
     * 提供实发工资报销任务
     *
     * @param o
     * @return
     */
    EcsReimSalaryTask queryReimTask(EmployeeSalaryDto o);

    /**
     * 查询指定月到公司的职工及当月对应的上班天数  进入本单位时间<=当前工资月的职工 且从未发过工资
     *
     * @param salaryMonth 查询月份
     */
    List<SpecialEmpSalaryVo> selectEmpJoinedThisMonth(String salaryMonth);

    /**
     * 查询指定月 职工进修/对口支援情况
     *
     * @param salaryMonth
     * @return
     */
    List<SpecialEmpSalaryVo> selectEmpOutGoingThisMonth(String salaryMonth);

    /**
     * 校验实发工资任务是否存在
     * @param bchno
     * @return
     */
    boolean checkReallySalaryTaskByBchno(@Param("bchno") String bchno);

    /**
     * 历史未扣款
     * @param hrpSalaryTask
     * @return
     */
    List<EmployeeReallySalaryVo> historyReduce(HrpSalaryTask hrpSalaryTask);
}
