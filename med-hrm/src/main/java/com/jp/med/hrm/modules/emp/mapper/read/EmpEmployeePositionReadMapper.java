package com.jp.med.hrm.modules.emp.mapper.read;

import com.jp.med.hrm.modules.emp.dto.EmpEmployeePositionDto;
import com.jp.med.hrm.modules.emp.vo.EmpEmployeePositionVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 员工  院内岗位 记录表
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 11:12:37
 */
@Mapper
public interface EmpEmployeePositionReadMapper extends BaseMapper<EmpEmployeePositionDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EmpEmployeePositionVo> queryList(EmpEmployeePositionDto dto);
}
