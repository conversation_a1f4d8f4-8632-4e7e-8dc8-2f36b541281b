package com.jp.med.hrm.modules.thesisCondition.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.hrm.modules.thesisCondition.dto.HrmPaperPageChargeDto;
import com.jp.med.hrm.modules.thesisCondition.vo.HrmPaperPageChargeVo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
public interface HrmPaperPageChargeReadMapper extends BaseMapper<HrmPaperPageChargeDto> {

    List<HrmPaperPageChargeVo> queryList(HrmPaperPageChargeDto dto);

    HrmPaperPageChargeDto queryById(Integer id);
}
