package com.jp.med.hrm.modules.emp.mapper.write;

import com.jp.med.hrm.modules.emp.dto.EmpEmployeeInfoDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.hrm.modules.emp.dto.EmpEmployeeModifyFileDto;
import com.jp.med.hrm.modules.emp.dto.EmpEmployeeModifyLogDto;
import com.jp.med.hrm.modules.emp.dto.MgtModifyData;
import com.jp.med.hrm.modules.emp.entity.EmpEmployeeInfoEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 员工信息表
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 11:12:37
 */
@Mapper
public interface EmpEmployeeInfoWriteMapper extends BaseMapper<EmpEmployeeInfoDto> {
    /**
     * 新增
     * @param dto
     */
    void saveEmployeeInfo(EmpEmployeeInfoEntity dto);

    void updateEmployeeInfo(EmpEmployeeInfoEntity dto);

    /**
     * 修改值
     * @param map
     */
    void update2(Map<String, Object> map);

    /**
     * 新增操作日志
     * @param log
     */
    void addOperateLog(EmpEmployeeModifyLogDto log);

    /**
     * 更新文件
     * @param a
     */
    void update3(EmpEmployeeModifyFileDto a);

    /**
     * 逻辑删除
     * @param data
     */
    void update4(Map<String, Object> data);

    /**
     * 修改审核状态
     * @param dto
     */
    void updateLogState(EmpEmployeeInfoDto dto);

    /**
     * 回退数据
     * @param param
     */
    void rollbackData(Map<String, Object> param);

    /**
     * 修改管理员值
     * @param managerModifyListMap
     */
    void updateManagerValue(MgtModifyData modifyData);

    void updateManagerFile(MgtModifyData m);

    void updateTrailTimeById(EmpEmployeeInfoDto dto);

    void logicalDeleteEmp(Integer empId);

    void updateEmpInfo(String fieldName,String orgId, Integer empId);
}
