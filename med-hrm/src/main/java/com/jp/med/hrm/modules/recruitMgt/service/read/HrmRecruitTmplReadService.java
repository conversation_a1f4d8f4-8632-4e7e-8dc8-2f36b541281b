package com.jp.med.hrm.modules.recruitMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.hrm.modules.recruitMgt.dto.HrmRecruitTmplDto;
import com.jp.med.hrm.modules.recruitMgt.vo.HrmRecruitTmplVo;

import java.util.List;

/**
 * 招聘模板
 * <AUTHOR>
 * @email -
 * @date 2023-11-13 19:32:27
 */
public interface HrmRecruitTmplReadService extends IService<HrmRecruitTmplDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<HrmRecruitTmplVo> queryList(HrmRecruitTmplDto dto);
}

