package com.jp.med.hrm.modules.push.impl.wn;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.jp.med.hrm.modules.emp.dto.EmpAdministrativeRankDto;
import com.jp.med.hrm.modules.emp.dto.EmpEmployeeInfoDto;
import com.jp.med.hrm.modules.org.dto.HrmOrgDto;
import com.jp.med.hrm.modules.push.abs.wn.WNPushDataAbs;
import com.jp.med.hrm.modules.push.dto.PushDataDto;
import com.jp.med.hrm.modules.push.dto.ReqInfo;
import com.jp.med.hrm.modules.push.dto.wn.GetOIDDto;
import com.jp.med.hrm.modules.push.dto.wn.OrgDto;
import com.jp.med.hrm.modules.push.dto.wn.req.ReqWrapper;
import com.jp.med.hrm.modules.push.enums.DataType;
import com.jp.med.hrm.modules.push.enums.wn.ModelCode;
import com.jp.med.hrm.modules.push.enums.wn.OperationType;
import com.jp.med.hrm.modules.push.enums.wn.StatusType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 推送组织数据
 *
 * <AUTHOR>
 * @date 2025/05/12
 */
@Slf4j
@Component("wnPushOrgData")
public class PushOrgData extends WNPushDataAbs {
    /**
     * 推送科室
     *
     * @param data 数据
     * @return boolean
     */
    @Override
    public boolean doPush(PushDataDto data) {
        HrmOrgDto hrmOrgDto = data.getHrmOrgDto();
        List<EmpEmployeeInfoDto> empEmployeeInfoDtoList = data.getEmpEmployeeInfoDtoList();

        if (StrUtil.isEmpty(hrmOrgDto.getWnOid())){
            GetOIDDto oidDto = new GetOIDDto()
                    .setSize(1)
                    .setHospitalOid(hospitalOid)
                    .setModelCode(ModelCode.BUSINESS_UNIT.getValue());
            String oid = getOID(oidDto,DataType.ORG);
            if (StrUtil.isEmpty(oid)) {
                log.error("没有获取到Oid，跳过推送，oid类型：{}", ModelCode.BUSINESS_UNIT.getValue());
                return false;
            }
            hrmOrgDto.setWnOid(oid);
        }

        ReqWrapper reqWrapper = buildOrgParam(hrmOrgDto,empEmployeeInfoDtoList);

        ReqInfo reqInfo = new ReqInfo()
                .setBody(reqWrapper);
        log.info("请求参数{}", reqInfo.getBodyJsonStr());

        HttpResponse execute = HttpRequest.post(getAllUrl(OperationType.REGISTER))
                .addHeaders(reqInfo.getHeader())
                .body(reqInfo.getBodyJsonStr())
                .execute();
        return parseResq(execute);
    }

    private ReqWrapper buildOrgParam(HrmOrgDto hrmOrgDto, List<EmpEmployeeInfoDto> empEmployeeInfoDtoList) {

        List<OrgDto.OrganizationXEmployee> organizationXEmployeeList = new ArrayList<>();
        empEmployeeInfoDtoList.forEach(empEmployeeInfoDto -> {
            if (StrUtil.isNotEmpty(empEmployeeInfoDto.getWnOid())){
                OrgDto.OrganizationXEmployee organizationXEmployee = new OrgDto.OrganizationXEmployee();
                String adminPositionCode = StatusType.ORG_EMP_RELA_TYPE.getDefaultValue();

                List<EmpAdministrativeRankDto> administrativeRankList = empEmployeeInfoDto.getAdministrativeRankList();
                if (CollUtil.isNotEmpty(administrativeRankList)) {
                    EmpAdministrativeRankDto empAdministrativeRankDto = administrativeRankList.get(0);
                    String administrativeRank = empAdministrativeRankDto.getAdministrativeRank();
                    if (StrUtil.isNotEmpty(administrativeRank)) {
                        adminPositionCode = getWnDictOid(StatusType.ORG_EMP_RELA_TYPE, administrativeRank);
                    }
                }
                organizationXEmployee.setAdminPositionCode(adminPositionCode)
                        .setEmployeeOid(empEmployeeInfoDto.getWnOid());
                organizationXEmployeeList.add(organizationXEmployee);
            }
        });

        String orgStatus = getWnDictOid(StatusType.BASE_STATUS, hrmOrgDto.getActiveFlag());

        Set<String> buTypeCodeList  = new HashSet<>();
        String orgType = hrmOrgDto.getOrgType();
        if (StrUtil.isNotEmpty(orgType)) {
            String[] split = orgType.split(",");
            for (String s : split) {
                String unitType = getWnDictOid(StatusType.UNIT_TYPE, s);
                buTypeCodeList.add(unitType);
            }
        }else {
            buTypeCodeList.add(StatusType.UNIT_TYPE.getDefaultValue());
        }

        OrgDto orgDto = new OrgDto()
                .setOrgOid(hrmOrgDto.getWnOid())
                .setOrgId(hrmOrgDto.getOrgId())
                .setOrgNo(hrmOrgDto.getOrgId())
                .setOrgName(hrmOrgDto.getOrgName())
                .setParentOrgOid(hrmOrgDto.getParentWnOid())
                .setOrgStatus(orgStatus)
                .setBuTypeCodeList(new ArrayList<>(buTypeCodeList))
                .setOrganizationXEmployeeList(organizationXEmployeeList);

        return ReqWrapper.builder()
                .request()
                .body()
                .dataItem(List.of(orgDto))
                .done()
                .head(getReqHead(DataType.ORG))
                .done()
                .build();
    }
}
