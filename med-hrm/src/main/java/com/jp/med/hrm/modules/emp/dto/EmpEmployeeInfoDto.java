package com.jp.med.hrm.modules.emp.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 员工信息表
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 11:12:37
 */
@Data
@TableName("hrm_employee_info" )
public class EmpEmployeeInfoDto extends CommonQueryDto {

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /** 员工编号 */
    @TableField(value = "emp_code")
    private String empCode;

    /** 员工姓名 */
    @TableField("emp_name")
    private String empName;

    /** 职工类型 */
    @TableField("emp_type")
    private String empType;

    /** 组织 */
    @TableField("hospital_id")
    private String hospitalId;

    /** 部门 */
    @TableField("org_id")
    private String orgId;

    /** 兼任科室 */
    @TableField("ajt_org_ids")
    private String ajtOrgIds;

    /** 职务 */
    @TableField("job")
    private String job;

    /** 证件号码 */
    @TableField("icd_card")
    private String icdCard;

    /** 出生日期 */
    @TableField("birthday")
    private String birthday;

    /** 年龄 */
    @TableField("age")
    private Integer age;

    /** 联系电话1 */
    @TableField("phone")
    private String phone;

    /** 员工状态 */
    @TableField("status")
    private String status;

    /** 证件类型（0身份证，1护照，2其他） */
    @TableField("icd_card_type")
    private String icdCardType;

    /** 性别（1男，2女） */
    @TableField("sex")
    private String sex;

    /** 各种任职状态结束时间（如转正为转正结束时间） /更改测量为可更新为null*/
    @TableField(value = "end_time" ,updateStrategy = FieldStrategy.IGNORED)
    private String endTime;

    /** 各种任职状态开始时间（如试用为试用开始时间，转正为转正开始时间）/更改测量为可更新为null */
    @TableField(value = "start_time" ,updateStrategy = FieldStrategy.IGNORED)
    private String startTime;

    /** 参加工作时间 */
    @TableField("join_job_time")
    private String joinJobTime;

    /** 血型 */
    @TableField("blood_type")
    private String bloodType;

    /** 邮箱 */
    @TableField("email")
    private String email;

    /** 婚姻状况（0:未婚、1:已婚、2:离婚、3:丧偶） */
    @TableField("marriage_condition")
    private String marriageCondition;

    /** 民族 */
    @TableField("mz")
    private String mz;

    /** 户口类型(0:农村，1:非农村） */
    @TableField("registered")
    private String registered;

    /** 籍贯 */
    @TableField("native_place")
    private String nativePlace;

    /** 户籍所在地 */
    @TableField("census_register")
    private String censusRegister;

    /** 出生地 */
    @TableField("birth_place")
    private String birthPlace;

    /** 通讯地址 */
    @TableField("communication_address")
    private String communicationAddress;

    /** 爱好特长 */
    @TableField("hobby")
    private String hobby;

    /** 健康状况 */
    @TableField("health_condition")
    private String healthCondition;

    /** 政治面貌 */
    @TableField("politics_status")
    private String politicsStatus;

    /** 工作状态（0:不可调配，1：可调配） */
    @TableField("job_type")
    private String jobType;

    /** 入职渠道 */
    @TableField("enter_channel")
    private String enterChannel;

    /** 到本单位时间 */
    @TableField("to_company_time")
    private String toCompanyTime;

    /** 黑名单 */
    @TableField("blacklist")
    private String blacklist;

    /** 头像路径 */
    @TableField("portrait_path")
    private String portraitPath;

    /** 头像路径 */
    @TableField("portrait_path_name")
    private String portraitPathName;

    /** 档案号 */
    @TableField("archive_num")
    private String archiveNum;

    /** 档案所在地 */
    @TableField("archive_location")
    private String archiveLocation;

    /** 个人身高 */
    @TableField("person_height")
    private Double personHeight;

    /** 个人体重 */
    @TableField("person_weight")
    private Double personWeight;

    /** 身份证有效期 */
    @TableField("icd_validity_date")
    private String icdValidityDate;

    /** 经费性质 */
    @TableField("funds_nature")
    private String fundsNature;

    /** 人员性质 */
    @TableField("employ_nature")
    private String employNature;

    /** 联系电话2 */
    @TableField("phone2")
    private String phone2;

    /** 短号 */
    @TableField("sort_phone")
    private String sortPhone;

    /** 教护身份 */
    @TableField("religious_identity")
    private String religiousIdentity;

    /** 转正日期 */
    @TableField("confirmation_date")
    private String confirmationDate;

    /** 试用期考核结果 */
    @TableField("prebationary_result")
    private String prebationaryResult;

    /** 试用期考核情况 */
    @TableField("prebationary_condition")
    private String prebationaryCondition;

    /** 身份证附件 */
    @TableField("icd_file")
    private String icdFile;

    /** 备注 */
    @TableField("remark")
    private String remark;

    /** 档案出生日期 */
    @TableField("archive_birthday")
    private String archiveBirthday;

    /** 入编时间 */
    @TableField("compile_time")
    private String compileTime;

    /** 调入时间 */
    @TableField("transferred_time")
    private String transferredTime;

    /** 连续工龄认定时间 */
    @TableField("seniority_sure_time")
    private String senioritySureTime;


    /** 模糊查询 */
    @TableField(exist = false)
    private String empCodeOrEmpName;

    /** id */
    @TableField(exist = false)
    private Integer empId;

    /** 附件存储的表 */
    @TableField(exist = false)
    private String fileTable;

    /** 附件存储的字段 */
    @TableField(exist = false)
    private String fileField;

    /** 文件路径 */
    @TableField(exist = false)
    private String filePath;

    /** 文件名 */
    @TableField(exist = false)
    private String fileName;

    /** 修改的数据 */
    @TableField(exist = false)
    private EmpModifyDto modifyData;

    /** 个人头像*/
    @TableField(exist = false)
    private MultipartFile employeeAvatar;

    /** 文件上传类型,1头像，2其他 */
    @TableField(exist = false)
    private String uploadType;

    /** 用户id 集合 */
    @TableField(exist = false)
    private List<Long> userIds;

    /** id 集合 */
    @TableField(exist = false)
    private List<Long> ids;

    /** 修改详情 */
    @TableField(exist = false)
    private List<EmpEmployeeModifyDto> modifyDetails;

    /** 修改详情-文件 */
    @TableField(exist = false)
    private List<EmpEmployeeModifyFileDto> modifyFileDetails;

    /** 是否新增 1：是 0：不是 */
    @TableField(exist = false)
    private String isAdd;

    /** 是否是管理者 */
    @TableField(exist = false)
    private String isManager;

    /** 模块名称 */
    @TableField(exist = false)
    private String moduleName;

    /** 业务状态 */
    @TableField(exist = false)
    private String stas;

    /** 审核状态 */
    @TableField(exist = false)
    private String auditState;

    /** 审核时间 */
    @TableField(exist = false)
    private String chkTime;

    /** 管理员修改值 */
    @TableField(exist = false)
    private Map<Long, String> managerModifyMap;

    /** 管理员修改值 */
    @TableField(exist = false)
    private List<MgtModifyData> managerModifyList;

    /** 限制人员类型-职业类别 */
    @TableField(exist = false)
    private String restrictedEmpType;

    /** 限制职工 (0001,0002,0003) */
    @TableField(exist = false)
    private String restrictedEmps;

    /** 科室集合 */
    @TableField(exist = false)
    private List<String> orgIds;

    /**
     * 管理员
     */
    @TableField(exist = false)
    private Boolean isAdmin;

    /**
     * emp_code list
     */
    @TableField(exist = false)
    private List<String> empCodeList;

    /** 修改信息执行日期 */
    @TableField(exist = false)
    private String infoExeDate;


    /** 开始月份 */
    @TableField(exist = false)
    private String startMonth;

    /** 结束月份 */
    @TableField(exist = false)
    private String endMonth;

    /**
     * 退休日期
     */
    @TableField("retire_date")
    private String retireDate;

    /**
     * 卫宁oid
     */
    @TableField("wn_oid")
    private String wnOid;

    @TableField(exist = false)
    private List<EmpAdministrativeRankDto> administrativeRankList;

    @TableField(exist = false)
    private String enable;

}
