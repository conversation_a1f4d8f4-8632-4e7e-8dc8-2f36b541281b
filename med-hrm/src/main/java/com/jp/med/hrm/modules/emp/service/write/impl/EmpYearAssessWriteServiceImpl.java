package com.jp.med.hrm.modules.emp.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.hrm.modules.emp.dto.*;
import com.jp.med.hrm.modules.emp.mapper.write.EmpEmployeeInfoWriteMapper;
import com.jp.med.hrm.modules.yearAssess.dto.YearAssessDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.hrm.modules.emp.mapper.write.EmpYearAssessWriteMapper;
import com.jp.med.hrm.modules.emp.service.write.EmpYearAssessWriteService;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 年度考核
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-15 14:18:22
 */
@Service
@Transactional(readOnly = false)
public class EmpYearAssessWriteServiceImpl extends ServiceImpl<EmpYearAssessWriteMapper, EmpYearAssessDto> implements EmpYearAssessWriteService {

    @Autowired
    private EmpYearAssessWriteMapper empYearAssessWriteMapper;

    /**
     * 保存职工年度登记述职信息
     */
    @Override
    public void updateRegistrationInfo(EmpYearAssessDto dto) {
        if (Objects.nonNull(dto.getEmpId())) {
            if (Objects.nonNull(dto.getId())) {
                empYearAssessWriteMapper.updateInfo(dto);
            } else {
                empYearAssessWriteMapper.saveInfo(dto);
            }
        } else {
            throw new AppException("没有人力资源用户数据,保存失败");
        }
    }

    /**
     * 批量更新年度考核结果
     */
    @Override
    public void updateAssessResult(EmpYearAssessDto dto) {
        EmpEmployeeModifyDto modifyDetails = dto.getModifyDetails();
        List<EmpEmployeeModifyLogDto> modifyInfo = modifyDetails.getModifyInfo();
        setLogInfo(modifyInfo, dto);
        if (!modifyInfo.isEmpty()) {
            empYearAssessWriteMapper.batchUpdate(modifyInfo);

            BatchUtil.batch("addOperateLog", modifyInfo, EmpEmployeeInfoWriteMapper.class);
        }

    }

    /**
     * 批量更新考核附件
     */
    @Override
    public void updateAssessFiles(EmpYearAssessDto dto) {
        List<EmpEmployeeModifyFileDto> modifyFileDetails = dto.getModifyFileDetails();
        List<EmpEmployeeModifyLogDto> modifyLogInfo = new ArrayList<>();
        if (!modifyFileDetails.isEmpty()) {
            modifyFileDetails.forEach(item -> {
                uploadFileToOss(item);
                addFileLog(item, dto, modifyLogInfo, false);
            });
            empYearAssessWriteMapper.batchUpdateFile(modifyFileDetails);
            //日志
            BatchUtil.batch("addOperateLog", modifyLogInfo, EmpEmployeeInfoWriteMapper.class);
        }

    }

    /**
     * 添加文件日志
     *
     * @param mf         数据
     * @param dto        参数
     * @param modifyInfo 详情
     * @param upload     是否上传
     */
    private void addFileLog(EmpEmployeeModifyFileDto mf,
                            EmpYearAssessDto dto,
                            List<EmpEmployeeModifyLogDto> modifyInfo,
                            boolean upload) {
        EmpEmployeeModifyLogDto d = new EmpEmployeeModifyLogDto();
        d.setEmpId((long) mf.getEmpId());
        d.setTableName(mf.getTableName());
        d.setModule(mf.getTitle());
        d.setCreateTime(DateUtil.getCurrentTime(null));
        d.setOpter(dto.getSysUser().getHrmUser().getEmpCode());
        d.setHospitalId(dto.getHospitalId());
        d.setOriginValue(mf.getOriginValue());
        d.setIsFile(MedConst.TYPE_1);
        d.setKey(mf.getFieldName());
        d.setTitle(mf.getFieldDisplayName());
        d.setType(mf.getType());
        d.setId(mf.getId());

        if (MedConst.TYPE_1.equals(dto.getIsManager())) {
            d.setStas(MedConst.TYPE_0);
        } else {
            d.setStas(MedConst.TYPE_1);
        }
        if (upload) {
            uploadFileToOss(mf);
        }
        // 新值
        d.setPresentValue(mf.getFilePath());
        d.setDisplayOriginValue(mf.getDisplayOriginValue());
        d.setDisplayPresentValue(mf.getFileName());
        modifyInfo.add(d);
    }

    private void uploadFileToOss(EmpEmployeeModifyFileDto mf) {
        StringBuilder filePaths = new StringBuilder();
        StringBuilder fileNames = new StringBuilder();
        if (CollectionUtil.isNotEmpty(mf.getFiles())) {
            mf.getFiles().forEach(file -> {
                String filePath = OSSUtil.uploadFile(OSSConst.BUCKET_HRM, mf.getTitle() + "/", file);
                filePaths.append(filePath);
                filePaths.append(",");
                fileNames.append(file.getOriginalFilename());
                fileNames.append(",");
            });
        }
        mf.setFilePath(filePaths.toString());
        mf.setFileName(fileNames.toString());
    }


    private void setLogInfo(List<EmpEmployeeModifyLogDto> modifyLogInfo, EmpYearAssessDto dto) {
        modifyLogInfo.forEach(d -> {
            d.setModule(dto.getModifyDetails().getTitle());
            d.setCreateTime(DateUtil.getCurrentTime(null));
            d.setOpter(dto.getSysUser().getHrmUser().getEmpCode());
            d.setIsFile(MedConst.TYPE_0);
            d.setHospitalId(dto.getHospitalId());
            if (MedConst.TYPE_1.equals(dto.getIsManager())) {
                d.setStas(MedConst.TYPE_0);
            } else {
                d.setStas(MedConst.TYPE_1);
            }
        });

    }

    /**
     * 获取修改的参数
     *
     * @param modifyInfo 修改数据
     * @param details    详情
     * @return 参数
     */
    private Map<String, Object> getModifyMap(List<EmpEmployeeModifyLogDto> modifyInfo,
                                             EmpEmployeeModifyDto details) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", modifyInfo.get(0).getId());
        map.put("tableName", details.getTableName());
        map.put("list", modifyInfo);
        return map;
    }
}
