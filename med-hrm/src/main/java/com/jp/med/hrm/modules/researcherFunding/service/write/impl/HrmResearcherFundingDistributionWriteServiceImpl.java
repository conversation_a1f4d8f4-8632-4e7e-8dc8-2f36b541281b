package com.jp.med.hrm.modules.researcherFunding.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.hrm.modules.researcherFunding.mapper.write.HrmResearcherFundingDistributionWriteMapper;
import com.jp.med.hrm.modules.researcherFunding.dto.HrmResearcherFundingDistributionDto;
import com.jp.med.hrm.modules.researcherFunding.service.write.HrmResearcherFundingDistributionWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 临床试验研究者经费分配明细表
 * <AUTHOR>
 * @email -
 * @date 2025-02-11 09:08:58
 */
@Service
@Transactional(readOnly = false)
public class HrmResearcherFundingDistributionWriteServiceImpl extends ServiceImpl<HrmResearcherFundingDistributionWriteMapper, HrmResearcherFundingDistributionDto> implements HrmResearcherFundingDistributionWriteService {
}
