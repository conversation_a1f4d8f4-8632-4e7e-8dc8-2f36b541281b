package com.jp.med.hrm.modules.config.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.hrm.modules.config.dto.HrmSalaryCfgLogDto;
import com.jp.med.hrm.modules.config.vo.HrmSalaryCfgLogVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 工资配置日志
 *
 * <AUTHOR>
 * @email -
 * @date 2024-03-18 16:57:18
 */
@Mapper
public interface HrmSalaryCfgLogReadMapper extends BaseMapper<HrmSalaryCfgLogDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<HrmSalaryCfgLogVo> queryList(HrmSalaryCfgLogDto dto);
}
