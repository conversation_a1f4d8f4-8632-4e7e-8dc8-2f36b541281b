package com.jp.med.hrm.modules.recruitMgt.controller;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.hrm.modules.recruitMgt.dto.HrmRecruitPubdDto;
import com.jp.med.hrm.modules.recruitMgt.service.read.HrmRecruitPubdReadService;
import com.jp.med.hrm.modules.recruitMgt.service.write.HrmRecruitPubdWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 招聘发布
 * <AUTHOR>
 * @email -
 * @date 2023-11-13 19:32:27
 */
@Api(value = "招聘发布", tags = "招聘发布")
@RestController
@RequestMapping("hrmRecruitPubd")
public class HrmRecruitPubdController {

    @Autowired
    private HrmRecruitPubdReadService hrmRecruitPubdReadService;

    @Autowired
    private HrmRecruitPubdWriteService hrmRecruitPubdWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询招聘发布")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody HrmRecruitPubdDto dto){
        return CommonResult.paging(hrmRecruitPubdReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增招聘发布")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody HrmRecruitPubdDto dto){
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        dto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        dto.setIsEnd(MedConst.ACTIVE_FLAG_0);
        hrmRecruitPubdWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改招聘发布")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody HrmRecruitPubdDto dto){
        if (StringUtils.isNotEmpty(dto.getIsEnd()) && MedConst.TYPE_1.equals(dto.getIsEnd())) {
            dto.setRealTime(DateUtil.getCurrentTime(null));
        }
        hrmRecruitPubdWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除招聘发布")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody HrmRecruitPubdDto dto){
        hrmRecruitPubdWriteService.removeById(dto);
        return CommonResult.success();
    }

}
