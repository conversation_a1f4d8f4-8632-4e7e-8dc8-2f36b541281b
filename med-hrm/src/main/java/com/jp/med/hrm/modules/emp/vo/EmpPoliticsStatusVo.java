package com.jp.med.hrm.modules.emp.vo;

import lombok.Data;

/**
 * 政治面貌
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 11:12:36
 */
@Data
public class EmpPoliticsStatusVo {


    private Integer id;

	/** 入党团时间 */
	private String joinTime;

	/** 转正时间 */
	private String formalTime;

	/** 退党团时间 */
	private String quitTime;

	/** 政治面貌 */
	private String politicsStatus;

	/** 职务 */
	private String job;

	/** 入党介绍人 */
	private String introducer;

	/** 所属党支部 */
	private String nowCommittee;

	/** 转入党支部时间 */
	private String rmvTime;

	/** 附件 */
	private String file;

	/** 附件名称 */
	private String fileName;
	/** 备注 */
	private String remark;

	/** 流程状态 */
	private Integer status;


    private String hospitalId;


    private Integer empId;

}
