package com.jp.med.hrm.modules.push.abs.wn;

import com.jp.med.hrm.modules.push.abs.ObtainDataAbs;
import com.jp.med.hrm.modules.push.entity.UrlConfigProperties;
import com.jp.med.hrm.modules.push.enums.wn.OperationType;

import javax.annotation.Resource;

public abstract class WNObtainOIDAbs extends ObtainDataAbs {

    @Resource
    private UrlConfigProperties urlConfigProperties;

    protected String getAllUrl(OperationType operationType) {
        return urlConfigProperties.getUrlConfigItem(operationType).getReqLink();
    }
}
