package com.jp.med.hrm.modules.postManagement.service.write.impl;

import com.jp.med.common.exception.AppException;
import com.jp.med.hrm.modules.postManagement.mapper.read.PerformanceStandardReadMapper;
import com.jp.med.hrm.modules.postManagement.vo.PerformanceStandardVo;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.hrm.modules.postManagement.mapper.write.PerformanceStandardWriteMapper;
import com.jp.med.hrm.modules.postManagement.dto.PerformanceStandardDto;
import com.jp.med.hrm.modules.postManagement.service.write.PerformanceStandardWriteService;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 事业人员绩效工资标准表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-25 14:56:57
 */
@Service
@Transactional(readOnly = false)
public class PerformanceStandardWriteServiceImpl extends ServiceImpl<PerformanceStandardWriteMapper, PerformanceStandardDto> implements PerformanceStandardWriteService {
    @Autowired
    PerformanceStandardReadMapper performanceStandardReadMapper;
    @Autowired
    PerformanceStandardWriteMapper performanceStandardWriteMapper;

    /**
     * 新增数据
     */
    @Override
    public void savePerformanceStan(PerformanceStandardDto dto) {
        //判重
        if (checkIsExist(dto)) {
            throw new AppException(dto.getLevelName1() + ',' + dto.getLevelName2() + ".已存在该记录");
        }
        performanceStandardWriteMapper.insert(dto);
    }

    /**
     * 批量更新数据
     */
    @Override
    public void updatePerformanceStan(List<PerformanceStandardDto> list) {
        for (PerformanceStandardDto dto : list) {
            if (checkIsExist(dto)) {
                throw new AppException(dto.getLevelName1() + ',' + dto.getLevelName2() + ".已存在该记录");
            }
        }
        if (list.size() > 0) {
            performanceStandardWriteMapper.updateBatchById(list);
        }

    }

    /*批量删除数据*/
    @Override
    public void deleteBatchIds(List<Integer> ids) {
        if (ids.size() > 0) {
            performanceStandardWriteMapper.deleteBatchIds(ids);
        }
    }

    /*检验数据重复性*/
    private boolean checkIsExist(PerformanceStandardDto dto) {
        List<PerformanceStandardDto> list = performanceStandardReadMapper.selectPerformanceStan(dto);
        if (list.size() > 0) {
            return true;
        }
        return false;
    }
}
