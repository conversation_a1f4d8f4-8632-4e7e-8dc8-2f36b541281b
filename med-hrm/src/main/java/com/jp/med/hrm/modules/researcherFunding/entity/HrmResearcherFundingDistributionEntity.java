package com.jp.med.hrm.modules.researcherFunding.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 临床试验研究者经费分配明细表
 * <AUTHOR>
 * @email -
 * @date 2025-02-11 09:08:58
 */
@Data
@TableName("hrm_researcher_funding_distribution")
public class HrmResearcherFundingDistributionEntity {

	/** 主键 */
	@TableId("id")
	private Integer id;

	/** 申请表ID */
	@TableField("apply_id")
	private Integer applyId;

	/** 部门 */
	@TableField("org_name")
	private String orgName;

	/** 工号 */
	@TableField("emp_code")
	private String empCode;

	/** 姓名 */
	@TableField("emp_name")
	private String empName;

	/** 分配金额 */
	@TableField("amount")
	private BigDecimal amount;

	/** 备注 */
	@TableField("remark")
	private String remark;

	/** 创建人 */
	@TableField("crter")
	private String crter;

	/** 创建时间 */
	@TableField("crte_time")
	private String crteTime;

	/** 有效标志 */
	@TableField("active_flag")
	private String activeFlag;

	/** 医疗机构id */
	@TableField("hospital_id")
	private String hospitalId;

}
