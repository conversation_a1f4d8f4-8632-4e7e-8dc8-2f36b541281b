package com.jp.med.hrm.modules.thesisCondition.vo;

import lombok.Data;

@Data
public class HrmAcademicWritingVo {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 流程实例编码
     */
    private String processInstanceCode;

    /**
     * 论文名称
     */
    private String thesisName;

    /**
     * 所属部门
     */
    private String department;

    /**
     * 所属部门Name
     */
    private String departmentName;

    /**
     * 主要研究者
     */
    private String mainResearcher;

    private String mainResearcherName;


    /**
     * 审核状态（0:待审，1:通过，2:未通过，3：已取消）
     */
    private String chkState;

    /**
     * 创建人
     */
    private String crter;

    /**
     * 创建人名称
     */
    private String crterName;

    /**
     * 创建时间
     */
    private String crteTime;

    /**
     * 有效标志
     */
    private String activeFlag;

    /**
     * 医疗机构id
     */
    private String hospitalId;

    /**
     * 学历
     */
    private String education;

    /**
     * 主编排位
     */
    private String editorRankZhu;

    /**
     * 父主编排位
     */
    private String editorRankFu;

    private String editorRankCan;

    /**
     * 著作类型
     */
    private String publicationType;

    /**
     * 主要章节
     */
    private String mainChapter;

    private String wordCount;

    /**
     * 出版社名称
     */
    private String publisherName;

    private String publisherType;

    /**
     * 内容简介
     */
    private String contentIntroduction;
}
