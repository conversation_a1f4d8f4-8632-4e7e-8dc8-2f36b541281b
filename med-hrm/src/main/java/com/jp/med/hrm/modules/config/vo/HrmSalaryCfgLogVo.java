package com.jp.med.hrm.modules.config.vo;

import lombok.Data;

/**
 * 工资配置日志
 *
 * <AUTHOR>
 * @email -
 * @date 2024-03-18 16:57:18
 */
@Data
public class HrmSalaryCfgLogVo {

    /**
     * $column.comments
     */
    private Integer id;

    /**
     * 关联表id
     */
    private Integer asocTabId;

    /**
     * 修改类型
     */
    private String modiType;

    /**
     * 职工工号
     */
    private String empCode;

    /**
     * 修改的字段
     */
    private String modiFieldCode;

    /**
     * 修改的字段名称
     */
    private String modiFieldName;

    /**
     * 旧值
     */
    private String oldVal;

    /**
     * 新值
     */
    private String newVal;

    /**
     * 显示的旧值
     */
    private String oldDisplayName;

    /**
     * 显示的新值
     */
    private String newDisplayName;

    /**
     * 创建人
     */
    private String crter;

    /**
     * 创建时间
     */
    private String crteTime;

    /**
     * 操作人姓名
     */
    private String crterName;

}
