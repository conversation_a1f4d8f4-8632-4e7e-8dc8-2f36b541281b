package com.jp.med.hrm.modules.attendanceMgt.vo;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description //请假统计Vo
 * <AUTHOR>
 * @Date 2024/2/27 9:12
 */
@Data
public class HrmLeaveStatVo {

    /**
     * 职工工号
     */
    private String empCode;

    /**
     * 员工名称
     */
    private String empName;

    /**
     * 科室
     */
    private String orgId;

    /**
     * 科室名称
     */
    private String orgName;

    /**
     * 职工类型
     */
    private String empType;

    /**
     * 职工类型
     */
    private String empTypeLabel;

    /**
     * 职工状态
     */
    private String status;

    /**
     * 职工状态
     */
    private String statusLabel;

    /**
     * 聘任职称
     */
    private String engageRank;

    /**
     * 执业资格
     */
    private String practicingType;

    /**
     * 执业资格
     */
    private String practicingTypeLabel;

    /**
     * 医疗机构id
     */
    private String hospitalId;

    /**
     * 工龄
     */
    private String currWorkAge;

    /**
     * 当年公休
     */
    private String currYearVacDays;

    /**
     * 去年公休
     */
    private String prevYearVacDays;

    /**
     * 年度考情统计
     */
    private Map<Integer, Map<String, Object>> yearStat = new HashMap<>();
    /**
     * 月度考勤统计
     */
    private Map<String, String> monthStat=new HashMap<>();


}
