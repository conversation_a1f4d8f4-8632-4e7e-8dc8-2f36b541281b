package com.jp.med.hrm.modules.hrmIssue.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.hrm.modules.hrmIssue.dto.HrmIssueMgtDto;
import com.jp.med.hrm.modules.hrmIssue.vo.HrmIssueMgtVo;

import java.util.List;

/**
 * 议题申报表
 * <AUTHOR>
 * @email -
 * @date 2024-11-06 16:04:44
 */
public interface HrmIssueMgtReadService extends IService<HrmIssueMgtDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<HrmIssueMgtVo> queryList(HrmIssueMgtDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<HrmIssueMgtVo> queryPageList(HrmIssueMgtDto dto);
}

