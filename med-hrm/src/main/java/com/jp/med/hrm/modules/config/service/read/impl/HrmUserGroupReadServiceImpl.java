package com.jp.med.hrm.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.hrm.modules.config.mapper.read.HrmUserGroupReadMapper;
import com.jp.med.hrm.modules.config.dto.HrmUserGroupDto;
import com.jp.med.hrm.modules.config.vo.HrmUserGroupVo;
import com.jp.med.hrm.modules.config.service.read.HrmUserGroupReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class HrmUserGroupReadServiceImpl extends ServiceImpl<HrmUserGroupReadMapper, HrmUserGroupDto> implements HrmUserGroupReadService {

    @Autowired
    private HrmUserGroupReadMapper hrmUserGroupReadMapper;

    @Override
    public List<HrmUserGroupVo> queryList(HrmUserGroupDto dto) {
        return hrmUserGroupReadMapper.queryList(dto);
    }

    @Override
    public List<HrmUserGroupVo> queryUser(HrmUserGroupDto dto) {
        return hrmUserGroupReadMapper.queryUser(dto);
    }

}
