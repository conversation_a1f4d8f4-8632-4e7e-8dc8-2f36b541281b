package com.jp.med.hrm.modules.emp.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.hrm.modules.emp.mapper.read.EmpProfessionalTitleReadMapper;
import com.jp.med.hrm.modules.emp.dto.EmpProfessionalTitleDto;
import com.jp.med.hrm.modules.emp.vo.EmpProfessionalTitleVo;
import com.jp.med.hrm.modules.emp.service.read.EmpProfessionalTitleReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EmpProfessionalTitleReadServiceImpl extends ServiceImpl<EmpProfessionalTitleReadMapper, EmpProfessionalTitleDto> implements EmpProfessionalTitleReadService {

    @Autowired
    private EmpProfessionalTitleReadMapper empProfessionalTitleReadMapper;

    @Override
    public List<EmpProfessionalTitleVo> queryList(EmpProfessionalTitleDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return empProfessionalTitleReadMapper.queryList(dto);
    }

}
