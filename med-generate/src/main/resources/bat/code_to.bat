@echo off
if "%1"=="" (
    echo =====================error=====================
    echo non java path
    timeout /t 5
    exit
)

if "%2"=="" (
    echo =====================error=====================
    echo non xml path
    timeout /t 5
    exit
)

if "%3"=="" (
    echo =====================error=====================
    echo non generate file path
    timeout /t 5
    exit
)

if "%4"=="" (
    echo =====================error=====================
    echo non file prefix
    timeout /t 5
    exit
)

set java_path=%1
set xml_path=%2
set genera_file_path=%3
set prefix=%4
REM set pf=%java_path:~0,2%
REM cd /d %pf%
echo =====================start moving java files=====================
robocopy %genera_file_path% %java_path%\controller %prefix%Controller.java
robocopy %genera_file_path% %java_path%\dto %prefix%Dto.java
robocopy %genera_file_path% %java_path%\service\read %prefix%ReadService.java
robocopy %genera_file_path% %java_path%\service\read\impl %prefix%ReadServiceImpl.java
robocopy %genera_file_path% %java_path%\service\write %prefix%WriteService.java
robocopy %genera_file_path% %java_path%\service\write\impl %prefix%WriteServiceImpl.java
robocopy %genera_file_path% %java_path%\mapper\read %prefix%ReadMapper.java
robocopy %genera_file_path% %java_path%\mapper\write %prefix%WriteMapper.java
robocopy %genera_file_path% %java_path%\vo %prefix%Vo.java
echo =====================end of moving the java file=====================

echo ============================================================

echo =====================start moving xml files=====================
robocopy %genera_file_path% %xml_path%\read %prefix%ReadMapper.xml
robocopy %genera_file_path% %xml_path%\write %prefix%WriteMapper.xml
echo =====================end of moving the xml file=====================
