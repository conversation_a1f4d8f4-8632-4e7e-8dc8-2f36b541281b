package ${package}.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import ${package}.dto.${moduleName}${className}Dto;
import ${package}.vo.${moduleName}${className}Vo;

import java.util.List;

/**
 * ${comments}
 * <AUTHOR>
 * @email ${email}
 * @date ${datetime}
 */
public interface ${moduleName}${className}ReadService extends IService<${moduleName}${className}Dto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<${moduleName}${className}Vo> queryList(${moduleName}${className}Dto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<${moduleName}${className}Vo> queryPageList(${moduleName}${className}Dto dto);
}

