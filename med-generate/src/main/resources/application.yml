spring:
  #mysql
  datasource:
#    url: ********************************************************************************************************************************
#    username: root
#    password: root
#    driver-class-name: com.mysql.cj.jdbc.Driver

    #PostgreSQL配置
     driverClassName: org.postgresql.Driver
   #  url: ********************************************
     url: **************************************
     username: postgres
     password: postgres

  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  resources:
    static-locations: classpath:/static/,classpath:/views/

pagehelper:
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

#指定数据库，可选值有【mysql、postgresql】
jp:
  database: postgresql
  generateProject: 'hrm.generator.properties'

generate:
  to:
    enable: false
    java-file-path: F:\Users\19583\Desktop\jpsh\gender\HRP\hrp-back\med-hrm\src\main\java\com\jp\med\hrm\modules\emp
    xml-file-path: F:\Users\19583\Desktop\jpsh\gender\HRP\hrp-back\med-hrm\src\main\resources\mapper\emp
    file-prefix: EmpAlloc
