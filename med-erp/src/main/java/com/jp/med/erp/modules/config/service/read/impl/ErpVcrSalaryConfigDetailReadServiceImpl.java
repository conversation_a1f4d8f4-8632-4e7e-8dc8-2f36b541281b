package com.jp.med.erp.modules.config.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.erp.modules.config.mapper.read.ErpVcrSalaryConfigDetailReadMapper;
import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDetailDto;
import com.jp.med.erp.modules.config.vo.ErpVcrSalaryConfigDetailVo;
import com.jp.med.erp.modules.config.service.read.ErpVcrSalaryConfigDetailReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class ErpVcrSalaryConfigDetailReadServiceImpl extends ServiceImpl<ErpVcrSalaryConfigDetailReadMapper, ErpVcrSalaryConfigDetailDto> implements ErpVcrSalaryConfigDetailReadService {

    @Autowired
    private ErpVcrSalaryConfigDetailReadMapper erpVcrSalaryConfigDetailReadMapper;

    @Override
    public List<ErpVcrSalaryConfigDetailVo> queryList(ErpVcrSalaryConfigDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return erpVcrSalaryConfigDetailReadMapper.queryList(dto);
    }

    @Override
    public List<ErpVcrSalaryConfigDetailVo> queryPageList(ErpVcrSalaryConfigDetailDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return erpVcrSalaryConfigDetailReadMapper.queryList(dto);
    }

}
