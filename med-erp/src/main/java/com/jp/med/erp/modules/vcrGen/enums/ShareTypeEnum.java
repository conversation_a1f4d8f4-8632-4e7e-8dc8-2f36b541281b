package com.jp.med.erp.modules.vcrGen.enums;

import io.seata.common.util.StringUtils;

public enum ShareTypeEnum {
    WATER("1","水费"),
    ELECTRIC("2","电费"),
    CLEANING("3","保洁费"),
    PHONE("4","电话费"),
    GAS("5","燃气费");

    private String type;

    private String shareTypeName;

    ShareTypeEnum(String type, String shareTypeName) {
        this.type = type;
        this.shareTypeName = shareTypeName;
    }

    public String getType() {
        return type;
    }

    public String getShareTypeName() {
        return shareTypeName;
    }

    public static ShareTypeEnum getByType(String type) {
        for (ShareTypeEnum status : ShareTypeEnum.values()) {
            if (StringUtils.equals(status.getType(),type)) {
                return status;
            }
        }
        return null;
    }
}
