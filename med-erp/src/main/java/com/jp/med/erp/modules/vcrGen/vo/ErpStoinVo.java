package com.jp.med.erp.modules.vcrGen.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 入库单
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:44
 */
@Data
public class ErpStoinVo {

	/** id */
	private Integer id;

	/** 入库单号 */
	private String stoinNum;

	/** 入库日期 */
	private String stoinDate;

	/** 数量合计 */
	private Integer totlcnt;

	/** 零售金额 */
	private BigDecimal rtalAmt;

	/** 进价金额 */
	private BigDecimal purcpricAmt;

	/** 发票号 */
	private String invono;

	/** 供货单位 */
	private String spler;

	/** 报销标识 */
	private String reimFlag;

	/** 同步日期 */
	private String syncDate;

	/** 医疗机构id */
	private String hospitalId;

	/** 文件 */
	private String att;

	/** 文件名称 */
	private String attName;

	/**
	 * 发票id
	 */
	private String invoId;

	/** 序号 **/
	private Integer xh;

	/** 入库类型 **/
	private String stoinType;

	/** 是否为退货 **/
	private String isBack;
}
