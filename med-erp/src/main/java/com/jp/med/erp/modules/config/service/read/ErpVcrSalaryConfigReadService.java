package com.jp.med.erp.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.erp.modules.config.dto.ErpVcrSalaryConfigDto;
import com.jp.med.erp.modules.config.vo.ErpVcrSalaryConfigVo;

import java.util.List;

/**
 * 工资凭证配置表
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 00:52:39
 */
public interface ErpVcrSalaryConfigReadService extends IService<ErpVcrSalaryConfigDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<ErpVcrSalaryConfigVo> queryList(ErpVcrSalaryConfigDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<ErpVcrSalaryConfigVo> queryPageList(ErpVcrSalaryConfigDto dto);
}

