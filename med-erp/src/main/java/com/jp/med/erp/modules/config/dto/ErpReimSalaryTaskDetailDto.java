package com.jp.med.erp.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 应发工资报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 02:01:20
 */
@Data
@TableName("ecs_reim_salary_task_detail" )
public class ErpReimSalaryTaskDetailDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 报销任务id */
    @TableField("task_id")
    private Integer taskId;

    /** 报销科室 */
    @TableField("org_id")
    private String orgId;

    /** 报销类型 */
    @TableField("reim_type")
    private String reimType;

    /** 报销金额 */
    @TableField("reim_amt")
    private BigDecimal reimAmt;

    /** 报销摘要 */
    @TableField("reim_desc")
    private String reimDesc;

    /** 工资项明细type */
    @TableField("type")
    private String type;

    /** 个人扣减员工编号 */
    @TableField("emp_code")
    private String empCode;

    /** 报销项目 */
    @TableField("reim_name")
    private String reimName;

    /** 人员类型(在编 招聘 临聘) */
    @TableField("emp_type")
    private String empType;

}
