package com.jp.med.erp.modules.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 应发工资报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-12-31 02:01:20
 */
@Data
@TableName("ecs_reim_salary_task")
public class ErpReimSalaryTaskEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 工资任务发放月份 */
	@TableField("ff_mth")
	private String ffMth;

	/** 工资条数 */
	@TableField("num")
	private Integer num;

	/** 应发合计汇总 */
	@TableField("should_pay")
	private BigDecimal shouldPay;

	/** 扣款合计汇总 */
	@TableField("reduce_pay")
	private BigDecimal reducePay;

	/** 实发合计汇总 */
	@TableField("real_pay")
	private BigDecimal realPay;

	/** 备注 */
	@TableField("remark")
	private String remark;

	/** 制表人 */
	@TableField("crter")
	private String crter;

	/** 制表时间 */
	@TableField("crte_time")
	private String crteTime;

	/** 是否报销 0:未报销 1:已报销 */
	@TableField("reim_flag")
	private String reimFlag;

	/** 工资任务id */
	@TableField("salary_id")
	private Integer salaryId;

	/** 报销id */
	@TableField("reim_id")
	private Integer reimId;

	/** 工资类型 */
	@TableField("type")
	private String type;

}
