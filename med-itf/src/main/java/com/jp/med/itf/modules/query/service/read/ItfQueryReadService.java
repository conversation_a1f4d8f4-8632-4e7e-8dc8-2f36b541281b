package com.jp.med.itf.modules.query.service.read;

import com.jp.med.itf.modules.query.dto.ItfQueryDto;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/25 14:14
 * @description:
 */
public interface ItfQueryReadService {

    /**
     * 查询
     *
     * @param dto  参数
     * @param request
     * @return 结果
     */
    List<Map<String, Object>> query(ItfQueryDto dto, HttpServletRequest request);
}
