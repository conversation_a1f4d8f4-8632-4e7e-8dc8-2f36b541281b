package com.jp.med.pms.modules.calc;

import com.jp.med.common.feign.EmpEmployeeInfoFeignService;
import com.jp.med.common.feign.HrmOrgFeignService;
import com.jp.med.pms.modules.pmsCalc.dto.CalcMonthlyPmsDto;
import com.jp.med.pms.modules.pmsCalc.service.calcEngine.PmsAwardCalcEngineService;
import com.jp.med.pms.modules.pmsCalcTemplate.service.read.PmsAwardCoefficientConfigReadService;
import com.jp.med.pms.modules.pmsCalcTemplate.service.read.impl.PmsAwardCalcService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@SpringBootTest
public class PmsAwardCalcEngineServiceTest {

    @Autowired
    private PmsAwardCalcEngineService pmsAwardCalcEngineService;

    @Autowired
    private PmsAwardCoefficientConfigReadService pmsAwardCoefficientConfigReadService;

    @Autowired
    private PmsAwardCalcService pmsAwardCalcService;
    private Map<String, Object> context;


    @Resource
    private EmpEmployeeInfoFeignService empEmployeeInfoFeignService;

    @Resource
    private HrmOrgFeignService hrmOrgFeignService;





    @BeforeEach
    void setUp() {
        context = new HashMap<>();
    }

    @Test
    void testCalculatePerformanceScoresWithRealData() {
        CalcMonthlyPmsDto calcMonthlyPmsDto = new CalcMonthlyPmsDto();
        calcMonthlyPmsDto.setCalcMonth("2024-09");
        PmsAwardCalcEngineService.PmsCalcContext pmsCalcContext = pmsAwardCalcEngineService.initContext(690L, calcMonthlyPmsDto);
        PmsAwardCalcEngineService.CalculationResult result = pmsAwardCalcEngineService
                .calculatePerformanceScores(pmsCalcContext);
        // 执行测试
        Map<String, Double> results = result.getResults();
        Map<String, String> errors = result.getErrors();

        errors.forEach((key, value) -> System.out.println("错误: " + key + " : " + value));

        // 验证结果
        assertNotNull(results);
        assertFalse(results.isEmpty(), "计算结果不应为空");

        // 对每个计算结果进行验证
        for (Map.Entry<String, Double> entry : results.entrySet()) {
            String itemCode = entry.getKey();
            Double score = entry.getValue();

            System.out.println("项目: " + itemCode + " 得分: " + score);
            // assertNotNull(score, "项目 " + itemCode + " 的得分不应为null");
            // assertTrue(score >= 0, "项目 " + itemCode + " 的得分应该大于等于0");
            // 可以添加更多的断言来验证特定项目的计算结果是否符合预期
        }
    }

    @Test
    void calcMonthlyPms() throws Exception {
        CalcMonthlyPmsDto dto = new CalcMonthlyPmsDto();
        dto.setCalcMonth("2024-10");
        dto.setIsExport(true);
        dto.setExportFileName("测试导出");
        pmsAwardCalcService.calcMonthlyPms(dto);
    }

    @Test
    void exportMultipleSheets() {
        // pmsAwardCalcExcelExportService.exportMultipleSheets();
    }
}
