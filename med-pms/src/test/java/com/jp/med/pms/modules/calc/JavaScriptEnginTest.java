//package com.jp.med.pms.modules.calc;
//
//import com.yomahub.liteflow.builder.LiteFlowNodeBuilder;
//import com.yomahub.liteflow.builder.el.ELBus;
//import com.yomahub.liteflow.builder.el.LiteFlowChainELBuilder;
//import com.yomahub.liteflow.builder.el.ThenELWrapper;
//import com.yomahub.liteflow.core.FlowExecutor;
//import com.yomahub.liteflow.exception.LiteFlowException;
//import com.yomahub.liteflow.flow.LiteflowResponse;
//import com.yomahub.liteflow.slot.DefaultContext;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.graalvm.polyglot.*;
//import org.graalvm.polyglot.proxy.*;
//
//import javax.annotation.Resource;
//import java.io.IOException;
//import java.io.InputStream;
//import java.math.BigDecimal;
//
//import java.rmi.server.ExportException;
//import java.util.List;
//import java.util.Stack;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
////@SpringBootTest
//public class JavaScriptEnginTest {
//
//    @Resource
//    private FlowExecutor flowExecutor;
//
//
//    @Test
//    public void graalJSEngTest() throws IOException {
//        InputStream testResourceAsStream = this.getClass().getClassLoader().getResourceAsStream("test.js");
//        InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("formula.js");
//        if (resourceAsStream == null || testResourceAsStream == null) {
//            throw new LiteFlowException("test.js or formula.js not found");
//        }
//
//
//        try {
//            // 读取编译后的 JavaScript 文件
//            String script = new String(resourceAsStream.readAllBytes());
//
//            // 创建 GraalVM 上下文
//            try (Context context = Context.newBuilder("js").allowAllAccess(false)
//                    .option("engine.WarnInterpreterOnly", "false")
//                    .build()) {
//                // 在上下文中评估 JavaScript 代码
//                context.eval("js", script);
//
//                // 获取 JavaScript 中的 evalFormula 函数
//                Value evalFormula = context.getBindings("js").getMember("evalFormula");
//
//                // 示例变量处理函数
//                Value variableHandler = context.eval("js", "(text)=>`['${text}']`");
//
//                // 示例公式作用域
//                Value formulaScope = context.eval("js", "({ 'cybls.default': 8000 }");
//
//                // 调用 evalFormula 函数
////                String formula = "${cybls.default}*FIXED(ABS(-1)+AVERAGE(1,2)+MAX(-1,-999.9)+MIN(1,1111111.222211)+SQRT(9)+SUM(1,2,3),10)";
//                String formula = "CHOOSE(MATCH(${cybls.default},{0,7000,7300,7600,7900,8100,8400,8700,9000,9300,9600,9900,10100,10400,10600,10800,11000,100000},1) ,1,1,0.9986,0.9945,0.9877,0.9816,0.9744,0.9659,0.9563,0.951,0.9455,0.9396,0.9336,0.9271,0.9205,0.9135,0.9063,0.9063)";
//                final List<String> ALLOWED_VARIABLES = List.of("cybls.default", "cybls.coefficient");
//                final List<String> ALLOWED_FUNCTIONS = List.of("ABS", "AVERAGE", "MAX", "MIN", "SQRT", "SUM", "FIXED", "CHOOSE", "MATCH");
//
//                if (!ExpressionValidator.isValidExpression(formula, ALLOWED_VARIABLES, ALLOWED_FUNCTIONS)) {
//                    throw new Exception("Invalid formula: " + formula);
//                }
//
//                Value result = evalFormula.execute(formula, variableHandler, formulaScope);
//                if (result.isException()) {
//                    RuntimeException runtimeException = result.throwException();
//                    throw new ExportException("Error evaluating formula: " + formula, runtimeException);
//                }
//                if (result.isNull()) {
//                    throw new ExportException("Error evaluating formula: " + formula);
//                }
//                double aDouble = result.asDouble();
//                BigDecimal bigDecimal = BigDecimal.valueOf(aDouble);
//                // 输出格式化后的值
//                String x = "Result: " + bigDecimal;
//                System.out.println(x);
//            }
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void helloWorld() throws IOException {
//        InputStream testResourceAsStream = this.getClass().getClassLoader().getResourceAsStream("test.js");
//        if (testResourceAsStream == null) {
//            throw new LiteFlowException("test.js not found");
//        }
//        String test = new String(testResourceAsStream.readAllBytes());
//        LiteFlowNodeBuilder.createScriptNode()
//                .setId("test")
//                .setName("test")
//                .setLanguage("js")
//                .setScript(test)
//                .build();
//
//        InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream("formula.js");
//        if (resourceAsStream == null) {
//            throw new LiteFlowException("formula.js not found");
//        }
//        String formula = new String(resourceAsStream.readAllBytes());
//
//        LiteFlowNodeBuilder.createScriptNode()
//                .setId("jsFormula")
//                .setName("jsFormula")
//                .setLanguage("js")
//                .setScript(formula)
//                .build();
//
//        ThenELWrapper el = ELBus.then("test", "js");
//        LiteFlowChainELBuilder.createChain().setChainId("chain1").setEL(
//                el.toEL()
//        ).build();
//        LiteflowResponse response = flowExecutor.execute2Resp("chain1");
//        DefaultContext contextBean = response.getContextBean(DefaultContext.class);
//        Object data = contextBean.getData("ret");
//        if (data != null) {
//            System.out.println(data);
//        }
//    }
//
//    static
//    public class ExpressionValidator {
//
//        // 示例数据：这些数据应该从外部传入
//
//        public static void main(String[] args) {
//            // 示例表达式
//            String expression1 = "${cybls.default}*${cybls.coefficient}+ABS(123)";
//            String expression11 = "${cybls.default} *   ${cybls.coefficient}+ABS(123 + ${cybls.coefficient} )";
//            String expression2 = "${cybls.default} * ${cybls.coefficient} + console.log(123)"; // 错误的表达式
//
////            System.out.println(isValidExpression(expression1)); // 应该返回 true
////            System.out.println(isValidExpression(expression11)); // 应该返回 true
////            System.out.println(isValidExpression(expression2)); // 应该返回 false
//        }
//
//        // 检查表达式的有效性
//        public static boolean isValidExpression(String expression, List<String> ALLOWED_VARIABLES, List<String> ALLOWED_FUNCTIONS) {
//            // 检查变量
//            Pattern variablePattern = Pattern.compile("\\$\\{([^}]+)\\}");
//            Matcher variableMatcher = variablePattern.matcher(expression);
//            while (variableMatcher.find()) {
//                String variable = variableMatcher.group(1);
//                if (!ALLOWED_VARIABLES.contains(variable)) {
//                    return false;
//                }
//            }
//
//            // 检查函数和括号匹配
//            Stack<String> stack = new Stack<>();
//            StringBuilder currentFunc = new StringBuilder();
//            for (char ch : expression.toCharArray()) {
//                if (Character.isLetter(ch)) {
//                    currentFunc.append(ch);
//                } else if (ch == '(') {
//                    if (currentFunc.length() > 0) {
//                        if (!ALLOWED_FUNCTIONS.contains(currentFunc.toString())) {
//                            return false;
//                        }
//                        stack.push(currentFunc.toString());
//                        currentFunc = new StringBuilder();
//                    }
//                    stack.push("(");
//                } else if (ch == ')') {
//                    if (stack.isEmpty() || !stack.pop().equals("(")) {
//                        return false;
//                    }
//                    if (!stack.isEmpty() && !stack.peek().equals("(")) {
//                        stack.pop();
//                    }
//                } else {
//                    currentFunc = new StringBuilder();
//                }
//            }
//
//            if (!stack.isEmpty()) {
//                return false;
//            }
//
//            // 检查基本语法
//            String simplifiedExpression = expression
//                    .replaceAll("\\$\\{[^}]+\\}", "V") // 替换变量
//                    .replaceAll("\\w+\\s*\\([^)]*\\)", "F") // 替换函数调用
//                    .replaceAll("-?\\d+(\\.\\d+)?([eE][-+]?\\d+)?", "N"); // 替换数字（包括小数和科学记数法）
//
//            return simplifiedExpression.matches("^[\\sVFN+\\-*/(),]+$");
//        }
//
//        // 检查表达式是否有匹配的括号
//        private static boolean isCloseBrackets(String expression) {
//            int openBrackets = 0;
//            for (char c : expression.toCharArray()) {
//                if (c == '(') {
//                    openBrackets++;
//                } else if (c == ')') {
//                    openBrackets--;
//                }
//                if (openBrackets < 0) {
//                    return false;
//                }
//            }
//            return openBrackets == 0;
//        }
//    }
//}
