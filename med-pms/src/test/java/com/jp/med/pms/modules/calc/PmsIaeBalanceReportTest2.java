package com.jp.med.pms.modules.calc;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jp.med.pms.modules.PmsIaeBalance.dto.PmsIaeBalanceConfigDto;
import com.jp.med.pms.modules.PmsIaeBalance.dto.PmsIaeBalanceReportDto;
import com.jp.med.pms.modules.PmsIaeBalance.mapper.read.PmsIaeBalanceConfigReadMapper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest
public class PmsIaeBalanceReportTest2 {

    @Resource
    private PmsIaeBalanceConfigReadMapper pmsIaeBalanceConfigReadMapper;

    /**
     * 成科室收支结余报表Excel
     */

    @Test
    public void generateReport() throws IOException {

        LambdaQueryWrapper<PmsIaeBalanceConfigDto> pmsIaeBalanceConfigDtoLambdaQueryWrapper = Wrappers
                .lambdaQuery(PmsIaeBalanceConfigDto.class);
        pmsIaeBalanceConfigDtoLambdaQueryWrapper.eq(PmsIaeBalanceConfigDto::getActive, 1);
        List<PmsIaeBalanceConfigDto> iaeBalanceConfigDtoList = pmsIaeBalanceConfigReadMapper
                .selectList(pmsIaeBalanceConfigDtoLambdaQueryWrapper);

        // 导出信息配置
        Map<String, List<PmsIaeBalanceConfigDto>> classificationMap = iaeBalanceConfigDtoList.stream()
                .collect(Collectors.groupingBy(PmsIaeBalanceConfigDto::getClassification)); // 按分类分组

        // 填充数据
        List<PmsIaeBalanceReportDto> excelData = generateTestData();

        try (Workbook workbook = new HSSFWorkbook()) {
            // 每个sheet的数据size,每页40条数据（动态调整）
            Integer sheetDataSize = 10;

            // 按incomeCostType分组
            Map<Integer, List<ExcelGroupEnum>> typeGroups = Arrays.stream(ExcelGroupEnum.values())
                    .collect(Collectors.groupingBy(ExcelGroupEnum::getIncomeCostType));

            Counter counter = new Counter();
            // 遍历每个incomeCostType类型
            for (Map.Entry<Integer, List<ExcelGroupEnum>> entry : typeGroups.entrySet()) {
                int incomeCostType = entry.getKey();
                List<ExcelGroupEnum> groupEnums = entry.getValue();

                // 获取当前类型的所有表头配置
                List<HeaderConfig> headers = getHeaderConfigsByType(groupEnums, classificationMap, counter);
                if (headers.isEmpty() || headers.size() < 2) {
                    continue;
                }

                // 计算需要的sheet数量
                int sheetCount = (int) Math.ceil((double) excelData.size() / sheetDataSize);

                for (int sheetIndex = 0; sheetIndex < sheetCount; sheetIndex++) {
                    // 根据incomeCostType获取sheet名称前缀
                    String prefix = getSheetPrefix(incomeCostType);
                    String sheetName = prefix + (sheetIndex + 1);
                    Sheet sheet = workbook.createSheet(sheetName);

                    int titleColumnSize = headers.stream()
                            .mapToInt(header -> header.subHeaders.size())
                            .sum();

                    // 创建标题行
                    String title = "2024年X月各科室" + prefix + "情况表";
                    createTitle(workbook, sheet, title, titleColumnSize - 1);

                    // 创建表头
                    createHeaders(workbook, sheet, headers);

                    // 计算当前sheet应该显示的数据范围
                    int startIndex = sheetIndex * sheetDataSize;
                    int endIndex = Math.min(startIndex + sheetDataSize, excelData.size());

                    // 合并两组数据并截取当前sheet需要的部分
                    List<PmsIaeBalanceReportDto> currentSheetData = excelData.subList(startIndex, endIndex);

                    // 填充数据
                    fillData(workbook, sheet, currentSheetData, headers);
                }
            }

            // 保存文件
            try (FileOutputStream fos = new FileOutputStream("科室收支报表.xls")) {
                workbook.write(fos);
            }
        }
    }

    /**
     * 创建标题
     *
     * @param workbook
     * @param sheet
     * @param titleColumnSize 标题合并列数
     */
    private void createTitle(Workbook workbook, Sheet sheet, String sheetName, int titleColumnSize) {
        Row titleRow = sheet.createRow(0);
        // 设置行高为34
        titleRow.setHeight((short) (34 * 20));

        // 创建标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        // 创建标题字体
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 28); // 设置字体大小为28
        titleFont.setFontName("华文新魏"); // 设置字体为华文新魏
        titleStyle.setFont(titleFont);
        // 设置水平居中
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置边框样式
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);

        // 创建标题单元格
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(sheetName);
        titleCell.setCellStyle(titleStyle);

        // 合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, titleColumnSize + 2));
    }

    /**
     * 创建表头
     */
    private void createHeaders(Workbook workbook, Sheet sheet, List<HeaderConfig> headers) {
        // 创建分组行
        Row groupRow = sheet.createRow(1);
        groupRow.setHeight((short) (30 * 20));

        // 创建分组样式
        CellStyle groupStyle = workbook.createCellStyle();
        groupStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        groupStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        // 设置四边框
        groupStyle.setBorderTop(BorderStyle.THIN);
        groupStyle.setBorderBottom(BorderStyle.THIN);
        groupStyle.setBorderLeft(BorderStyle.THIN);
        groupStyle.setBorderRight(BorderStyle.THIN);

        // 设置字体为仿宋_GB2312，12号，加粗
        Font groupFont = workbook.createFont();
        groupFont.setFontName("仿宋_GB2312");
        groupFont.setFontHeightInPoints((short) 12);
        groupFont.setBold(true);
        groupStyle.setFont(groupFont);

        // 创建子表头行
        Row subHeaderRow = sheet.createRow(2);
        subHeaderRow.setHeight((short) (25 * 20));

        // 创建子表头样式
        CellStyle subHeaderStyle = workbook.createCellStyle();
        subHeaderStyle.setAlignment(HorizontalAlignment.CENTER);
        subHeaderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        subHeaderStyle.setWrapText(true);
        // 设置四边框
        subHeaderStyle.setBorderTop(BorderStyle.THIN);
        subHeaderStyle.setBorderBottom(BorderStyle.THIN);
        subHeaderStyle.setBorderLeft(BorderStyle.THIN);
        subHeaderStyle.setBorderRight(BorderStyle.THIN);

        // 设置子表头字体
        Font subHeaderFont = workbook.createFont();
        subHeaderFont.setFontName("宋体");
        subHeaderFont.setFontHeightInPoints((short) 10);
        subHeaderStyle.setFont(subHeaderFont);

        for (HeaderConfig header : headers) {
            // 创建分组单元格
            Cell groupCell = groupRow.createCell(header.startCol);

            // 检查是否包含反斜杠,需要特殊处理
            if (header.group.contains("\\")) {
                // 分割字符串,将文本分为上下两部分
                String[] parts = header.group.split("\\\\");

                // 获取绘图对象,用于绘制对角线
                HSSFPatriarch patriarch = ((HSSFSheet) sheet).createDrawingPatriarch();
                HSSFClientAnchor anchor = new HSSFClientAnchor();

                // 设置对角线的起始和结束坐标
                // 从左上角(1行,startCol列)到右下角(3行,startCol+2列)
                anchor.setCol1(header.startCol); // 起始列
                anchor.setRow1(1); // 起始行(第2行)
                anchor.setCol2(header.startCol + 2); // 结束列(+2表示跨2列)
                anchor.setRow2(3); // 结束行(第3行)

                // 创建并设置对角线
                HSSFSimpleShape line = patriarch.createSimpleShape(anchor);
                line.setShapeType(HSSFSimpleShape.OBJECT_TYPE_LINE); // 设置为直线
                line.setLineStyle(HSSFSimpleShape.LINESTYLE_SOLID); // 实线样式
                line.setLineWidth(HSSFSimpleShape.LINEWIDTH_DEFAULT);

                // 创建第一行单元格样式(右对齐)
                CellStyle topStyle = workbook.createCellStyle();
                topStyle.cloneStyleFrom(groupStyle);
                topStyle.setAlignment(HorizontalAlignment.RIGHT); // 右对齐
                topStyle.setBorderBottom(BorderStyle.NONE); // 去除下边框

                // 创建第二行单元格样式(左对齐)
                CellStyle bottomStyle = workbook.createCellStyle();
                bottomStyle.cloneStyleFrom(groupStyle);
                bottomStyle.setAlignment(HorizontalAlignment.LEFT); // 左对齐
                bottomStyle.setBorderTop(BorderStyle.NONE); // 去除上边框

                // 创建并合并第一行单元格(类别)
                Cell topCell = groupRow.createCell(header.startCol);
                topCell.setCellValue(parts[0]); // 设置为"类别"
                topCell.setCellStyle(topStyle);
                sheet.addMergedRegion(new CellRangeAddress(
                        1, 1, header.startCol, header.startCol + 1));

                // 创建并合并第二行单元格(科室信息)
                Cell bottomCell = subHeaderRow.createCell(header.startCol);
                bottomCell.setCellValue(parts[1]); // 设置为"科室信息"
                bottomCell.setCellStyle(bottomStyle);
                sheet.addMergedRegion(new CellRangeAddress(
                        2, 2, header.startCol, header.startCol + 1));

                // 为合并区域内的其他单元格设置边框样式
                for (int j = header.startCol + 1; j <= header.startCol + 1; j++) {
                    Cell topBorderCell = groupRow.createCell(j);
                    Cell bottomBorderCell = subHeaderRow.createCell(j);
                    topBorderCell.setCellStyle(topStyle);
                    bottomBorderCell.setCellStyle(bottomStyle);
                }
            } else {
                // 普通表头处理
                groupCell.setCellValue(header.group);
                groupCell.setCellStyle(groupStyle);

                // 合并分组单元格
                sheet.addMergedRegion(new CellRangeAddress(
                        1, 1, header.startCol, header.endCol));
            }

            // 为合并区域内的所有单元格设置样式和边框
            for (int j = header.startCol; j <= header.endCol; j++) {
                Cell cell = groupRow.getCell(j);
                if (cell == null) {
                    cell = groupRow.createCell(j);
                }
                // 复制groupStyle的所有属性
                CellStyle cellStyle = workbook.createCellStyle();
                cellStyle.cloneStyleFrom(groupStyle);
                // 确保边框样式被正确复制
                cellStyle.setBorderTop(BorderStyle.THIN);
                cellStyle.setBorderBottom(BorderStyle.THIN);
                cellStyle.setBorderLeft(BorderStyle.THIN);
                cellStyle.setBorderRight(BorderStyle.THIN);
                cell.setCellStyle(cellStyle);
            }

            // 创建子表头并设置边框
            int col = header.startCol;
            for (String subHeader : header.subHeaders) {
                Cell subHeaderCell = subHeaderRow.createCell(col);
                subHeaderCell.setCellValue(subHeader);
                // 为每个子表头单元格创建独立的样式
                CellStyle cellStyle = workbook.createCellStyle();
                cellStyle.cloneStyleFrom(subHeaderStyle);
                // 确保边框样式被正确复制
                cellStyle.setBorderTop(BorderStyle.THIN);
                cellStyle.setBorderBottom(BorderStyle.THIN);
                cellStyle.setBorderLeft(BorderStyle.THIN);
                cellStyle.setBorderRight(BorderStyle.THIN);
                subHeaderCell.setCellStyle(cellStyle);
                sheet.setColumnWidth(col, 12 * 256);
                col++;
            }
        }
    }

    /**
     * 填充数据
     */
    private void fillData(Workbook workbook, Sheet sheet,
            List<PmsIaeBalanceReportDto> data, List<HeaderConfig> headers) {
        // 创建数据单元格样式
        CellStyle dataStyle = workbook.createCellStyle();
        // 设置水平居中
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置字体
        Font dataFont = workbook.createFont();
        dataFont.setFontHeightInPoints((short) 10);
        dataFont.setFontName("宋体");
        dataStyle.setFont(dataFont);

        // 设置四边框
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);

        sheet.setColumnWidth(0, 600);

        // 创建科室特殊样式
        CellStyle deptSpecialStyle = workbook.createCellStyle();
        Font deptSpecialFont = workbook.createFont();
        deptSpecialFont.setFontName("仿宋_GB2312");
        deptSpecialFont.setFontHeightInPoints((short) 12);
        deptSpecialFont.setBold(true);
        deptSpecialStyle.setFont(deptSpecialFont);
        // 设置四边框
        deptSpecialStyle.setBorderTop(BorderStyle.THIN);
        deptSpecialStyle.setBorderBottom(BorderStyle.THIN);
        deptSpecialStyle.setBorderLeft(BorderStyle.THIN);
        deptSpecialStyle.setBorderRight(BorderStyle.THIN);

        // 设置数据单元格左对齐
        dataStyle.setAlignment(HorizontalAlignment.LEFT);

        int rowNum = 3;

        // 创建序号行
        Row headerRow = sheet.createRow(rowNum++);
        headerRow.setHeight((short) (25 * 20)); // 设置行高
        int col = 0;

        // 添加"科室"和"科室代码"列
        Cell deptCell = headerRow.createCell(col++);
        deptCell.setCellValue("科室名称");
        deptCell.setCellStyle(dataStyle);

        sheet.setColumnWidth(col - 1, 30 * 256);

        Cell deptCodeCell = headerRow.createCell(col++);
        deptCodeCell.setCellValue("科室代码");
        deptCodeCell.setCellStyle(dataStyle);

        // 计算并添加序号列[1]-[n]
        int totalColumns = totalColumns = headers.stream()
                .mapToInt(h -> h.subHeaders.size())
                .sum();

        // 创建序号列样式
        CellStyle indexStyle = workbook.createCellStyle();
        indexStyle.cloneStyleFrom(dataStyle);
        indexStyle.setAlignment(HorizontalAlignment.CENTER);

        for (int i = 1; i <= totalColumns; i++) {
            Cell cell = headerRow.createCell(col++);
            cell.setCellValue("[" + i + "]");
            cell.setCellStyle(indexStyle);
            // 设置序号列宽度
            sheet.setColumnWidth(col - 1, 10 * 256);
        }
        // 获取PmsIaeBalanceReportDto的所有字段
        Field[] fields = PmsIaeBalanceReportDto.class.getDeclaredFields();

        // 填充数据行
        for (PmsIaeBalanceReportDto dto : data) {
            Row row = sheet.createRow(rowNum++);
            row.setHeight((short) (25 * 20)); // 设置数据行高
            col = 0;

            // 科室信息
            row.createCell(col++).setCellValue(dto.getDeptName());
            row.createCell(col++).setCellValue(dto.getDeptCode());

            // 分组数据
            for (HeaderConfig header : headers) {
                for (String subHeader : header.subHeaders) {
                    Cell cell = row.createCell(col++);
                    Field field = Arrays.stream(fields)
                            .filter(f -> {
                                Excel annotation = f.getAnnotation(Excel.class);
                                return annotation != null && annotation.name().equals(subHeader);
                            })
                            .findFirst()
                            .orElse(null);
                    if (field != null) {
                        field.setAccessible(true);
                        try {
                            Object value = field.get(dto);
                            if (value instanceof BigDecimal) {
                                cell.setCellValue(((BigDecimal) value).doubleValue());
                            } else if (value instanceof Double) {
                                cell.setCellValue((Double) value);
                            } else if (value instanceof Integer) {
                                cell.setCellValue((Integer) value);
                            } else if (value instanceof String) {
                                cell.setCellValue((String) value);
                            } else if (value instanceof Date) {
                                cell.setCellValue((Date) value);
                            } else if (value == null) {
                                cell.setCellValue("");
                            } else {
                                cell.setCellValue(value.toString());
                            }
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                            cell.setCellValue("");
                        }
                    }
                }
            }

            // 设置样式
            for (int i = 0; i < col; i++) {
                Cell cell = row.getCell(i);
                if (i == 0 || i == 1) {
                    cell.setCellStyle(deptSpecialStyle);
                } else {
                    cell.setCellStyle(dataStyle);
                }
            }
        }
    }

    /**
     * 生成测试数据
     * 根据PmsIaeBalanceReportDto的字段生成模拟数据
     *
     * @return 科室收支报表数据列表
     */
    private List<PmsIaeBalanceReportDto> generateTestData() {
        List<PmsIaeBalanceReportDto> dataList = new ArrayList<>();

        // 添加临床服务科室数据 todo 单独处理
        // PmsIaeBalanceReportDto clinicalService = generateDeptData("临床服务", 1);
        // dataList.add(clinicalService);

        String[] departments = {
                "肾内、内分泌科",
                "呼吸与免疫医学科",
                "普外一科", "普外二科", "骨科", "神经外科", "泌尿外科",
                "胸心外科", "整形外科", "烧伤科", "眼科", "耳鼻喉科",
                "口腔科", "妇产科", "儿科", "新生儿科", "中医科",
                "针灸科", "康复科", "皮肤科", "精神科", "肿瘤科",
                "放射科", "检验科", "病理科", "药剂科", "手术科",
                "麻醉科", "急诊科", "重症医学科", "感染科", "核医学科"
        };

        // 为每个科室生成测试数据
        for (int i = 0; i < departments.length; i++) {
            PmsIaeBalanceReportDto deptData = generateDeptData(departments[i], i + 4);
            dataList.add(deptData);
        }
        return dataList;
    }

    /**
     * 生成指定范围内的随机BigDecimal
     *
     * @param min 最小值
     * @param max 最大值
     * @return 随机BigDecimal
     */
    private BigDecimal randomDecimal(double min, double max) {
        double random = min + Math.random() * (max - min);
        return new BigDecimal(String.format("%.2f", random));
    }

    /**
     * 生成单个科室的模拟数据
     *
     * @param deptName 科室名称
     * @param order    排序号
     * @return 科室数据对象
     */
    private PmsIaeBalanceReportDto generateDeptData(String deptName, int order) {
        PmsIaeBalanceReportDto dept = new PmsIaeBalanceReportDto();
        // 基本信息设置
        dept.setDeptName(deptName);
        dept.setDeptCode("DEPT" + String.format("%03d", order));
        dept.setStatMonth("2024-01");
        dept.setOrderInteger(order);

        // 一、医疗服务收入
        dept.setDiagnosticIncome(randomDecimal(10000, 50000)); // 诊查收入
        dept.setBedIncome(randomDecimal(20000, 80000)); // 床位收入
        dept.setTreatmentIncome(randomDecimal(30000, 100000)); // 治疗收入
        dept.setRehabilitationIncome(randomDecimal(15000, 60000)); // 康复收入
        dept.setEcgIncome(randomDecimal(5000, 20000)); // 心电图收入
        dept.setSurgeryIncome(randomDecimal(50000, 200000)); // 手术收入
        dept.setIcuIncome(randomDecimal(40000, 150000)); // ICU收入
        dept.setTransferEscortFee(randomDecimal(2000, 10000)); // 转诊护送费
        dept.setPhysicalExamIncome(randomDecimal(10000, 40000)); // 体检收入
        dept.setOtherOutpatientIncome(randomDecimal(5000, 20000)); // 其他门诊收入

        // 计算医疗服务收入合计
        BigDecimal medicalServiceTotal = dept.getDiagnosticIncome()
                .add(dept.getBedIncome())
                .add(dept.getTreatmentIncome())
                .add(dept.getRehabilitationIncome())
                .add(dept.getEcgIncome())
                .add(dept.getSurgeryIncome())
                .add(dept.getIcuIncome())
                .add(dept.getTransferEscortFee())
                .add(dept.getPhysicalExamIncome())
                .add(dept.getOtherOutpatientIncome());
        dept.setMedicalServiceIncome(medicalServiceTotal);

        // 二、药品收入
        dept.setWesternMedicineIncome(randomDecimal(100000, 300000)); // 西药收入
        dept.setPatentMedicineIncome(randomDecimal(50000, 150000)); // 中成药收入
        dept.setChineseHerbalIncome(randomDecimal(30000, 100000)); // 中草药收入
        dept.setDrugIncome(dept.getWesternMedicineIncome()
                .add(dept.getPatentMedicineIncome())
                .add(dept.getChineseHerbalIncome()));

        // 三、变动成本/科室费用
        dept.setStaffCount(BigDecimal.valueOf((int) (10 + Math.random() * 40))); // 人员数量
        dept.setBaseSalary(randomDecimal(50000, 150000)); // 基本工资
        dept.setAllowance(randomDecimal(10000, 30000)); // 津贴
        dept.setTempSalary(randomDecimal(5000, 20000)); // 临时工工资
        dept.setOvertimePay(randomDecimal(3000, 10000)); // 加班费
        dept.setNightMealAllowance(randomDecimal(2000, 8000)); // 夜班餐补
        dept.setTravelExpense(randomDecimal(1000, 5000)); // 差旅费
        dept.setUtilityExpense(randomDecimal(5000, 15000)); // 水电费
        dept.setPdaExpense(randomDecimal(1000, 3000)); // PDA费用
        // 四、总务材料费用
        dept.setCottonProducts(randomDecimal(2000, 8000)); // 设置棉织品费用
        dept.setTelephoneExpense(randomDecimal(1000, 4000)); // 设置电话费用
        dept.setOtherExpense(randomDecimal(3000, 10000)); // 设置其他费用
        dept.setMedicalWasteDisposal(randomDecimal(2000, 7000)); // 设置医废处置费用
        dept.setCanteenExpense(randomDecimal(5000, 15000)); // 设置食堂费用
        // 计算总务材料费用合计
        dept.setGeneralMaterialCost(dept.getCottonProducts()
                .add(dept.getTelephoneExpense())
                .add(dept.getOtherExpense())
                .add(dept.getMedicalWasteDisposal())
                .add(dept.getCanteenExpense()));

        // 五、医用材料费用
        dept.setMedicalSanitaryMaterial(randomDecimal(3000, 9000)); // 设置手消液费用
        dept.setSupplyRoomMaterial(randomDecimal(5000, 15000)); // 设置供应室材料费用
        dept.setPatientDebtManagement(randomDecimal(2000, 8000)); // 设置出院病人欠费管理费用
        dept.setDevelopmentFund(randomDecimal(10000, 30000)); // 设置提取发展基金费用
        dept.setMedicalSanitaryMaterial(randomDecimal(4000, 12000)); // 设置医用卫生材料费用
        dept.setRequisitionedDrugs(randomDecimal(20000, 60000)); // 设置领用药品费用
        // 计算医用材料费用合计
        dept.setMedicalMaterialCost(dept.getMedicalSanitaryMaterial()
                .add(dept.getSupplyRoomMaterial())
                .add(dept.getPatientDebtManagement())
                .add(dept.getDevelopmentFund())
                .add(dept.getMedicalSanitaryMaterial())
                .add(dept.getRequisitionedDrugs()));

        // 六、固定资产费用
        dept.setEquipmentMaintenance(randomDecimal(5000, 20000)); // 设置设备维修费用
        dept.setBuildingDepreciation(randomDecimal(10000, 40000)); // 设置房屋折旧费用
        dept.setEquipmentDepreciation(randomDecimal(15000, 50000)); // 设置设备折旧费用
        // 计算固定资产费用合计
        dept.setFixedAssetsCost(dept.getEquipmentMaintenance()
                .add(dept.getBuildingDepreciation())
                .add(dept.getEquipmentDepreciation())); // 固定资产费用合计

        // 计算总收入和支出
        dept.setTotalIncome(medicalServiceTotal.add(dept.getDrugIncome())); // 总收入

        // 计算总支出(包含所有成本费用)
        BigDecimal totalExpense = calculateTotalExpense(dept)
                .add(dept.getGeneralMaterialCost())
                .add(dept.getMedicalMaterialCost())
                .add(dept.getFixedAssetsCost());
        dept.setTotalExpense(totalExpense);

        // 计算结余金额
        dept.setBalanceAmount(dept.getTotalIncome().subtract(dept.getTotalExpense()));

        return dept;
    }

    /**
     * 计算总支出
     *
     * @param dept 科室数据
     * @return 总支出金额
     */
    private BigDecimal calculateTotalExpense(PmsIaeBalanceReportDto dept) {
        return dept.getBaseSalary()
                .add(dept.getAllowance())
                .add(dept.getTempSalary())
                .add(dept.getOvertimePay())
                .add(dept.getNightMealAllowance())
                .add(dept.getTravelExpense())
                .add(dept.getUtilityExpense())
                .add(dept.getPdaExpense());
    }

    /**
     * Excel分组枚举
     */
    @Getter
    @AllArgsConstructor
    public enum ExcelGroupEnum {
        // 医疗服务收入分组
        MEDICAL_SERVICE(true, "医疗服务收入", 1),
        // 药品收入分组
        DRUG_INCOME(true, "药品收入", 1),
        // 收入总计
        INCOME_TOTAL(false, "总计", 1),
        // 科室费用分组
        DEPT_VARIABLE_COST(true, "变动成本/科室费用", 2),
        // 总务材料分组
        GENERAL_MATERIAL_COST(true, "变动成本/总务材料", 2),
        // 医用材料费分组
        MEDICAL_MATERIAL_COST(true, "变动成本/医用材料费", 3),
        // 固定资产折旧分组
        FIXED_ASSETS_COST(true, "固定资产折旧/设备维修费", 3),
        // 支出总计
        EXPENSE_TOTAL(false, "业务支出总计", 3),
        // 基本数字分析分组
        BASIC_NUMERICAL_ANALYSIS(true, "基本数字分析", 3),
        // 其他分组
        OTHER(true, "其他", 5);

        // 是否添加排序前缀
        private final boolean hasOrderPrefix;
        // 分组名称
        private final String groupName;
        // 收入成本类型
        private final int incomeCostType;

        /**
         * 根据分类获取分组枚举
         *
         * @param classification 分类名称
         * @return 对应的分组枚举
         */
        public static ExcelGroupEnum getByClassification(String classification) {
            switch (classification) {
                case "医疗服务收入":
                    return MEDICAL_SERVICE;
                case "药品收入":
                    return DRUG_INCOME;
                case "总计":
                case "变动成本/科室费用":
                    return DEPT_VARIABLE_COST;
                case "变动成本/总务材料":
                    return GENERAL_MATERIAL_COST;
                case "变动成本/医用材料费":
                    return MEDICAL_MATERIAL_COST;
                case "固定资产折旧/设备维修费":
                    return FIXED_ASSETS_COST;
                case "业务支出总计":
                    return EXPENSE_TOTAL;
                case "基本数字分析":
                    return BASIC_NUMERICAL_ANALYSIS;
                default:
                    return OTHER;
            }
        }
    }

    /**
     * 表头配置类
     */
    private static class HeaderConfig {
        String group;
        List<String> subHeaders;
        int startCol;
        int endCol;

        HeaderConfig(String group, List<String> subHeaders, int startCol) {
            this.group = group;
            this.subHeaders = subHeaders;
            this.startCol = startCol;
            this.endCol = startCol + subHeaders.size() - 1;
        }
    }

    // 新增方法：根据类型获取对应的组配置
    private List<HeaderConfig> getHeaderConfigsByType(List<ExcelGroupEnum> groupEnums,
            Map<String, List<PmsIaeBalanceConfigDto>> classificationMap,
            Counter counter) {
        List<HeaderConfig> configs = new ArrayList<>();
        int col = 0;

        // 基本信息
        configs.add(new HeaderConfig("类别\\科室信息", Arrays.asList(), col));
        col += 2;

        for (ExcelGroupEnum groupEnum : groupEnums) {
            List<String> headers = classificationMap
                    .getOrDefault(groupEnum.getGroupName(), new ArrayList<>())
                    .stream()
                    .map(PmsIaeBalanceConfigDto::getItemName)
                    .collect(Collectors.toList());

            if (headers.isEmpty()) {
                continue;
            }
            String groupName = getGroupNameWithPrefix(groupEnum, counter);

            configs.add(new HeaderConfig(groupName, headers, col));
            col += headers.size();
        }

        return configs;
    }

    // 新增方法：获取sheet名称前缀
    private String getSheetPrefix(int incomeCostType) {
        switch (incomeCostType) {
            case 1:
                return "收入";
            case 2:
                return "成本";
            case 3:
                return "费用";
            default:
                return "其他";
        }
    }

    // 计数器类
    private static class Counter {
        private int value;

        public int getAndIncrement() {
            return value++;
        }
    }

    // 新增方法：获取带序号前缀的分组名称
    private String getGroupNameWithPrefix(ExcelGroupEnum groupEnum, Counter counter) {
        // 如果不需要添加序号前缀，直接返回分组名称
        if (!groupEnum.isHasOrderPrefix()) {
            return groupEnum.getGroupName();
        }

        // 定义中文数字数组
        String[] chineseNumbers = {"一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};

        // 使用计数器获取序号并递增
        int currentOrder = counter.getAndIncrement();

        // 如果序号超出数组范围，返回原始名称
        if (currentOrder >= chineseNumbers.length) {
            return groupEnum.getGroupName();
        }

        // 返回带序号的分组名称
        return chineseNumbers[currentOrder] + "、" + groupEnum.getGroupName();
    }

}
