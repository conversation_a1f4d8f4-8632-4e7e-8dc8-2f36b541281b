//package com.jp.med.pms.modules.wnbb;
//
//import cn.hutool.http.HttpRequest;
//import cn.hutool.http.HttpResponse;
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import com.jp.med.pms.modules.PmsIaeBalance.dto.PmsIaeBalanceConfigDto;
//import jdk.nashorn.api.scripting.ScriptObjectMirror;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.codec.binary.Base64;
//import org.apache.poi.ss.usermodel.Cell;
//import org.apache.poi.ss.usermodel.DateUtil;
//import org.apache.poi.ss.usermodel.Row;
//import org.apache.poi.xssf.usermodel.XSSFSheet;
//import org.apache.poi.xssf.usermodel.XSSFWorkbook;
//import org.jsoup.Jsoup;
//import org.jsoup.nodes.Document;
//
//import javax.crypto.Cipher;
//import javax.crypto.spec.SecretKeySpec;
//import javax.script.ScriptEngine;
//import javax.script.ScriptEngineManager;
//import javax.script.ScriptException;
//import java.io.ByteArrayInputStream;
//import java.io.FileWriter;
//import java.io.IOException;
//import java.io.InputStream;
//import java.net.URLEncoder;
//import java.nio.charset.StandardCharsets;
//import java.util.*;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//import java.util.stream.Collectors;
//
//@Slf4j
//public class test01 {
//
//    private static final String testReportName = "门诊住院医疗收入统计";
//    // private static final String testReportName = "全院高值耗材消耗排行统计";
//    private static final String baseUrl = "http://hrp-mid:9527/winningreport";
//    private static final String baseUrl0 = "http://hrp-mid:9527";
//
//    private static final String fakerBaseUrl = "http://***********:8088/winningreport";
//    private static final String fakerBaseUrl0 = "http://***********:8088";
//    private static final String JSESSIONID = "JSESSIONID=8DD8F7FE81715B32AD26D09F8BA9C3DC;";
//    // private static final String JSESSIONID = "";
//    private static final String USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36";
//    private static final String ExportUrl = "#{baseUrl}/ReportServer?op=export&sessionID=#{sessionID}&format=excel&extype=simple";
//    private static final String SECRET_KEY = "winningreportapp";
//
//    public static void main(String[] args) throws Exception {
//        String jsessionId = JSESSIONID;
//        // 如果JSESSIONID为空才进行登录
//        if (jsessionId == null || jsessionId.isEmpty()) {
//            // 创建登录参数
//            HashMap<String, Object> loginParams = new HashMap<>();
//            loginParams.put("id", "0338");
//            loginParams.put("password", aesEncrypt("*123123hj", SECRET_KEY));
//            loginParams.put("moduleKey", "wnbb");
//
//            // 发送登录请求
//            HttpResponse response = HttpRequest.post(baseUrl + "/login")
//                    .form(loginParams)
//                    .execute();
//
//            // 从登录响应中获取JSESSIONID
//            List<String> cookies = response.headers().get("Set-Cookie");
//            if (cookies != null) {
//                for (String cookie : cookies) {
//                    if (cookie.startsWith("JSESSIONID")) {
//                        jsessionId = cookie.split(";")[0];
//                        System.out.println("获取到 JSESSIONID: " + jsessionId);
//                        break;
//                    }
//                }
//            }
//
//            String body = response.body();
//            System.out.println("登录响应: " + body);
//
//            JSONObject result = JSONUtil.parseObj(body);
//            if (result.getInt("code") == 0) {
//                System.out.println("登录成功!");
//            } else {
//                System.out.println("登录失败: " + result.getStr("msg"));
//            }
//        }
//
//        // 登录成功后获取菜单树
//        HttpResponse menuResponse = HttpRequest.get(baseUrl + "/menu/loadTree")
//                .header("Cookie", jsessionId)
//                .form("moduleKey", "wnbb")
//                .form("_", System.currentTimeMillis())
//                .header("User-Agent", USER_AGENT)
//
//                .header("Referer", fakerBaseUrl + "/home")
//                .execute();
//
//        String menuBody = menuResponse.body();
//        System.out.println("菜单树响应: " + menuBody);
//
//        // 使用Hutool序列化JSON
//        JSONObject menuJson = JSONUtil.parseObj(menuBody);
//        System.out.println("序列化后的菜单树: " + menuJson.toStringPretty());
//
//        MenuTree menuTree = JSONUtil.toBean(menuJson, MenuTree.class);
//
//        // map化
//        HashMap<String, String> reportPathMap = new HashMap<>();
//        // 递归处理子节点
//        for (ChildrenNode child : menuTree.getChildrenNode()) {
//            processMenuChildrenNode(child, reportPathMap);
//        }
//
//        // 用于测试输出
//        reportPathMap.forEach((name, path) -> {
//            System.out.println("名称: " + name + ", 路径: " + path);
//        });
//
//        // 动态访问节点
//        String reportletPath = reportPathMap.get(testReportName);
//        String reportUrl = String.format(baseUrl + "/ReportServer?reportlet=%s&uniqueCode=",
//                reportletPath);
//        System.out.println("报表详情URL: " + reportUrl);
//
//        // 访问报表页面HTML
//        HttpResponse reportHtmlResponse = HttpRequest.get(reportUrl)
//                .header("Cookie", jsessionId)
//                .header("User-Agent", USER_AGENT)
//                .header("Referer", fakerBaseUrl + "/home")
//                .execute();
//        String reportHtml = reportHtmlResponse.body();
//        System.out.println("报表HTML响应: " + baseUrl + "/ReportServer\n" + reportHtml);
//
//        // 提取 this.loadReportPane({xxx}) 中的 xxx
//        String loadReportPaneStr = extractBigObject(reportHtml);
//        System.out.println("报表: loadReportPaneStr" + loadReportPaneStr);
//
//        LoadReportPane loadReportPane = transLoadReportPaneStr(loadReportPaneStr);
//        System.out.println("报表loadReportPane: " + loadReportPane);
//
//        Param loadReportPaneParam = null;
//        if (loadReportPane != null) {
//            loadReportPaneParam = loadReportPane.getParam();
//        }
//        if (loadReportPaneParam == null) {
//            System.out.println("loadReportPaneParam is null");
//            return;
//        }
//
//        String[] loadReportPaneItemsIndex = loadReportPaneParam.getHtml().getItemsIndex();
//        System.out.println("loadReportPaneItemsIndex: " + Arrays.toString(loadReportPaneItemsIndex));
//        HashMap<String, Items> loadReportPaneItemsMap = loadReportPaneParam.getHtml().getItems();
//        for (Map.Entry<String, Items> entry : loadReportPaneItemsMap.entrySet()) {
//            String itemsIndex = entry.getKey();
//            Items items = entry.getValue();
//            System.out.println();
//            System.out.println();
//            System.out.println(itemsIndex + ":" + JSONUtil.toJsonStr(items));
//            System.out.println();
//            System.out.println();
//        }
//
//        // 正则匹配 sessionID=xxx 放在数组中
//        Pattern pattern = Pattern.compile("sessionID=(\\w+)");
//        Matcher matcher = pattern.matcher(reportHtml);
//        List<String> sessionIdsList = new ArrayList<>();
//        while (matcher.find()) {
//            sessionIdsList.add(matcher.group(1));
//        }
//        String[] sessionIds = sessionIdsList.toArray(new String[0]);
//        System.out.println("sessionIds: " + Arrays.toString(sessionIds));
//
//        // 1.发送预检查请求
//        // http://hrp-mid:9527/winningreport/bgjl/check
//        HttpResponse checkResponse = HttpRequest.post(baseUrl + "/bgjl/check")
//                .header("Cookie", jsessionId)
//                .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
//                .header("X-Requested-With", "XMLHttpRequest")
//                .header("User-Agent", USER_AGENT)
//                .form("path", reportletPath)
//                .header("Referer", fakerBaseUrl + "/home")
//                .execute();
//        System.out.println("预检查响应: " + checkResponse.body());
//
//        // 2.发送查询报表请求
//        Map<String, Object> parametersMap = new HashMap<>();
//        // parametersMap.put("LABELJSRQ", "[7ed3][675f][65e5][671f]");
//        // parametersMap.put("LABELKSRQ", "[5f00][59cb][65e5][671f]");
//        // parametersMap.put("LABELTJKJ", "[7ef4][5ea6][9009][62e9]");
//        parametersMap.put("TJKJ", "1");
//        // parametersMap.put("LABELKSJB", "[79d1][5ba4][7c7b][578b]");
//        parametersMap.put("KSJB", "3");
//        // parametersMap.put("LABELXMLX", "[9879][76ee][7c7b][578b]");
//        parametersMap.put("XMLX", "1");
//        // parametersMap.put("LABELTJFS", "[7edf][8ba1][65b9][5f0f]");
//        parametersMap.put("TJFS", "0");
//        // parametersMap.put("LABELTJFW", "[8303][56f4][9009][62e9]");
//        parametersMap.put("TJFW", "1");
//        // parametersMap.put("LABELXMDMJ", "[9879][76ee][7b5b][9009]");
//        parametersMap.put("XMDMJ", "[5b][5d]");
//        // parametersMap.put("LABELKSDMJ", "[9009][62e9][79d1][5ba4]");
//        parametersMap.put("KSDMJ", "[5b][5d]");
//        // parametersMap.put("LABELSXNR", "[7b5b][9009][5185][5bb9]");
//        parametersMap.put("SXNR", "[5b][5d]");
//        // parametersMap.put("LABELSXTJ", "[4e8c][6b21][7b5b][9009]");
//        parametersMap.put("SXTJ", "");
//        // parametersMap.put("LABELTJKJ2", "[7ef4][5ea6][6269][5c55]");
//        parametersMap.put("TJKJ2", "-1");
//        parametersMap.put("YYDM", "01");
//        // parametersMap.put("LABELYYDM", "[9662][533a]");
//        parametersMap.put("JSRQ", "2024-11-15");
//        parametersMap.put("KSRQ", "2024-11-01");
//        // String parameters = JSONUtil.toJsonStr(parametersMap);
//        String parameters = "{\"LABELJSRQ\":\"[7ed3][675f][65e5][671f]\",\"LABELKSRQ\":\"[5f00][59cb][65e5][671f]\",\"LABELTJKJ\":\"[7ef4][5ea6][9009][62e9]\",\"TJKJ\":\"1\",\"LABELKSJB\":\"[79d1][5ba4][7c7b][578b]\",\"KSJB\":\"3\",\"LABELXMLX\":\"[9879][76ee][7c7b][578b]\",\"XMLX\":\"1\",\"LABELTJFS\":\"[7edf][8ba1][65b9][5f0f]\",\"TJFS\":\"0\",\"LABELTJFW\":\"[8303][56f4][9009][62e9]\",\"TJFW\":\"1\",\"LABELXMDMJ\":\"[9879][76ee][7b5b][9009]\",\"XMDMJ\":[5b][5d],\"LABELKSDMJ\":\"[9009][62e9][79d1][5ba4]\",\"KSDMJ\":[5b][5d],\"LABELSXNR\":\"[7b5b][9009][5185][5bb9]\",\"SXNR\":[5b][5d],\"LABELSXTJ\":\"[4e8c][6b21][7b5b][9009]\",\"SXTJ\":\"\",\"LABELTJKJ2\":\"[7ef4][5ea6][6269][5c55]\",\"TJKJ2\":\"-1\",\"YYDM\":\"01\",\"LABELYYDM\":\"[9662][533a]\",\"JSRQ\":\"2024-11-15\",\"KSRQ\":\"2024-11-01\"}";
//        String queryUrl = baseUrl + "/ReportServer" +
//                "?op=fr_dialog" +
//                "&cmd=parameters_d" +
//                "&sessionID=" + sessionIds[0];
//
//        String tabList = "1,2,3,4,5,6,8,12,13,16,15,17,19,23,24,26,27,28,29,31,32,33,34,35,37,38,41,42,43,44,45,47,48,50,49,53,52,54,55,56,58,60,328,330,336,339,324,325,326,322,321,320,319,317,306,308,311,110,109,148,84";
//        String queryCookie = jsessionId
//                + " " + "tab_list=" + URLEncoder.encode(tabList, StandardCharsets.UTF_8);
//        HttpResponse queryResponse = HttpRequest.post(queryUrl)
//                .header("Accept", "*/*")
//                .header("Accept-Language", "en")
//                .header("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8")
//                .header("Cookie", queryCookie)
//                .header("Origin", baseUrl0)
//                .header("Pragma", "no-cache")
//                .header("Proxy-Connection", "keep-alive")
//                // .header("Referer",
//                // fakerBaseUrl0 + URLEncoder.encode("/ReportServer?reportlet=",
//                // StandardCharsets.UTF_8)
//                // + URLEncoder.encode(reportletPath, StandardCharsets.UTF_8)
//                // + "&uniqueCode=")
//                .header("Referer",
//                        "http://hrp-mid:9527/winningreport/ReportServer?reportlet=wnbb%5Ccw%5C%E5%8C%BB%E9%99%A2%E6%94%B6%E5%85%A5%E7%BB%9F%E8%AE%A1.cpt&uniqueCode=")
//                .header("User-Agent", USER_AGENT)
//                .header("X-Requested-With", "XMLHttpRequest")
//                .form("__parameters__", parameters)
//                .execute();
//
//        System.out.println("查询报表响应: " + queryUrl + "\n" + queryResponse.toString());
//        // 获取报表详情
//        // saveCsv(sessionIds, jsessionId);
//        // 获取Excel文件
//
//        String excelUrl = ExportUrl.replace("#{sessionID}", sessionIds[0]).replace("#{baseUrl}", baseUrl);
//        System.out.println("报表ExcelURL: " + excelUrl);
//        // 下载Excel文件
//        HttpResponse excelResponse = HttpRequest.get(excelUrl)
//                .header("Cookie", jsessionId)
//                .header("User-Agent", USER_AGENT)
//                .header("Referer", fakerBaseUrl + "/home")
//                .execute();
//
//        // 读取Excel文件
//        PmsIaeBalanceConfigDto pmsIaeBalanceConfigDto = new PmsIaeBalanceConfigDto();
//        pmsIaeBalanceConfigDto.setDeptCodeColumn(1); // 科室代码列
//        pmsIaeBalanceConfigDto.setHeadStartRow(3); // 表头开始行
//        pmsIaeBalanceConfigDto.setTitleRow(1); // 标题行
//        // 使用POI读取Excel
//        Map<String, Map<String, String>> resultMap = new HashMap<>();
//        try (InputStream is = new ByteArrayInputStream(excelResponse.bodyBytes())) {
//            XSSFWorkbook workbook = new XSSFWorkbook(is);
//            XSSFSheet sheet = workbook.getSheetAt(0);
//
//            // 读取表头
//            Row headerRow = sheet.getRow(pmsIaeBalanceConfigDto.getHeadStartRow() - 1);
//            List<String> headers = new ArrayList<>();
//            for (Cell cell : headerRow) {
//                headers.add(getCellValueAsString(cell));
//            }
//
//            // 读取数据行
//            for (int i = pmsIaeBalanceConfigDto.getHeadStartRow(); i <= sheet.getLastRowNum(); i++) {
//                Row row = sheet.getRow(i);
//                if (row == null)
//                    continue;
//
//                // 获取科室代码
//                Cell deptCodeCell = row.getCell(pmsIaeBalanceConfigDto.getDeptCodeColumn() - 1);
//                if (deptCodeCell == null)
//                    continue;
//                String deptCode = getCellValueAsString(deptCodeCell);
//
//                // 存储该行数据
//                Map<String, String> rowData = new HashMap<>();
//                for (int j = 0; j < headers.size(); j++) {
//                    Cell cell = row.getCell(j);
//                    String value = cell == null ? "" : getCellValueAsString(cell);
//                    rowData.put(headers.get(j), value);
//                }
//
//                if (deptCode.equals("--")) {
//                    deptCode = "合计";
//                }
//                resultMap.put(deptCode, rowData);
//            }
//
//            workbook.close();
//        } catch (IOException e) {
//            throw new RuntimeException("读取Excel文件失败", e);
//        }
//
//    }
//
//    /**
//     * 获取单元格值
//     */
//    private static String getCellValueAsString(Cell cell) {
//        if (cell == null) {
//            return "";
//        }
//        switch (cell.getCellType()) {
//            case STRING:
//                return cell.getStringCellValue();
//            case NUMERIC:
//                if (DateUtil.isCellDateFormatted(cell)) {
//                    return cell.getLocalDateTimeCellValue().toString();
//                }
//                return String.valueOf(cell.getNumericCellValue());
//            case BOOLEAN:
//                return String.valueOf(cell.getBooleanCellValue());
//            case FORMULA:
//                try {
//                    return String.valueOf(cell.getNumericCellValue());
//                } catch (IllegalStateException e) {
//                    return cell.getStringCellValue();
//                }
//            case BLANK:
//            default:
//                return "";
//        }
//    }
//
//    private static void saveCsv(String[] sessionIds, String jsessionId) {
//        String reportDetailUrl = baseUrl + "/ReportServer" +
//                "?_=" + System.currentTimeMillis() +
//                "&__boxModel__=true" +
//                "&op=page_content" +
//                "&sessionID=" + sessionIds[0] +
//                "&pn=1" +
//                "&__webpage__=true" +
//                "&_paperWidth=816" +
//                "&_paperHeight=663" +
//                "&__fit__=false";
//
//        // 创建CSV文件
//        String csvFilePath = "report_data.csv";
//        try (FileWriter csvWriter = new FileWriter(csvFilePath)) {
//            // 获取第一页数据和总页数
//            HttpResponse reportResponse = getReportPage(reportDetailUrl, jsessionId);
//            String reportBody = reportResponse.body();
//            Map.Entry<Integer, Integer> pageNumbers = extractPageNumbers(reportBody);
//            int totalPages = pageNumbers.getValue();
//
//            System.out.println("开始获取报表数据,总页数: " + totalPages);
//
//            // 处理第一页数据
//            ExtractedData firstPageData = extractReportData(reportBody);
//            writeCSVHeader(csvWriter, firstPageData);
//            writeCSVData(csvWriter, firstPageData);
//
//            // 处理剩余页面
//            for (int pageNum = 2; pageNum <= totalPages; pageNum++) {
//                String pageUrl = reportDetailUrl.replace("pn=1", "pn=" + pageNum);
//                HttpResponse pageResponse = getReportPage(pageUrl, jsessionId);
//                ExtractedData pageData = extractReportData(pageResponse.body());
//                writeCSVData(csvWriter, pageData);
//
//                System.out.println("已处理第 " + pageNum + " 页数据");
//                Thread.sleep(1000); // 避免请求过快
//            }
//
//            System.out.println("数据已保存到: " + csvFilePath);
//        } catch (Exception e) {
//            System.err.println("处理报表数据失败: " + e.getMessage());
//            throw new RuntimeException("处理报表数据失败", e);
//        }
//    }
//
//    /**
//     * 获取报表页面数据
//     */
//    private static HttpResponse getReportPage(String url, String jsessionId) {
//        log.info("获取报表页面数据: {}", url);
//        return HttpRequest.get(url)
//                .header("Cookie", jsessionId)
//                .header("User-Agent", USER_AGENT)
//                .header("Referer", fakerBaseUrl + "/home")
//                .execute();
//    }
//
//    /**
//     * 写入CSV文件头
//     */
//    private static void writeCSVHeader(FileWriter writer, ExtractedData data) throws IOException {
//        // 写入科室表头
//        List<String> deptHeaders = data.getDepartment().getHeader();
//        for (int i = 0; i < deptHeaders.size(); i++) {
//            writer.append(deptHeaders.get(i));
//            if (i < deptHeaders.size() - 1) {
//                writer.append(",");
//            }
//        }
//        // writer.append("\n");
//
//        // 写入项目表头
//        List<String> projectHeaders = data.getProject().getHeader();
//        for (int i = 0; i < projectHeaders.size(); i++) {
//            writer.append(projectHeaders.get(i));
//            if (i < projectHeaders.size() - 1) {
//                writer.append(",");
//            }
//        }
//        writer.append("\n");
//    }
//
//    /**
//     * 写入CSV数据
//     */
//    private static void writeCSVData(FileWriter writer, ExtractedData data) throws IOException {
//        Map<String, List<String>> projectData = data.getProject().getData();
//        List<Map.Entry<String, List<String>>> sortedEntries = new ArrayList<>(projectData.entrySet());
//
//        // 自定义排序规则
//        sortedEntries.sort((e1, e2) -> {
//            String key1 = e1.getKey();
//            String key2 = e2.getKey();
//
//            // 空值和"合计"放到最后
//            if (key1.isEmpty() || "合计".equals(key1))
//                return 1;
//            if (key2.isEmpty() || "合计".equals(key2))
//                return -1;
//
//            return key1.compareTo(key2);
//        });
//
//        // 遍历排序后的科室数据
//        for (Map.Entry<String, List<String>> entry : sortedEntries) {
//            String deptName = entry.getKey();
//            List<String> values = entry.getValue();
//
//            // 写入科室名称
//            writer.append(escapeCsvField(deptName)).append(",");
//
//            // 写入该科室的所有数据
//            for (int i = 0; i < values.size(); i++) {
//                writer.append(escapeCsvField(values.get(i)));
//                if (i < values.size() - 1) {
//                    writer.append(",");
//                }
//            }
//            writer.append("\n");
//        }
//    }
//
//    /**
//     * CSV字段转义处理
//     */
//    private static String escapeCsvField(String field) {
//        if (field == null) {
//            return "";
//        }
//
//        // 如果字段包含逗号、引号或换行符，需要用引号包围并对引号进行转义
//        if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
//            return "\"" + field.replace("\"", "\"\"") + "\"";
//        }
//        return field;
//    }
//
//    /**
//     * 提取报表数据
//     */
//    private static ExtractedData extractReportData(String html) {
//        Document doc = Jsoup.parse(html);
//        ExtractedData data = new ExtractedData();
//
//        // 提取标题
//        String title = doc.select(
//                "body > div > div > div > table > tbody > tr:nth-child(1) > td:nth-child(2) > div > table > tbody > tr:nth-child(1) > td > div")
//                .text().trim();
//        data.setTitle(title);
//
//        // 提取项目表头
//        List<String> projectHeader = Arrays.stream(doc.select(
//                "body > div > div > div > table > tbody > tr:nth-child(1) > td:nth-child(2) > div > table > tbody > tr:nth-child(7)")
//                .text()
//                .split(" "))
//                .filter(s -> !s.isEmpty())
//                .collect(Collectors.toList());
//
//        // 提取项目数据
//        List<List<String>> projectData = doc.select(
//                "body > div > div > div > table > tbody > tr:nth-child(2) > td:nth-child(2) > div > table > tbody > tr")
//                .stream()
//                .map(row -> Arrays.asList(row.text().split("\\s+")))
//                .collect(Collectors.toList());
//
//        // 提取科室表头
//        List<String> departmentHeader = Arrays.stream(doc.select(
//                "body > div > div > div > table > tbody > tr:nth-child(1) > td:nth-child(1) > div > table > tbody")
//                .text()
//                .split(" "))
//                .filter(s -> !s.isEmpty())
//                .collect(Collectors.toList());
//
//        // 提取科室数据
//        List<List<String>> departmentData = doc.select(
//                "body > div > div > div > table > tbody > tr:nth-child(2) > td:nth-child(1) > div > table > tbody > tr")
//                .stream()
//                .map(row -> Arrays.asList(row.text().split("\\s+")))
//                .collect(Collectors.toList());
//
//        // 合并科室和项目数据
//        Map<String, List<String>> combinedData = new HashMap<>();
//        for (int i = 0; i < departmentData.size(); i++) {
//            String deptName = departmentData.get(i).get(0);
//            deptName = "——".equals(deptName) ? "合计" : deptName;
//            combinedData.put(deptName, i < projectData.size() ? projectData.get(i) : new ArrayList<>());
//        }
//
//        // 设置数据
//        ProjectData projectDataObj = new ProjectData();
//        projectDataObj.setHeader(projectHeader);
//        projectDataObj.setData(combinedData);
//        data.setProject(projectDataObj);
//
//        DepartmentData departmentDataObj = new DepartmentData();
//        departmentDataObj.setHeader(departmentHeader);
//        departmentDataObj.setData(departmentData);
//        data.setDepartment(departmentDataObj);
//
//        return data;
//    }
//
//    // 数据模型类
//    @Data
//    static class ExtractedData {
//        private String title;
//        private ProjectData project;
//        private DepartmentData department;
//    }
//
//    @Data
//    static class ProjectData {
//        private List<String> header;
//        private Map<String, List<String>> data;
//    }
//
//    @Data
//    static class DepartmentData {
//        private List<String> header;
//        private List<List<String>> data;
//    }
//
//    /**
//     * 从字符串中提取页数
//     */
//    public static Map.Entry<Integer, Integer> extractPageNumbers(String content) {
//        int currentPageIndex = -1;
//        int reportTotalPage = -1;
//
//        Pattern pattern = Pattern.compile("FR\\._p\\.currentPageIndex\\s*=\\s*(\\d+);");
//        Matcher matcher = pattern.matcher(content);
//        if (matcher.find()) {
//            currentPageIndex = Integer.parseInt(matcher.group(1));
//        }
//
//        pattern = Pattern.compile("FR\\._p\\.reportTotalPage\\s*=\\s*(\\d+);");
//        matcher = pattern.matcher(content);
//        if (matcher.find()) {
//            reportTotalPage = Integer.parseInt(matcher.group(1));
//        }
//
//        return new AbstractMap.SimpleEntry<>(currentPageIndex, reportTotalPage);
//    }
//
//    /**
//     * AES加密
//     */
//    private static String aesEncrypt(String content, String key) {
//        try {
//            byte[] raw = key.getBytes(StandardCharsets.UTF_8);
//            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
//            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
//            cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
//            byte[] encrypted = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
//            return Base64.encodeBase64String(encrypted);
//        } catch (Exception e) {
//            throw new RuntimeException("AES加密失败", e);
//        }
//    }
//
//    /**
//     * 将 loadReportPane 字符串转换为 LoadReportPane 对象
//     */
//    @SuppressWarnings("all")
//    public static LoadReportPane transLoadReportPaneStr(String script) {
//        ScriptEngineManager manager = new ScriptEngineManager();
//        ScriptEngine engine = manager.getEngineByName("nashorn");
//
//        try {
//            engine.eval("var result = " + script);
//            ScriptObjectMirror result = (ScriptObjectMirror) engine.get("result");
//
//            String jsonString = JSONUtil.toJsonStr(result);
//
//            LoadReportPane loadReportPane = JSONUtil.toBean(jsonString, LoadReportPane.class);
//            return loadReportPane;
//
//        } catch (ScriptException e) {
//            e.printStackTrace();
//        }
//        return null;
//    }
//
//    /**
//     * 提取大对象
//     */
//    public static String extractBigObject(String str) {
//        int startIndex = str.indexOf("this.loadReportPane(");
//        if (startIndex == -1) {
//            return null; // 未找到 loadReportPane
//        }
//
//        startIndex = str.indexOf("{", startIndex);
//        if (startIndex == -1) {
//            return null; // 未找到 {
//        }
//
//        int braceCount = 0;
//        int endIndex = -1;
//
//        for (int i = startIndex; i < str.length(); i++) {
//            char currentChar = str.charAt(i);
//            if (currentChar == '{') {
//                braceCount++;
//            } else if (currentChar == '}') {
//                braceCount--;
//            }
//
//            // 当 braceCount 为 0 时，表示找到了匹配的 }
//            if (braceCount == 0) {
//                endIndex = i;
//                break;
//            }
//        }
//
//        if (endIndex == -1) {
//            return null; // 未找到匹配的 }
//        }
//
//        return "{" + str.substring(startIndex + 1, endIndex).trim() + "}"; // 提取大对象内容并去除空格
//    }
//
//    /**
//     * 递归处理子节点
//     */
//    private static void processMenuChildrenNode(ChildrenNode node, HashMap<String, String> pathMap) {
//        if (node == null)
//            return;
//
//        // 处理当前节点
//        if (node.getPath() != null) {
//            if (node.getPath().contains("&")) {
//                pathMap.put(node.getName(), node.getPath().split("&")[0]);
//                pathMap.put(node.getName(), node.getPath().split("&")[0]);
//            } else {
//                pathMap.put(node.getName(), node.getPath());
//            }
//        }
//
//        // 递归处理子节点
//        if (node.getChildrenNode() != null) {
//            for (ChildrenNode child : node.getChildrenNode()) {
//                processMenuChildrenNode(child, pathMap);
//            }
//        }
//    }
//
//    /**
//     * 加载报表面板
//     */
//    @Data
//    public static class LoadReportPane {
//        private Param param;
//        private Sheets sheets;
//        // private Browserbg browserbg;
//    }
//
//}
//
/// **
// * 背景参数
// */
//@Data
//class Parambg {
//    private String background;
//}
//
///**
// * 表单项配置
// */
//@Data
//class Items {
//    private String widgetName;
//    private String color;
//    private String fontfamily;
//    private boolean invisible;
//    private String widgetUrl;
//    private int fontsize;
//    private String type;
//    private String textColor;
//    private boolean verticalcenter;
//    private ControlAttr controlAttr;
//    private boolean autoline;
//    private String textalign;
//    private int x;
//    private int width;
//    private int y;
//    private boolean disabled;
//    private boolean needSubmit;
//    private String value;
//    private String decoration;
//    private boolean wrap;
//    private int height;
//}
//
//@Data
//class ControlAttrData {
//    private String text;
//    private String value;
//}
//
//@Data
//class ControlAttr {
//    private ControlAttrData data;
//    private String value;
//}
//
///**
// * HTML配置
// */
//@Data
//class Html {
//    private String widgetName;
//    private Parambg parambg;
//    private String[] itemsIndex;
//    private boolean invisible;
//    private String widgetUrl;
//    private boolean paraDisplay;
//    private boolean refresh;
//    private int absoluteResolutionScaleW;
//    private String type;
//    private boolean hasResize;
//    private boolean delayDisplayContent;
//    private int absoluteCompState;
//    private int vgap;
//    private int compInterval;
//    private int width;
//    private boolean __FIT__;
//    private boolean disabled;
//    private String widgetBackground;
//    private int absoluteResolutionScaleH;
//    private String position;
//    private HashMap<String, Items> items;
//    private int hgap;
//    private boolean scrollable;
//}
//
///**
// * 参数配置
// */
//@Data
//class Param {
//    private boolean delay;
//    private Parambg parambg;
//    private int alignLocation;
//    private int width;
//    private int showType;
//    private Html html;
//    private int height;
//}
//
///**
// * 工作表配置
// */
//@Data
//class Sheets {
//    private boolean closable;
//    private String id;
//    private boolean lazyload;
//    private String title;
//}
//
///**
// * 浏览器背景配置
// */
//@Data
//class Browserbg {
//}
//
//@Data
//class MenuReport {
//    private Object id;
//    private Object path;
//    private Integer itemId;
//    private Object reportType;
//    private String samplePath;
//    private String sampleExplain;
//    private String keyWords;
//    private String uniqueCode;
//}
//
//@Data
//class Node {
//    private Integer id;
//    private String name;
//    private Integer pid;
//    private Object categoryId;
//    private Object category;
//    private Integer ordering;
//    private Integer pcVisible;
//    private Integer padVisible;
//    private Integer mobileVisible;
//    private Integer homePage;
//    private MenuReport menuReport;
//    private Object hasChild;
//    private String moduleKey;
//    private Integer isParent;
//}
//
//@Data
//class ChildrenNode {
//    private Node node;
//    private Integer id;
//    private String name;
//    private Integer pid;
//    private Integer isparent;
//    private Object categoryid;
//    private Integer level;
//    private String nodeType;
//    private String path;
//    private Integer ordering;
//    private List<ChildrenNode> childrenNode;
//}
//
//@Data
//class MenuTree {
//    private Object node;
//    private Integer id;
//    private String name;
//    private Integer pid;
//    private Integer isparent;
//    private Object categoryid;
//    private Integer level;
//    private Object nodeType;
//    private Object path;
//    private Object ordering;
//    private List<ChildrenNode> childrenNode;
//}
