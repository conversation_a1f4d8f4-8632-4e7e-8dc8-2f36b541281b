{
  "properties" : { },
  "id" : "8d979129a5af4d40be0ae0627a2ef04b",
  "script" : null,
  "groupId" : "d71e2d6ea3a24ad48a50217b00c2aa6f",
  "name" : "004_2579_占床日奖励",
  "createTime" : null,
  "updateTime" : 1745569428631,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer2579",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "headers" : [ ],
  "paths" : [ ],
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import java.time.temporal.ChronoUnit
import java.util.regex.Pattern
import java.time.LocalDate
import java.time.Duration
import java.time.LocalDateTime
import com.jp.med.pms.util.HolidayUtils
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.math.BigDecimal
import java.util.stream.Collectors
import cn.hutool.core.util.StrUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSON
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import '@post:/WinningReportFetch/extractData0' as extractData;
import java.util.ArrayList
/**
 * 住院出区指标统计 (报表ID: 2579) 相关指标说明:
 *
 *
 * 2. 占床日奖励 (zcrjl):
 *    - 基于康复科每月出院床日数，
 *      取自卫宁报表运管部 "住院出区指标统计" 表中自然月康复科出院床日数。
 *    - 服务效率/质量指标，每月统计，**仅适用于康复医学科。**
 *
 */


let isTest = false
var startDate
var endDate

if (!body || !(body.extractedData)) {
    log.info("测试环境")
    isTest = true
    startDate = "2025-02-01"
    endDate = "2025-02-28"

} else {
    log.info("正式环境")
    startDate = body.extractedData.variablesMap.get("&{last.month.firstDay}")
    endDate = body.extractedData.variablesMap.get("&{last.month.lastDay}")
}
// 触发器和数据提取
var = trigger = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2579",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "科室",
                "KSRQ": "2024-12-01",
                "JSRQ": "2024-12-31",
                "YYDM": "01",
                // "specifyOssFile": "/wnReport/2025/01/06/1525/住院出区指标统计12月.xlsx",
                "bucket": "pms"
            }
        }
    }
}

body = {
    trigger
}
var resData = extractData()

let data = resData.jsonData



var result = {
    zcrjl: {},
}

body = {
    trigger
}
var resData2 = extractData()

let data2 = resData2.jsonData


data2.computeIfPresent("康复医学科", (k, v) => {
    if (v != null) {
        result.zcrjl.put(k, v.get("住院天数"))
    }
    return v
})


return {
    transformedData: {
        ...result
    }
}
return result