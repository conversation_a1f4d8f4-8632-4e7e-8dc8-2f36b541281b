{
  "properties" : { },
  "id" : "copy1744790787361d10455",
  "script" : null,
  "groupId" : "bc47d7f25b614b8f8b118c4e3fac572d",
  "name" : "2349床位收入",
  "createTime" : null,
  "updateTime" : 1744859548762,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer_2349_MEDICAL_SERVICE_BED",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "headers" : [ ],
  "paths" : [ ],
  "description" : "康复收入\tMEDICAL_SERVICE_REHABILITATION\n其他收入（门诊）\tMEDICAL_SERVICE_OTHER\n治疗收入\tMEDICAL_SERVICE_TREATMENT\n诊查收入\tMEDICAL_SERVICE_DIAGNOSTIC\n床位收入\tMEDICAL_SERVICE_BED\n",
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import java.math.BigDecimal
import java.util.stream.Collectors
import java.util.Collections
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import '@post:/WinningReportFetch/deptCodeTransform' as deptCodeTransform;
import '@/pms/performanceDeptNameMatch' as performanceDeptNameMatch;

import '@post:/WinningReportFetch/extractData0' as extractData;
import java.math.BigDecimal;



// 定义日期变量
var startDate = "2025-02-01"
var endDate = "2025-02-28"

var newDataMap = {}
let result = {
    MEDICAL_SERVICE_BED: {},
    zlf: {}
}
var {
    deptMap,
    deptMergeMap
} = performanceDeptNameMatch()

// 初始化用于存储计算过程详情的Map
var calculationDetails = {};

// 成本控制 卫宁报表  2349
// 采集项目：
//  其他收入（门诊）	MEDICAL_SERVICE_OTHER

var trigger = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2349",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "执行科室",
                // // "specifyOssFile": "/wnReport/2025/04/16/1603/wnbb_cw_医院收入统计.cpt.xlsx",
                // // "specifyOssFile": "/wnReport/2025/04/16/1623/wnbb_cw_医院收入统计.cpt.xlsx",
                "KSRQ": startDate,
                "JSRQ": endDate,
                "tjfs": "1",
                "tjfw": "1",
                "xmlx": "1",
                "tjkj": "2",
                "ksjb": "3",
                "tjkj2": "-1",
                "kdorzx": "1",
                "ksdm": "-1",
                "dxmdm": "-1",
                "xmdm": "-1"
            }
        }
    }
}

body = {
    trigger
}
const resData = extractData()
let dataObj = resData.jsonData
var newDataMap = {}
var {
    deptMap,
    deptMergeMap
} = performanceDeptNameMatch()
// return deptMergeMap
deptMergeMap.forEach((匹配科室, 执行科室List) => {
    newDataMap.computeIfAbsent(匹配科室, (k, v) => {
        return 执行科室List
            .stream()
            .filter(name => dataObj.containsKey(name))
            .map(name => dataObj.get(name))
            .collect(Collectors.toList());
    })
})





// log.info("dataObj{}",dataObj::stringify)
// 通用计算规则 诊查
newDataMap.forEach((dept, vList) => {


    var totalCwf = BigDecimal.ZERO // 用于记录总的诊查费
    var totalZlf = BigDecimal.ZERO // 用于记录总的资料费

    vList.forEach(b -> {

        var cwf = b.get("床位费").asDecimal(BigDecimal.ZERO)
        var zxf = b.get("诊查费").asDecimal(BigDecimal.ZERO)
        var xj = b.get("小计").asDecimal(BigDecimal.ZERO)


        totalCwf = totalCwf.add(cwf)
        totalZlf = totalZlf.add(xj.subtract(zxf).subtract(cwf))
        log.info("计算治疗费 {},{},", dept, xj + "-" + cwf + "-" + "-" + zxf)

    })

    var logEntry = {
        "step": "基础计算",
        "details": {


            "诊查费": totalZcf,

        }
    }
    calculationDetails.computeIfAbsent(dept, k -> []).add(logEntry);



    result.MEDICAL_SERVICE_BED.put(
        dept, totalCwf
    )
    result.zlf.put(
        dept, totalZlf
    )

});






return result