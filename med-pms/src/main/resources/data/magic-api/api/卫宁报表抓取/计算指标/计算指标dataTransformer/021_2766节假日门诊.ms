{
  "properties" : { },
  "id" : "d09a2ea04c044c9d8e890b1434be1d4a",
  "script" : null,
  "groupId" : "d71e2d6ea3a24ad48a50217b00c2aa6f",
  "name" : "021_2766节假日门诊",
  "createTime" : null,
  "updateTime" : 1747381991926,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer_2766_jjrmz",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : "",
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import cn.hutool.json.JSONUtil
import java.time.LocalDate
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import WinningReportExporter
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import java.math.BigDecimal;
import '@post:/WinningReportFetch/extractData0' as extractData;
import com.jp.med.pms.util.HolidayUtils
import java.util.stream.Collectors

// var winningReportExporter = new WinningReportExporter()
// return winningReportExporter.login(winningReportExporter.username, winningReportExporter.password);

/**
 * 提取报表数据的通用函数
 */
var extractDataFn = (queryParams) => {
    let trigger = {
        "type": "magic-api",
        "magicApi": {
            "path": "/WinningReportFetch/extractData",
            "method": "POST",
            "body": {
                "reportId": "2766",
                "paramsPath": ".queryParams",
                "queryParams": {
                    ...queryParams,
                    "WneportExcelConfigGroupColumnName": "科室名称",
                    "returnArrayData": true,
                    "bucket": "pms",
                }
            }
        }
    }
    body = {
        trigger
    }
    const resData = extractData()
    let data = resData.jsonData

    // 合并生长发育门诊到 儿童保健门诊

    // --- 开始: 合并生长发育门诊到 儿童保健门诊 ---
    var targetDept = '儿童保健门诊'
    var sourceDept = '生长发育门诊' // 请确认实际的科室名称是否准确

    if (data != null && data.isMap() && data.containsKey(sourceDept)) {
        log.info("发现源科室 '{}' 数据，准备合并到目标科室 '{}'", sourceDept, targetDept);
        var sourceList = data.get(sourceDept) // 获取源科室的数据列表

        if (sourceList != null && sourceList.isList() && !sourceList.isEmpty()) {
            var targetList = data.get(targetDept) // 获取目标科室的数据列表

            if (targetList != null && targetList.isList()) {
                // 目标科室已存在，将源科室数据追加到目标列表
                log.info("目标科室 '{}' 已存在，追加 {} 条记录", targetDept, sourceList.size());
                targetList.addAll(sourceList)
            } else {
                // 目标科室不存在，将源科室数据直接设置为目标科室的数据
                log.info("目标科室 '{}' 不存在，将源科室数据设为其数据 ({} 条记录)", targetDept, sourceList.size());
                // 创建一个新的 List 防止修改原始 sourceList (如果它可能在别处被引用)
                data.put(targetDept, new ArrayList(sourceList))
            }
            // 从 Map 中移除已合并的源科室条目
            data.remove(sourceDept)
            log.info("已移除源科室 '{}'", sourceDept);
        } else {
            log.info("源科室 '{}' 数据为空或非列表，无需合并", sourceDept);
            // 如果源数据为空，也可以选择移除它
            if (data.containsKey(sourceDept)) {
                data.remove(sourceDept);
            }
        }
    } else {
        log.info("数据中不存在源科室 '{}' 或数据格式不正确，无需合并", sourceDept);
    }
    // --- 结束: 合并操作 ---

    // 返回处理后的数据
    return data
}

/**
 * 节假日门诊挂号统计报表模块
 *
 * 1. 报表功能：
 *    - 根据日历上的休息日和调休规则核定节假日，统计相应的收费挂号人次。
 *    - 该报表主要用于服务效率/质量指标的考核。
 * 2. 适用对象：
 *    - 该指标适用于儿保医生。科室代码	科室名称 1162 儿童保健门诊
 * 3. 数据来源：
 *    - 挂号系统的收费挂号人次数据。2766
 *    - 日历系统中的节假日信息。
 * 4. 统计规则：
 *    - 获取指定年月的1日到最后一天的双休日挂号人次合计
 *    - 减去调休工作日的双休日挂号人次（周末需要上班的日期）
 *    - 减去节假日期间工作日的普通日挂号人次（周一至周五的法定节假日）
 *    - 统计结果用于服务效率/质量指标的考核。
 */

// 传入年月参数，例如"2025-02"
var calculateHolidayRegistrations = (yearMonth) => {
    var result = {}
    var formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")

    // 解析年月
    var parts = yearMonth.split("-")
    var year = Integer.parseInt(parts[0])
    var month = Integer.parseInt(parts[1])

    // 获取该月的第一天和最后一天
    var firstDay = LocalDate.of(year, month, 1)
    var lastDay = YearMonth.of(year, month).atEndOfMonth()

    // 1. 先获取整月的数据（1号到月底）
    var monthlyData = extractDataFn({
        "KSRQ": firstDay.format(formatter),
        "JSRQ": lastDay.format(formatter)
    })

    log.info("获取 {} 全月挂号数据", yearMonth)

    // 2. 获取科室的双休日挂号人次总计
    var weekendRegistrations = {}
    monthlyData.compute("儿童保健门诊", (deptName, detailList) => {
        if (detailList == null) {
            return 0
        }
        var count = detailList.stream()
            .reduce(0, (a, b) => {
                return a + b.get("双休日挂号人次").asInt(0)
            })
        weekendRegistrations.put(deptName, count)
        return count
    })

    log.info("双休日挂号人次总计: {}", JSONUtil.toJsonStr(weekendRegistrations))

    // 3. 获取特殊工作日
    var specialWorkdays = HolidayUtils.getSpecialWorkdays(yearMonth)

    // 4. 处理调休工作日（周末但需要上班）
    var workdaysOnHoliday = specialWorkdays.stream()
        .filter(day => day.get("type").equals("调休工作日"))
        .collect(Collectors.toList())

    // 5. 处理节假日期间工作日（节假日但是周一至周五）
    var holidaysOnWorkday = specialWorkdays.stream()
        .filter(day => day.get("type").equals("节假日期间工作日"))
        .collect(Collectors.toList())

    log.info("调休工作日: {}", JSONUtil.toJsonStr(workdaysOnHoliday))
    log.info("节假日期间工作日: {}", JSONUtil.toJsonStr(holidaysOnWorkday))

    // 6. 处理调休工作日（需从双休日挂号人次中减去）
    var workdayOnHolidayRegistrations = 0
    if (!workdaysOnHoliday.isEmpty()) {
        for (day in workdaysOnHoliday) {
            var date = day.get("date")
            var dayData = extractDataFn({
                "KSRQ": date,
                "JSRQ": date
            })

            dayData.compute("儿童保健门诊", (deptName, detailList) => {
                if (detailList == null) {
                    return 0
                }
                var count = detailList.stream()
                    .reduce(0, (a, b) => {
                        return a + b.get("双休日挂号人次").asInt(0)
                    })
                workdayOnHolidayRegistrations += count
                return count
            })
        }
    }

    log.info("调休工作日挂号人次: {}", workdayOnHolidayRegistrations)

    // 7. The holiday on workday registrations (need to be subtracted from normal workday registrations)
    var holidayOnWorkdayRegistrations = 0
    if (!holidaysOnWorkday.isEmpty()) {
        for (day in holidaysOnWorkday) {
            var date = day.get("date")
            var dayData = extractDataFn({
                "KSRQ": date,
                "JSRQ": date
            })

            dayData.compute("儿童保健门诊", (deptName, detailList) => {
                if (detailList == null) {
                    return 0
                }
                var count = detailList.stream()
                    .reduce(0, (a, b) => {
                        return a + b.get("普通日挂号人次").asInt(0)
                    })
                holidayOnWorkdayRegistrations += count
                return count
            })
        }
    }

    log.info("节假日期间工作日挂号人次: {}", holidayOnWorkdayRegistrations)

    // 8. 计算最终结果：双休日挂号人次 - 调休工作日挂号人次 - 节假日期间工作日挂号人次
    var finalRegistrations = {}
    weekendRegistrations.forEach((deptName, count) => {
        // 这里简化处理，假设所有的调休和节假日都在一个科室
        var adjustedCount = count - workdayOnHolidayRegistrations + holidayOnWorkdayRegistrations
    log.info("count {} - workdayOnHolidayRegistrations {}  - holidayOnWorkdayRegistrations {}",  count , workdayOnHolidayRegistrations , holidayOnWorkdayRegistrations)
        
        finalRegistrations.put(deptName, adjustedCount)
    })

    log.info("最终计算的节假日挂号人次: {}", JSONUtil.toJsonStr(finalRegistrations))

    return finalRegistrations
}



var startDate
var endDate
var isTest = false;

if (!body || !(body.extractedData)) {
    log.info("测试环境")
    isTest = true
    startDate = "2025-02-01"
    endDate = "2025-02-28"

} else {
    log.info("正式环境")
    startDate = body.extractedData.variablesMap.get("&{last.month.firstDay}")
    endDate = body.extractedData.variablesMap.get("&{last.month.lastDay}")
}

var yearMonth = "2025-02"

// 调用计算函数
var jjrmzResult = calculateHolidayRegistrations(startDate.substring(0, 7))

return {
    transformedData: {
        jjrmz: jjrmzResult
    }
}