{
  "properties" : { },
  "id" : "abd055eca40f45f9a4cbd38cbec94dcf",
  "script" : null,
  "groupId" : "d71e2d6ea3a24ad48a50217b00c2aa6f",
  "name" : "016_2591麻醉手术人数_麻醉恢复例工作量",
  "createTime" : null,
  "updateTime" : 1737451436118,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer_2591",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 200,\n    \"status\": 0,\n    \"message\": \"success\",\n    \"data\": {\n        \"mzssrsResult\": {\n            \"麻醉科门诊\": 993\n        }\n    }\n}",
  "description" : "",
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import java.time.temporal.ChronoUnit
import java.util.regex.Pattern
import java.time.LocalDate
import java.time.Duration
import java.time.LocalDateTime
import com.jp.med.pms.util.HolidayUtils
import java.time.LocalTime

import java.time.format.DateTimeFormatter

import java.math.BigDecimal

import java.util.stream.Collectors
import cn.hutool.core.util.StrUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSON
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import '@post:/WinningReportFetch/extractData0' as extractData;
import java.util.ArrayList

// 手术指标各维度分析

var startDate
var endDate
var isTest = false;

if (!body || !(body.extractedData)) {
    log.info("测试环境")
    isTest = true
    startDate = "2025-02-01"
    endDate = "2025-02-28"

} else {
    log.info("正式环境")
    startDate = body.extractedData.variablesMap.get("&{last.month.firstDay}")
    endDate = body.extractedData.variablesMap.get("&{last.month.lastDay}")
}
let trigger = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2586",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "科室名称",
                "SSKS": "-1",
                "TJKJ": "1",
                "KSRQ": startDate,
                "JSRQ": endDate,
                // "specifyOssFile": "/wnReport/2025/01/06/1432/wnbb_ygb_全院收费项目统计.cpt.xlsx",

            }
        }
    }
}





body = {
    trigger
}
const resData = extractData()

var data = resData.jsonData


var result = {

}


/**
 * 指标名称: 麻醉/手术人数
 * 指标代码: mzssrs
 * 数据来源: 手术指标各维度分析 (报表ID: 2591)
 * 报表路径: 卫宁报表运管部下“手术指标各维度分析”
 * 指标类型: 服务效率/质量指标
 * 统计周期: 每月
 * 适用对象: 麻醉科医护
 */
var mzssrsResult = {}
var needBigItemNames = ["麻醉费", "手术费"]
data.computeIfPresent("麻醉科门诊", (k, v) => {
    if (v != null) {
        var detailList = null
        if (v.getClass() != ArrayList) {
            detailList = []
            detailList.add(v)
        } else {
            detailList = v
        }
        var mzssrs = detailList.stream().filter(detail => {
            return needBigItemNames.contains(detail.get("大项目名称"))
        }).reduce(0, (a, b) => {
            return a + b.get("项目数量")::int(0)
        })
        mzssrsResult.put(k, mzssrs)
    }
    return v
})




/**
 * 指标名称: 麻醉恢复/例工作量
 * 指标代码: mzhflgzl
 * 数据来源: 手术指标各维度分析 (报表ID: 2591)
 * 报表路径: 卫宁报表运管部下“手术指标各维度分析”
 * 指标类型: 服务效率/质量指标
 * 统计周期: 每月
 * 适用对象: 
 *   - 麻醉科医生附表
 *   - 手麻科护理
 */
var mzhflgzlResult = {}
var needBigItemNames = ["麻醉费", "手术费"]
data.compute("麻醉科门诊", (k, v) => {
    if (v != null) {
        var detailList = null
        if (v.getClass() != ArrayList) {
            detailList = []
            detailList.add(v)
        } else {
            detailList = v
        }
        var mzssrs = detailList.stream().filter(detail => {
            return needBigItemNames.contains(detail.get("大项目名称"))
        }).reduce(0, (a, b) => {
            return a + b.get("项目数量")::int(0)
        })
        mzssrsResult.put(k, mzssrs)
    }
})



return {
    transformedData: {
        mzssrs: mzssrsResult,
        mzhflgzl: mzhflgzlResult
    }
}