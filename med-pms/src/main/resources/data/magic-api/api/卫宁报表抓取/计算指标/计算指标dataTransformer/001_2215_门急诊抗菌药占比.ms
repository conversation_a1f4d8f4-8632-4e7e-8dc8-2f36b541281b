{
  "properties" : { },
  "id" : "7fb2e3df3c454e539fae66f709339529",
  "script" : null,
  "groupId" : "d71e2d6ea3a24ad48a50217b00c2aa6f",
  "name" : "001_2215_门急诊抗菌药占比",
  "createTime" : null,
  "updateTime" : 1745822467715,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer_2215",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================

import java.time.temporal.ChronoUnit
import java.util.regex.Pattern
import java.time.LocalDate
import java.time.Duration
import java.time.LocalDateTime
// import com.jp.med.pms.util.HolidayUtils
import java.time.LocalTime

import java.time.format.DateTimeFormatter

import java.math.BigDecimal

import java.util.stream.Collectors
import cn.hutool.core.util.StrUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSON
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import '@post:/WinningReportFetch/extractData0' as extractData;
import java.util.ArrayList

var startDate
var endDate
var data
var isTest = false;
if (!body || !(body.extractedData)) {
    log.info("测试环境")
    isTest = true
    startDate = "2025-02-01"
    endDate = "2025-02-28"
} else {
    log.info("正式环境")

    startDate = body.extractedData.variablesMap.get("&{last.month.firstDay}")
    endDate = body.extractedData.variablesMap.get("&{last.month.lastDay}")
}

let trigger = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2215",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "出院科室",
                "KSRQ": startDate,
                "JSRQ": endDate,
                "KSDM": "-1",
                // "specifyOssFile": "wnReport/2025/01/03/wnbb_ygb_出区病人结算状态管理.cpt.xlsx",
                // "bucket": "pms"
            }
        }
    }
}
body = {
    trigger
}
const resData = extractData()

let data = resData.jsonData

let a = []
let result = {}
let okStatusList = []
okStatusList.add("出院结算")
// okStatusList.add("在院结算") //在院结算的只有妇产科生产的才会出现这种情况。因为需要先结算婴儿再结算母亲的
data.forEach((dept, detailList) => {
    var count = detailList.stream().filter(detail => {
        return okStatusList.indexOf(detail.get("结算状态")) != -1
    }).count()
    result.put(dept, count)
})

return result