{
  "properties" : { },
  "id" : "39c080bfb8794168944efc85de6ec637",
  "script" : null,
  "groupId" : "d71e2d6ea3a24ad48a50217b00c2aa6f",
  "name" : "007_2583_发药处方数量_配药量.ms",
  "createTime" : null,
  "updateTime" : 1745806550329,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer_2583",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 200,\n    \"status\": 0,\n    \"message\": \"success\",\n    \"data\": {\n        \"fycfslmzyfzb\": {\n            \"住院药房\": \"0.0\",\n            \"城南院区药房\": \"138.0\",\n            \"急诊药房\": \"480.0\",\n            \"中药房\": \"75.0\",\n            \"儿科药房\": \"0.0\",\n            \"门诊西药房\": \"2573.0\"\n        },\n        \"pylmzyfzb\": {\n            \"住院药房\": \"0.0\",\n            \"城南院区药房\": \"134.0\",\n            \"急诊药房\": \"0.0\",\n            \"中药房\": \"0.0\",\n            \"儿科药房\": \"0.0\",\n            \"门诊西药房\": \"2581.0\"\n        }\n    }\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import java.time.temporal.ChronoUnit
import java.util.regex.Pattern
import java.time.LocalDate
import java.time.Duration
import java.time.LocalDateTime
import com.jp.med.pms.util.HolidayUtils
import java.time.LocalTime

import java.time.format.DateTimeFormatter

import java.math.BigDecimal

import java.util.stream.Collectors
import cn.hutool.core.util.StrUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSON
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import '@post:/WinningReportFetch/extractData0' as extractData;
import java.util.ArrayList

/**
 * ==================================================
 * 发药处方数量（门诊药房指标）
 * ==================================================
 * 指标代码: fycfslmzyfzb
 * 
 * 描述:
 * 统计门诊药房配发药的数量。
 * 
 * 数据来源:
 * 卫宁报表 -> 运管部 -> 门诊配发药统计表
 * （包含门诊西药房和急诊药房的数据）
 * 
 * 分类:
 * 服务效率/质量指标
 * ==================================================
 */

/**
 * ==================================================
 * 配药量（门诊药房指标）
 * ==================================================
 * 指标代码: pylmzyfzb
 * 
 * 描述:
 * 统计门诊药房配发药的数量。
 * 
 * 数据来源:
 * 卫宁报表 -> 运管部 -> 门诊配发药统计表
 * （包含门诊西药房和急诊药房的数据）
 * 
 * 分类:
 * 服务效率/质量指标
 * ==================================================
 */



var startDate
var endDate
var isTest = false;

if (!body || !(body.extractedData)) {
    log.info("测试环境")
    isTest = true
    startDate = "2025-02-01"
    endDate = "2025-02-28"

} else {
    log.info("正式环境")
    startDate = body.extractedData.variablesMap.get("&{last.month.firstDay.time}")
    endDate = body.extractedData.variablesMap.get("&{last.month.lastDay.time}")
}

let trigger = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2583",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "药房名称",
                "KSRQ": startDate,
                "JSRQ": endDate,
                "YYDM": "01",
                // "specifyOssFile": "wnReport/2025/01/03/wnbb_ygb_出区病人结算状态管理.cpt.xlsx",
                // "bucket": "pms"
            }
        }
    }
}




body = {
    trigger
}
const resData = extractData()

let data = resData.jsonData



let result = {
    fycfslmzyfzb: {},
    pylmzyfzb: {},
}
let okStatusList = []
okStatusList.add("出院结算")

// okStatusList.add("在院结算") //在院结算的只有妇产科生产的才会出现这种情况。因为需要先结算婴儿再结算母亲的
data.forEach((dept, detail) => {
    if (dept.contains("制表人") || dept.contains("合计")) {
        return
    }
    if (detail.get("发药处方数量").equals("发药处方数量")) {
        return
    }

    result.fycfslmzyfzb.put(dept, detail.get("发药处方数量"))
    result.pylmzyfzb.put(dept, detail.get("配药量"))
})

return {
    transformedData: {
        ...result
    }
}