{
  "properties" : { },
  "id" : "e411103cbd334af9b264fe3fe55d1a42",
  "script" : null,
  "groupId" : "97a3e763eb36484ca09c4c1ac033c133",
  "name" : "测试",
  "createTime" : null,
  "updateTime" : 1737453011297,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "mstest",
  "method" : "GET",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 200,\n    \"status\": 0,\n    \"message\": \"success\",\n    \"data\": \"java.math.BigDecimal\"\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
// exit 500, 'Internal Server Error'

import java.math.BigDecimal

import java.time.DayOfWeek
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.LocalTime;
import java.time.LocalDate;

var a1 = "213".asDecimal()
var a2 = "213".asDecimal()
return (a1.add(a2) - (a1 + a2)).getClass()
var a = {
    b: {
        c: 1
    }
}

return Optional.ofNullable(a.b.c?.d).orElse(1);
// return a.b.c.asDecimal(BigDecimal.ZERO)

// var a = []
// return a.stream().reduce(1, (aa, bb) => aa + bb)
// 1. 定义与输入字符串格式匹配的 DateTimeFormatter
var formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

// 2. 输入的日期时间字符串
var dateString = "2024110804:06:00";

// 3. 将字符串格式化为标准格式
dateString = dateString.replaceFirst("(\\d{4})(\\d{2})(\\d{2})(\\d{2}):(\\d{2}):(\\d{2})", "$1-$2-$3 $4:$5:$6");

// 4. 解析为 LocalDateTime
var dateTime = LocalDateTime.parse(dateString, formatter);

// 5. 从 LocalDateTime 中提取 LocalTime
var time = dateTime.toLocalTime();

var list = [1, 2, 3]


// 获取当前日期
LocalDate today = LocalDate.now();

// 获取今天是星期几
DayOfWeek dayOfWeek = today.getDayOfWeek();
// 创建一个星期几到中文的映射

return dayOfWeek.ordinal() + 1
MapdayOfWeekMap = new HashMap();
dayOfWeekMap.put(DayOfWeek.MONDAY, "星期一");
dayOfWeekMap.put(DayOfWeek.TUESDAY, "星期二");
dayOfWeekMap.put(DayOfWeek.WEDNESDAY, "星期三");
dayOfWeekMap.put(DayOfWeek.THURSDAY, "星期四");
dayOfWeekMap.put(DayOfWeek.FRIDAY, "星期五");
dayOfWeekMap.put(DayOfWeek.SATURDAY, "星期六");
dayOfWeekMap.put(DayOfWeek.SUNDAY, "星期日");
return dayOfWeekMap.get(dayOfWeek)