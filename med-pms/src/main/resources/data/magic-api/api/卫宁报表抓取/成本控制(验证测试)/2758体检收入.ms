{
  "properties" : { },
  "id" : "3d6efc3ce4c44674b8a9ad0ee61d525c",
  "script" : null,
  "groupId" : "bc47d7f25b614b8f8b118c4e3fac572d",
  "name" : "2758体检收入",
  "createTime" : null,
  "updateTime" : 1744884211735,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer_2758",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import java.math.BigDecimal;
// 成本控制-体检收入
import '@post:/WinningReportFetch/extractData0' as extractData;
import '@post:/WinningReportFetch/deptCodeTransform' as deptCodeTransform;
import '@/pms/performanceDeptNameMatch' as performanceDeptNameMatch;

var {
    deptMap,
    deptMergeMap
} = performanceDeptNameMatch()
const trigger = {
    type: 'magic-api',
    magicApi: {
        path: '/WinningReportFetch/extractData',
        method: 'POST',
        body: {
            reportId: '2758',
            paramsPath: '.queryParams',
            queryParams: {
                // "WneportExcelConfigGroupColumnName": '执行科室',
                "WneportExcelConfigGroupColumnName": '执行科室',
                // "specifyOssFile": "/wnReport/2025/04/17/1533/wnbb_ygb_体检科收入统计.cpt.xlsx",
                // KSRQ: '&{KSRQ.firstDay.time}',
                // JSRQ: '&{JSRQ.lastDay.time}',
                // "KSRQ": "2024-06-29 00:00:00",
                // "JSRQ": "2024-06-30 23:59:59"
                "KSRQ": "2025-02-01 00:00:00",
                "JSRQ": "2025-02-28 23:59:59"
            },
        },
    },
}

body = {
    trigger
}
const resData = extractData()

let data = resData.jsonData

log.info("计算成本控制-体检收入")
// log.info("计算成本控制-体检收入 body:{}",body)
// 只有部分医生进入统计
const StatisticalDoctorList = ["张滔", "秦晓霞"]
const DontNeedItemList = ["中成药费", "西药费"]
const result = {}
var sumAmout = BigDecimal.ZERO
data.forEach((k, v) => {
    if (v.getClass() == ArrayList) {
        let sum = v.stream()
            .filter(item => {
                var case1 = StatisticalDoctorList.contains(item.get("医生"))
                var case2 = !DontNeedItemList.contains(item.get("类别"))

                return case1 && case2
            })
            .reduce(0.0, (a, b) => {
                return a + b.get("总金额")::double(0.0)
            })

        var amout = new BigDecimal(sum).setScale(1, BigDecimal.ROUND_HALF_UP)

        if (k == '') {
            k = "无科室"
            // sumAmout = sumAmout.add(amout)
            log.info("无 {}",sum)
            result.put(k, amout)


        } else {
            body.deptName = k
            let deptCodeRes = deptCodeTransform()
            // result.put(deptCodeRes, new BigDecimal(sum).setScale(1, BigDecimal.ROUND_HALF_UP))
            result.put(deptMap.get(k), amout)
            sumAmout = sumAmout.add(amout)
            log.info("({})科室:{} 总金额:{}", deptCodeRes, v[0].get("执行科室"), result.get(deptCodeRes))
        }
    }
})

return {
    result,
    // 微信费用	65653.67
    sumAmout:sumAmout+1886+65653.67
    // +65653.67-1886
}