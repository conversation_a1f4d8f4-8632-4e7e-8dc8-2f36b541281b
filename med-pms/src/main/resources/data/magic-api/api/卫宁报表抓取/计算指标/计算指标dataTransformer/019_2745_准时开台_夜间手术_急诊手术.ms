{
  "properties" : { },
  "id" : "46e331a199fc4aeaa9ab01f8c24dfa84",
  "script" : null,
  "groupId" : "d71e2d6ea3a24ad48a50217b00c2aa6f",
  "name" : "019_2745_准时开台_夜间手术_急诊手术",
  "createTime" : null,
  "updateTime" : 1745826421484,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer_2745",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 200,\n    \"status\": 0,\n    \"message\": \"success\",\n    \"data\": {\n        \"ssgzlzsktResult\": {\n            \"皮肤科门诊\": 0,\n            \"康复医学科\": 0,\n            \"肾内、内分泌科\": 0,\n            \"妇产科\": 22,\n            \"胃肠外科\": 12,\n            \"耳鼻咽喉头颈外科\": 14,\n            \"神经内科\": 2,\n            \"普通外科门诊\": 0,\n            \"城南院区妇产科门诊\": 0,\n            \"泌尿外科门诊\": 0,\n            \"乳腺科门诊\": 0,\n            \"眼科\": 20,\n            \"全科医疗科\": 0,\n            \"胸外科\": 5,\n            \"当前科室名称\": 0,\n            \"老年病科\": 0,\n            \"肝胆胰外科\": 16,\n            \"呼吸与危重症医学科\": 0,\n            \"脱发门诊\": 0,\n            \"重症医学科\": 0,\n            \"城南院区骨科门诊\": 0,\n            \"心血管内科\": 0,\n            \"胃肠外科减重与代谢外科门诊\": 0,\n            \"泌尿外科\": 25,\n            \"消化内科\": 1,\n            \"肝胆胰外科门诊\": 0,\n            \"儿科\": 0,\n            \"感染性疾病科\": 0,\n            \"神经外科\": 0\n        },\n        \"ssgzlyjssResult\": {\n            \"皮肤科门诊\": 0,\n            \"康复医学科\": 0,\n            \"肾内、内分泌科\": 1,\n            \"妇产科\": 5,\n            \"胃肠外科\": 6,\n            \"耳鼻咽喉头颈外科\": 0,\n            \"神经内科\": 3,\n            \"普通外科门诊\": 0,\n            \"城南院区妇产科门诊\": 0,\n            \"泌尿外科门诊\": 0,\n            \"乳腺科门诊\": 0,\n            \"眼科\": 0,\n            \"全科医疗科\": 3,\n            \"胸外科\": 1,\n            \"当前科室名称\": 0,\n            \"老年病科\": 14,\n            \"肝胆胰外科\": 1,\n            \"呼吸与危重症医学科\": 0,\n            \"脱发门诊\": 0,\n            \"重症医学科\": 1,\n            \"城南院区骨科门诊\": 0,\n            \"心血管内科\": 3,\n            \"胃肠外科减重与代谢外科门诊\": 0,\n            \"泌尿外科\": 6,\n            \"消化内科\": 0,\n            \"肝胆胰外科门诊\": 0,\n            \"儿科\": 0,\n            \"感染性疾病科\": 0,\n            \"神经外科\": 4\n        },\n        \"ssgzljzssResult\": {\n            \"皮肤科门诊\": 0,\n            \"康复医学科\": 0,\n            \"肾内、内分泌科\": 1,\n            \"妇产科\": 15,\n            \"胃肠外科\": 26,\n            \"耳鼻咽喉头颈外科\": 0,\n            \"神经内科\": 7,\n            \"普通外科门诊\": 0,\n            \"城南院区妇产科门诊\": 0,\n            \"泌尿外科门诊\": 0,\n            \"乳腺科门诊\": 0,\n            \"眼科\": 0,\n            \"全科医疗科\": 0,\n            \"胸外科\": 1,\n            \"当前科室名称\": 0,\n            \"老年病科\": 0,\n            \"肝胆胰外科\": 4,\n            \"呼吸与危重症医学科\": 0,\n            \"脱发门诊\": 0,\n            \"重症医学科\": 2,\n            \"城南院区骨科门诊\": 0,\n            \"心血管内科\": 4,\n            \"胃肠外科减重与代谢外科门诊\": 0,\n            \"泌尿外科\": 35,\n            \"消化内科\": 0,\n            \"肝胆胰外科门诊\": 0,\n            \"儿科\": 1,\n            \"感染性疾病科\": 0,\n            \"神经外科\": 10\n        },\n        \"ssgzljjrssResult\": {\n            \"皮肤科门诊\": 1,\n            \"康复医学科\": 0,\n            \"肾内、内分泌科\": 0,\n            \"妇产科\": 0,\n            \"胃肠外科\": 0,\n            \"耳鼻咽喉头颈外科\": 0,\n            \"神经内科\": 0,\n            \"普通外科门诊\": 0,\n            \"城南院区妇产科门诊\": 0,\n            \"泌尿外科门诊\": 0,\n            \"乳腺科门诊\": 0,\n            \"眼科\": 0,\n            \"全科医疗科\": 0,\n            \"胸外科\": 0,\n            \"当前科室名称\": 0,\n            \"老年病科\": 0,\n            \"肝胆胰外科\": 0,\n            \"呼吸与危重症医学科\": 0,\n            \"脱发门诊\": 0,\n            \"重症医学科\": 0,\n            \"城南院区骨科门诊\": 0,\n            \"心血管内科\": 0,\n            \"胃肠外科减重与代谢外科门诊\": 2,\n            \"泌尿外科\": 0,\n            \"消化内科\": 0,\n            \"肝胆胰外科门诊\": 1,\n            \"儿科\": 0,\n            \"感染性疾病科\": 0,\n            \"神经外科\": 0\n        }\n    }\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import java.time.temporal.ChronoUnit
import java.util.regex.Pattern
import java.time.LocalDate
import java.time.Duration
import java.time.LocalDateTime
import com.jp.med.pms.util.HolidayUtils
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.math.BigDecimal
import java.util.stream.Collectors
import cn.hutool.core.util.StrUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSON
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import '@post:/WinningReportFetch/extractData0' as extractData;
import java.util.ArrayList

/**
 * 手术工作量统计汇总 (基于卫宁报表: "按手麻系统统计手术信息（新）", ID: 2745)
 *
 *  - 准时开台 (ssgzlzskt):
 *      手术室类别: 手术室、城南院区手术室、介入室、消化内科内镜室
 *      筛选条件: 手术台次=1, 开始时间 8:00-9:00, 排除急诊
 *      统计维度: 手术科室
 *      统计内容: 符合条件的首台手术数量
 *
 *  - 夜间手术 (ssgzlyjss):
 *      手术室类别: 手术室、城南院区手术室、介入室、消化内科内镜室
 *      筛选条件: 开始时间 22:00-7:59
 *      统计维度: 手术科室
 *      统计内容: 符合条件的手术数量
 *
 *  - 急诊手术 (ssgzljzss):
 *      手术室类别: 手术室、城南院区手术室、介入室、消化内科内镜室
 *      筛选条件: 手术类别为急诊, 入院到手术时间<=24小时
 *      统计维度: 手术科室
 *      统计内容: 符合条件的手术数量
 *
 *  - 节假日手术 (ssgzljjrss):
 *      手术室类别: 手术室、城南院区手术室、介入室、消化内科内镜室
 *      筛选条件: 手术日期为节假日
 *      统计维度: 手术科室
 *      统计内容: 符合条件的手术数量，并按占比计算奖励基数
 *
 *  说明:
 *      - 时间格式在实际使用中需做相应转换。
 *      - 所有筛选条件逻辑关系为 AND。
 *      - 节假日手术需进行额外计算。
 */







var startDate
var endDate
var isTest = false;

if (!body || !(body.extractedData)) {
    log.info("测试环境")
    isTest = true
    startDate = "2025-02-01"
    endDate = "2025-03-07"

} else {
    log.info("正式环境")
    startDate = body.extractedData.variablesMap.get("&{last.month.firstDay}")
    endDate = body.extractedData.variablesMap.get("&{last.month.lastDay+7}")
}

let trigger = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2745",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "当前科室名称",
                // // "specifyOssFile": "/wnReport/2025.2月按手麻系统统计手术信息xlsx.xlsx",
                //   // "specifyOssFile": "/wnReport/2025/04/10/1437/wnbb_ygb_各类手术统计2.cpt.xlsx",
                "YYDM": "01",
                "KSRQ": startDate,
                "JSRQ": endDate,
                "FSORCQ": "0",
                "KSDM": "1056,1102,1090,1063"
            }

        }
    }
}

body = {
    trigger
}
const resData = extractData()

let data = resData.jsonData


var ssgzlzsktResult = {}
var ssgzlyjssResult = {}
var ssgzljzssResult = {}
var ssgzljjrssResult = {}


var isValidTimeFn = (timeStr) => {
    if (StrUtil.isBlank(timeStr) || timeStr::string('').length() != "2024110113:15:00".length()) {
        return false
    }
    return true
}

var getTimeFn = (dateString) => {
    // 1. 定义与输入字符串格式匹配的 DateTimeFormatter
    var formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    // 3. 将字符串格式化为标准格式
    dateString = dateString.replaceFirst("(\\d{4})(\\d{2})(\\d{2})(\\d{2}):(\\d{2}):(\\d{2})", "$1-$2-$3 $4:$5:$6");

    // 4. 解析为 LocalDateTime
    var dateTime = LocalDateTime.parse(dateString, formatter);


    // 5. 从 LocalDateTime 中提取 LocalTime
    LocalTime time = dateTime.toLocalTime();
    return time
}

var getDateTimeFn = (dateString) => {
    // 1. 定义与输入字符串格式匹配的 DateTimeFormatter
    var formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    // 3. 将字符串格式化为标准格式
    dateString = dateString.replaceFirst("(\\d{4})(\\d{2})(\\d{2})(\\d{2}):(\\d{2}):(\\d{2})", "$1-$2-$3 $4:$5:$6");

    // 4. 解析为 LocalDateTime
    LocalDateTime dateTime = LocalDateTime.parse(dateString, formatter);



    return dateTime
}



var isDirtyDataFn = (dept, detail) => {
    if (detail.getClass() == ArrayList) {
        if (detail.isEmpty()) {
            return true
        }
        if (detail.get(0).get("序号").equals(dept) || detail.get(0).get("住院号").equals(dept)) {
            return true
        }
    }
    if (detail == null) {
        return true
    }


    if (detail.get(0).get("序号").equals(dept) || detail.get(0).get("住院号").equals(detail.get(0).get("序号"))) {
        return true
    }
    return false
}
/**
 * 准时开台 (ssgzlzskt)
 * - 手术室类别: 手术室、城南院区手术室、介入室、消化内科内镜室
 * - 筛选条件: 手术台次=1, 开始时间 8:00-9:00, 排除急诊
 * - 统计维度: 手术科室
 * - 统计内容: 符合条件的首台手术数量
 */
var ssgzlzsktFn = (dept, detailList) => {
    var ssgzlzsktCount = detailList.stream().filter(item => {
        var Condition1 = item.get("手术台次") == 1;
        var Condition2 = false;


        if (!isValidTimeFn(item.get("开始时间"))) {
            return false
        }

        try {

            var dateString = item.get("开始时间");

            // 4. 解析为 LocalDateTime
            var time = getTimeFn(dateString)


            // 4. 定义时间范围的开始和结束时间
            var startTime = LocalTime.of(8, 0); // 8:00
            var endTime = LocalTime.of(9, 0); // 9:00

            // 5. 判断时间是否在范围内 (包含开始和结束时间)
            Condition2 = !time.isBefore(startTime) && !time.isAfter(endTime);

        } catch (e) {
            // 字符串格式错误或解析失败，返回 false 并打印异常信息
            log.error("ssgzlzsktFn日期时间字符串解析失败:{} {}", item.get("开始时间"), e);
            e.printStackTrace()
            Condition2 = false;
            throw e;
        }
        var Condition3 = !StrUtil.equals(item.get("手术类别"), "急诊手术"); // 修改为排除急诊
        if (Condition1 && Condition2 && Condition3 && dept == "耳鼻咽喉头颈外科") {
            log.info("准时开台住院号:{}", item.住院号)
        }
        return Condition1 && Condition2 && Condition3;
    }).count();
    if (!isDirtyDataFn(dept, detailList)) {

        ssgzlzsktResult.put(dept, ssgzlzsktCount);
    }
};

/**
 * 夜间手术 (ssgzlyjss)
 * - 手术室类别: 手术室、城南院区手术室、介入室、消化内科内镜室
 * - 筛选条件: 开始时间 22:00-7:59
 * - 统计维度: 手术科室
 * - 统计内容: 符合条件的手术数量
 */
var ssgzlyjssFn = (dept, detailList) => {
    var ssgzlyjssCount = detailList.stream().filter(item => {
        var Condition1 = false;

        if (!isValidTimeFn(item.get("开始时间"))) {
            return false
        }

        try {
            var dateString = item.get("开始时间");

            // 4. 解析为 LocalDateTime
            var time = getTimeFn(dateString)

            // 4. 定义时间范围的开始和结束时间
            var nightStartTime = LocalTime.of(22, 0); // 22:00
            var nightEndTime = LocalTime.of(7, 59); // 7:59

            // 5. 判断时间是否在夜间范围内 (跨天情况)
            if (nightStartTime.isBefore(nightEndTime)) {
                // 不跨天的情况
                Condition1 = !time.isBefore(nightStartTime) && !time.isAfter(nightEndTime);
            } else {
                // 跨天的情况
                Condition1 = !time.isBefore(nightStartTime) || !time.isAfter(nightEndTime);
            }

        } catch (e) {
            // 字符串格式错误或解析失败，返回 false 并打印异常信息
            log.error("日期时间字符串解析失败:{}", e.getMessage());
            Condition1 = false;
        }

        return Condition1;
    }).count();
    if (!isDirtyDataFn(dept, detailList)) {
        ssgzlyjssResult.put(dept, ssgzlyjssCount);

    }
};





/**
 * 急诊手术 (ssgzljzss)
 * - 手术室类别: 手术室、城南院区手术室、介入室、消化内科内镜室
 * - 筛选条件: 手术类别为急诊, 入院到手术时间<=24小时
 * - 统计维度: 手术科室
 * - 统计内容: 符合条件的手术数量
 */
var ssgzljzsstemp = {}
var ssgzljzssFn = (dept, detailList) => {
    var allowedOperatingRoomTypes = ["手术室", "城南院区手术室", "介入室", "介入放射室", '手术室(本部) ', "消化内科内镜室"];
    // 手术室(本部) 介入放射室 城南院区手术室
    let sum = [];
    var ssgzljzssTemp = detailList.stream().filter(item => {
        // 条件1: 手术类别为急诊手术
        var Condition1 = StrUtil.equals(item.get("手术类别"), "急诊手术");

        // 条件3: 手术室类别为指定类别之一
        // var Condition3 = allowedOperatingRoomTypes.includes(item.get("手术室类别"));

        // 条件2: 入院到手术时间 <= 24小时
        var Condition2 = false;

        if (!isValidTimeFn(item.get("开始时间"))) {
            return false;
        }

        if (!isValidTimeFn(item.get("入区时间"))) {
            return false;
        }


        // 2. 解析入院时间和手术时间
        LocalDateTime admissionTime = getDateTimeFn(item.get("入区时间"));
        LocalDateTime surgeryTime = getDateTimeFn(item.get("开始时间"));


        // 计算时间差
        Duration duration = Duration.between(admissionTime, surgeryTime);

        // 计算分钟差
        long minutesDifference = duration.toMinutes();

        Condition2 = minutesDifference <= 24 * 60; // 24小时转换为分钟



        return Condition1 && Condition2;
    }).collect(Collectors.toList())


    log.info("急诊手术ssgzljzssTemp：dept{}：{}", dept, ssgzljzssTemp.map(item => item.住院号))

    var ssgzljzssCount = ssgzljzssTemp.size()
    // 将统计结果存入结果集
    if (!isDirtyDataFn(dept, detailList)) {
        ssgzljzssResult.put(dept, ssgzljzssCount);
    }
};


/**
 * 节假日手术 (ssgzljjrss)
 * - 手术室类别: 手术室、城南院区手术室、介入室、消化内科内镜室
 * - 筛选条件: 手术日期为节假日
 * - 统计维度: 手术科室
 * - 统计内容: 符合条件的手术数量，并按占比计算奖励基数
 */


var ssgzljjrssFn = (dept, detailList) => {
    var ssgzljjrssList = detailList.stream().filter(item => {
        // 1. 获取手术日期
        var surgeryDateStr = item.get("手术日期");
        var surgeryDate = null;
        if (StrUtil.isBlank(surgeryDateStr)) {
            return false
        }


        // 2. 将字符串解析为 LocalDate 对象
        // 2024/11/1
        // 2024-11-01


        // 定义正则表达式匹配两种日期格式
        String regex1 = "^\\d{4}/\\d{1,2}/\\d{1,2}$"; // 匹配 yyyy/M/d
        String regex2 = "^\\d{4}-\\d{2}-\\d{2}$"; // 匹配 yyyy-MM-dd

        // 定义对应的日期格式化器
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy/M/d");
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        var surgeryDate = null
        // 判断日期字符串的格式

        if (Pattern.matches(regex1, surgeryDateStr)) {
            surgeryDate = LocalDate.parse(surgeryDateStr, formatter1);
        } else if (Pattern.matches(regex2, surgeryDateStr)) {
            surgeryDate = LocalDate.parse(surgeryDateStr, formatter2);
        } else {
            log.info("不支持的时间格式 {}", surgeryDateStr)
            return false
        }

        // 3. 判断手术日期是否为节假日
        var isHoliday = HolidayUtils.isHoliday(surgeryDate);
        if (isHoliday) {
            log.info("节假日 {} = {}", surgeryDate, HolidayUtils.getHolidayName(surgeryDate));
        }

        // 4. 排除急诊手术
        var isEmergency = !StrUtil.equals(item.get("手术类别"), "急诊手术");

        return isHoliday && isEmergency;
    }).collect(Collectors.toList())
    // .collect(Collectors.toList())
    log.info("{}", JSONUtil.toJsonStr(ssgzljjrssList))



    // 将结果存入结果集

    if (!isDirtyDataFn(dept, detailList)) {
        ssgzljjrssResult.put(dept, 1);

    }
};
data.forEach((dept, detailList) => {
    ssgzlzsktFn(dept, detailList)
    ssgzlyjssFn(dept, detailList)
    ssgzljzssFn(dept, detailList)
    ssgzljjrssFn(dept, detailList)
})


return {
    transformedData: {
        ssgzlzskt: ssgzlzsktResult,
        ssgzlyjss: ssgzlyjssResult,
        ssgzljzss: ssgzljzssResult,
        ssgzljjrss: ssgzljjrssResult
    }
}