{
  "properties" : { },
  "id" : "copy1737364780394d98969",
  "script" : null,
  "groupId" : "24f1c8948f1943819669e1f42cb3918b",
  "name" : "2579_出院病例数",
  "createTime" : null,
  "updateTime" : 1737367151061,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer2579_cybls",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "headers" : [ ],
  "paths" : [ ],
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import java.time.temporal.ChronoUnit
import java.util.regex.Pattern
import java.time.LocalDate
import java.time.Duration
import java.time.LocalDateTime
import com.jp.med.pms.util.HolidayUtils
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.math.BigDecimal
import java.util.stream.Collectors
import cn.hutool.core.util.StrUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSON
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import '@post:/WinningReportFetch/extractData0' as extractData;
import java.util.ArrayList
/**
 * 住院出区指标统计 (报表ID: 2579) 相关指标说明:
 *
 * 1. 平均住院日 (pjzyr):
 *    - 卫宁报表运管部 "住院出区指标统计" 表累计值，每年1月起累计。
 *    - 服务效率/质量指标，每月统计，适用于各科室医护。
 *    - **康复医学科需剔除脑瘫儿数据。**
 *
 * 2. 占床日奖励 (zcrjl):
 *    - 基于康复科每月出院床日数，取自卫宁报表运管部 "住院出区指标统计" 表中自然月康复科出院床日数。
 *    - 服务效率/质量指标，每月统计，**仅适用于康复医学科。**
 *
 * 3. 出院病例数 (cybls):
 *    - 以出区病人为统计口径，统计自然月出院病人。取自卫宁报表运管部 "住院出区指标统计" 表中自然月各科室出区病人总数。
 *    - 服务效率/质量指标，每月统计，适用于各科室医护。
 *    - **重症医学科以收治病人数为准，取自卫宁报表运管部 "重症医学科收治病人情况" 表的病人总数。急诊科统计急诊病例数。**
 */


var result = {
    pjzyr: {},
    zcrjl: {},
    cybls: {}
}



// 触发器和数据提取
trigger = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2579",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "科室",
                "KSRQ": "2024-12-01",
                "JSRQ": "2024-12-31",
                "YYDM": "01",
                // "specifyOssFile": "/wnReport/2025/01/06/1525/住院出区指标统计12月.xlsx",
                "bucket": "pms"
            }
        }
    }
}

body = {
    trigger
}
var resData = extractData()

let data = resData.jsonData


/**
 * 住院出区指标统计 (报表ID: 2579) 相关指标说明 - 出院病例数 (cybls):
 *
 * - 以出区病人为统计口径，统计自然月出院病人。取自卫宁报表运管部 "住院出区指标统计" 表中自然月各科室出区病人总数。
 * - 服务效率/质量指标，每月统计，适用于各科室医护。
 * - **重症医学科以收治病人数为准，取自卫宁报表运管部 "重症医学科收治病人情况" 表的病人总数。急诊科统计急诊病例数。**
 */
//1
data.forEach((deptName, detail) => {
    if (!deptName.equals("重症医学科")) {
        result.cybls.put(deptName, detail.get("出区人次"))
    }
})
// 2重症医学科
// 触发器和数据提取
trigger = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2642",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "结算状态",
                "KSRQ": "2024-12-01",
                "JSRQ": "2024-12-31",
            }
        }
    }
}

body = {
    trigger
}
var resData2 = extractData()

let data2 = resData2.jsonData
data2.compute("出院结算", (k, v) => {
    if (v != null && v.getClass() == ArrayList) {
        result.cybls.put("重症医学科", v.size().asString())
    }
})
//3急诊

trigger = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "871",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "科室名称",
                "KSRQ": "2024-12-01",
                "JSRQ": "2024-12-31",
                "KSJB": "3",
            }
        }
    }
}

body = {
    trigger
}
var resData3 = extractData()
var a = ""

let data3 = resData3.jsonData
// return data3
data3.forEach((dept, detail) => {
    //todo 急诊是哪些
    if (dept.contains("急诊")) {
        result.cybls.put(dept, detail.get("急诊人次").asDecimal(BigDecimal.ZERO))
    }
})


return {
    transformedData: {
        cybls: result.cybls
    }
}