{
  "properties" : { },
  "id" : "456bbd26bac94463a5a16897fb07d2e2",
  "script" : null,
  "groupId" : "97a3e763eb36484ca09c4c1ac033c133",
  "name" : "SwitchDemo",
  "createTime" : null,
  "updateTime" : 1734342464756,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "sd",
  "method" : "GET",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 200,\n    \"status\": 0,\n    \"message\": \"系统内部出现错误\"\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import com.jp.med.pms.modules.analysis.service.read.impl.PmsInseqiEfftReadServiceImpl



PmsInseqiEfftReadServiceImpl.test()



import log; //org.slf4j.Logger
// 使用方法与SLF4J完全一致
log.info('Hello');
log.info('Hello {}','MagicAPI');
log.debug('test');

return db.table('act_hi_identitylink')
    .select()