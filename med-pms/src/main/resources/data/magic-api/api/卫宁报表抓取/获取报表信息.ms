{
  "properties" : { },
  "id" : "8a5027e4e3dc4e5aa2547b2092d71bbd",
  "script" : null,
  "groupId" : "97a3e763eb36484ca09c4c1ac033c133",
  "name" : "获取报表信息",
  "createTime" : null,
  "updateTime" : 1744017235608,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "fetchReportLoadReportPane",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "{\n  \"reportId\":\"2586\",\n  \"queryParams\":{}\n}",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 200,\n    \"status\": 0,\n    \"message\": \"success\",\n    \"data\": 123\n}",
  "description" : null,
  "requestBodyDefinition" : {
    "name" : "root",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "reportId",
      "value" : "",
      "description" : "",
      "required" : true,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "queryParams",
      "value" : "",
      "description" : "",
      "required" : false,
      "dataType" : "Object",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  },
  "responseBodyDefinition" : {
    "name" : "",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "code",
      "value" : "200",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "status",
      "value" : "0",
      "description" : "",
      "required" : false,
      "dataType" : "Integer",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "message",
      "value" : "success",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    }, {
      "name" : "data",
      "value" : "reportId空",
      "description" : "",
      "required" : false,
      "dataType" : "String",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  }
}
================================
import cn.hutool.json.JSONUtil
import org.springframework.data.redis.core.StringRedisTemplate

import cn.hutool.json.JSONArray
import cn.hutool.json.JSONBeanParser
import WinningReportExporter
import cn.hutool.json.JSONObject
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import cn.hutool.core.util.StrUtil;
import java.util.concurrent.TimeUnit;
// var @value:qwe = 123
if (StrUtil.isBlank(body.reportId)) {
    return "reportId空"
}

var queryParams=body.queryParams
if (!queryParams) {
    queryParams = new JSONObject()

    // 获取当前日期
    LocalDate today = LocalDate.now();

    // 获取上个月的第一天
    LocalDate firstDayOfLastMonth = today.minusMonths(1).withDayOfMonth(1);

    // 获取上个月的最后一天
    LocalDate lastDayOfLastMonth = today.minusMonths(1).withDayOfMonth(today.minusMonths(1).lengthOfMonth());

    // 定义日期格式
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    // 格式化日期
    String firstDayFormatted = firstDayOfLastMonth.format(formatter);
    String lastDayFormatted = lastDayOfLastMonth.format(formatter);


    queryParams.put("KSRQ", firstDayFormatted)
    queryParams.put("JSRQ", lastDayFormatted)
}
var key = "fetchReportLoadReportPane"+body.reportId

// var result = WinningReportExporter.fetchReport("2349", queryParams)
var cache  = StringRedisTemplate.opsForValue().get(key)
if(cache){
    return cache::json
}
var result = WinningReportExporter.fetchReportLoadReportPane(body.reportId)
StringRedisTemplate.opsForValue().set(key,JSONUtil.toJsonStr(result))
StringRedisTemplate.expire(key,300,TimeUnit.MINUTES)

return result