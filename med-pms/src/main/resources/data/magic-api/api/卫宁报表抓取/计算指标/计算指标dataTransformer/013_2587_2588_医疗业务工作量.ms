{
  "properties" : { },
  "id" : "229d430e53324d22a0661bfec927cbcc",
  "script" : null,
  "groupId" : "d71e2d6ea3a24ad48a50217b00c2aa6f",
  "name" : "013_2587_2588_医疗业务工作量",
  "createTime" : null,
  "updateTime" : 1737509597208,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer_2587_2588",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "headers" : [ ],
  "paths" : [ ],
  "description" : "",
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import java.time.temporal.ChronoUnit
import java.util.regex.Pattern
import java.time.LocalDate
import java.time.Duration
import java.time.LocalDateTime
import com.jp.med.pms.util.HolidayUtils
import java.time.LocalTime

import java.time.format.DateTimeFormatter

import java.math.BigDecimal

import java.util.stream.Collectors
import cn.hutool.core.util.StrUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSON
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import '@post:/WinningReportFetch/extractData0' as extractData;
import java.util.ArrayList



// // 医技科室项目统计

// let trigger1 = {
//     "type": "magic-api",
//     "magicApi": {
//         "path": "/WinningReportFetch/extractData",
//         "method": "POST",
//         "body": {
//             "reportId": "2587",
//             "paramsPath": ".queryParams",
//             "queryParams": {
//                 "WneportExcelConfigGroupColumnName": "科室名称",
//                 "TJFW": "0",
//                 "KSDM": "-1",
//                 "DXMDM": "-1",
//                 "XMDM": "-1",
//                 "KSRQ": "2024-12-01",
//                 "JSRQ": "2024-12-31",
//                 // // "specifyOssFile": "/wnReport/2025/01/06/1515/wnbb_ygb_治疗项目统计.cpt.xlsx",
//                 // "specifyOssFile": "/wnReport/2025/01/08/1424/wnbb_ygb_医技科室项目统计.cpt.xlsx",
//                 "bucket": "pms"
//             }
//         }
//     }
// }

// // ,会计项目统计

// let trigger2 = {
//     "type": "magic-api",
//     "magicApi": {
//         "path": "/WinningReportFetch/extractData",
//         "method": "POST",
//         "body": {
//             "reportId": "2588",
//             "paramsPath": ".queryParams",
//             "queryParams": {
//                 "WneportExcelConfigGroupColumnName": "执行科室",
//                 "TJFW": "0",
//                 "KDORZX": "1",
//                 "KSDM": "-1",
//                 "DXMDM": "-1",
//                 "XMDM": "-1",
//                 "KSRQ": "2024-12-01",
//                 "JSRQ": "2024-12-31",
//                 // "specifyOssFile": "/wnReport/2025/01/06/1525/wnbb_ygb_治疗项目统计.cpt.xlsx",
//                 "bucket": "pms"
//             }
//         }
//     }
// }


// body = {
//     trigger: trigger1
// }
// const resData1 = extractData()


// body = {
//     trigger: trigger2
// }



// const resData2 = extractData()

// var data2587 = resData1.jsonData

// var data2588 = resData2.jsonData
let data2587 = body.extractedData.get("2587").get("jsonData")::json
let data2588 = body.extractedData.get("2588").get("jsonData")::json


var result = {
    yzrc: {}
}

/**
 * 指标名称: 医疗业务工作量
 * 指标代码: ylywgzl
 * 数据来源: 
 *   - 医技科室项目统计 (报表ID: 2587)
 *   - 会计项目统计 (报表ID: 2588)
 * 统计逻辑:
 *   1. 统计临床科室开单产生的住院患者工作量：
 *      - 由医技科室（CT室、DR摄影室、超声诊断室、磁共振成像室、检验科、病理科）接收执行的医嘱人次。
 *      - 根据人次和各个医嘱项目的点值计算总点值。
 *      - 用总点值计算医护指标奖励额：
 *        - 医生：总点值 * 2.5 奖励值。
 *        - 护理：总点值 * 4.5 奖励值。
 *   2. 数据来源报表：
 *      - 卫宁报表运管部下“医技科室项目统计”报表。
 *      - 病理科数据在“会计项目统计”报表。
 *   3. 急诊科特殊逻辑：
 *      - 根据“门诊医生分科药品统计表”计算：
 *        - （总金额 - 药品费用）* 0.015 / 0.8。
 *      - 后续将迁移至卫宁报表。
 *   4. 特殊科室奖励：
 *      - 儿科、感染科医护比其他科室多 0.5 元，模板有系数。
 * 指标类型: 工作量-成本控制指标
 * 统计周期: 每月
 * 适用对象: 各科室医护
 */

/**
 * 计算医疗业务工作量指标。
 * <p>
 * 该指标用于衡量医院各科室的医疗业务工作量，并作为医护指标奖励的依据之一。
 * 指标分为医技科室和急诊科两种计算方式。
 * </p>
 *
 */
// public class MedicalBusinessWorkload {

//医技科室
let medicalTechDepartments = [
    "CT室",
    "DR摄影室",
    "超声诊断室",
    "磁共振成像室",
    "检验科",
    "病理科"
];



/**
 * 根据科室类型计算医疗业务工作量。
 *
 * @param departmentType 科室类型，例如："医技科室", "急诊科"
 * @param params       计算所需的参数，具体参数取决于科室类型
 * @return 计算得到的医疗业务工作量
 */
// public double calculateWorkload(String departmentType, Object params) {
//     if ("医技科室".equals(departmentType)) {
//         return calculateMedicalTechnologyWorkload(params);
//     } else if ("急诊科".equals(departmentType)) {
//         return calculateEmergencyDepartmentWorkload(params);
//     } else {
//         throw new IllegalArgumentException("不支持的科室类型：" + departmentType);
//     }
// }
var calculateWorkloadFn = (deptName, detailList) => {
    // 医技科室
    if (medicalTechDepartments.contains(deptName)) {
        var yzrc = detailList.steam().reduce(0, (a, b) => {
            return a + b.get("医嘱人次").asInt(0)
        })
        result.yzrc.put(deptName, yzrc)
    }
    if (deptName.equals("病理科")) {
        var yzrc = detailList.steam().reduce(0, (a, b) => {
            return a + b.get("医嘱人次").asInt(0)
        })
        result.yzrc.put(deptName, yzrc)
    }
    // 急诊科
}

/**
 * 计算医技科室的医疗业务工作量。
 * <p>
 * 数据来源于卫宁报表的“医技科室项目统计”和“会计项目统计”报表（病理科数据）。
 * 数据项 ID 可能为 2587 和 2588。
 * </p>
 * <p>
 * 计算步骤：
 * <ol>
 *     <li>统计临床科室开单产生的住院患者工作量。</li>
 *     <li>获取医技科室（CT室、DR摄影室、超声诊断室、磁共振成像室、检验科）接收执行的医嘱人次。</li>
 *     <li>获取病理科接收执行的医嘱人次（数据来源于“会计项目统计”报表）。</li>
 *     <li>根据人次和各个医嘱项目的点值算出总点值。</li>
 *     <li>根据总点值计算医护指标奖励额：
 *         <ul>
 *             <li>医生：总点值 * 2.5</li>
 *             <li>护理：总点值 * 4.5</li>
 *         </ul>
 *     </li>
 * </ol>
 * </p>
 *
 * @param params  包含计算医技科室工作量所需的参数，例如医嘱人次和项目点值信息
 * @return 医技科室的医疗业务工作量
 * @see #calculateEmergencyDepartmentWorkload(Object)
 * @see <a href="[卫宁报表链接或说明，如果知道的话]">卫宁报表 - 运管部 - 医技科室项目统计</a>
 * @see <a href="[卫宁报表链接或说明，如果知道的话]">卫宁报表 - 运管部 - 会计项目统计</a>
 */
// private double calculateMedicalTechnologyWorkload(Object params) {
//     // TODO: 根据参数实现医技科室工作量的计算逻辑
//     return 0.0;
// }

/**
 * 计算急诊科的医疗业务工作量。
 * <p>
 * 计算公式：根据“门诊医生分科药品统计表”（总金额 - 药品费用）* 0.015 / 0.8 得出。
 * 后续将迁移至卫宁报表。
 * </p>
 * <p>
 * 特殊说明：
 * <ul>
 *     <li>儿科，感染科医护比其他多 0.5 元，模版有系数。</li>
 * </ul>
 * </p>
 *
 * @param params 包含计算急诊科工作量所需的参数，例如总金额和药品费用信息
 * @return 急诊科的医疗业务工作量
 * @see #calculateMedicalTechnologyWorkload(Object)
 * @see <a href="[门诊医生分科药品统计表链接或说明，如果知道的话]">门诊医生分科药品统计表</a>
 */
// private double calculateEmergencyDepartmentWorkload(Object params) {
//     // TODO: 根据参数实现急诊科工作量的计算逻辑
//     return 0.0;
// }

// }

return {
    mzssrsResult
}