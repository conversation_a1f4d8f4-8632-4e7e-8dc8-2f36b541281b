{
  "properties" : { },
  "id" : "copy1736736984720d21356",
  "script" : null,
  "groupId" : "24f1c8948f1943819669e1f42cb3918b",
  "name" : "2766门诊人次急",
  "createTime" : null,
  "updateTime" : 1738895645902,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer_2766_mzrcj",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "headers" : [ ],
  "paths" : [ ],
  "description" : "",
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
// import com.jp.med.pms.bo.WinningReportExporter
import WinningReportExporter
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import java.math.BigDecimal;
import '@post:/WinningReportFetch/extractData0' as extractData;
import com.jp.med.pms.util.HolidayUtils

// var winningReportExporter = new WinningReportExporter()
// return winningReportExporter.login(winningReportExporter.username, winningReportExporter.password);

let trigger = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2766",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "科室名称",
                "returnArrayData": true,
                "KSRQ": "2024-12-01",
                "JSRQ": "2024-12-31",
                // "specifyOssFile": "/wnReport/2025/01/06/1525/挂号统计报表.xlsx",
                "bucket": "pms"

            }
        }
    }
}

body = {
    trigger
}
const resData = extractData()

let data = resData.jsonData



const result = {}

/**
 * 报表：门诊人次（急）(Emergency Outpatient Visits) - "挂号统计报表" (Registration Statistics Report)
 * 描述：统计特定科室的挂号总人次和收费总额，包括加急诊耳鼻咽喉头颈外科和急诊眼科。
 * 服务类型：服务效率/质量指标 (Service Efficiency/Quality Indicator)
 * 适用范围：适用于耳鼻咽喉头颈外科和眼科医生。
 * 原始数据：
 * 门诊人次（急）	mzrcj	挂号统计报表	2766
 * “挂号统计报表”，统计科室收费合计挂号人次。	服务效率/质量指标	加急诊耳鼻咽喉头颈外科、急诊眼科的收费合计挂号人次。	每月	该指标适用耳鼻咽喉头颈外科、眼科医生。
 */
var mzrcjResult = {}
var eyeRequiredDeptNames = [
    "眼科门诊", "急诊眼科"
]
var ENTRHequiredDeptNames = [
    "耳鼻咽喉头颈外科门诊",
    "急诊耳鼻咽喉头颈外科"
]

data.forEach((deptName, detailList) => {
    var registrationCount
    if (eyeRequiredDeptNames.contains(deptName)) {
        registrationCount = detailList.stream()
            .reduce(0, (a, b) => {
                return a + b.get("收费合计挂号人次").asInt(0)
            })
        mzrcjResult.compute("眼科门诊", (k, v) => {
            if (v != null) {
                return registrationCount + v
            } else {
                return registrationCount
            }
        })

    }
    if (ENTRHequiredDeptNames.contains(deptName)) {

        registrationCount = detailList.stream()
            .reduce(0, (a, b) => {
                return a + b.get("收费合计挂号人次").asInt(0)
            })
        mzrcjResult.compute("耳鼻咽喉头颈外科门诊", (k, v) => {
            if (v != null) {
                return registrationCount + v
            } else {
                return registrationCount
            }
        })
    }
})





return {

    mzrcjResult
}