{
  "properties" : { },
  "id" : "copy1735895896510d96723",
  "script" : null,
  "groupId" : "24f1c8948f1943819669e1f42cb3918b",
  "name" : "2769临床科室门诊手术费",
  "createTime" : null,
  "updateTime" : 1745822335980,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer_2769",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "headers" : [ ],
  "paths" : [ ],
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================

import java.time.temporal.ChronoUnit
import java.util.regex.Pattern
import java.time.LocalDate
import java.time.Duration
import java.time.LocalDateTime
import com.jp.med.pms.util.HolidayUtils
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.math.BigDecimal
import java.util.stream.Collectors
import cn.hutool.core.util.StrUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSON
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import '@post:/WinningReportFetch/extractData0' as extractData;
import java.util.ArrayList

// 门诊科室匹配表
let deptMapping = {
    "产科门诊": "妇产科",
    "城南院区妇产科门诊": "城南院区妇产科门诊",
    "妇科门诊": "妇产科",
    "妇科专家门诊": "妇科专家门诊",
    "肝胆胰外科门诊": "肝胆胰外科",
    "泌尿外科门诊": "泌尿外科",
    "泌尿外科日间手术门诊": "0",
    "皮肤科门诊": "皮肤科",
    "普通外科门诊": "神经外科",
    "乳腺科门诊": "肝胆胰外科",
    "疼痛科门诊": "疼痛科",
    "胃肠外科、疝与腹壁外科门诊": "胃肠外科",
    "肛肠科门诊": "肛肠科门诊",
    "麻醉疼痛科门诊": "麻醉疼痛科门诊",
    "脱发门诊": "皮肤科"
}

// 触发器和数据提取
let trigger = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2769",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "科室名称",
                "KSRQ": "2024-12-01 00:00:00",
                "JSRQ": "2024-12-31 23:59:59",
                // "YYDM": "01",
                // // "specifyOssFile": "wnReport/2025/01/03/wnbb_ygb_出区病人结算状态管理.cpt.xlsx",
                // "bucket": "pms"
            }
        }
    }
}

body = {
    trigger
}
const resData = extractData()

let data = resData.jsonData

// 合并科室名称
let mergedData = {}
data.forEach((dept, detailList) => {
    let matchedDept = deptMapping.get(dept) || dept // 如果找不到匹配科室，则使用原始科室名称
    if (matchedDept != "0") { // 过滤掉匹配科室为 "0" 的数据
        if (!mergedData.containsKey(matchedDept)) {
            mergedData.put(matchedDept, new ArrayList())
        }
        mergedData.get(matchedDept).addAll(detailList)
    }
})

// return mergedData

// 计算结果
let result = {}

let obstetricsClinicList = [
    "妇产科", "城南院区妇产科门诊", "妇科专家门诊" // 注意：这里使用匹配后的科室名称
]
let painDepartmentList = [
    "麻醉疼痛科门诊"
]

// 临床科室门诊手术费
let clinicalDepartmentOutpatientSurgeryFeeResult = {}
// 手术费
let operationFeeResult = {}
mergedData.forEach((dept, detailList) => {
    let clinicalDepartmentOutpatientSurgeryFee = BigDecimal.ZERO

    if (obstetricsClinicList.contains(dept)) {
        // 妇产科类门诊：临床科室门诊手术费 = 总费用 - 高值卫材 - 低值卫材 - 不插管全身麻醉
        clinicalDepartmentOutpatientSurgeryFee = detailList.stream()
            .reduce(BigDecimal.ZERO, (a, b) ->
                a.add(b.get("总费用").asDecimal(BigDecimal.ZERO)
                    .subtract(b.get("高值卫材").asDecimal(BigDecimal.ZERO))
                    .subtract(b.get("低值卫材").asDecimal(BigDecimal.ZERO))
                    .subtract(b.get("不插管全身麻醉").asDecimal(BigDecimal.ZERO))
                )
            );
    } else {
        // 其他科室：临床科室门诊手术费 = 总费用 - 高值卫材 - 低值卫材
        clinicalDepartmentOutpatientSurgeryFee = detailList.stream()
            .reduce(BigDecimal.ZERO, (a, b) ->
                a.add(b.get("总费用").asDecimal(BigDecimal.ZERO)
                    .subtract(b.get("高值卫材").asDecimal(BigDecimal.ZERO))
                    .subtract(b.get("低值卫材").asDecimal(BigDecimal.ZERO))
                )
            );
    }

    BigDecimal surgeryFee = BigDecimal.ZERO;
    if (obstetricsClinicList.contains(dept)) {
        // 妇产科类门诊：手术费 = 总费用
        surgeryFee = detailList.stream()
            .reduce(BigDecimal.ZERO, (a, b) ->
                a.add(b.get("总费用").asDecimal(BigDecimal.ZERO))
            );
    } else if (!painDepartmentList.contains(dept)) {
        // 其他科室（疼痛科除外）：手术费 = 总费用 - （神经麻醉 / 2）
        // 疼痛科不参与计算，手术费默认为 0
        surgeryFee = detailList.stream()
            .reduce(BigDecimal.ZERO, (a, b) ->
                a.add(b.get("总费用").asDecimal(BigDecimal.ZERO)
                    .subtract(b.get("神经阻滞麻醉").asDecimal(BigDecimal.ZERO)
                        .divide(BigDecimal.valueOf(2), BigDecimal.ROUND_HALF_UP))
                )
            );
    }
    clinicalDepartmentOutpatientSurgeryFeeResult.put(dept, clinicalDepartmentOutpatientSurgeryFee)
    operationFeeResult.put(dept, surgeryFee)
})

return {
   mzssgzl: clinicalDepartmentOutpatientSurgeryFeeResult,
    operationFeeResult
}