{
  "properties" : { },
  "id" : "copy1736321557681d66450",
  "script" : null,
  "groupId" : "24f1c8948f1943819669e1f42cb3918b",
  "name" : "2771诊查费奖励",
  "createTime" : null,
  "updateTime" : 1736412859335,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer_2771",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "headers" : [ ],
  "paths" : [ ],
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import java.time.temporal.ChronoUnit
import java.util.regex.Pattern
import java.time.LocalDate
import java.time.Duration
import java.time.LocalDateTime
// import com.jp.med.pms.util.HolidayUtils
import java.time.LocalTime

import java.time.format.DateTimeFormatter

import java.math.BigDecimal

import java.util.stream.Collectors
import cn.hutool.core.util.StrUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSON
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import '@post:/WinningReportFetch/extractData0' as extractData;
import java.util.ArrayList



/**
 * 诊查费奖励
 * 项目代码: zcfjl
 * 数据来源: 坐诊时间查询
 * 项目ID: 2771
 * 状态: 缺少信息, 进行中
 * 计算公式: 
 *   1. 专科门诊周一到周五满勤的情况下，周末的诊查费剔除已提的“门诊挂号奖励”后，诊查费全部核算至相应科室。
 *   2. 目前使用外挂报表运管办下的“坐诊时间”查询，后续将迁移至卫宁报表。
 *   3. 根据科室在岗医生的不同：
 *      - 8个医生以下（含8个）需完成5日工作量；
 *      - 8个以上医生需完成10日工作量。
 *   4. 人工判断并与门诊部主任对排班后决定奖励。
 * 指标类型: 管理效能指标
 * 统计频率: 每月
 * 适用范围: 该指标适用于各科室医生。
 * 实现需求:
 *   1. 需要查询医生明细；
 *   2. 可做成后台维护各科室医生人员数，自动判断；或者重新设计统计表单。
 */
let trigger = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2771",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "科室名称",
                "KSRQ": "2024-12-01 00:00:00",
                "JSRQ": "2024-12-31 23:59:59",
                // "specifyOssFile": "/wnReport/2025/01/09/1622/wnbb_yyb_坐诊时间查询.cpt.xlsx",
                "returnArray": true,
                "queryTimeout": 600000,
                "bucket": "pms"
            }
        }
    }
}




body = {
    trigger
}
const resData = extractData()

let data = resData.jsonData

print(data.便民门诊::stringify)

data.便民门诊.stream().forEach(item => {
    // item.工作量 
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    // 将字符串解析为LocalDate对象
    LocalDate date = LocalDate.parse(item.get("日期"), formatter);
    // 获取今天是星期几
    DayOfWeek dayOfWeek = date.getDayOfWeek();
    // 输出星期几
    log.info("今天是星期: " + (dayOfWeek.ordinal() + 1));
})


// 统计周一到周五的工作量
int workDaysCount = 0; // 工作日总数
int fullAttendanceDays = 0; // 满勤天数

// for (item: data) {
//     // 解析日期
//     LocalDate date = LocalDate.parse(item.get("日期"), formatter);
//     DayOfWeek dayOfWeek = date.getDayOfWeek();

//     // 判断是否为周一到周五
//     if (dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY) {
//         workDaysCount++; // 工作日总数加1

//         // 获取工作量（假设工作量为整数）
//         int workload = Integer.parseInt(item.get("工作量"));

//         // 判断是否满勤（假设满勤标准是工作量 >= 8）
//         if (workload >= 8) {
//             fullAttendanceDays++; // 满勤天数加1
//         }
//     }
// }

// // 判断是否满勤
// boolean isFullAttendance = (fullAttendanceDays == workDaysCount);

// // 输出结果
// System.out.println("工作日总数: " + workDaysCount);
// System.out.println("满勤天数: " + fullAttendanceDays);
// System.out.println("是否满勤: " + isFullAttendance);
// FileUtil.writeString(JSONUtil.toJsonStr(data), "/Users/<USER>/Downloads/output.json", "UTF-8");


/**
 * 诊查费奖励核算模块
 *
 * 1. 奖励范围：
 *    - 专科门诊在周一到周五满勤的情况下，周末的诊查费剔除已提的“门诊挂号奖励”后，
 *      全部核算至相应科室。
 *
 * 2. 数据来源：
 *    - 目前使用外挂报表（运管办下的“坐诊时间”查询），后续计划迁移至卫宁报表。
 *
 * 3. 工作量要求：
 *    - 科室在岗医生数量不同，工作量要求也不同：
 *      - 8个医生以下（含8个）：需完成5日工作量。
 *      - 8个医生以上：需完成10日工作量。
 *
 * 4. 奖励决策：
 *    - 人工判断科室在岗医生数量及工作量完成情况。
 *    - 与门诊部主任对排班后，决定奖励分配。
 *
 * 5. 优化建议：
 *    - 自动化数据提取：迁移至卫宁报表系统，确保数据统一且实时更新。
 *    - 工作量计算规则化：根据医生数量自动判断工作量要求。
 *    - 奖励分配自动化：系统自动核算奖励，提供人工审核功能。
 *    - 排班与奖励联动：排班数据实时同步，标注满勤医生。
 *    - 报表与可视化：提供可视化报表，支持导出功能。
 *
 * 6. 实施步骤：
 *    - 需求确认：与相关部门确认需求。
 *    - 系统开发：在卫宁报表中开发“诊查费奖励”模块。
 *    - 测试与上线：小范围测试后正式上线。
 *    - 培训与支持：对相关人员进行培训，提供技术支持。
 *
 * 示例流程：
 *    开始
 *      ↓
 *    提取周末诊查费数据（剔除门诊挂号奖励）
 *      ↓
 *    判断科室在岗医生数量
 *      ↓
 *      ├── 8人以下：检查是否完成5日工作量
 *      └── 8人以上：检查是否完成10日工作量
 *      ↓
 *    核算诊查费奖励
 *      ↓
 *    门诊部主任审核
 *      ↓
 *    奖励分配至科室
 *      ↓
 *    结束
 */