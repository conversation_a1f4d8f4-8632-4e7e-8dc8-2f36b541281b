{
  "properties" : { },
  "id" : "copy1733996284889d81400",
  "script" : null,
  "groupId" : "97a3e763eb36484ca09c4c1ac033c133",
  "name" : "抽取卫宁报表数据",
  "createTime" : null,
  "updateTime" : 1735089779418,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "extractData",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 200,\n    \"status\": 0,\n    \"message\": \"系统内部出现错误\"\n}",
  "description" : null,
  "requestBodyDefinition" : {
    "name" : "root",
    "value" : "",
    "description" : "",
    "required" : false,
    "dataType" : "Object",
    "type" : null,
    "defaultValue" : null,
    "validateType" : "",
    "error" : "",
    "expression" : "",
    "children" : [ {
      "name" : "trigger",
      "value" : "",
      "description" : "",
      "required" : false,
      "dataType" : "Object",
      "type" : null,
      "defaultValue" : null,
      "validateType" : "",
      "error" : "",
      "expression" : "",
      "children" : [ ]
    } ]
  },
  "responseBodyDefinition" : null
}
================================
import com.jp.med.common.exception.AppException
import cn.hutool.json.JSONArray
import cn.hutool.json.JSONBeanParser
import WinningReportExporter
import cn.hutool.json.JSONObject
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import cn.hutool.core.util.StrUtil;
import log

import '@/wn/variableTransformation' as variableTransformation;

log.info("抽取卫宁报表数据 ：body：{}",body)

let startTime = System.currentTimeMillis()

let triggerBody = variableTransformation(body)

let result = WinningReportExporter.fetchReport(triggerBody.reportId, triggerBody.queryParams)
let saveIPmsEtlData = {
    itemCode: triggerBody.reportId,
    data: {},
    jsonData: {},
    filePath: 'string',
    capturedBy: 'system',
    sourceName: 'string',
    captureStatus: 'success', // 采集状态
    comment: 'string',
    fileOssUrl,
    startTime,
    endTime
}
let endTime = System.currentTimeMillis()
saveIPmsEtlData.fileOssUrl = result.fileOssUrl
saveIPmsEtlData.put("jsonData", result.get("result"))
saveIPmsEtlData.put("data", result.get("result")::stringify)

return saveIPmsEtlData
