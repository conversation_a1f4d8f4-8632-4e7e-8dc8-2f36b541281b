{
  "properties" : { },
  "id" : "71aea1fcf362429289a994770a9e6da3",
  "script" : null,
  "groupId" : "df6ef5dd493847718c35b492f2c65a1e",
  "name" : "下载缓存的文件",
  "createTime" : null,
  "updateTime" : 1747365814371,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "downloadCacheFile",
  "description" : null,
  "returnType" : null,
  "mappingPath" : null,
  "parameters" : [ {
    "name" : "body",
    "value" : null,
    "description" : null,
    "required" : false,
    "dataType" : "String",
    "type" : "java.lang.String",
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ]
}
================================
import org.springframework.data.redis.core.StringRedisTemplate
import cn.hutool.crypto.digest.DigestUtil




// // 获取同查询参数已下载的Excel文件
var sha256HexKey = "pmsETL:ossFileCache:" + DigestUtil.sha256Hex(body);
String ossFilePath = stringRedisTemplate.opsForValue()
    .get(sha256HexKey);


return ossFilePath
