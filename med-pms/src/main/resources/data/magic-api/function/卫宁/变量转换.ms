{
  "properties" : { },
  "id" : "159dfbd826794f4ca10717e2191d6e02",
  "script" : null,
  "groupId" : "1e314e3ba8524a5e8d6c4875d545ba49",
  "name" : "变量转换",
  "createTime" : null,
  "updateTime" : 1747123672680,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "variableTransformation",
  "description" : null,
  "returnType" : null,
  "mappingPath" : null,
  "parameters" : [ {
    "name" : "body",
    "value" : null,
    "description" : null,
    "required" : false,
    "dataType" : "String",
    "type" : null,
    "defaultValue" : null,
    "validateType" : null,
    "error" : null,
    "expression" : null,
    "children" : null
  } ]
}
================================
import cn.hutool.json.JSONArray
import cn.hutool.json.JSONBeanParser
import WinningReportExporter
import cn.hutool.json.JSONObject
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import cn.hutool.core.util.StrUtil;

let variablesMap = {}



const bodyJSONObject = new JSONObject(body)

let needReplaceObj = bodyJSONObject


if (StrUtil.isNotBlank(body.get("paramsPath"))) {
    needReplaceObj = bodyJSONObject.getByPath(body.get("paramsPath"))
    bodyJSONObject.remove("paramsPath")
}

for (v in needReplaceObj.entrySet()) {
    const key = v.getKey()
    const value = v.getValue()
    System.out.println("key:" + key + "value:" + value)
    if (variablesMap.get(value)) {
        System.out.println("替换参数：：" + key + ':' + value + ":" + variablesMap.get(value));
        needReplaceObj[key] = variablesMap.get(value)
    }

}

return bodyJSONObject