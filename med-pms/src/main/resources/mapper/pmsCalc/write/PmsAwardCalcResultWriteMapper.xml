<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.pms.modules.pmsCalc.mapper.write.PmsAwardCalcResultWriteMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.pms.modules.pmsCalc.dto.PmsAwardCalcResultDto"
        id="pmsAwardCalcResultMap">
        <result property="id" column="id" />
        <result property="calcMonth" column="calc_month" />
        <result property="templateId" column="template_id" />
        <result property="templateName" column="template_name" />
        <result property="deptCode" column="dept_code" />
        <result property="deptName" column="dept_name" />
        <result property="totalAwardAmount" column="total_award_amount" />
        <result property="calcStatus" column="calc_status" />
        <result property="errorMessage" column="error_message" />
        <result property="calcDetails" column="calc_details" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <insert id="insertMannagerAwardApportion">
        INSERT INTO pms_manager_apportion (summary_month,
                                           summary_detail,
                                           status,
                                           sign,
                                           att,
                                           att_name,
                                           remark,
                                           creator,
                                           create_time,
                                           active_flag,
                                           hospital_id)
        VALUES (#{calcMonth},
                #{awardApportionDetailsJson},
                #{status},
                #{sign},
                #{att},
                #{attName},
                #{remark},
                #{creator},
                #{createTime},
                #{activeFlag},
                #{hospitalId})

    </insert>

    <delete id="deleteMannagerAwardApportion">
        UPDATE pms_manager_apportion
        SET active_flag = '0'
        WHERE summary_month = #{calcMonth}
          AND hospital_id = #{hospitalId}
    </delete>

</mapper>
