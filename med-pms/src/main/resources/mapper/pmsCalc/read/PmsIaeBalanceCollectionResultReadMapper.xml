<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.pms.modules.pmsCalc.PmsCalcImport.mapper.PmsIaeBalanceCollectionResultReadMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.pms.modules.pmsCalc.PmsCalcImport.vo.PmsIaeBalanceCollectionResultVo"
               id="iaeBalanceCollectionResultMap">
        <result property="id" column="id"/>
        <result property="deptName" column="dept_name"/>
        <result property="hrpDeptCode" column="hrp_dept_code"/>
        <result property="importDeptCode" column="import_dept_code"/>
        <result property="deptCodeVersion" column="dept_code_version"/>
        <result property="metaDataSnapshot" column="meta_data_snapshot"/>
        <result property="itemName" column="item_name"/>
        <result property="itemCode" column="item_code"/>
        <result property="statisticsFrequency" column="statistics_frequency"/>
        <result property="itemValue" column="item_value"/>
        <result property="collectionDate" column="collection_date"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="creatdBy" column="creatd_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="batchCode" column="batch_code"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.pms.modules.pmsCalc.PmsCalcImport.vo.PmsIaeBalanceCollectionResultVo">
        select a.id                   as id,
<!--               a.dept_name            as deptName,-->
               a.hrp_dept_code        as hrpDeptCode,
               ho.org_name as   deptName,
               a.import_dept_code     as importDeptCode,
               a.dept_code_version    as deptCodeVersion,
               a.meta_data_snapshot   as metaDataSnapshot,
<!--               a.item_name            as itemName,-->
               b.item_name            as itemName,
               a.item_code            as itemCode,
               a.statistics_frequency as statisticsFrequency,
               a.item_value           as itemValue,
               a.collection_date      as collectionDate,
               a.hospital_id          as hospitalId,
               a.creatd_by            as creatdBy,
               a.create_time          as createTime,
               a.update_by            as updateBy,
               a.update_time          as updateTime,
               a.is_deleted           as isDeleted,
               a.batch_code           as batchCode
        from pms_iae_balance_collection_result a
        left join hrm_org ho on ho.org_id = a.hrp_dept_code
        left join pms_iae_balance_config b on a.item_code = b.item_code
        <where>
            <if test="id != null">
                and a.id = #{id}
            </if>
            <if test="itemName != null">
                and a.item_name like concat('%', #{itemName}, '%')
            </if>
            <if test="itemCode != null">
                and a.item_code like concat('%', #{itemCode}, '%')
            </if>
            <if test="batchCode != null">
                and a.batch_code = #{batchCode}
            </if>
        </where>
        order by a.id
    </select>

    <select id="queryBalanceReportList" resultType="com.jp.med.pms.modules.pmsCalc.PmsCalcImport.vo.PmsIaeBalanceCollectionResultVo">
        select id                   as id,
               dept_name            as deptName,
               hrp_dept_code        as hrpDeptCode,
               import_dept_code     as importDeptCode,
               dept_code_version    as deptCodeVersion,
               meta_data_snapshot   as metaDataSnapshot,
               item_name            as itemName,
               item_code            as itemCode,
               statistics_frequency as statisticsFrequency,
               item_value           as itemValue,
               collection_date      as collectionDate,
               hospital_id          as hospitalId,
               creatd_by            as creatdBy,
               create_time          as createTime,
               update_by            as updateBy,
               update_time          as updateTime,
               is_deleted           as isDeleted,
               batch_code           as batchCode
        from (
        select *,
               max(id) over (partition by hrp_dept_code, item_code) as max_id
        from pms_iae_balance_collection_result
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="statMonth != null and statMonth != ''">
                and to_char(collection_date, 'YYYY-MM') = #{statMonth}
            </if>
            and is_deleted = false
        </where>
        ) a
        where a.id = a.max_id
        order by id
    </select>
</mapper>
