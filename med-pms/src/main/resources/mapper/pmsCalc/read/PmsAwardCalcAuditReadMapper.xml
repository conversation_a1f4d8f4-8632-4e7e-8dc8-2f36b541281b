<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.pms.modules.pmsFirstAllocation.mapper.PmsAwardCalcAuditReadMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.pms.modules.pmsFirstAllocation.vo.PmsAwardCalcAuditVo" id="awardCalcAuditMap">
        <result property="id" column="id"/>
        <result property="calcMonth" column="calc_month"/>
        <result property="processInstanceCode" column="process_instance_code"/>
        <result property="remark" column="remark"/>
        <result property="creator" column="creator"/>
        <result property="creatorSign" column="creator_sign"/>
        <result property="createTime" column="create_time"/>
        <result property="updator" column="updator"/>
        <result property="updateTime" column="update_time"/>
        <result property="activeFlag" column="active_flag"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.pms.modules.pmsFirstAllocation.vo.PmsAwardCalcAuditVo">
        select a.id                    as id,
               a.calc_month            as calcMonth,
               a.process_instance_code as processInstanceCode,
               a.remark                as remark,
               a.creator               as creator,
               emp.emp_name            as creatorName,
               a.creator_sign          as creatorSign,
               a.create_time           as createTime,
               a.updator               as updator,
               a.update_time           as updateTime,
               a.active_flag           as activeFlag,
               a.hospital_id           as hospitalId
        from pms_award_calc_audit a
                 left join hrm_employee_info emp on a.creator = emp.emp_code
        <where>
            <if test="calcMonth != null and calcMonth != ''">
                and a.calc_month = #{calcMonth}
            </if>
            <if test="status != null and status != ''">
                and a.status = #{status}
            </if>
            <if test="calcYear != null and calcYear != ''">
                and substring(a.calc_month, 1, 4) = #{calcYear}
            </if>
            and a.active_flag = '1'
        </where>
        order by a.calc_month desc
    </select>

    <select id="checkCalcResult" resultType="java.lang.Boolean">
        select case
                   when exists (select id
                                from pms_award_calc_audit
                                where calc_month = #{calcMonth}
                                  and status in ('0', '1')
                                  and active_flag = '1') then 1
                   else 0
                   end as result
    </select>
</mapper>
