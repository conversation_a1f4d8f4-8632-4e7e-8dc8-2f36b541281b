<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.pms.modules.PmsIaeBalance.mapper.read.PmsIaeBalanceReportReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.pms.modules.PmsIaeBalance.vo.PmsIaeBalanceReportVo" id="iaeBalanceReportMap">
        <result property="id" column="id"/>
        <result property="statMonth" column="stat_month"/>
        <result property="deptCode" column="dept_code"/>
        <result property="deptName" column="dept_name"/>
        <result property="medicalServiceIncome" column="medical_service_income"/>
        <result property="drugIncome" column="drug_income"/>
        <result property="otherIncome" column="other_income"/>
        <result property="totalIncome" column="total_income"/>
        <result property="deptVariableCost" column="dept_variable_cost"/>
        <result property="generalMaterialCost" column="general_material_cost"/>
        <result property="medicalMaterialCost" column="medical_material_cost"/>
        <result property="fixedAssetsCost" column="fixed_assets_cost"/>
        <result property="totalExpense" column="total_expense"/>
        <result property="balanceAmount" column="balance_amount"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.pms.modules.PmsIaeBalance.vo.PmsIaeBalanceReportVo">
        select
            *
        from pms_iae_balance_report
    </select>

</mapper>
