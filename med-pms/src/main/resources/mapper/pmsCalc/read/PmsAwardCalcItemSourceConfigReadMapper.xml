<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.pms.modules.pmsCalcTemplate.mapper.read.PmsAwardCalcItemSourceConfigReadMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCalcItemSourceConfigVo"
               id="awardCalcItemSourceConfigMap">
        <result property="id" column="id"/>
        <result property="sourceName" column="source_name"/>
        <result property="sourceCode" column="source_code"/>
        <result property="templateId" column="template_id"/>
        <result property="defaultRefItem" column="default_ref_item"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCalcItemSourceConfigVo">
        select a.id               as id,
               a.source_name      as sourceName,
               a.source_code      as sourceCode,
               a.template_id      as templateId,
               a.default_ref_item as defaultRefItem,
               a.remark           as remark,
               a.item_code        as itemCode,
               CASE
                   WHEN a.default_ref_item = '1' THEN '默认值'
                   ELSE COALESCE(pacic.item_name, '')
                   END            AS itemName
                ,
               a.default_value    as defaultValue
        from pms_award_calc_item_source_config a
                 left join pms_award_calc_item_config pacic on pacic.item_code = a.item_code

        <where>
            <if test="sourceCode != null">
                a.source_code = #{sourceCode}
            </if>
            <if test="templateId != null">
                and a.template_id = #{templateId}
            </if>
            <if test="itemCode != null">
                and a.item_code = #{itemCode}
            </if>
            <if test="defaultRefItem != null">
                and a.default_ref_item = #{defaultRefItem}
            </if>
        </where>
    </select>

    <select id="queryUseItemSourceTemplate" resultType="com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCalcDeptTemplateVo">
        select distinct pacdt.id, pacdt.template_dept
        from pms_award_calc_item_source_config a
                 left join pms_award_calc_dept_template pacdt on pacdt.id = a.template_id::bigint
        <where>
            <if test="itemCode != null">
                and a.item_code = #{itemCode}
            </if>
        </where>
    </select>
</mapper>
