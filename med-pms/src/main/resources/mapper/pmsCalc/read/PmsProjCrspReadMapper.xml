<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.pms.modules.pmsCalc.mapper.read.PmsProjCrspReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.pms.modules.PmsIaeBalance.vo.PmsProjCrspVo" id="projCrspMap">
        <result property="drordCode" column="drord_code"/>
        <result property="chrgitmCode" column="chrgitm_code"/>
        <result property="memo" column="memo"/>
        <result property="loc" column="loc"/>
        <result property="itemNum" column="item_num"/>
        <result property="majclsCode" column="majcls_code"/>
        <result property="ybsl" column="ybsl"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.pms.modules.PmsIaeBalance.vo.PmsProjCrspVo">
        select
            drord_code as drordCode,
            chrgitm_code as chrgitmCode,
            memo as memo,
            loc as loc,
            item_num as itemNum,
            majcls_code as majclsCode,
            ybsl as ybsl
        from pms_proj_crsp
    </select>

</mapper>
