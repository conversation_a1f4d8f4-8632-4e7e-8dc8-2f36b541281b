<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jp.med.pms.modules.pmsCalc.mapper.read.PmsUploadReminderReadMapper">
    <sql id="Base_Column_List">
        id,
        hospital_id,
        pms_data_mapping_type,
        item_name,
        emp_id,
        emp_name,
        dept_id,
        dept_name,
        reminder_time,
        reminder_message,
        reminder_status,
        month_period,
        created_at,
        updated_at,
        created_by,
        updated_by
    </sql>

    <!-- 查询基础SQL -->
    <sql id="selectSql">
        SELECT
        <include refid="Base_Column_List"/>
        FROM pms_upload_reminder
        <where>

            <if
                    test="dto.pmsDataMappingType != null and dto.pmsDataMappingType != ''">
                AND pms_data_mapping_type = #{dto.pmsDataMappingType}
            </if>
            <if
                    test="dto.itemName != null and dto.itemName != ''">
                AND item_name LIKE CONCAT('%',
                                          #{dto.itemName}, '%')
            </if>
            <if test="dto.empId != null and dto.empId != ''">
                AND emp_id =
                    #{dto.empId}
            </if>
            <if test="dto.empName != null and dto.empName != ''">
                AND emp_name LIKE
                    CONCAT('%', #{dto.empName}, '%')
            </if>
            <if test="dto.deptId != null and dto.deptId != ''">
                AND dept_id = #{dto.deptId}
            </if>
            <if test="dto.deptName != null and dto.deptName != ''">
                AND dept_name LIKE CONCAT('%', #{dto.deptName}, '%')
            </if>
            <if
                    test="dto.reminderStatus != null and dto.reminderStatus != ''">
                AND reminder_status
                    = #{dto.reminderStatus}
            </if>
            <if test="dto.monthPeriod != null and dto.monthPeriod != ''">
                AND month_period = #{dto.monthPeriod}
            </if>
            <if
                    test="dto.startTime != null and dto.startTime != ''">
                AND reminder_time &gt;=
                    TO_TIMESTAMP(#{dto.startTime}, 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if
                    test="dto.endTime != null and dto.endTime != ''">
                AND reminder_time &lt;=
                    TO_TIMESTAMP(#{dto.endTime}, 'YYYY-MM-DD HH24:MI:SS')
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="queryPage" resultType="com.jp.med.pms.modules.pmsCalc.dto.PmsUploadReminderDto">
        <include refid="selectSql"/>
        ORDER BY reminder_time DESC
    </select>

    <!-- 列表查询 -->
    <select id="queryList" resultType="com.jp.med.pms.modules.pmsCalc.dto.PmsUploadReminderDto">
        <include refid="selectSql"/>
        ORDER BY reminder_time DESC
    </select>

    <!-- 根据ID查询 -->
    <select id="queryById" resultType="com.jp.med.pms.modules.pmsCalc.dto.PmsUploadReminderDto">
        SELECT
        <include refid="Base_Column_List"/>
        FROM pms_upload_reminder
        WHERE id = #{id}
    </select>

    <!-- 科室查询辅助方法：用于在Java代码中调用后处理（仅包含基础SQL，具体的科室过滤在Java中进行） -->
    <select id="queryByDeptInfo"
            resultType="com.jp.med.pms.modules.pmsCalc.dto.PmsUploadReminderDto">
        SELECT
        <include
                refid="Base_Column_List"/>
        FROM pms_upload_reminder
        <where>
            <if test="pmsDataMappingType != null and pmsDataMappingType != ''">
                AND
                pms_data_mapping_type = #{pmsDataMappingType}
            </if>
            <if
                    test="itemName != null and itemName != ''">
                AND item_name LIKE CONCAT('%',
                                          #{itemName}, '%')
            </if>
        </where>
        ORDER BY reminder_time DESC
    </select>
</mapper>