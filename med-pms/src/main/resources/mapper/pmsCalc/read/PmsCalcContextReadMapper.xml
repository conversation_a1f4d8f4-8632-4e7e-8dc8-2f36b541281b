<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jp.med.pms.modules.pmsCalc.mapper.read.PmsCalcContextReadMapper">
    <select id="getHumanCostAnalysis" resultType="java.util.Map">
        select ff_mth     as "ffMonth",
               should_pay as "shouldPay",
               reduce_pay as "reducePay",
               real_pay   as "realPay",
               entp_pay   as "entpPay"
        from hrm_salary_really_task
        where active_flag = '1'
          and substring(ff_mth, 1, 4) = #{calcYear}
    </select>
</mapper>
