<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.pms.modules.pmsCalcTemplate.mapper.read.PmsAwardCalcDeptTemplateReadMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCalcDeptTemplateVo"
               id="awardCalcDeptTemplateMap">
        <result property="id" column="id"/>
        <result property="templateName" column="template_name"/>
        <result property="templateDept" column="template_dept"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="active" column="active"/>
        <result property="version" column="version"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCalcDeptTemplateVo">
        select a.id            as id,
               a.template_name as templateName,
               a.template_dept as templateDept,
               a.template_type as templateType,
               ho.org_name     as templateDeptName,
               a.award_apportion_emp_code
                               as awardApportionEmpCode,
               emp.emp_name    as awardApportionEmpName,
               a.create_user   as
                                  createUser,
               a.create_time   as createTime,
               a.update_time   as updateTime,
               a.active        as active,
               a.version       as version,
               a.order_integer as orderInteger,
               a.pms_dept_name as pmsDeptName
        from pms_award_calc_dept_template a
                 left join hrm_employee_info emp on emp.emp_code = a.award_apportion_emp_code
                 left join
             hrm_employee_info hei on hei.emp_code = create_user
                 left join hrm_org ho on ho.org_id =
                                         template_dept
            
        <where>
            <if test="id != null and id != ''">
                and a.id = #{id}
            </if>

            <!-- 绩效科室名称 -->
            <if test="pmsDeptName != null and pmsDeptName != ''">
                and a.pms_dept_name = #{pmsDeptName}
            </if>
            <!-- 模板名称或科室名称模糊查询 -->
            <if
                    test="templateName != null and templateName != ''">
                and (template_name like
                     concat('%', #{templateName}, '%') or ho.org_name like concat('%', #{templateName}, '%'))
            </if>
            <!-- 模板科室HRP精确匹配 -->
            <if
                    test="templateDept != null and templateDept != ''">
                and template_dept =
                    #{templateDept}
            </if>
            <!-- 模板类型精确匹配 -->
            <if test="templateType != null and templateType != ''">
                and template_type = #{templateType}
            </if>
            <!-- 创建人精确匹配 -->
            <if test="createUser != null and createUser != ''">
                and a.create_user = #{createUser}
            </if>

            <!-- 是否启用状态查询 -->
            <if test="active != null and active != ''">
                and a.active
                    = #{active}
            </if>
            <!-- 版本号精确匹配 -->
            <if test="version != null">
                and a.version = #{version}
            </if>
        </where>
        order by a.order_integer asc
    </select>

    <select id="queryMaxVersionList"
            resultType="com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCalcDeptTemplateVo">
        select a.id            as
                                  id,
               a.template_name as templateName,
               a.template_dept as templateDept,
               a.template_type as
                                  templateType,
               ho.org_name     as templateDeptName,
               a.create_user   as createUser,
               a.create_time   as
                                  createTime,
               a.update_time   as updateTime,
               a.active        as active,
               a.version       as version,
               a.order_integer as orderInteger
        from (select *
              from (select *,
                           max(id) over (partition by
                               template_dept,template_type) as max_id
                    from pms_award_calc_dept_template) a
              where id =
                    max_id) a
                 left join hrm_employee_info hei on hei.emp_code = create_user
                 left join hrm_org ho
                           on ho.org_id = template_dept
        <where>
            <!-- 模板名称或科室名称模糊查询 -->
            <if test="templateName != null and templateName != ''">
                and (template_name like concat('%', #{templateName}, '%') or ho.org_name like concat('%',
                                                                                                     #{templateName},
                                                                                                     '%'))
            </if>
            <!-- 模板科室精确匹配 -->
            <if test="templateDept != null and templateDept != ''">
                and template_dept = #{templateDept}
            </if>
            <!-- 模板类型精确匹配 -->
            <if test="templateType != null and templateType != ''">
                and template_type = #{templateType}
            </if>
            <!-- 创建人精确匹配 -->
            <if test="createUser != null and createUser != ''">
                and a.create_user = #{createUser}
            </if>

            <!-- 是否启用状态查询 -->
            <if test="active != null and active != ''">
                and a.active = #{active}
            </if>
            <!-- 版本号精确匹配 -->
            <if test="version != null">
                and a.version = #{version}
            </if>
        </where>
    </select>

    <select id="queryTemplatesByItemCode"
            resultType="com.jp.med.pms.modules.pmsCalcTemplate.dto.PmsAwardCalcDeptTemplateDto">
        SELECT DISTINCT t.id,
                        t.template_name,
                        t.template_dept,
                        ho.org_name as templateDeptName,
                        t.template_type,
                        t.award_apportion_emp_code,
                        t.create_user,
                        t.create_time,
                        t.update_time,
                        t.active,
                        t.version,
                        t.order_integer,
                        c.allow_view_item_detail
        FROM pms_award_calc_dept_template t
                 INNER JOIN
             pms_award_coefficient_config c ON t.id = c.template_id
                 LEFT JOIN hrm_org ho ON
            t.template_dept = ho.org_id
        WHERE c.item_code = #{itemCode}
        ORDER BY t.order_integer ASC,
                 t.id DESC
    </select>


    <select id="queryTemplatesByItemCodeAndBatchCode"
            resultType="com.jp.med.pms.modules.pmsCalcTemplate.dto.PmsAwardCalcDeptTemplateDto">
                    SELECT DISTINCT r.hrp_dept_code,t.id,
                        t.template_name,
                        t.template_dept,
                        ho.org_name as templateDeptName,
                        t.template_type,
                        t.award_apportion_emp_code,
                        t.create_user,
                        t.create_time,
                        t.update_time,
                        t.active,
                        t.version,
                        t.order_integer
        FROM pms_award_calc_dept_template t
                 INNER JOIN
             pms_award_coefficient_config c ON t.id = c.template_id
                 LEFT JOIN hrm_org ho ON

            t.template_dept = ho.org_id
						LEFT JOIN pms_award_collection_result r on r.hrp_dept_code = t.template_dept and r.batch_code = #{batchCode}
        WHERE c.item_code = #{itemCode}
        ORDER BY t.order_integer ASC,
                 t.id DESC
    </select>
</mapper>