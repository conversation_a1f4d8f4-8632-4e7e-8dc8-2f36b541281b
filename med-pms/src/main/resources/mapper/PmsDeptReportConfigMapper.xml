<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jp.med.pms.modules.pmsMonthlyStaffNumberReport.mapper.PmsDeptReportConfigMapper">

    <!-- 检查用户是否有权限上报指定科室和类型的数据 -->
    <select id="checkReportPermission" resultType="boolean">
        SELECT 
            CASE 
                WHEN COUNT(*) > 0 THEN TRUE 
                ELSE FALSE 
            END
        FROM pms_dept_report_config 
        WHERE pms_dept_name = #{pmsDeptName}
          AND hrp_org_id = #{hrpOrgId}
          AND is_enabled = true
          AND (report_type_codes IS NULL 
               OR report_type_codes = '' 
               OR FIND_IN_SET(#{reportTypeCode}, report_type_codes) > 0)
          AND (reporter_emp_codes IS NULL 
               OR reporter_emp_codes = '' 
               OR FIND_IN_SET(#{empCode}, reporter_emp_codes) > 0)
    </select>

    <!-- 查询用户可上报的科室列表 -->
    <select id="queryUserReportableDepts" resultType="com.jp.med.pms.modules.pmsMonthlyStaffNumberReport.dto.PmsDeptReportConfigDto">
        SELECT 
            id,
            pms_dept_name,
            hrp_org_id,
            hrp_org_name,
            report_type_codes,
            reporter_emp_codes,
            reporter_emp_names,
            is_enabled,
            remark,
            hospital_id,
            created_at,
            updated_at,
            created_by,
            updated_by
        FROM pms_dept_report_config 
        WHERE is_enabled = true
          AND (reporter_emp_codes IS NULL 
               OR reporter_emp_codes = '' 
               OR FIND_IN_SET(#{empCode}, reporter_emp_codes) > 0)
        ORDER BY created_at DESC
    </select>

</mapper> 