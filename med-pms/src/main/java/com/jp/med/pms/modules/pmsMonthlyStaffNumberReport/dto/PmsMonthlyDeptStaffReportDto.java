package com.jp.med.pms.modules.pmsMonthlyStaffNumberReport.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 科室月度人员上报DTO (重构版本)
 *
 * <AUTHOR>

 */
@Data
@TableName("pms_monthly_dept_staff_report")
@EqualsAndHashCode(callSuper = true)
public class PmsMonthlyDeptStaffReportDto extends CommonQueryDto {

    @TableId("id")
    private Integer id;

    /**
     * 绩效系统科室名称
     */
    @TableField("pms_dept_name")
    private String pmsDeptName;

    /**
     * HRP系统机构ID
     */
    @TableField("hrp_org_id")
    private String hrpOrgId;

    /**
     * HRP系统机构名称
     */
    @TableField("hrp_org_name")
    private String hrpOrgName;

    /**
     * 上报月份
     */
    @TableField("report_month")
    private Date reportMonth;

    /**
     * 上报类型代码
     */
    @TableField("report_type_code")
    private String reportTypeCode;

    /**
     * 人员类型代码
     */
    @TableField("type_code")
    private String typeCode;

    /**
     * 人员数量
     */
    @TableField("staff_count")
    private Integer staffCount;

    /**
     * 工作天数
     */
    @TableField("work_days")
    private Integer workDays;

    /**
     * 百分比
     */
    @TableField("percentage")
    private BigDecimal percentage;

    /**
     * 计算后人数
     */
    @TableField("calculated_count")
    private BigDecimal calculatedCount;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 批次号
     */
    @TableField("batch_code")
    private String batchCode;

    /**
     * 医院ID
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    @TableField(exist = false)
    @ExcelIgnore
    private boolean sqlAutowiredHospitalCondition = true;

    // 扩展字段
    @TableField(exist = false)
    @ExcelIgnore
    private List<PmsMonthlyDeptStaffReportDto> reportList;

    @TableField(exist = false)
    @ExcelIgnore
    private String typeName;

    @TableField(exist = false)
    @ExcelIgnore
    private String category;

    @TableField(exist = false)
    @ExcelIgnore
    private String calculationMethod;

    @TableField(exist = false)
    @ExcelIgnore
    private Boolean needDaysInput;

    @TableField(exist = false)
    @ExcelIgnore
    private Boolean needPercentageInput;

    @TableField(exist = false)
    @ExcelIgnore
    private BigDecimal defaultPercentage;

    @TableField(exist = false)
    @ExcelIgnore
    private Integer totalDaysOfMonth;

    @TableField(exist = false)
    @ExcelIgnore
    private BigDecimal totalCalculatedCount;
} 