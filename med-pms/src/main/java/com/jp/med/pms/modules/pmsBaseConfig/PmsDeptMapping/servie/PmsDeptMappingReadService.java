package com.jp.med.pms.modules.pmsBaseConfig.PmsDeptMapping.servie;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.pms.modules.pmsBaseConfig.PmsDeptMapping.dto.PmsDeptMappingDto;
import com.jp.med.pms.modules.pmsBaseConfig.PmsDeptMapping.vo.PmsDeptMappingVo;

import java.util.List;
import java.util.Set;

/**
 * PMS科室代码映射表
 *
 * <AUTHOR>
 * @email -
 * @date 2024-11-21 14:42:08
 */
public interface PmsDeptMappingReadService extends IService<PmsDeptMappingDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<PmsDeptMappingVo> queryList(PmsDeptMappingDto dto);

    /**
     * 分页查询列表
     *
     * @param dto
     * @return
     */
    List<PmsDeptMappingVo> queryPageList(PmsDeptMappingDto dto);

    /**
     * 获取来源系统列表
     *
     * @return
     */
    List<String> getSourceSystemList();

    /**
     * 获取所有绩效科室名称列表
     *
     * @return 绩效科室名称集合
     */
    Set<String> getAllPmsNames();
}

