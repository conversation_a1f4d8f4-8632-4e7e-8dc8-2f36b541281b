package com.jp.med.pms.modules.pmsMonthlyStaffNumberReport.requestBody;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 管理员科室人员上报总览请求体
 */
@Data
@ApiModel("管理员科室人员上报总览请求体")
public class AdminStaffOverviewRequestBody {

    @ApiModelProperty(value = "上报月份", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date reportMonth;

    @ApiModelProperty("科室名称列表（可选，为空则查询所有科室）")
    private List<String> deptNames;

    @ApiModelProperty("上报类型代码列表（可选，为空则查询所有类型）")
    private List<String> reportTypeCodes;

    @ApiModelProperty("医院ID")
    private String hospitalId;
} 