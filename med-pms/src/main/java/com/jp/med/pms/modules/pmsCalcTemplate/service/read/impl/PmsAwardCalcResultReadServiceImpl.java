package com.jp.med.pms.modules.pmsCalcTemplate.service.read.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.exception.AppException;
import com.jp.med.pms.modules.pmsCalc.dto.PmsAwardCalcResultDto;
import com.jp.med.pms.modules.pmsCalc.mapper.read.PmsAwardCalcResultReadMapper;
import com.jp.med.pms.modules.pmsCalc.service.read.PmsAwardCalcResultReadService;
import com.jp.med.pms.modules.pmsCalc.vo.PmsAwardCalcResultVo;
import com.jp.med.pms.modules.pmsCalc.vo.PmsAwardSalarySummaryVo;
import com.jp.med.pms.modules.pmsCalcTemplate.mapper.read.PmsAwardCalcItemConfigReadMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PmsAwardCalcResultReadServiceImpl extends ServiceImpl<PmsAwardCalcResultReadMapper, PmsAwardCalcResultDto> implements PmsAwardCalcResultReadService {

    @Resource
    private PmsAwardCalcResultReadMapper pmsAwardCalcResultReadMapper;
    @Autowired
    private PmsAwardCalcItemConfigReadMapper pmsAwardCalcItemConfigReadMapper;

    /**
     * 分页查询
     *
     * @param dto
     * @return
     */
    @Override
    public List<PmsAwardCalcResultVo> queryPageList(PmsAwardCalcResultDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return pmsAwardCalcResultReadMapper.queryList(dto);
    }

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<PmsAwardCalcResultVo> queryList(PmsAwardCalcResultDto dto) throws Exception {
        int maxVersion = getMaxVersion(dto);
        dto.setVersion(maxVersion);
        return pmsAwardCalcResultReadMapper.queryList(dto);
    }

    // getmaxVersion
    @Override
    public int getMaxVersion(PmsAwardCalcResultDto dto) throws Exception {
        if (StrUtil.isBlank(dto.getCalcMonth())) {
            throw new Exception("缺少参数CalcMonth");

        }
        LambdaQueryWrapper<PmsAwardCalcResultDto> eqed = Wrappers.lambdaQuery(PmsAwardCalcResultDto.class).eq(PmsAwardCalcResultDto::getCalcMonth, dto.getCalcMonth())
                // .eq(PmsAwardCalcResultDto::getTemplateId, dto.getTemplateId())
                .isNotNull(PmsAwardCalcResultDto::getVersion).orderByDesc(PmsAwardCalcResultDto::getVersion).last("limit 1");
        List<PmsAwardCalcResultDto> list = list(eqed);
        if (list != null && !list.isEmpty()) {
            return list.get(0).getVersion();
        } else {
            return 0;
        }

    }

    @Override
    public List<PmsAwardCalcResultVo> getAwardByQuantity(PmsAwardCalcResultDto dto) {
        if (StrUtil.isBlank(dto.getCalcMonth())) {
            dto.setCalcMonth(DateUtil.format(new Date(), "yyyy-MM"));
        }
        if (StrUtil.isBlank(dto.getDeptCode())) {
            dto.setDeptCode(dto.getSysUser().getHrmUser().getHrmOrgId());
        }
        return baseMapper.queryMaxVersionAwardByQuantity(dto);
    }

    @Override
    public List<PmsAwardCalcResultVo> calcResultList(PmsAwardCalcResultDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        if (StrUtil.isBlank(dto.getCalcMonth())) {
            throw new AppException("请选择月份");
        }
        // 1.获取一级指标
        List<String> firstLevelItemList = pmsAwardCalcItemConfigReadMapper.queryFirstLevelItem(dto);
        if (CollectionUtils.isEmpty(firstLevelItemList)) {
            throw new AppException("未获取到一级指标");
        }
        // 为每个一级指标拼接"合计"后缀(除了"按量计算绩效合计")
        firstLevelItemList = firstLevelItemList.stream().map(item -> "按量计算绩效合计".equals(item) ? item : item + "合计").collect(Collectors.toList());
        // 2.查询一级指标 一次分配计算结果
        List<PmsAwardCalcResultVo> firstLevelItemCalcResultList = baseMapper.queryFirstLevelItemCalcResult(dto.getCalcMonth(), firstLevelItemList);
        // 按templateId分组
        Map<Long, List<PmsAwardCalcResultVo>> groupByTemplateId = firstLevelItemCalcResultList.stream().collect(Collectors.groupingBy(PmsAwardCalcResultVo::getTemplateId));

        // 处理每个分组
        List<PmsAwardCalcResultVo> result = new ArrayList<>();
        groupByTemplateId.forEach((templateId, list) -> {
            // 找出按量计算绩效合计作为父节点
            PmsAwardCalcResultVo parent = list.stream().filter(vo -> "按量计算绩效合计".equals(vo.getItemCode())).findFirst().orElse(null);

            if (parent != null) {
                // 按itemCode分组并汇总
                Map<String, BigDecimal> itemCodeSum = list.stream().filter(vo -> !"按量计算绩效合计".equals(vo.getItemCode())).collect(Collectors.groupingBy(PmsAwardCalcResultVo::getItemCode, Collectors.reducing(BigDecimal.ZERO, PmsAwardCalcResultVo::getTotalAwardAmount, BigDecimal::add)));

                // 创建子节点
                List<PmsAwardCalcResultVo> children = itemCodeSum.entrySet().stream().map(entry -> {
                    PmsAwardCalcResultVo child = new PmsAwardCalcResultVo();
                    child.setItemCode(entry.getKey());
                    child.setTotalAwardAmount(entry.getValue());
                    return child;
                }).collect(Collectors.toList());

                parent.setChildren(children);
                result.add(parent);
            }
        });
        return result;
    }

    @Override
    public List<PmsAwardSalarySummaryVo> getAwardByHospital(PmsAwardCalcResultDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        if (StrUtil.isBlank(dto.getCalcMonth())) {
            throw new AppException("请选择月份");
        }
        // 1.获取一级指标
        List<String> firstLevelItemList = pmsAwardCalcItemConfigReadMapper.queryFirstLevelItem(dto);
        if (CollectionUtils.isEmpty(firstLevelItemList)) {
            throw new AppException("未获取到一级指标");
        }
        // 为每个一级指标拼接"合计"后缀(除了"按量计算绩效合计")
        firstLevelItemList = firstLevelItemList.stream().map(item -> "按量计算绩效合计".equals(item) ? item : item + "合计").collect(Collectors.toList());
        // 2.查询一级指标 一次分配计算结果
        List<PmsAwardCalcResultVo> firstLevelItemCalcResultList = baseMapper.queryFirstLevelItemCalcResult(dto.getCalcMonth(), firstLevelItemList);
        // 按templateDept_summaryType分组
        Map<String, List<PmsAwardCalcResultVo>> groupByTemplateDept = firstLevelItemCalcResultList.stream().collect(Collectors.groupingBy(item -> item.getTemplateDept() + "_" + item.getSummaryType()));

        // 结果集
        List<PmsAwardSalarySummaryVo> result = new ArrayList<>();

        groupByTemplateDept.forEach((key, list) -> {
            PmsAwardSalarySummaryVo vo = result.stream().filter(item -> key.equals(item.getOrgCode() + "_" + item.getSummaryType())).findFirst().orElseGet(() -> {
                PmsAwardSalarySummaryVo newVo = new PmsAwardSalarySummaryVo(list.get(0).getSummaryType(), list.get(0).getTemplateDept(), list.get(0).getTemplateDeptName());
                result.add(newVo);
                return newVo;
            });

            list.forEach(item -> {
                // 安全处理 BigDecimal 类型的 staffCount 累加
                BigDecimal currentStaffCount = Optional.ofNullable(vo.getStaffCount()).orElse(BigDecimal.ZERO);
                BigDecimal itemStaffCount = Optional.ofNullable(item.getStaffCount()).orElse(BigDecimal.ZERO);
                vo.setStaffCount(currentStaffCount.add(itemStaffCount));
                vo.setTotal(vo.getTotal().add(item.getTotalAwardAmount() == null ? BigDecimal.ZERO : item.getTotalAwardAmount()));
                vo.setTotalPerformance(vo.getTotalPerformance().add(item.getTotalAwardAmount() == null ? BigDecimal.ZERO : item.getTotalAwardAmount()));
                if (item.getItemCode() != null) {
                    switch (item.getItemCode()) {
                        case "服务效率/质量指标合计":
                            vo.setServiceQuality(item.getTotalAwardAmount() == null ? BigDecimal.ZERO : item.getTotalAwardAmount());
                            break;
                        case "医疗质量安全管理指标合计":
                            vo.setMedicalQuality(item.getTotalAwardAmount() == null ? BigDecimal.ZERO : item.getTotalAwardAmount());
                            break;
                        case "管理效能指标合计":
                            vo.setManagementAbility(item.getTotalAwardAmount() == null ? BigDecimal.ZERO : item.getTotalAwardAmount());
                            break;
                        case "工作量-成本控制指标合计":
                            vo.setWorkloadControl(item.getTotalAwardAmount() == null ? BigDecimal.ZERO : item.getTotalAwardAmount());
                            break;
                    }
                }
            });

            vo.setBaseAllowance(vo.getStaffCount().multiply(BigDecimal.valueOf(700)));
        });

        return result;
    }

    /**
     * 模板类型常量定义
     * 用于区分不同岗位类型的绩效计算模板
     */
    private static final class TemplateType {
        /**
         * 临床护士长
         */
        static final String NURSE = "0";
        /**
         * 临床主任
         */
        static final String DOCTOR = "1";
        /**
         * 医技主任
         */
        static final String MEDICAL_TECH = "2";

        /**
         * 其他岗位
         */
        static final String OTHER = "3";
        /**
         * 辅助岗位
         */
        static final String AUXILIARY = "4";
        /**
         * 管理岗位
         */
        static final String MANAGEMENT = "5";
    }

    /**
     * 职务类型常量定义
     * 用于区分不同职务类型
     */
    private static final class RankType {
        /**
         * 医院领导
         */
        static final String HOSPITAL_LEADER = "513";
        /**
         * 临床科室主任
         */
        static final String CLINICAL_DEPARTMENT_DIRECTOR = "514";
        /**
         * 医技科室主任
         */
        static final String MEDICAL_TECH_DEPARTMENT_DIRECTOR = "515";
        /**
         * 护士长
         */
        static final String HEAD_NURSE = "516";
        /**
         * 职能科室护士长
         */
        static final String FUNCTIONAL_DEPARTMENT_HEAD_NURSE = "517";
        /**
         * 职能科室主任
         */
        static final String FUNCTIONAL_DEPARTMENT_DIRECTOR = "518";
    }

    @Override
    public Map<String, Object> selectRankAwardByHospital(PmsAwardCalcResultDto dto) {
        return pmsAwardCalcResultReadMapper.selectRankAwardByHospital(dto);
    }


    @Override
    public List<Map<String, Object>> getRankAwardByHospital(PmsAwardCalcResultDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        if (StrUtil.isBlank(dto.getCalcMonth())) {
            throw new AppException("请选择月份");
        }
        log.debug("获取原始数据" + DateUtil.now());
        // 1.查询数据
        // 1.1获取绩效原始数据
        List<PmsAwardCalcResultVo> firstLevelItemCalcResultList = pmsAwardCalcResultReadMapper.queryFirstLevelItemCalcResult(dto.getCalcMonth(), List.of("bysfjxhj", "按量计算绩效合计"));
        // 1.2获取管理岗位职工数据
        List<Map<String, Object>> managementStaffList = pmsAwardCalcResultReadMapper.queryManagementStaffList(dto);
        //获取本月所有上报人数
        BigDecimal allReportStaffCount = pmsAwardCalcResultReadMapper.queryAllReportStaffCount(dto.getCalcMonth());
        log.debug("原始数据获取结束" + DateUtil.now());
        if (CollectionUtils.isEmpty(firstLevelItemCalcResultList) || CollectionUtils.isEmpty(managementStaffList) || allReportStaffCount == null || allReportStaffCount.compareTo(BigDecimal.ZERO) == 0) {
            throw new AppException("未获取到月度绩效相关数据");
        }


        // 2.生成 全院临床 计算表
        log.debug("生成计算表开始" + DateUtil.now());
        List<Map<String, Object>> calcResults = generateCalcResults(firstLevelItemCalcResultList, managementStaffList);
        log.debug("生成计算表结束" + DateUtil.now());


        // 3.生成行政后勤科室主任、护士长计算表
        log.debug("生成行政后勤科室主任、护士长计算表开始" + DateUtil.now());
        Map<String, Object> adminResults = generateAdminResults(firstLevelItemCalcResultList, managementStaffList, allReportStaffCount);
        calcResults.add(adminResults);
        log.debug("生成行政后勤科室主任、护士长计算表结束" + DateUtil.now());

        // 3.生成统计表
        log.debug("生成统计表开始" + DateUtil.now());
        generateFinalResults(calcResults);
        log.debug("生成统计表结束" + DateUtil.now());

        return calcResults;
    }

    @Override
    public Map<String, Object> getRankAwardByHospital2(PmsAwardCalcResultDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        if (StrUtil.isBlank(dto.getCalcMonth())) {
            throw new AppException("请选择月份");
        }
        log.debug("获取原始数据" + DateUtil.now());
        HashMap<String, Object> resultMap = new HashMap<>();
        // 1.查询数据
        // 1.1获取绩效原始数据
        List<PmsAwardCalcResultVo> firstLevelItemCalcResultList = pmsAwardCalcResultReadMapper.queryFirstLevelItemCalcResult(dto.getCalcMonth(), List.of("bysfjxhj", "按量计算绩效合计"));
        // 1.2获取管理岗位职工数据
        List<Map<String, Object>> managementStaffList = pmsAwardCalcResultReadMapper.queryManagementStaffList(dto);
        //获取本月所有上报人数
        BigDecimal allReportStaffCount = pmsAwardCalcResultReadMapper.queryAllReportStaffCount(dto.getCalcMonth());
        log.debug("原始数据获取结束" + DateUtil.now());
        if (CollectionUtils.isEmpty(firstLevelItemCalcResultList) || CollectionUtils.isEmpty(managementStaffList) || allReportStaffCount == null || allReportStaffCount.compareTo(BigDecimal.ZERO) == 0) {
            throw new AppException("未获取到月度绩效相关数据");
        }
        resultMap.put("firstLevelItemCalcResultList", firstLevelItemCalcResultList);
        resultMap.put("managementStaffList", managementStaffList);
        resultMap.put("allReportStaffCount", allReportStaffCount);
        return resultMap;
    }

    /**
     * 生成计算表结果
     *
     * @param firstLevelItemCalcResultList 一级指标计算结果
     * @param managementStaffList          管理岗位职工数据
     * @return 计算表结果
     */
    private List<Map<String, Object>> generateCalcResults(List<PmsAwardCalcResultVo> firstLevelItemCalcResultList, List<Map<String, Object>> managementStaffList) {

        // 职工信息 按orgId进行分组
        Map<String, List<Map<String, Object>>> orgIdGroupedStaff = managementStaffList.stream().collect(Collectors.groupingBy(item -> String.valueOf(item.get("orgId"))));

        // 按模板类型分组
        Map<String, List<PmsAwardCalcResultVo>> groupByTemplateType = firstLevelItemCalcResultList.stream().filter(item -> "bysfjxhj".equals(item.getItemCode())).collect(Collectors.groupingBy(PmsAwardCalcResultVo::getTemplateType));

        // 处理每种模板类型的数据
        return groupByTemplateType.entrySet().stream().sorted(Map.Entry.comparingByKey()) // 添加排序
                .map(entry -> processTemplateType(entry.getKey(), entry.getValue(), orgIdGroupedStaff)).collect(Collectors.toList());
    }

    /**
     * 获取全院临床平均
     */
    private BigDecimal getHospitalAverage(List<PmsAwardCalcResultVo> pmsAwardCalcResultList, BigDecimal allReportStaffCount) {
        BigDecimal total = pmsAwardCalcResultList.stream().map(item -> Optional.ofNullable(item.getTotalAwardAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal average = allReportStaffCount.compareTo(BigDecimal.ZERO) > 0 ? total.multiply(BigDecimal.valueOf(0.8)).multiply(BigDecimal.valueOf(0.98)).divide(allReportStaffCount, 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
        return average;
    }

    /**
     * 生成行政后勤科室主任、护士长计算表
     *
     * @param firstLevelItemCalcResultList 一级指标计算结果
     * @param managementStaffList          管理岗位职工数据
     * @param allReportStaffCount          本月所有上报人数
     */
    private Map<String, Object> generateAdminResults(List<PmsAwardCalcResultVo> firstLevelItemCalcResultList, List<Map<String, Object>> managementStaffList, BigDecimal allReportStaffCount) {
        //按量计算绩效合计
        List<PmsAwardCalcResultVo> aljsjxhjList = firstLevelItemCalcResultList.stream().filter(item -> "按量计算绩效合计".equals(item.getItemCode())).collect(Collectors.toList());
        //全院临床平均
        BigDecimal hospitalAverage = getHospitalAverage(aljsjxhjList, allReportStaffCount);
        //获取行后科室主任、护士长职工
        List<Map<String, Object>> allManagementStaffList = managementStaffList.stream().filter(item -> List.of(RankType.FUNCTIONAL_DEPARTMENT_HEAD_NURSE, RankType.FUNCTIONAL_DEPARTMENT_DIRECTOR).contains(item.get("rank"))).collect(Collectors.toList());
        //获取行后科室主任、护士长职工 计算表
        return processManagementCalcTable(hospitalAverage, allManagementStaffList);
    }


    /**
     * 处理单个模板类型的数据
     *
     * @param templateType      模板类型
     * @param items             模板类型数据
     * @param orgIdGroupedStaff 职工信息
     * @return 处理后的数据
     */
    private Map<String, Object> processTemplateType(String templateType, List<PmsAwardCalcResultVo> items, Map<String, List<Map<String, Object>>> orgIdGroupedStaff) {
        log.debug("处理模板类型{" + templateType + "}计算表生成开始" + DateUtil.now());

        Map<String, Object> typeMap = new HashMap<>();
        typeMap.put("模板类型", templateType);
        typeMap.put("原始数据", items);

        List<Map<String, Object>> calcTableList = items.stream().flatMap(item -> generateCalcTableItem(templateType, item, orgIdGroupedStaff.get(item.getTemplateDept())).stream()).filter(item -> !item.isEmpty()).collect(Collectors.toList());
        typeMap.put("计算表", calcTableList);
        typeMap.put("计算表size", calcTableList.size());

        log.debug("处理模板类型{" + templateType + "}计算表生成结束" + DateUtil.now());
        log.debug("处理模板类型{" + templateType + "}计算表汇总开始" + DateUtil.now());

        String[] summaryFields = {};
        Map<String, String> rowUpdateFiledMap = new HashMap<>();
        // 根据不同模板类型 处理 计算表汇总数据
        switch (templateType) {
            case TemplateType.NURSE:
                summaryFields = new String[]{"本月护理绩效总金额", "护士上班人数", "进修/休假人数", "科室平均", "医院发放部分0.25", "医院统筹部分0.15", "规模系数部分0.01", "合计", "考核得分", "应发数合计"};
                rowUpdateFiledMap.put("实发统筹部分0.15", "医院统筹部分0.15");
                processTableSummary("计算表", typeMap, calcTableList, summaryFields, rowUpdateFiledMap);
                break;
            case TemplateType.DOCTOR:
                summaryFields = new String[]{"医生绩效总金额", "医生上班人数", "进修/休假人数", "科室扣减应领数", "医院发放部分0.5", "规模系数部分0.02", "合计", "考核得分", "应发数合计"};
                processTableSummary("计算表", typeMap, calcTableList, summaryFields, rowUpdateFiledMap);
                break;
            case TemplateType.MEDICAL_TECH:
                summaryFields = new String[]{"科室上班人数", "进修/休假人数", "科室扣减应领数", "医院发放部分0.5", "合计", "考核得分", "应发数合计"};
                processTableSummary("计算表", typeMap, calcTableList, summaryFields, rowUpdateFiledMap);
                break;
            case TemplateType.OTHER:
                // TODO: 待做
                summaryFields = new String[]{"科室上班人数", "进修/休假人数", "科室扣减应领数", "医院发放部分0.5", "合计", "考核得分", "应发数合计"};
                processTableSummary("计算表", typeMap, calcTableList, summaryFields, rowUpdateFiledMap);
                break;
            case TemplateType.AUXILIARY:
                // TODO: 待做
                summaryFields = new String[]{"科室上班人数", "进修/休假人数", "科室扣减应领数", "医院发放部分0.5", "合计", "考核得分", "应发数合计"};
                processTableSummary("计算表", typeMap, calcTableList, summaryFields, rowUpdateFiledMap);
                break;
        }

        log.debug("处理模板类型{" + templateType + "}计算表汇总结束" + DateUtil.now());
        return typeMap;
    }

    /**
     * 生成单条计算表记录
     *
     * @param templateType 模板类型
     * @param item         模板类型数据
     * @param empStaff     职工信息
     * @return 处理后的数据
     */
    private List<Map<String, Object>> generateCalcTableItem(String templateType, PmsAwardCalcResultVo item, List<Map<String, Object>> empStaff) {
        log.debug("处理{" + item.getAwardApportionEmpName() + "}计算行数据开始" + DateUtil.now());
        List<Map<String, Object>> calcTableList = new ArrayList<>();
        if (CollectionUtils.isEmpty(empStaff)) {
            return calcTableList;
        }
        // 根据模板类型处理具体数据
        switch (templateType) {
            case TemplateType.NURSE:
                processNurseCalcTable(calcTableList, item, empStaff);
                break;
            case TemplateType.DOCTOR:
                processDoctorCalcTable(calcTableList, item, empStaff);
                break;
            case TemplateType.MEDICAL_TECH:
                processMedicalTechCalcTable(calcTableList, item, empStaff);
                break;
            case TemplateType.OTHER:
                // TODO: 待做
                processOtherCalcTable(null, item);
                break;
            case TemplateType.AUXILIARY:
                // TODO: 待做
                processAuxiliaryCalcTable(null, item);
                break;
        }
        log.debug("处理{" + item.getAwardApportionEmpName() + "}计算行数据结束" + DateUtil.now());
        return calcTableList;
    }

    /**
     * 添加汇总和汇总平均数据
     *
     * @param title             标题
     * @param result            结果集
     * @param calcTableList     计算表列表
     * @param summaryFields     汇总和平均值字段
     * @param rowUpdateFiledMap 汇总平均值更新到行记录 字段 Map<rowKey,avgKey>
     */
    private void processTableSummary(String title, Map<String, Object> result, List<Map<String, Object>> calcTableList, String[] summaryFields, Map<String, String> rowUpdateFiledMap) {
        if (CollectionUtils.isEmpty(calcTableList)) {
            return;
        }
        Map<String, Object> summaryMap = new HashMap<>();
        Map<String, Object> avgMap = new HashMap<>();

        // 1.计算汇总值
        for (String field : summaryFields) {
            summaryMap.put(field, calculateSum(calcTableList, field));
        }

        // 2.计算平均值
        for (String field : summaryFields) {
            avgMap.put(field, calculateAvg(calcTableList, summaryMap, field));
        }

        // 3. 更新每条行记录
        calcTableList.forEach(item -> {
            rowUpdateFiledMap.forEach((rowKey, avgKey) -> {
                item.put(rowKey, avgMap.get(avgKey));
            });
        });

        result.put(title + "汇总", summaryMap);
        result.put(title + "平均值", avgMap);
    }

    /**
     * 处理护理岗位计算表
     * 临床【护士长】岗位绩效计算表
     *
     * @param calcTableList 计算表列表
     * @param item          模板类型数据
     * @param empStaff      职工信息
     */
    private void processNurseCalcTable(List<Map<String, Object>> calcTableList, PmsAwardCalcResultVo item, List<Map<String, Object>> empStaff) {

        // 过滤护士长
        List<Map<String, Object>> nurseStaffList = empStaff.stream().filter(emp -> List.of(RankType.HEAD_NURSE).contains(emp.get("rank").toString())).collect(Collectors.toList());

        // 遍历新增护士长
        nurseStaffList.forEach(emp -> {
            Map<String, Object> calcTable = new HashMap<>();
            // 添加基础信息
            calcTable.put("科室编码", item.getTemplateDept());
            calcTable.put("科室", item.getTemplateDeptName());
            calcTable.put("工号", emp.get("empCode"));
            calcTable.put("姓名", emp.get("empName"));
            calcTable.put("职务类别", emp.get("rankName"));


            BigDecimal totalAwardAmount = Optional.ofNullable(item.getTotalAwardAmount()).orElse(BigDecimal.ZERO).divide(BigDecimal.valueOf(nurseStaffList.size()), 2, RoundingMode.UP);
            calcTable.put("本月护理绩效总金额", totalAwardAmount);

            BigDecimal workStaffCount = Optional.ofNullable(item.getWorkStaffCount()).orElse(BigDecimal.ZERO);
            calcTable.put("护士上班人数", workStaffCount);

            BigDecimal leaveStaffCount = Optional.ofNullable(item.getLeaveStaffCount()).orElse(BigDecimal.ZERO);
            calcTable.put("进修/休假人数", leaveStaffCount);

            BigDecimal deptAverage = workStaffCount.compareTo(BigDecimal.ZERO) > 0 ? totalAwardAmount.divide(workStaffCount, 0, RoundingMode.UP) : BigDecimal.ZERO;
            calcTable.put("科室平均", deptAverage);

            BigDecimal hospitalPart = calculateRatio(deptAverage, 0.25);
            calcTable.put("医院发放部分0.25", hospitalPart);

            BigDecimal hospitalPoolPart = calculateRatio(deptAverage, 0.15);
            calcTable.put("医院统筹部分0.15", hospitalPoolPart);

            BigDecimal scalePart = calculateRatio(item.getTotalAwardAmount(), 0.01);
            calcTable.put("规模系数部分0.01", scalePart);

            BigDecimal total = calculateTotal(deptAverage, hospitalPart, hospitalPoolPart, scalePart);
            calcTable.put("合计", total);

            BigDecimal assessmentScore = BigDecimal.valueOf(92.47); // TODO: 公式未知
            calcTable.put("考核得分", assessmentScore);

            BigDecimal finalAmount = calculateFinalAmount(total, assessmentScore);
            calcTable.put("应发数合计", finalAmount);

            calcTableList.add(calcTable);
        });
    }

    /**
     * 处理医生岗位计算表
     * 临床【主任】岗位绩效计算表
     */
    private void processDoctorCalcTable(List<Map<String, Object>> calcTableList, PmsAwardCalcResultVo item, List<Map<String, Object>> empStaff) {
        // 过滤临床科室主任
        List<Map<String, Object>> nurseStaffList = empStaff.stream().filter(emp -> List.of(RankType.CLINICAL_DEPARTMENT_DIRECTOR).contains(emp.get("rank").toString())).collect(Collectors.toList());
        // 遍历新增临床科室主任
        nurseStaffList.forEach(emp -> {
            Map<String, Object> calcTable = new HashMap<>();
            // 添加基础信息
            calcTable.put("科室编码", item.getTemplateDept());
            calcTable.put("科室", item.getTemplateDeptName());
            calcTable.put("工号", emp.get("empCode"));
            calcTable.put("姓名", emp.get("empName"));
            calcTable.put("职务类别", emp.get("rankName"));


            BigDecimal totalAwardAmount = Optional.ofNullable(item.getTotalAwardAmount()).orElse(BigDecimal.ZERO);
            calcTable.put("医生绩效总金额", totalAwardAmount);

            BigDecimal workStaffCount = Optional.ofNullable(item.getWorkStaffCount()).orElse(BigDecimal.ZERO);
            calcTable.put("医生上班人数", workStaffCount);

            BigDecimal leaveStaffCount = Optional.ofNullable(item.getLeaveStaffCount()).orElse(BigDecimal.ZERO);
            calcTable.put("进修/休假人数", leaveStaffCount);

            BigDecimal deptAverage = workStaffCount.compareTo(BigDecimal.ZERO) > 0 ? totalAwardAmount.divide(workStaffCount, 0, RoundingMode.HALF_UP) : BigDecimal.ZERO; // TODO: 公式未知
            calcTable.put("科室扣减应领数", deptAverage);

            BigDecimal hospitalPart = calculateRatio(deptAverage, 0.5);
            calcTable.put("医院发放部分0.5", hospitalPart);

            BigDecimal scalePart = calculateRatio(totalAwardAmount, 0.02);
            calcTable.put("规模系数部分0.02", scalePart);

            BigDecimal total = calculateTotal(deptAverage, hospitalPart, scalePart);
            calcTable.put("合计", total);

            BigDecimal assessmentScore = BigDecimal.valueOf(93.91); // TODO: 公式未知
            calcTable.put("考核得分", assessmentScore);

            BigDecimal finalAmount = calculateFinalAmount(total, assessmentScore);
            calcTable.put("应发数合计", finalAmount);

            calcTableList.add(calcTable);
        });
    }


    /**
     * 处理医技岗位计算表
     * 医技【主任】岗位绩效计算表
     */
    private void processMedicalTechCalcTable(List<Map<String, Object>> calcTableList, PmsAwardCalcResultVo item, List<Map<String, Object>> empStaff) {
        // 过滤医技科室主任
        List<Map<String, Object>> nurseStaffList = empStaff.stream().filter(emp -> List.of(RankType.MEDICAL_TECH_DEPARTMENT_DIRECTOR).contains(emp.get("rank").toString())).collect(Collectors.toList());
        // 遍历新增医技科室主任
        nurseStaffList.forEach(emp -> {
            Map<String, Object> calcTable = new HashMap<>();
            // 添加基础信息
            calcTable.put("科室编码", item.getTemplateDept());
            calcTable.put("科室", item.getTemplateDeptName());
            calcTable.put("工号", emp.get("empCode"));
            calcTable.put("姓名", emp.get("empName"));
            calcTable.put("职务类别", emp.get("rankName"));

            BigDecimal totalAwardAmount = Optional.ofNullable(item.getTotalAwardAmount()).orElse(BigDecimal.ZERO);// TODO:
            // 来源未知
            calcTable.put("医技绩效总金额", totalAwardAmount);

            BigDecimal workStaffCount = Optional.ofNullable(item.getWorkStaffCount()).orElse(BigDecimal.ZERO);
            calcTable.put("科室上班人数", workStaffCount);

            BigDecimal leaveStaffCount = Optional.ofNullable(item.getLeaveStaffCount()).orElse(BigDecimal.ZERO);
            calcTable.put("进修/休假人数", leaveStaffCount);

            BigDecimal deptAverage = workStaffCount.compareTo(BigDecimal.ZERO) > 0 ? totalAwardAmount.divide(workStaffCount, 0, RoundingMode.HALF_UP) : BigDecimal.ZERO; // TODO: 公式未知
            calcTable.put("科室扣减应领数", deptAverage);

            BigDecimal hospitalPart = calculateRatio(deptAverage, 0.5);
            calcTable.put("医院发放部分0.5", hospitalPart);

            BigDecimal total = calculateTotal(deptAverage, hospitalPart);
            calcTable.put("合计", total);

            BigDecimal assessmentScore = BigDecimal.valueOf(93.91); // TODO: 公式未知
            calcTable.put("考核得分", assessmentScore);

            BigDecimal finalAmount = calculateFinalAmount(total, assessmentScore);
            calcTable.put("应发数合计", finalAmount);

            calcTableList.add(calcTable);
        });
    }

    /**
     * 处理其他岗位计算表
     */
    private void processOtherCalcTable(Map<String, Object> calcTable, PmsAwardCalcResultVo item) {
        // TODO: 实现其他岗位计算表逻辑
    }

    /**
     * 处理辅助岗位计算表
     */
    private void processAuxiliaryCalcTable(Map<String, Object> calcTable, PmsAwardCalcResultVo item) {
        // TODO: 实现辅助岗位计算表逻辑
    }

    /**
     * 行后科室主任、护士长计算表
     */
    private Map<String, Object> processManagementCalcTable(BigDecimal hospitalAverage, List<Map<String, Object>> allManagementStaffList) {
        Map<String, Object> typeMap = new HashMap<>();
        typeMap.put("模板类型", TemplateType.MANAGEMENT);
        typeMap.put("全院临床平均", hospitalAverage);

        log.debug("处理模板类型{" + TemplateType.MANAGEMENT + "}计算表生成开始" + DateUtil.now());
        List<Map<String, Object>> calcTableList = generateManagementCalcTableItem(hospitalAverage, allManagementStaffList);
        log.debug("处理模板类型{" + TemplateType.MANAGEMENT + "}计算表生成结束" + DateUtil.now());
        typeMap.put("计算表", calcTableList);
        typeMap.put("计算表size", calcTableList.size());

        // 计算表汇总
        Map<String, String> rowUpdateFiledMap = new HashMap<>();
        String[] summaryFields = new String[]{"绩效总额", "科室基数", "考核分数", "科室考核后绩效", "考核后绩效", "扣已发血防编制基础绩效", "总值班", "院监、感控督查、支书、支委管理岗位绩效、DIP结余奖励、献血奖", "合计"};
        processTableSummary("计算表", typeMap, calcTableList, summaryFields, rowUpdateFiledMap);

        return typeMap;
    }

    /**
     * 生成行后科室主任、护士长计算表
     */
    private List<Map<String, Object>> generateManagementCalcTableItem(BigDecimal hospitalAverage, List<Map<String, Object>> allManagementStaffList) {
        List<Map<String, Object>> calcTableList = new ArrayList<>();

        allManagementStaffList.forEach(emp -> {
            Map<String, Object> calcTable = new HashMap<>();
            // 添加基础信息
            calcTable.put("科室编码", emp.get("orgId"));
            calcTable.put("科室", emp.get("orgName"));
            calcTable.put("工号", emp.get("empCode"));
            calcTable.put("姓名", emp.get("empName"));
            calcTable.put("职务类别", emp.get("rankName"));

            // 全院临床平均
            calcTable.put("全院临床平均", hospitalAverage);

            BigDecimal totalAwardAmount = calculateRatio(hospitalAverage, 2.3); // TODO: 比例未知
            calcTable.put("绩效总额", totalAwardAmount);

            BigDecimal deptAverage = BigDecimal.valueOf(5401); // TODO: 基数未知
            calcTable.put("科室基数", deptAverage);

            BigDecimal assessmentScore = BigDecimal.valueOf(98.59);// TODO: 考核分数未知
            calcTable.put("考核分数", assessmentScore);

            BigDecimal deptAfterAssessment = deptAverage.multiply(BigDecimal.valueOf(0.3)).multiply(assessmentScore).divide(BigDecimal.valueOf(100), 0, RoundingMode.HALF_UP).add(deptAverage.multiply(BigDecimal.valueOf(0.7)));
            calcTable.put("科室考核后绩效", deptAfterAssessment);

            BigDecimal totalAfterAssessment = totalAwardAmount.subtract(deptAverage).add(deptAfterAssessment).add(BigDecimal.valueOf(860));//TODO 额外添加数未知
            calcTable.put("考核后绩效", totalAfterAssessment);

            BigDecimal deductedBasicPerformance = BigDecimal.valueOf(1000);//TODO 数据来源未知
            calcTable.put("扣已发血防编制基础绩效", deductedBasicPerformance);

            BigDecimal totalSchedule = BigDecimal.valueOf(120);//TODO 数据来源未知
            calcTable.put("总值班", totalSchedule);

            BigDecimal totalSupervision = BigDecimal.valueOf(1000);//TODO 数据来源未知
            calcTable.put("院监、感控督查、支书、支委管理岗位绩效、DIP结余奖励、献血奖", totalSupervision);

            BigDecimal total = totalAfterAssessment.add(deductedBasicPerformance).add(totalSchedule).add(totalSupervision);
            calcTable.put("合计", total);

            calcTableList.add(calcTable);
        });
        return calcTableList;
    }

    /**
     * 计算比例金额
     */
    private BigDecimal calculateRatio(BigDecimal base, double ratio) {
        if (base == null) {
            return BigDecimal.ZERO;
        }
        return base.multiply(BigDecimal.valueOf(ratio)).setScale(1, RoundingMode.HALF_UP);
    }

    /**
     * 计算合计金额
     */
    private BigDecimal calculateTotal(BigDecimal... amounts) {
        return Arrays.stream(amounts).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算最终应发金额
     *
     * @param total           合计金额
     * @param assessmentScore 考核得分
     * @return 最终应发金额
     */
    private BigDecimal calculateFinalAmount(BigDecimal total, BigDecimal assessmentScore) {
        if (total == null || assessmentScore == null) {
            return BigDecimal.ZERO;
        }
        return total.multiply(assessmentScore).divide(BigDecimal.valueOf(100), 0, RoundingMode.HALF_UP);
    }

    /**
     * 计算汇总值
     */
    private BigDecimal calculateSum(List<Map<String, Object>> calcTableList, String field) {
        return calcTableList.stream().map(item -> (BigDecimal) item.get(field)).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算平均值
     */
    private BigDecimal calculateAvg
    (List<Map<String, Object>> calcTableList, Map<String, Object> summaryMap, String field) {
        BigDecimal deptAverage = Optional.ofNullable((BigDecimal) summaryMap.get(field)).orElse(BigDecimal.ZERO);
        return calcTableList.isEmpty() ? BigDecimal.ZERO : deptAverage.divide(BigDecimal.valueOf(calcTableList.size()), 0, RoundingMode.HALF_UP);
    }

    /**
     * 生成最终结果(包含统计表)
     */
    private void generateFinalResults(List<Map<String, Object>> calcResults) {
        if (CollectionUtils.isEmpty(calcResults)) {
            return;
        }
        // 处理每个计算结果
        calcResults.forEach(this::generateStatisticsTable);
    }

    /**
     * 为单个计算结果生成统计表
     */
    private void generateStatisticsTable(Map<String, Object> result) {
        if (result.isEmpty()) {
            return;
        }
        String templateType = (String) result.get("模板类型");
        log.debug("模板类型{" + templateType + "} 生成统计表开始" + DateUtil.now());

        List<Map<String, Object>> calcTables = (List<Map<String, Object>>) result.get("计算表");

        // 生成统计表列表
        List<Map<String, Object>> statisticsTables = calcTables.stream().map(calcTable -> generateSingleStatisticsTable(templateType, calcTable)).filter(item -> !item.isEmpty()).collect(Collectors.toList());

        result.put("统计表", statisticsTables);

        log.debug("模板类型{" + templateType + "} 生成统计表结束" + DateUtil.now());
        log.debug("模板类型{" + templateType + "} 统计表汇总开始" + DateUtil.now());

        String[] summaryFields = {};
        Map<String, String> rowUpdateFiledMap = new HashMap<>();
        // 根据不同模板类型 处理 统计表汇总数据
        switch (templateType) {
            case TemplateType.NURSE:
                summaryFields = new String[]{"扣减科室平均医院统一发放数", "医院发放0.25/0.15/0.01部分", "考核得分", "合计"};
                processTableSummary("统计表", result, statisticsTables, summaryFields, rowUpdateFiledMap);

                BigDecimal totalPoolPart = (BigDecimal) ((Map<String, Object>) result.get("统计表平均值")).get("扣减科室平均医院统一发放数");
                statisticsTables.forEach(item -> {
                    BigDecimal total = Optional.ofNullable((BigDecimal) item.get("合计")).orElse(BigDecimal.ZERO);
                    BigDecimal assessmentScore = Optional.ofNullable((BigDecimal) item.get("考核得分")).orElse(BigDecimal.ZERO);
                    BigDecimal finalAmount = totalPoolPart.multiply(BigDecimal.valueOf(0.1)).multiply(assessmentScore.subtract(BigDecimal.valueOf(90))).divide(BigDecimal.valueOf(10), 0, RoundingMode.HALF_UP);
                    item.put("奖励性考核0.1", finalAmount);
                    item.put("实发合计", total.add(finalAmount));

                });
                summaryFields = new String[]{"扣减科室平均医院统一发放数", "医院发放0.25/0.15/0.01部分", "考核得分", "合计", "奖励性考核0.1", "实发合计"};
                processTableSummary("统计表", result, statisticsTables, summaryFields, rowUpdateFiledMap);
                break;
            case TemplateType.DOCTOR:
                summaryFields = new String[]{"医生绩效总金额", "医生上班人数", "进修/休假人数", "科室扣减应领数", "医院发放部分0.5", "规模系数部分0.02", "合计", "考核得分", "应发数合计"};
                processTableSummary("统计表", result, statisticsTables, summaryFields, rowUpdateFiledMap);
                break;
            case TemplateType.MEDICAL_TECH:
                // 医技无统计表
                processTableSummary("统计表", result, statisticsTables, summaryFields, rowUpdateFiledMap);
                break;
            case TemplateType.OTHER:
                // TODO : 待做
                processTableSummary("统计表", result, statisticsTables, summaryFields, rowUpdateFiledMap);
                break;
            case TemplateType.AUXILIARY:
                // TODO : 待做
                processTableSummary("统计表", result, statisticsTables, summaryFields, rowUpdateFiledMap);
                break;
            case TemplateType.MANAGEMENT:
                // 管理无统计表
                processTableSummary("统计表", result, statisticsTables, summaryFields, rowUpdateFiledMap);
                break;
        }
        log.debug("模板类型{" + templateType + "} 统计表汇总结束" + DateUtil.now());
    }

    /**
     * 生成单个统计表
     */
    private Map<String, Object> generateSingleStatisticsTable(String templateType, Map<String, Object> calcTable) {
        Map<String, Object> statisticsTable = new HashMap<>();
        log.debug("处理{" + calcTable.get("姓名") + "}统计行数据开始" + DateUtil.now());

        // 复制基础信息
        statisticsTable.put("科室编码", calcTable.get("科室编码"));
        statisticsTable.put("科室", calcTable.get("科室"));
        statisticsTable.put("工号", calcTable.get("工号"));
        statisticsTable.put("姓名", calcTable.get("姓名"));
        statisticsTable.put("职务类别", calcTable.get("职务类别"));

        // 根据不同模板类型处理统计数据
        switch (templateType) {
            case TemplateType.NURSE:
                processNurseStatistics(statisticsTable, calcTable);
                break;
            case TemplateType.DOCTOR:
                processDoctorStatistics(statisticsTable, calcTable);
                break;
            case TemplateType.MEDICAL_TECH:
                // 医技无统计表
                statisticsTable.clear();
                break;
            case TemplateType.OTHER:
                // TODO: 待做
                processOtherStatistics(statisticsTable, calcTable);
                break;
            case TemplateType.AUXILIARY:
                // TODO: 待做
                processAuxiliaryStatistics(statisticsTable, calcTable);
                break;
            case TemplateType.MANAGEMENT:
                // 管理无统计表
                statisticsTable.clear();
                break;
        }
        log.debug("处理{" + calcTable.get("姓名") + "}统计行数据结束" + DateUtil.now());
        return statisticsTable;
    }

    /**
     * 处理护理岗位统计表
     */
    private void processNurseStatistics(Map<String, Object> statisticsTable, Map<String, Object> calcTable) {
        BigDecimal deptAverage = Optional.ofNullable((BigDecimal) calcTable.get("科室平均")).orElse(BigDecimal.ZERO);
        statisticsTable.put("扣减科室平均医院统一发放数", deptAverage);

        // 计算医院发放部分总和
        BigDecimal hospitalPart = Optional.ofNullable((BigDecimal) calcTable.get("医院发放部分0.25")).orElse(BigDecimal.ZERO);
        BigDecimal poolPart = Optional.ofNullable((BigDecimal) calcTable.get("实发统筹部分0.15")).orElse(BigDecimal.ZERO);
        BigDecimal scalePart = Optional.ofNullable((BigDecimal) calcTable.get("规模系数部分0.01")).orElse(BigDecimal.ZERO);
        BigDecimal totalHospitalPart = hospitalPart.add(poolPart).add(scalePart);

        statisticsTable.put("医院发放0.25/0.15/0.01部分", totalHospitalPart);
        BigDecimal assessmentScore = Optional.ofNullable((BigDecimal) calcTable.get("考核得分")).orElse(BigDecimal.ZERO);
        statisticsTable.put("考核得分", assessmentScore);
        BigDecimal finalAmount = deptAverage.add(totalHospitalPart).multiply(assessmentScore).divide(BigDecimal.valueOf(100), 0, RoundingMode.HALF_UP);
        statisticsTable.put("合计", finalAmount);
        statisticsTable.put("奖励性考核0.1", null);
        statisticsTable.put("实发合计", null);
    }

    /**
     * 处理医生岗位统计表
     */
    private void processDoctorStatistics(Map<String, Object> statisticsTable, Map<String, Object> calcTable) {
        statisticsTable.put("医生绩效总金额", calcTable.get("医生绩效总金额"));
        statisticsTable.put("医生上班人数", calcTable.get("医生上班人数"));
        statisticsTable.put("进修/休假人数", calcTable.get("进修/休假人数"));
        statisticsTable.put("科室扣减应领数", calcTable.get("科室扣减应领数"));
        statisticsTable.put("医院发放部分0.5", calcTable.get("医院发放部分0.5"));
        statisticsTable.put("规模系数部分0.02", calcTable.get("规模系数部分0.02"));
        statisticsTable.put("合计", calcTable.get("合计"));
        statisticsTable.put("考核得分", calcTable.get("考核得分"));
        statisticsTable.put("应发数合计", calcTable.get("应发数合计"));
    }


    /**
     * 处理其他岗位统计表
     */
    private void processOtherStatistics(Map<String, Object> statisticsTable, Map<String, Object> calcTable) {
        //TODO 实现其他岗位统计表的生成逻辑
    }

    /**
     * 处理辅助岗位统计表
     */
    private void processAuxiliaryStatistics(Map<String, Object> statisticsTable, Map<String, Object> calcTable) {
        //TODO 实现辅助岗位统计表的生成逻辑
    }
}

