package com.jp.med.pms.modules.pmsETL.excel;

import com.alibaba.fastjson.JSON;
import com.jp.med.pms.modules.pmsCalc.service.calcEngine.PmsCalcProgressService;
import com.jp.med.pms.modules.pmsCalc.service.calcEngine.PmsCalculationLifecycleManager;
import com.jp.med.pms.modules.pmsCalc.vo.PmsCalcVariableItemVo;
import com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCoefficientConfigVo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;

/**
 * 绩效Excel导出业务类
 */
@Component
@Slf4j
public class PmsExcelExportBo {

    @Resource
    private PmsCalcProgressService progressService;
    @Resource
    private PmsCalculationLifecycleManager pmsCalculationLifecycleManager;

    // Excel表头定义
    public final static String[] heads = {"序号", "项目", "数量/分数", "奖励系数", "目标值", "应得奖金额", "常量信息", "值"};

    /**
     * 替换不允许的字符
     *
     * @param inputString 输入字符串
     * @return 替换后的字符串
     */
    public static String replaceUnwantedChars(String inputString) {
        // 定义不允许的字符
        String[] unwantedChars = {"/", "\\", "?", "*", "[", "]", ":"};

        // 替换每个不允许的字符
        for (String unwantedChar : unwantedChars) {
            inputString = inputString.replace(unwantedChar, "");
        }

        return inputString.trim();
    }

    /**
     * 获取生成Excel的sheet数据
     *
     * @param sheetConfig Sheet配置
     * @return SheetData对象
     */
    public SheetData getSheetData(SheetConfig sheetConfig) {
        log.info("开始生成Excel sheet数据，配置信息：{}", JSON.toJSONString(sheetConfig));

        var pmsCalcContext = pmsCalculationLifecycleManager.getPmsCalcContext(sheetConfig.getTemplateId());
        var calculationResult = pmsCalculationLifecycleManager.getCalculationResult(sheetConfig.getTemplateId());

        if (pmsCalcContext == null) {
            log.error("无法为模板ID: {} 获取到PmsCalcContext", sheetConfig.getTemplateId());
            return new SheetData();
        }

        SheetData sheetData = new SheetData();
        sheetData.setSheetName(sheetConfig.getSheetName());
        sheetData.setTitle(sheetConfig.getTitle());
        sheetData.setHeaders(sheetConfig.getHeads());

        List<Map<String, Object>> data = new ArrayList<>();

        int index = 1;
        // 动态一级绩效指标：
        Map<String, List<PmsAwardCoefficientConfigVo>> classificationMap = pmsCalcContext.getClassificationMap();
        Map<String, List<PmsCalcVariableItemVo>> formulaVariablesMap = pmsCalcContext
                .getFormulaVariablesMap();
        List<PmsAwardCoefficientConfigVo> awardCoefficientConfigList = pmsCalcContext.getAwardCoefficientConfigList();
        Map<String, Object> variableContext = pmsCalcContext.getVariableContext();

        List<String> sortClassifications = sortClassifications(pmsCalcContext.getClassificationMap().keySet());
        for (String classification : sortClassifications) {
            Map<String, Map<String, String>> itemsMap = new HashMap<>();
            // 未分类二级指标
            var variableItems = formulaVariablesMap.get(classification);
            // 添加已计算的值
            if (formulaVariablesMap.containsKey("已计算")) {
                var calculatedItems = formulaVariablesMap.get("已计算");
                calculatedItems.stream()
                        .filter(calculatedItem -> variableItems.stream()
                                .anyMatch(item -> item.getItemName().equals(calculatedItem.getItemName())))
                        .forEach(variableItems::add);
            }
            // 添加合计
            if (formulaVariablesMap.containsKey("合计")) {
                var summaryItems = formulaVariablesMap.get("合计");
                summaryItems.stream()
                        .filter(summaryItem -> summaryItem.getItemName().startsWith(classification))
                        .forEach(variableItems::add);
            }

            // 二级指标map key 名称 value 项目以及计算值
            Map<String, List<PmsCalcVariableItemVo>> secondaryVariableMap = PmsCalcVariableItemVo
                    .groupByItemName(variableItems);
            // 遍历二级指标map
            double sum = 0.0;
            for (Map.Entry<String, List<PmsCalcVariableItemVo>> entry : secondaryVariableMap
                    .entrySet()) {

                List<PmsCalcVariableItemVo> secondaryVariableItems = entry.getValue();
                Map<String, String> item = new HashMap<>();
                for (PmsCalcVariableItemVo variableItem : secondaryVariableItems) {
                    String value = variableItem.getValue();
                    String itemResult = getItemResult(variableItem.getValue(), variableContext);
                    // Double itemResult = variableItem.getResult(calculationResult);
                    if (value.startsWith("calculated.")) {
                        // itemResult = variableItem.getResult(calculationResult);
                        item.put("应得奖金额", (itemResult));
                        // sum += itemResult;

                    }
                    if (value.endsWith(".coefficient")) {
                        item.put("奖励系数", (itemResult));
                    } else if (value.endsWith(".default")) {
                        item.put("数量/分数", (itemResult));
                    } else if (value.startsWith("calculated.")) {
                        item.put("应得奖金额", (itemResult));
                    } else if (value.startsWith("targetValue.")) {
                        item.put("目标值", (itemResult));
                    } else {
                        item.put(variableItem.getLabel(), (itemResult));
                    }

                }
                itemsMap.put(entry.getKey(), item);

            }

            String itemSum = getItemResult("performanceSummary." + classification.replace("合计", "") + "合计",
                    variableContext);
            log.info(" 合计{}", itemSum);
            addItemWithSubItems(data, Integer.toString(index), classification, "", "", (itemSum), itemsMap);
            index++;
        }

        sheetData.setData(data);
        return sheetData;

    }

    private String getItemResult(String value, Map<String, Object> variableContext) {

        Object rawValue = variableContext.get(value);
        if (rawValue == null) {
            log.error("rawValue is null :{}", value);
            return "0";
        }
        double itemResult;
        if (rawValue instanceof String) {
            itemResult = Double.parseDouble((String) rawValue);
        } else if (rawValue instanceof Double) {
            itemResult = (Double) rawValue;
        } else if (rawValue instanceof Integer) {
            itemResult = ((Integer) rawValue).doubleValue();
        } else {
            log.error("getItemResult类型错误;{}", value);
            itemResult = 0;
        }
        DecimalFormat df = new DecimalFormat("#.000");
        return df.format(itemResult);
    }

    /**
     * 填充数据
     *
     * @param workbook  工作簿
     * @param sheet     工作表
     * @param sheetData 表格数据
     * @param headers   表头
     */
    public void fillData(Workbook workbook, Sheet sheet, SheetData sheetData, String[] headers,
                         SheetConfig sheetConfig) {
        // 创建基本样式
        CellStyle normalStyle = createNormalStyle(workbook);
        CellStyle mergedStyle = createMergedStyle(workbook, normalStyle);
        CellStyle specialStyle = createSpecialStyle(workbook, normalStyle);
        CellStyle cardStyle = createCardStyle(workbook);
        CellStyle cardTitleStyle = createCardTitleStyle(workbook);

        int rowNum = 2;
        int constantInfoColIndex = getConstantInfoColumnIndex(headers);

        for (Map<String, Object> rowData : sheetData.getData()) {
            Row row = sheet.createRow(rowNum++);
            fillRowData(row, rowData, headers, normalStyle, specialStyle, cardTitleStyle, sheetConfig);

            // 合并序号列
            // 检查当前行数据中的"序号"是否存在且不为空
            if (rowData.get("序号") != null && !rowData.get("序号").toString().isEmpty()) {
                // 记录当前行的起始行号
                int startRow = rowNum - 1;
                // 初始化结束行号为起始行号
                int endRow = startRow;
                // 循环查找连续的空"序号"行
                while (endRow < sheetData.getData().size() &&
                        (sheetData.getData().get(endRow).get("序号") == null ||
                                sheetData.getData().get(endRow).get("序号").toString().isEmpty())) {
                    endRow++; // 继续向下查找
                }
                // 如果找到的结束行大于起始行，说明需要合并单元格
                if (endRow > startRow) {
                    // 创建合并区域，合并从起始行到结束行的第一列
                    CellRangeAddress mergedRegion = new CellRangeAddress(startRow, endRow + 1, 0, 0);
                    sheet.addMergedRegion(mergedRegion); // 将合并区域添加到工作表中

                    // 设置合并单元格的样式
                    for (int i = mergedRegion.getFirstRow(); i <= mergedRegion.getLastRow(); i++) {
                        Row mergedRow = sheet.getRow(i); // 获取合并区域内的每一行
                        if (mergedRow != null) {
                            Cell mergedCell = mergedRow.getCell(mergedRegion.getFirstColumn()); // 获取合并单元格
                            if (mergedCell != null) {
                                mergedCell.setCellStyle(mergedStyle); // 设置合并单元格的样式
                            }
                        }
                    }
                }
            }
        }

        int constRowIndex = fillConstantInfo(sheet, sheetData.getConstantInfo(), constantInfoColIndex, 2, cardStyle,
                cardTitleStyle);

        if (sheetConfig.pageSize != null) {
            addFooter(sheet, headers.length, Math.max(rowNum, constRowIndex), sheetConfig);
        }
    }

    /**
     * 填充行数据
     *
     * @param row            行
     * @param rowData        行数据
     * @param headers        表头
     * @param normalStyle    普通样式
     * @param specialStyle   特殊样式
     * @param cardTitleStyle 卡片标题样式
     */
    private void fillRowData(Row row, Map<String, Object> rowData, String[] headers, CellStyle normalStyle,
                             CellStyle specialStyle, CellStyle cardTitleStyle, SheetConfig sheetConfig) {
        int colNum = 0;
        for (String head : headers) {
            Cell cell = row.createCell(colNum++);
            Object value = rowData.get(head);
            if (value != null) {
                // 对于特定列,设置为数字格式
                if (head.equals("数量/分数") || head.equals("奖励系数") || head.equals("目标值") || head.equals("应得奖金额")) {
                    try {
                        double numericValue = Double.parseDouble(value.toString());
                        cell.setCellValue(numericValue);
                        // 创建数字格式
                        CellStyle numberStyle = row.getSheet().getWorkbook().createCellStyle();
                        numberStyle.cloneStyleFrom(normalStyle);
                        DataFormat format = row.getSheet().getWorkbook().createDataFormat();
                        numberStyle.setDataFormat(format.getFormat("#,##0.000")); // 设置三位小数
                        cell.setCellStyle(numberStyle);
                    } catch (NumberFormatException e) {
                        // 如果无法解析为数字,则保持原样
                        cell.setCellValue(value.toString());
                        cell.setCellStyle(normalStyle);
                    }
                } else {
                    cell.setCellValue(value.toString());
                    cell.setCellStyle(
                            head.equals("项目") && isSpecialProject(value, sheetConfig) ? specialStyle : normalStyle);
                }
            } else {
                cell.setCellStyle(normalStyle);
            }
        }
    }

    /*
     * 获取生成Excle的sheet数据
     */

    /**
     * 填充常量信息
     *
     * @param sheet          工作表
     * @param constantInfo   常量信息
     * @param startCol       起始列
     * @param startRow       起始行
     * @param cardStyle      卡片样式
     * @param cardTitleStyle 卡片标题样式
     */
    private int fillConstantInfo(Sheet sheet, Map<String, Object> constantInfo, int startCol, int startRow,
                                 CellStyle cardStyle, CellStyle cardTitleStyle) {
        if (constantInfo == null || startCol == -1)
            return 1;
        int currentRow = startRow;
        for (Map.Entry<String, Object> entry : constantInfo.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof Map) {
                fillMapValue(sheet, entry.getKey(), (Map<?, ?>) value, startCol, currentRow, cardStyle, cardTitleStyle);
                currentRow += ((Map<?, ?>) value).size() + 2; // 加1以包括标题行
            } else if (value instanceof List) {
                fillListValue(sheet, entry.getKey(), (List<?>) value, startCol, currentRow, cardStyle, cardTitleStyle);
                currentRow += ((List<?>) value).size() + 2; // 加1以包括标题行
            } else {
                fillSingleValue(sheet, entry.getKey(), value, startCol, currentRow, cardStyle, cardTitleStyle);
                currentRow += 2;
            }
        }
        return currentRow - 2;
    }

    /**
     * 添加常量信息到SheetData
     *
     * @param sheetData SheetData对象
     */
    public void addConstantsToSheetData(SheetData sheetData) {
        // 设置常量
        List<String> doctors = new ArrayList<>();
        doctors.add("张三");
        doctors.add("李四");
        doctors.add("王五");
        sheetData.setDoctors(doctors);

        // 模拟常量信息
        Map<String, Object> constantInfo = new HashMap<>();
        constantInfo.put("科室医生人员", doctors);
        constantInfo.put("科室名称", "肾内科");

        constantInfo.put("计算时间", new Date());
        // 设置科室总奖金池和人均奖金
        constantInfo.put("科室总奖金池", 100000.00);
        constantInfo.put("人均奖金", 20000.00);

        // 模拟map信息列表
        List<Map<String, Object>> mockMapList = new ArrayList<>();
        Map<String, Object> map1 = new HashMap<>();
        map1.put("项目名称", "门诊量");
        map1.put("数量", 500);
        map1.put("单价", 20.5);
        mockMapList.add(map1);
        constantInfo.put("测试信息", mockMapList);

        // 可以根据需要添加更多模拟的常量信息
        sheetData.setConstantInfo(constantInfo);
    }

    /**
     * 对分类进行排序
     *
     * @param classifications 原始分类集合
     * @return 排序后的分类列表
     */
    private List<String> sortClassifications(Set<String> classifications) {
        // 按照指定顺序排序分类，未包含的分类添加到末尾
        List<String> sortClassifications = Arrays.asList("服务效率/质量指标", "医疗质量安全管理指标", "管理效能指标", "工作量-成本控制指标", "其他",
                "按量计算绩效合计");
        List<String> sortedClassifications = new ArrayList<>();

        // 先添加sortClassifications中存在于classifications的分类
        for (String classification : sortClassifications) {
            if (classifications.contains(classification)) {
                sortedClassifications.add(classification);
            }
        }

        // 再添加classifications中未包含在sortClassifications的分类
        for (String classification : classifications) {
            if (!sortClassifications.contains(classification)) {
                sortedClassifications.add(classification);
            }
        }

        return sortedClassifications;
    }

    /**
     * 创建标题行
     *
     * @param workbook
     * @param sheet
     * @param title
     */
    public void createTitleRow(Workbook workbook, Sheet sheet, String title) {
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(title);

        CellStyle titleStyle = workbook.createCellStyle();
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setFontName("方正姚体");
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleCell.setCellStyle(titleStyle);

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));
    }

    // 创建表头行
    public void createHeaderRow(Workbook workbook, Sheet sheet, String[] headers) {
        Row headerRow = sheet.createRow(1);
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 12);
        headerFont.setFontName("楷体_GB2312");
        headerStyle.setFont(headerFont);

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    /**
     * 填充数据
     *
     * @param workbook
     * @param sheet
     * @param sheetData
     */

    /**
     * 创建合并样式
     *
     * @param workbook  工作簿
     * @param baseStyle 基础样式
     * @return 单元格样式
     */
    private CellStyle createMergedStyle(Workbook workbook, CellStyle baseStyle) {
        CellStyle style = workbook.createCellStyle();
        style.cloneStyleFrom(baseStyle);
        style.setAlignment(HorizontalAlignment.CENTER);
        return style;
    }

    /**
     * 创建普通样式
     *
     * @param workbook 工作簿
     * @return 单元格样式
     */
    private CellStyle createNormalStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 创建特殊样式
     *
     * @param workbook  工作簿
     * @param baseStyle 基础样式
     * @return 单元格样式
     */
    private CellStyle createSpecialStyle(Workbook workbook, CellStyle baseStyle) {
        CellStyle style = workbook.createCellStyle();
        style.cloneStyleFrom(baseStyle);
        Font font = workbook.createFont();
        font.setFontName("楷体_GB2312");
        font.setFontHeightInPoints((short) 12);
        font.setBold(true);
        style.setFont(font);
        return style;
    }

    /**
     * 创建卡片标题样式
     *
     * @param workbook 工作簿
     * @return 卡片标题样式
     */
    private CellStyle createCardTitleStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setWrapText(true);

        // 设置楷体字体
        Font font = workbook.createFont();
        font.setFontName("楷体_GB2312");
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);

        return style;
    }

    /**
     * 创建卡片样式
     *
     * @param workbook 工作簿
     * @return 单元格样式
     */
    private CellStyle createCardStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setWrapText(true);
        return style;
    }

    /**
     * 导出Excel的sheet数据
     */
    @NoArgsConstructor
    @Data
    public static class SheetData {

        /**
         * 模板id
         */
        private String templateId;

        /**
         * sheet名称
         */
        private String sheetName;
        /**
         * 标题
         */
        private String title;
        /**
         * 表头
         */
        private String[] headers;
        /**
         * 数据
         */
        private List<Map<String, Object>> data;

        /**
         * 常量信息
         */
        private Map<String, Object> constantInfo;
        /**
         * 医生
         */
        private List<String> doctors;
        /**
         * 表头宽度
         */
        private Map<String, Integer> headerWidthMap;

    }

    /**
     * 获取常量信息列索引
     *
     * @param headers 表头数组
     * @return 常量信息列索引
     */
    private int getConstantInfoColumnIndex(String[] headers) {
        for (int i = 0; i < headers.length; i++) {
            if ("常量信息".equals(headers[i])) {
                return i;
            }
        }
        return -1;
    }

    /**
     * Sheet配置类
     */
    @Data
    public static class SheetConfig {

        // 页签名称
        private String sheetName;

        // 标题
        private String title;

        // 表头
        private String[] heads = PmsExcelExportBo.heads;

        // 模板ID
        private Long templateId;

        // 页大小
        private String pageSize;

        // 页码
        private String pageNum;

        private Integer order;

        public String getTitle() {
            return replaceUnwantedChars(title);
        }

        public String getSheetName() {
            return replaceUnwantedChars(sheetName);
        }
    }

    /**
     * 填充map值
     *
     * @param sheet          工作表
     * @param key            键
     * @param map            映射
     * @param startCol       起始列
     * @param startRow       起始行
     * @param style          样式
     * @param cardTitleStyle 卡片标题样式
     */
    private void fillMapValue(Sheet sheet, String key, Map<?, ?> map, int startCol, int startRow, CellStyle style,
                              CellStyle cardTitleStyle) {
        Row row = sheet.getRow(startRow);

        int currentRow = startRow++;

        Cell titleKeyCell = row.createCell(startCol);
        titleKeyCell.setCellValue(key);
        titleKeyCell.setCellStyle(cardTitleStyle);
        for (Map.Entry<?, ?> entry : map.entrySet()) {
            row = sheet.getRow(currentRow);
            if (row == null)
                row = sheet.createRow(currentRow);

            Cell keyCell = row.createCell(startCol + 1);
            keyCell.setCellValue(entry.getKey().toString());
            keyCell.setCellStyle(style);

            Cell valueCell = row.createCell(startCol + 2);
            valueCell.setCellValue(entry.getValue().toString());
            valueCell.setCellStyle(style);

            currentRow++;
        }
    }

    /**
     * 填充列表值
     *
     * @param sheet          工作表
     * @param key            键
     * @param list           列表
     * @param startCol       起始列
     * @param startRow       起始行
     * @param style          样式
     * @param cardTitleStyle 卡片标题样式
     */
    private void fillListValue(Sheet sheet, String key, List<?> list, int startCol, int startRow, CellStyle style,
                               CellStyle cardTitleStyle) {
        Row row = sheet.getRow(startRow);
        if (row == null) {
            row = sheet.createRow(startRow);
        }

        // 创建并设置key单元格
        Cell keyCell = row.createCell(startCol);
        keyCell.setCellValue(key);
        keyCell.setCellStyle(cardTitleStyle);

        int currentRow = startRow + 1; // 从下一行开始填充列表项

        for (Object item : list) {
            Row itemRow = sheet.getRow(currentRow);
            if (itemRow == null) {
                itemRow = sheet.createRow(currentRow);
            }

            Cell cell = itemRow.createCell(startCol); // 在key的右侧列填充列表项
            cell.setCellValue(item.toString());
            cell.setCellStyle(style);

            currentRow++;
        }
    }

    /**
     * 填充单个值
     *
     * @param sheet          工作表
     * @param key            键
     * @param value          值
     * @param startCol       起始列
     * @param startRow       起始行
     * @param style          样式
     * @param cardTitleStyle 卡片标题样式
     */
    private void fillSingleValue(Sheet sheet, String key, Object value, int startCol, int startRow, CellStyle style,
                                 CellStyle cardTitleStyle) {
        Row row = sheet.getRow(startRow);
        if (row == null)
            row = sheet.createRow(startRow);

        Cell keyCell = row.createCell(startCol);
        keyCell.setCellValue(key);
        keyCell.setCellStyle(cardTitleStyle);

        Cell valueCell = row.createCell(startCol + 1);
        valueCell.setCellValue(value.toString());
        valueCell.setCellStyle(style);
    }

    /**
     * 添加页脚
     *
     * @param sheet       工作表
     * @param columnCount 列数
     * @param rowNum      行号
     */
    private void addFooter(Sheet sheet, int columnCount, int rowNum, SheetConfig sheetConfig) {
        Row footerRow = sheet.createRow(rowNum);
        Cell footerCell = footerRow.createCell(0);
        footerCell.setCellValue("共" + sheetConfig.getPageSize() + "页 本第" + sheetConfig.getPageNum() + "页");

        // 创建单元格样式
        CellStyle footerStyle = sheet.getWorkbook().createCellStyle();
        footerStyle.setAlignment(HorizontalAlignment.CENTER);
        footerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置字体为11号
        Font footerFont = sheet.getWorkbook().createFont();
        footerFont.setFontHeightInPoints((short) 11);
        footerStyle.setFont(footerFont);
        // 设置单元格样式
        footerCell.setCellStyle(footerStyle);

        // 合并单元格并居中
        CellRangeAddress mergedRegion = new CellRangeAddress(rowNum, rowNum, 0, columnCount - 1);
        sheet.addMergedRegion(mergedRegion);

        // 为合并后的单元格设置样式
        RegionUtil.setBorderTop(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, mergedRegion, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, mergedRegion, sheet);
    }

    /**
     * 添加带子项的数据
     *
     * @param data
     * @param index
     * @param name
     * @param quantity
     * @param rewardFactor
     * @param rewardAmount
     * @param subItems
     */
    private void addItemWithSubItems(List<Map<String, Object>> data, String index, String name, String quantity,
                                     String rewardFactor, String rewardAmount, Map<String, Map<String, String>> subItems) {
        Map<String, Object> item = new HashMap<>();
        item.put("序号", index);
        item.put("项目", name);
        item.put("数量/分数", quantity);
        item.put("奖励系数", rewardFactor);
        item.put("应得奖金额", rewardAmount);
        data.add(item);

        for (Map.Entry<String, Map<String, String>> entry : subItems.entrySet()) {
            Map<String, Object> subItem = new HashMap<>();
            subItem.put("序号", "");
            subItem.put("项目", entry.getKey());
            Map<String, String> subItemData = entry.getValue();
            subItem.put("数量/分数", subItemData.get("数量/分数"));
            subItem.put("奖励系数", subItemData.get("奖励系数"));
            subItem.put("应得奖金额", subItemData.get("应得奖金额"));
            data.add(subItem);
        }
    }

    /**
     * 调整列宽
     *
     * @param sheet   工作表
     * @param headers 表头数组
     */
    public void adjustColumnWidth(Sheet sheet, String[] headers) {
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
            // 获取当前列宽
            int columnWidth = sheet.getColumnWidth(i);
            // 增加列宽,添加4个字符的宽度
            sheet.setColumnWidth(i, columnWidth + 256 * 4); // 256 = 1个字符宽度
        }
    }

    /**
     * 添加边框
     *
     * @param workbook
     * @param sheet
     */
    public void addBorders(Workbook workbook, Sheet sheet, String[] headers) {
        CellStyle borderedStyle = workbook.createCellStyle();
        borderedStyle.setBorderTop(BorderStyle.THIN);
        borderedStyle.setBorderBottom(BorderStyle.THIN);
        borderedStyle.setBorderLeft(BorderStyle.THIN);
        borderedStyle.setBorderRight(BorderStyle.THIN);

        int lastRow = sheet.getLastRowNum();
        for (int i = 1; i <= lastRow; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                for (int j = 0; j < headers.length; j++) {
                    Cell cell = row.getCell(j);
                    if (cell == null) {
                        cell = row.createCell(j);
                    }
                    CellStyle currentStyle = cell.getCellStyle();
                    CellStyle newStyle = workbook.createCellStyle();
                    newStyle.cloneStyleFrom(currentStyle);
                    newStyle.setBorderTop(borderedStyle.getBorderTop());
                    newStyle.setBorderBottom(borderedStyle.getBorderBottom());
                    newStyle.setBorderLeft(borderedStyle.getBorderLeft());
                    newStyle.setBorderRight(borderedStyle.getBorderRight());
                    cell.setCellStyle(newStyle);
                }
            }
        }
    }

    /**
     * 辅助方法：判断是否为特殊项目
     *
     * @param value
     * @return
     */
    private boolean isSpecialProject(Object value, SheetConfig sheetConfig) {
        if (value == null)
            return false;
        if (value instanceof String) {
            var pmsCalcContext = pmsCalculationLifecycleManager.getPmsCalcContext(sheetConfig.getTemplateId());

            if (pmsCalcContext == null) {
                log.warn("在isSpecialProject中无法为模板ID: {} 获取到PmsCalcContext", sheetConfig.getTemplateId());
                return false;
            }
            var classificationMap = pmsCalcContext.getClassificationMap();
            var classificationSet = classificationMap.keySet();
            String projectName = value.toString();
            return classificationSet.contains(projectName);
        }
        return false;
    }

}