package com.jp.med.pms.modules.pmsETL.IndicatorDetails;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 指标详情数据实体类
 * 用于前端IndicatorDetailModal组件展示指标详情数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndicatorDetail {
    
    /**
     * 数据来源描述
     */
    private String dataSource;
    
    /**
     * 多标签页数据（当需要多标签页展示时使用）
     */
    private List<TabData> tabs;
    
    /**
     * 单表格列配置（当使用单表格展示时使用）
     */
    private List<ColumnConfig> columns;
    
    /**
     * 单表格数据（当使用单表格展示时使用）
     */
    private List<Map<String, Object>> data;
    
    /**
     * 扩展属性，用于存储额外的元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 是否使用多标签页模式
     */
    public boolean isTabMode() {
        return tabs != null && !tabs.isEmpty();
    }
    
    /**
     * 是否使用单表格模式
     */
    public boolean isSingleTableMode() {
        return columns != null && !columns.isEmpty() && data != null;
    }
} 