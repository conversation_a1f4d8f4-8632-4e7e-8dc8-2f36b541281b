package com.jp.med.pms.modules.pmsCalc.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.pms.modules.PmsIaeBalance.dto.PmsIaeBalanceConfigDto;
import com.jp.med.pms.modules.PmsIaeBalance.mapper.read.PmsIaeBalanceConfigReadMapper;
import com.jp.med.pms.modules.pmsCalc.dto.PmsDatchStatusesPersistence;
import com.jp.med.pms.modules.pmsCalc.requestBody.PmsMonthlyReportTasRB;
import com.jp.med.pms.modules.pmsCalc.requestBody.PmsUploadReminderRB;
import com.jp.med.pms.modules.pmsCalc.service.calcEngine.PmsDatchStatusesPersistenceService;
import com.jp.med.pms.modules.pmsCalc.service.write.PmsUploadReminderWriteService;
import com.jp.med.pms.modules.pmsCalc.vo.PmsMonthlyReportTaskVo;
import com.jp.med.pms.modules.pmsCalcTemplate.dto.PmsAwardCalcDeptTemplateDto;
import com.jp.med.pms.modules.pmsCalcTemplate.dto.PmsAwardCalcItemConfigDto;
import com.jp.med.pms.modules.pmsCalcTemplate.mapper.read.PmsAwardCalcDeptTemplateReadMapper;
import com.jp.med.pms.modules.pmsCalcTemplate.mapper.read.PmsAwardCalcItemConfigReadMapper;
import com.jp.med.pms.modules.pmsCalcTemplate.service.read.PmsAwardCalcDeptTemplateReadService;
import com.jp.med.pms.modules.pmsCalcTemplate.service.read.PmsAwardCalcItemConfigReadService;
import com.jp.med.pms.modules.pmsCalcTemplate.service.write.PmsAwardCalcItemConfigWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 绩效指标月度上报任务
 */
@Api(value = "绩效指标月度上报任务", tags = "绩效指标月度上报任务")
@RestController
@RequestMapping("pmsMonthlyReportTask")
public class PmsMonthlyReportTaskController {

    @Autowired
    private PmsAwardCalcItemConfigReadService pmsAwardCalcItemConfigReadService;

    @Autowired
    private PmsAwardCalcItemConfigWriteService pmsAwardCalcItemConfigWriteService;

    @Autowired
    private PmsAwardCalcItemConfigReadMapper pmsAwardCalcItemConfigReadMapper;

    @Autowired
    private PmsIaeBalanceConfigReadMapper pmsIaeBalanceConfigReadMapper;

    @Autowired
    private PmsDatchStatusesPersistenceService pmsDatchStatusesPersistenceService;

    @Autowired
    private PmsUploadReminderWriteService pmsUploadReminderWriteService;

    @Autowired
    private PmsAwardCalcDeptTemplateReadService pmsAwardCalcDeptTemplateReadService;
    @Autowired
    private PmsAwardCalcDeptTemplateReadMapper pmsAwardCalcDeptTemplateReadMapper;

    /**
     * 根据月份生成该月的第一天和最后一天的日期字符串
     *
     * @param month 月份
     * @return 包含开始日期和结束日期的数组，格式为 ["yyyy-MM-dd 00:00:00", "yyyy-MM-dd 23:59:59"]
     */
    private String[] generateStartAndEndTimeFromMonth(Date month) {
        if (month == null) {
            return null;
        }

        // 将 Date 转换为 LocalDate
        LocalDate localDate = month.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 获取该月的年月
        YearMonth yearMonth = YearMonth.of(localDate.getYear(), localDate.getMonth());

        // 获取该月的第一天和最后一天
        LocalDate firstDayOfMonth = yearMonth.atDay(1);
        LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();

        // 格式化日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String startTime = firstDayOfMonth.format(formatter) + " 00:00:00";
        String endTime = lastDayOfMonth.format(formatter) + " 23:59:59";

        return new String[]{startTime, endTime};
    }

    /**
     * 查询科室人员待上报任务
     * <p>
     * 1. 查询绩效指标数据
     * 2. 查询成本控制指标数据
     * 3. 合并两类数据并返回
     */
    @ApiOperation("查询科室人员待上报任务")
    @PostMapping("/queryDeptEmpTask")
    public CommonResult<?> queryDeptEmpTask(@RequestBody PmsMonthlyReportTasRB dto) {
        // 设置医院条件
        dto.setSqlAutowiredHospitalCondition(true);

        // 根据月份生成开始时间和结束时间
        if (dto.getMonth() != null) {
            String[] times = generateStartAndEndTimeFromMonth(dto.getMonth());
            if (times != null) {
                dto.setStartTime(times[0]);
                dto.setEndTime(times[1]);
            }
        }

        // 1. 查询绩效指标数据
        var awardQuery = new PmsAwardCalcItemConfigDto();
        BeanUtils.copyProperties(dto, awardQuery);
        var awardList = pmsAwardCalcItemConfigReadMapper.queryList(awardQuery);

        // 处理绩效指标模版Excel文件URL
        awardList.forEach(item -> {
            //        1.1 查询采集上报进度
            List<PmsAwardCalcDeptTemplateDto> templates = pmsAwardCalcDeptTemplateReadMapper.queryTemplatesByItemCodeAndBatchCode(item.getItemCode(), (item.getLastCollectId()));
            var totalExpectedDepts = templates.stream().map(PmsAwardCalcDeptTemplateDto::getTemplateDept).collect(Collectors.toSet()).size();
            item.setTotalExpectedDepts(totalExpectedDepts);
            var needReportedDeptsCount = templates.stream().map(PmsAwardCalcDeptTemplateDto::getHrpDeptCode).filter(StrUtil::isNotBlank).collect(Collectors.toSet()).size();
            item.setNeedReportedDeptsCount(needReportedDeptsCount);
            item.setNotReportedDeptData(templates.stream().filter(item1 -> StrUtil.isBlank(item1.getHrpDeptCode())).collect(Collectors.toList()));
            if (item.getFileUrl() != null) {
                try {
                    String excelUrl = OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_PMS, item.getFileUrl());
                    item.setFileUrl(excelUrl);
                } catch (Throwable throwable) {
                    // URL获取失败时保持原值
                }
            }
        });

        // 2. 查询成本控制指标数据
        var iaeQuery = new PmsIaeBalanceConfigDto();
        BeanUtils.copyProperties(dto, iaeQuery);
        iaeQuery.setQueryLastCollectTime(true);
        iaeQuery.setStartTime(dto.getStartTime());
        iaeQuery.setEndTime(dto.getEndTime());
        var iaeList = pmsIaeBalanceConfigReadMapper.queryList(iaeQuery);

        // 处理成本控制指标模版Excel文件URL
        iaeList.forEach(item -> {
            if (item.getFileUrl() != null) {
                try {
                    String excelUrl = OSSUtil.getPresignedObjectUrl(OSSConst.BUCKET_PMS, item.getFileUrl());
                    item.setFileUrl(excelUrl);
                } catch (Throwable throwable) {
                    // URL获取失败时保持原值
                }
            }
        });

        // 3. 合并数据并设置类型标识
        ArrayList<PmsMonthlyReportTaskVo> result = new ArrayList<>();

        // 转换并添加绩效指标数据
        var awardVoList = BeanUtil.copyToList(awardList, PmsMonthlyReportTaskVo.class);
        awardVoList.forEach(item -> {
            item.setPmsDataMappingType("pmsAwardCalcItem");
        });

        // 转换并添加成本控制指标数据
        var iaeVoList = BeanUtil.copyToList(iaeList, PmsMonthlyReportTaskVo.class);
        iaeVoList.forEach(item -> {
            item.setPmsDataMappingType("pmsIaeReportItem");
        });

        result.addAll(awardVoList);
        result.addAll(iaeVoList);

        return CommonResult.success(result);
    }

    /**
     * 绩效上报看板统计数据
     */
    @ApiOperation("绩效上报看板统计数据")
    @PostMapping("/queryPmsReportBoardData")
    public CommonResult<?> queryPmsReportBoardData(@RequestBody PmsMonthlyReportTasRB dto) {
        // 设置医院条件
        dto.setSqlAutowiredHospitalCondition(true);

        // 根据月份生成开始时间和结束时间
        if (dto.getMonth() != null) {
            String[] times = generateStartAndEndTimeFromMonth(dto.getMonth());
            if (times != null) {
                dto.setStartTime(times[0]);
                dto.setEndTime(times[1]);
            }
        }

        // 1. 查询绩效指标数据
        var awardQuery = new PmsAwardCalcItemConfigDto();
        BeanUtils.copyProperties(dto, awardQuery);
        var awardList = pmsAwardCalcItemConfigReadMapper.queryList(awardQuery);


        // 2. 查询成本控制指标数据
        var iaeQuery = new PmsIaeBalanceConfigDto();
        BeanUtils.copyProperties(dto, iaeQuery);
        iaeQuery.setQueryLastCollectTime(true);
        iaeQuery.setStartTime(dto.getStartTime());
        iaeQuery.setEndTime(dto.getEndTime());
        var iaeList = pmsIaeBalanceConfigReadMapper.queryList(iaeQuery);

        // 3. 统计绩效指标上传情况
        int awardTotal = awardList.size();
        int awardUploaded = 0;
        for (var item : awardList) {
            if (item.getLastCollectId() != null) {
                awardUploaded++;
            }
        }
        int awardNotUploaded = awardTotal - awardUploaded;

        // 4. 统计成本控制指标上传情况
        int iaeTotal = iaeList.size();
        int iaeUploaded = 0;
        for (var item : iaeList) {
            if (item.getLastCollectId() != null) {
                iaeUploaded++;
            }
        }
        int iaeNotUploaded = iaeTotal - iaeUploaded;

        // 5. 查询指标自动采集指标情况
        var batchStatusQuery = new PmsDatchStatusesPersistence();
        // 使用月度频率作为默认值
        // batchStatusQuery.setStatisticsFrequency("MONTHLY");
        if (dto.getStartTime() != null && dto.getEndTime() != null) {
            try {
                // 转换时间格式
                LocalDateTime startTime = LocalDateTime.parse(dto.getStartTime().replace(" ", "T"));
                LocalDateTime endTime = LocalDateTime.parse(dto.getEndTime().replace(" ", "T"));
                batchStatusQuery.setDataStartTime(startTime);
                batchStatusQuery.setDataEndTime(endTime);
            } catch (Exception e) {
                // 时间格式转换失败，忽略时间条件
            }
        }

        // 查询最新的批处理状态
        var queryWrapper = new QueryWrapper<PmsDatchStatusesPersistence>();
        queryWrapper.lambda()
                .eq(batchStatusQuery.getStatisticsFrequency() != null,
                        PmsDatchStatusesPersistence::getStatisticsFrequency,
                        batchStatusQuery.getStatisticsFrequency())
                .ge(batchStatusQuery.getDataStartTime() != null,
                        PmsDatchStatusesPersistence::getDataStartTime,
                        batchStatusQuery.getDataStartTime())
                .le(batchStatusQuery.getDataEndTime() != null,
                        PmsDatchStatusesPersistence::getDataEndTime,
                        batchStatusQuery.getDataEndTime())
                .orderByDesc(PmsDatchStatusesPersistence::getId);

        // 限制只查询最新的一条记录
        var batchStatusList = pmsDatchStatusesPersistenceService.list(queryWrapper);

        // 6. 构建返回结果
        var result = new HashMap<String, Object>();

        // 绩效指标上传情况
        var awardStats = new HashMap<String, Object>();
        awardStats.put("total", awardTotal);
        awardStats.put("uploaded", awardUploaded);
        awardStats.put("notUploaded", awardNotUploaded);
        awardStats.put("details", awardList);
        result.put("awardStats", awardStats);

        // 成本控制指标上传情况
        var iaeStats = new HashMap<String, Object>();
        iaeStats.put("total", iaeTotal);
        iaeStats.put("uploaded", iaeUploaded);
        iaeStats.put("notUploaded", iaeNotUploaded);
        iaeStats.put("details", iaeList);
        result.put("iaeStats", iaeStats);

        // 指标自动采集指标情况
        var batchStats = new HashMap<String, Object>();
        if (!batchStatusList.isEmpty()) {
            var latestBatchStatus = batchStatusList.get(0);
            var batchStatus = latestBatchStatus.getBatchStatus();

            batchStats.put("totalTasks", batchStatus.getTotalTasks());
            batchStats.put("completedTasks", batchStatus.getCompletedTasks().get());
            batchStats.put("failedTasks", batchStatus.getFailedTasks().get());
            batchStats.put("status", batchStatus.getStatus().getDescription());
            batchStats.put("startTime", batchStatus.getStartTime());
            batchStats.put("endTime", batchStatus.getEndTime());
            batchStats.put("details", batchStatus);
        } else {
            batchStats.put("totalTasks", 0);
            batchStats.put("completedTasks", 0);
            batchStats.put("failedTasks", 0);
            batchStats.put("status", "未采集");
            batchStats.put("startTime", null);
            batchStats.put("endTime", null);
            batchStats.put("details", null);
        }
        result.put("batchStats", batchStats);

        return CommonResult.success(result);
    }

    /**
     * 催促人员上报指标
     */
    @ApiOperation("催促人员上报指标")
    @PostMapping("/remindEmpUpload")
    public CommonResult<?> remindEmpUpload(@RequestBody PmsMonthlyReportTasRB dto) {
        // 设置医院条件
        dto.setSqlAutowiredHospitalCondition(true);

        // 创建催促请求对象
        PmsUploadReminderRB reminderRB = new PmsUploadReminderRB();
        BeanUtils.copyProperties(dto, reminderRB);

        // 设置默认催促消息
        if (reminderRB.getReminderMessage() == null || reminderRB.getReminderMessage().isEmpty()) {
            reminderRB.setReminderMessage("请及时上报绩效指标数据，以便系统进行绩效计算。");
        }

        // 调用催促服务
        boolean result = pmsUploadReminderWriteService.handleRemindRequest(reminderRB);

        return CommonResult.success(result);
    }

}
