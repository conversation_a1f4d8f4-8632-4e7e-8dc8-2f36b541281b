package com.jp.med.pms.modules.pmsBenefitAnalysis.config.PmsAssetChargeCfg.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @email -
 * @date 2023-12-27 15:04:02
 */
@Data
public class PmsAssetChargeCfgVo {

	/** id */
	private Integer id;

	/** 存放科室id */
	private String orgId;

	/** 资产编码 */
	private String assetCode;

	/** 资产名称 */
	private String assetName;

	/** 项目编码 */
	private String itemCode;

	/** 备注 */
	private String remark;

	/** 医院id */
	private String hospitalId;

	/** 状态标识 */
	private Integer activeFlag;

	/** 创建人 */
	private String cter;

	/** 创建时间 */
	private String createTime;

	/** 修改人 */
	private String updtr;

	/** 修改时间 */
	private String updateTime;

	/** 删除人 */
	private String delter;

	/** 删除时间 */
	private String deleteTime;

}
