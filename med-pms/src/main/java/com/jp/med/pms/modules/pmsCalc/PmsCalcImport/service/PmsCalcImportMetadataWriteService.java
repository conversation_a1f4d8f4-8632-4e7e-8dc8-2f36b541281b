package com.jp.med.pms.modules.pmsCalc.PmsCalcImport.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.dto.PmsAwardCollectionResultDto;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.dto.PmsCalcImportMetadataDto;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.mapper.PmsAwardCollectionResultWriteMapper;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.mapper.PmsCalcImportMetadataWriteMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;

@Service
@Transactional(readOnly = false)
public class PmsCalcImportMetadataWriteService
        extends ServiceImpl<PmsCalcImportMetadataWriteMapper, PmsCalcImportMetadataDto> {

    @Resource
    private PmsAwardCollectionResultWriteMapper pmsAwardCollectionResultWriteMapper;

    public void saveMetaDataAndResult(PmsCalcImportMetadataDto dto) {
        save(dto);

        if (CollectionUtil.isEmpty(dto.getAwardCollectionResultDtos())) {
            return;
        }

        Collection<PmsAwardCollectionResultDto> awardCollectionResultDtos = dto.getAwardCollectionResultDtos();
        awardCollectionResultDtos.forEach(awardCollectionResultDto -> {
            awardCollectionResultDto.setBatchCode(dto.getId());
        });
        pmsAwardCollectionResultWriteMapper.insert(awardCollectionResultDtos);

    }
}