package com.jp.med.pms.modules.pmsETL.IndicatorDetails;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;

/**
 * 指标详情工厂类
 * 提供便捷的工厂方法和常用的列配置模板
 */
public class IndicatorDetailFactory {
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat DATETIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 创建CMI值奖励指标详情
     */
    public static IndicatorDetail createCMIRewardDetail(
            BigDecimal deptCMI, BigDecimal hospitalAvgCMI, 
            BigDecimal rewardCoefficient, List<Map<String, Object>> caseDetails) {
        
        BigDecimal cmiDiff = deptCMI.subtract(hospitalAvgCMI);
        BigDecimal rewardAmount = cmiDiff.multiply(rewardCoefficient).multiply(new BigDecimal("100"));
        
        // 汇总数据标签页
        List<Map<String, Object>> summaryData = Arrays.asList(
            createRow("科室CMI值", deptCMI.toString(), "", "本月科室病例复杂度指数"),
            createRow("全院平均CMI", hospitalAvgCMI.toString(), "", "全院病例复杂度平均值"),
            createRow("CMI差值", cmiDiff.toString(), "", "科室CMI - 全院平均CMI"),
            createRow("奖励系数", rewardCoefficient.toString(), "元", "每0.01差值奖励金额"),
            createRow("最终奖励金额", rewardAmount.toString(), "元", "差值 × 奖励系数 × 100")
        );
        
        return IndicatorDetailBuilder.create()
                .dataSource("HIS系统 + 病案系统")
                .addTab(
                    IndicatorDetailBuilder.TabBuilder.create("summary", "汇总数据")
                        .addColumn("统计项目", "item", 150)
                        .addColumn("数值", "value", 120)
                        .addColumn("单位", "unit", 80)
                        .addColumn("备注", "remark", 200)
                        .data(summaryData)
                )
                .addTab(
                    IndicatorDetailBuilder.TabBuilder.create("detail", "病例明细")
                        .addColumn("住院号", "inpatientNo", 120)
                        .addColumn("患者姓名", "patientName", 100)
                        .addColumn("主诊断", "mainDiagnosis", 200)
                        .addColumn("DRG分组", "drgGroup", 100)
                        .addColumn("CMI值", "cmiValue", 100)
                        .addColumn(
                            IndicatorDetailBuilder.ColumnBuilder.create("复杂度等级", "complexityLevel")
                                .width(120)
                                .renderType(ColumnRenderType.TAG)
                                .renderConfig("colorMap", Map.of(
                                    "高", "error",
                                    "中", "warning", 
                                    "低", "success"
                                ))
                        )
                        .data(caseDetails)
                )
                .build();
    }
    
    /**
     * 创建出院病例数指标详情
     */
    public static IndicatorDetail createDischargeCountDetail(List<Map<String, Object>> dailyData) {
        return IndicatorDetailBuilder.create()
                .dataSource("HIS住院系统")
                .addColumn("日期", "date", 120)
                .addColumn("出院人次", "dischargeCount", 100)
                .addColumn("其中：正常出院", "normalDischarge", 120)
                .addColumn("转院", "transfer", 80)
                .addColumn("死亡", "death", 80)
                .addColumn("其他", "other", 80)
                .addColumn("累计出院", "totalDischarge", 100)
                .data(dailyData)
                .build();
    }
    
    /**
     * 创建再入院指标详情
     */
    public static IndicatorDetail createReadmissionDetail(
            List<Map<String, Object>> summaryData, 
            List<Map<String, Object>> caseData) {
        
        return IndicatorDetailBuilder.create()
                .dataSource("HIS住院系统 + 质控系统")
                .addTab(
                    IndicatorDetailBuilder.TabBuilder.create("summary", "统计汇总")
                        .addColumn("统计指标", "indicator", 150)
                        .addColumn("数量", "count", 100)
                        .addColumn("占比", "percentage", 100)
                        .addColumn("质控标准", "standard", 150)
                        .addColumn(
                            IndicatorDetailBuilder.ColumnBuilder.create("达标情况", "compliance")
                                .width(120)
                                .renderType(ColumnRenderType.TAG)
                                .renderConfig("colorMap", Map.of(
                                    "达标", "success",
                                    "不达标", "error"
                                ))
                        )
                        .data(summaryData)
                )
                .addTab(
                    IndicatorDetailBuilder.TabBuilder.create("cases", "病例明细")
                        .addColumn("患者姓名", "patientName", 100)
                        .addColumn("首次住院号", "firstAdmissionNo", 120)
                        .addColumn("首次出院日期", "firstDischargeDate", 120)
                        .addColumn("再次住院号", "secondAdmissionNo", 120)
                        .addColumn("再次入院日期", "secondAdmissionDate", 120)
                        .addColumn("间隔天数", "intervalDays", 100)
                        .addColumn("再入院原因", "readmissionReason", 200)
                        .addColumn(
                            IndicatorDetailBuilder.ColumnBuilder.create("类型", "type")
                                .width(100)
                                .renderType(ColumnRenderType.TAG)
                                .renderConfig("colorMap", Map.of(
                                    "计划内", "info",
                                    "非计划", "warning"
                                ))
                        )
                        .data(caseData)
                )
                .build();
    }
    
    /**
     * 创建人员统计指标详情
     */
    public static IndicatorDetail createStaffCountDetail(
            String staffType, 
            List<Map<String, Object>> summaryData, 
            List<Map<String, Object>> detailData) {
        
        return IndicatorDetailBuilder.create()
                .dataSource("HRM人力资源系统")
                .addTab(
                    IndicatorDetailBuilder.TabBuilder.create("summary", staffType + "统计")
                        .addColumn("职级分类", "category", 120)
                        .addColumn("在岗人数", "onDuty", 100)
                        .addColumn("请假人数", "onLeave", 100)
                        .addColumn("进修人数", "training", 100)
                        .addColumn("总人数", "total", 100)
                        .addColumn("出勤率", "attendanceRate", 100)
                        .data(summaryData)
                )
                .addTab(
                    IndicatorDetailBuilder.TabBuilder.create("detail", "人员明细")
                        .addColumn("姓名", "name", 100)
                        .addColumn("工号", "empCode", 120)
                        .addColumn("职级", "title", 120)
                        .addColumn("科室", "department", 120)
                        .addColumn(
                            IndicatorDetailBuilder.ColumnBuilder.create("状态", "status")
                                .width(100)
                                .renderType(ColumnRenderType.TAG)
                                .renderConfig("colorMap", Map.of(
                                    "在岗", "success",
                                    "请假", "warning",
                                    "进修", "info"
                                ))
                        )
                        .addColumn("联系电话", "phone", 120)
                        .data(detailData)
                )
                .build();
    }
    
    /**
     * 创建请假详情
     */
    public static IndicatorDetail createLeaveDetail(
            List<Map<String, Object>> summaryData,
            List<Map<String, Object>> detailData) {
        
        return IndicatorDetailBuilder.create()
                .dataSource("HRM人力资源系统 + 考勤系统")
                .addTab(
                    IndicatorDetailBuilder.TabBuilder.create("summary", "请假统计")
                        .addColumn("请假类型", "leaveType", 120)
                        .addColumn("人数", "count", 100)
                        .addColumn("总天数", "totalDays", 100)
                        .addColumn("平均天数", "avgDays", 100)
                        .addColumn("占比", "percentage", 100)
                        .data(summaryData)
                )
                .addTab(
                    IndicatorDetailBuilder.TabBuilder.create("detail", "请假明细")
                        .addColumn("姓名", "name", 100)
                        .addColumn("工号", "empCode", 120)
                        .addColumn("职级", "title", 120)
                        .addColumn("请假类型", "leaveType", 100)
                        .addColumn("开始时间", "startDate", 120)
                        .addColumn("结束时间", "endDate", 120)
                        .addColumn("天数", "days", 80)
                        .addColumn("请假原因", "reason", 200)
                        .addColumn(
                            IndicatorDetailBuilder.ColumnBuilder.create("状态", "status")
                                .width(100)
                                .renderType(ColumnRenderType.TAG)
                                .renderConfig("colorMap", Map.of(
                                    "已批准", "success",
                                    "审批中", "warning",
                                    "已销假", "info"
                                ))
                        )
                        .data(detailData)
                )
                .build();
    }
    
    /**
     * 常用列配置工厂方法
     */
    public static class ColumnFactory {
        
        /**
         * 创建状态列
         */
        public static IndicatorDetailBuilder.ColumnBuilder statusColumn(String title, String key, 
                                                                        Map<String, String> colorMap) {
            return IndicatorDetailBuilder.ColumnBuilder.create(title, key)
                    .width(100)
                    .renderType(ColumnRenderType.TAG)
                    .renderConfig("colorMap", colorMap);
        }
        
        /**
         * 创建金额列
         */
        public static IndicatorDetailBuilder.ColumnBuilder amountColumn(String title, String key) {
            return IndicatorDetailBuilder.ColumnBuilder.create(title, key)
                    .width(120)
                    .align("right")
                    .renderType(ColumnRenderType.NUMBER)
                    .renderConfig("precision", 2)
                    .renderConfig("format", "currency");
        }
        
        /**
         * 创建百分比列
         */
        public static IndicatorDetailBuilder.ColumnBuilder percentageColumn(String title, String key) {
            return IndicatorDetailBuilder.ColumnBuilder.create(title, key)
                    .width(100)
                    .align("right")
                    .renderType(ColumnRenderType.NUMBER)
                    .renderConfig("format", "percentage");
        }
        
        /**
         * 创建日期列
         */
        public static IndicatorDetailBuilder.ColumnBuilder dateColumn(String title, String key) {
            return IndicatorDetailBuilder.ColumnBuilder.create(title, key)
                    .width(120)
                    .renderType(ColumnRenderType.DATE)
                    .renderConfig("format", "YYYY-MM-DD");
        }
        
        /**
         * 创建日期时间列
         */
        public static IndicatorDetailBuilder.ColumnBuilder datetimeColumn(String title, String key) {
            return IndicatorDetailBuilder.ColumnBuilder.create(title, key)
                    .width(150)
                    .renderType(ColumnRenderType.DATE)
                    .renderConfig("format", "YYYY-MM-DD HH:mm:ss");
        }
    }
    
    /**
     * 工具方法：创建数据行
     */
    private static Map<String, Object> createRow(String item, String value, String unit, String remark) {
        Map<String, Object> row = new HashMap<>();
        row.put("item", item);
        row.put("value", value);
        row.put("unit", unit);
        row.put("remark", remark);
        return row;
    }
    
    /**
     * 工具方法：创建数据行
     */
    public static Map<String, Object> createRow(String... keyValuePairs) {
        Map<String, Object> row = new HashMap<>();
        for (int i = 0; i < keyValuePairs.length - 1; i += 2) {
            row.put(keyValuePairs[i], keyValuePairs[i + 1]);
        }
        return row;
    }
    
    /**
     * 工具方法：格式化日期
     */
    public static String formatDate(Date date) {
        return date != null ? DATE_FORMAT.format(date) : "";
    }
    
    /**
     * 工具方法：格式化日期时间
     */
    public static String formatDateTime(Date date) {
        return date != null ? DATETIME_FORMAT.format(date) : "";
    }
    
    /**
     * 工具方法：脱敏姓名
     */
    public static String maskName(String name) {
        if (name == null || name.length() <= 1) {
            return name;
        }
        return name.charAt(0) + "**" + (name.length() > 2 ? name.charAt(name.length() - 1) : "");
    }
    
    /**
     * 工具方法：脱敏电话
     */
    public static String maskPhone(String phone) {
        if (phone == null || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }
} 