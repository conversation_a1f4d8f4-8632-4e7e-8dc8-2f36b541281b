package com.jp.med.pms.modules.pmsCalcTemplate.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.pms.modules.pmsCalcTemplate.dto.PmsAwardCalcItemConfigDto;
import com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCalcItemConfigVo;

import java.util.List;

/**
 * 绩效表计算项目配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-18 23:03:00
 */
public interface PmsAwardCalcItemConfigReadService extends IService<PmsAwardCalcItemConfigDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<PmsAwardCalcItemConfigVo> queryList(PmsAwardCalcItemConfigDto dto);

    /**
     * 分页查询列表
     *
     * @param dto
     * @return
     */
    List<PmsAwardCalcItemConfigVo> queryPageList(PmsAwardCalcItemConfigDto dto);

    /**
     * 查询科室人员待上报任务
     *
     * @param dto
     * @return
     */
    List<PmsAwardCalcItemConfigVo> queryDeptEmpTask(PmsAwardCalcItemConfigDto dto);

    /**
     * 获取Award项目分类选项
     *
     * @param dto
     * @return
     */
    List<String> getClassificationOptions(PmsAwardCalcItemConfigDto dto);

}

