package com.jp.med.pms.modules.pmsETL.scheduler.status;

import com.jp.med.pms.modules.pmsETL.exception.ScheduleException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class ScheduleStatusService {

    public final ConcurrentHashMap<String, BatchStatus> batchStatuses = new ConcurrentHashMap<>();
    public final ConcurrentHashMap<String, TaskStatus> taskStatuses = new ConcurrentHashMap<>();

    /**
     * 创建批量任务
     *
     * @param batchId    批量任务的ID
     * @param totalTasks 任务总数
     */
    public void createBatch(String batchId, int totalTasks, String statisticsFrequency, LocalDateTime dataStartTime,
                            LocalDateTime dataEndTime) {
        batchStatuses.put(batchId,
                new BatchStatus(batchId, totalTasks, statisticsFrequency, dataStartTime, dataEndTime));
    }

    public void createTask(String batchId, String taskId, String batchCode, int configId, String configName) {
        TaskStatus taskStatus = new TaskStatus(taskId, configId, batchCode, configName, TaskStatusEnum.PENDING);

        taskStatuses.put(taskId, taskStatus);
        BatchStatus batchStatus = batchStatuses.get(batchId);
        if (batchStatus != null) {
            batchStatus.addTask(taskStatus);
        } else {
            throw new ScheduleException("Batch not found: " + batchId);
        }
    }

    public void updateTaskStatus(String taskId, TaskStatusEnum status, String errorMessage, String message) {
        TaskStatus taskStatus = taskStatuses.get(taskId);
        if (taskStatus != null) {

            taskStatus.setErrorMessage(errorMessage);

            taskStatus.setMessage(message);

            taskStatus.setStatus(status);
        }
    }

    /**
     * 更新批量任务的完成数量
     *
     * @param batchId 批量任务的ID
     */
    public void incrementCompletedTasks(String batchId) {
        BatchStatus batchStatus = batchStatuses.get(batchId);
        if (batchStatus != null) {
            batchStatus.incrementCompletedTasks();
        }
    }

    public void incrementFailedTasks(String batchId) {
        BatchStatus batchStatus = batchStatuses.get(batchId);
        if (batchStatus != null) {
            batchStatus.incrementFailedTasks();
        }
    }

    public Collection<BatchStatus> getAllBatchStatuses() {
        return batchStatuses.values();
    }

    public BatchStatus getBatchStatus(String batchId) {
        return (batchStatuses.get(batchId));
    }

    public TaskStatus getTaskStatus(String taskId) {
        TaskStatus taskStatus = taskStatuses.get(taskId);
        if (taskStatus != null) {
            return (taskStatus);
        }
        return null;
    }

    public List<StatusChangeRecord> getTaskStatusChangeHistory(String taskId) {
        TaskStatus taskStatus = taskStatuses.get(taskId);
        if (taskStatus != null) {
            return taskStatus.getStatusChangeHistory();
        }
        return null;
    }

    public List<StatusChangeRecord> getBatchStatusChangeHistory(String batchId) {
        BatchStatus batchStatus = batchStatuses.get(batchId);
        if (batchStatus != null) {
            return batchStatus.getStatusChangeHistory();
        }
        return null;
    }

}