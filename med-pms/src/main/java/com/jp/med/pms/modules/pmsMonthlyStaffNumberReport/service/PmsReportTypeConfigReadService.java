package com.jp.med.pms.modules.pmsMonthlyStaffNumberReport.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jp.med.pms.modules.pmsMonthlyStaffNumberReport.dto.PmsReportTypeConfigDto;

import java.util.List;

/**
 * 上报类型配置读取Service
 *
 * <AUTHOR>

 */
public interface PmsReportTypeConfigReadService {

    /**
     * 分页查询上报类型配置
     */
    IPage<PmsReportTypeConfigDto> queryPageList(PmsReportTypeConfigDto dto);

    /**
     * 查询上报类型配置列表
     */
    List<PmsReportTypeConfigDto> queryList(PmsReportTypeConfigDto dto);

    /**
     * 查询启用的上报类型配置
     */
    List<PmsReportTypeConfigDto> queryActiveReportTypes();

    /**
     * 根据ID查询
     */
    PmsReportTypeConfigDto queryById(Integer id);

    /**
     * 根据类型代码查询
     */
    PmsReportTypeConfigDto queryByTypeCode(String typeCode);
} 