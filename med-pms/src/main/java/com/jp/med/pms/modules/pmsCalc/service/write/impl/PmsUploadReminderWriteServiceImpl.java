package com.jp.med.pms.modules.pmsCalc.service.write.impl;

import com.jp.med.common.context.UserContext;
import com.jp.med.common.dto.message.SysMessageDto;
import com.jp.med.common.feign.SysMessageFeignService;
import com.jp.med.common.ienum.SysMessageTypeEnum;
import com.jp.med.pms.modules.pmsCalc.dto.PmsUploadReminder;
import com.jp.med.pms.modules.pmsCalc.mapper.write.PmsUploadReminderWriteMapper;
import com.jp.med.pms.modules.pmsCalc.requestBody.PmsUploadReminderRB;
import com.jp.med.pms.modules.pmsCalc.service.write.PmsUploadReminderWriteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.BatchResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 绩效指标上报催促记录写入服务实现类
 */
@Service
@Slf4j
public class PmsUploadReminderWriteServiceImpl implements PmsUploadReminderWriteService {

    @Autowired
    private PmsUploadReminderWriteMapper pmsUploadReminderWriteMapper;

    @Autowired
    private SysMessageFeignService sysMessageFeignService;

    @Override
    public boolean save(PmsUploadReminder entity) {
        return pmsUploadReminderWriteMapper.insert(entity) > 0;
    }

    @Override
    public boolean update(PmsUploadReminder entity) {
        return pmsUploadReminderWriteMapper.updateById(entity) > 0;
    }

    @Override
    public boolean removeById(Long id) {
        return pmsUploadReminderWriteMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(List<Long> ids) {
        return pmsUploadReminderWriteMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleRemindRequest(PmsUploadReminderRB rb) {
        List<PmsUploadReminder> reminderList = new ArrayList<>();
        Date currentTime = new Date();

        // 获取当前年月作为周期标识
        String monthPeriod = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));

        // 如果有指定的目标列表，则为每个目标创建提醒记录
        if (rb.getReminderTargets() != null && !rb.getReminderTargets().isEmpty()) {
            for (PmsUploadReminderRB.PmsReminderTarget target : rb.getReminderTargets()) {
                PmsUploadReminder reminder = new PmsUploadReminder();
                reminder.setHospitalId(rb.getHospitalId());
                reminder.setPmsDataMappingType(target.getPmsDataMappingType());
                reminder.setItemName(target.getItemName());
                reminder.setEmpId(target.getEmpId());
                reminder.setEmpName(target.getEmpName());
                reminder.setDeptId(target.getDeptId());
                reminder.setDeptName(target.getDeptName());
                reminder.setReminderTime(currentTime);
                reminder.setReminderMessage(target.getReminderMessage());
                reminder.setReminderStatus("SENT");
                reminder.setMonthPeriod(monthPeriod);

                reminderList.add(reminder);
            }
        } else {
            // 如果没有指定目标列表，则使用请求体中的信息创建单个提醒
            PmsUploadReminder reminder = new PmsUploadReminder();
            reminder.setHospitalId(rb.getHospitalId());
            reminder.setPmsDataMappingType(rb.getPmsDataMappingType());
            reminder.setItemName(rb.getItemName());
            reminder.setEmpId(rb.getEmpId());
            reminder.setEmpName(rb.getEmpName());
            reminder.setDeptId(rb.getDeptCode());
            reminder.setDeptName(rb.getDeptName());
            reminder.setReminderTime(currentTime);
            reminder.setReminderMessage(rb.getReminderMessage());
            reminder.setReminderStatus("SENT");
            reminder.setMonthPeriod(monthPeriod);

            reminderList.add(reminder);
        }

        // 发送系统通知
        reminderList.stream().map(reminder -> {
            SysMessageDto sysMessageDto = new SysMessageDto();
            sysMessageDto.setType(SysMessageTypeEnum.PMS_REMIND_UPLOAD.getCode());
            sysMessageDto.setTitle(reminder.getReminderMessage());
            sysMessageDto.setPushText(reminder.getReminderMessage());
            sysMessageDto.setGotoUrl("/pms/PmsMonthlyDeptStaffNumberReport");
            sysMessageDto.setPushTime(currentTime);
            sysMessageDto.setReadFlag(0);
            sysMessageDto.setCreator(UserContext.getEmpCode());
            sysMessageDto.setUsers(new String[]{reminder.getEmpId()});
            return sysMessageDto;
        }).forEach(reminder -> {
            try {
                sysMessageFeignService.sendMessage(reminder);
            } catch (Exception e) {
                log.error("发送系统通知失败", e);
            }
        });

        // 批量保存提醒记录
        List<BatchResult> insert = pmsUploadReminderWriteMapper.insert(reminderList);
        return insert != null && !insert.isEmpty();
        // return saveBatch(reminderList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<Long> ids, String status) {
        if (ids == null || ids.isEmpty() || status == null || status.isEmpty()) {
            return false;
        }

        // 遍历ID列表，逐个更新状态
        int successCount = 0;
        for (Long id : ids) {
            PmsUploadReminder reminder = new PmsUploadReminder();
            reminder.setId(id);
            reminder.setReminderStatus(status);

            if (pmsUploadReminderWriteMapper.updateById(reminder) > 0) {
                successCount++;
            }
        }

        // 只要有一个更新成功，就认为操作成功
        return successCount > 0;
    }
}