package com.jp.med.pms.modules.PmsIaeBalance.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.pms.modules.PmsIaeBalance.dto.PmsIaeBalanceReportDeptConfigDto;
import com.jp.med.pms.modules.PmsIaeBalance.mapper.write.PmsIaeBalanceReportDeptConfigWriteMapper;
import com.jp.med.pms.modules.PmsIaeBalance.service.write.PmsIaeBalanceReportDeptConfigWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 成本控制报表-科室配置信息表
 *
 * <AUTHOR>
 * @email -
 * @date 2025-05-18 00:13:33
 */
@Service
@Transactional(readOnly = false)
public class PmsIaeBalanceReportDeptConfigWriteServiceImpl extends ServiceImpl<PmsIaeBalanceReportDeptConfigWriteMapper, PmsIaeBalanceReportDeptConfigDto> implements PmsIaeBalanceReportDeptConfigWriteService {

    /**
     * 批量更新科室配置
     * 使用事务确保全部成功或全部失败
     *
     * @param dtoList 科室配置列表
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatch(List<PmsIaeBalanceReportDeptConfigDto> dtoList) {
        return this.updateBatchById(dtoList);
    }
}
