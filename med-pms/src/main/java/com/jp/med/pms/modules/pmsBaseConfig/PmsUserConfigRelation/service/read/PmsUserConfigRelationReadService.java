package com.jp.med.pms.modules.pmsBaseConfig.PmsUserConfigRelation.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.pms.modules.pmsBaseConfig.PmsUserConfigRelation.dto.PmsUserConfigRelationDto;
import com.jp.med.pms.modules.pmsBaseConfig.PmsUserConfigRelation.vo.PmsUserConfigRelationVo;

import java.util.List;

/**
 * PMS用户关联配置表
 *
 * <AUTHOR>
 * @email -
 * @date 2025-05-19 23:11:45
 */
public interface PmsUserConfigRelationReadService extends IService<PmsUserConfigRelationDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<PmsUserConfigRelationVo> queryList(PmsUserConfigRelationDto dto);

    /**
     * 分页查询列表
     *
     * @param dto
     * @return
     */
    List<PmsUserConfigRelationVo> queryPageList(PmsUserConfigRelationDto dto);
}

