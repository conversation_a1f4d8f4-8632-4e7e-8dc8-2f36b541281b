package com.jp.med.pms.modules.pmsCalc.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.pms.modules.pmsCalc.dto.PmsItemTemplateRelationDto;
import com.jp.med.pms.modules.pmsCalcTemplate.dto.PmsAwardCoefficientConfigDto;

/**
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-22 05:05:40
 */
public interface PmsAwardCoefficientConfigWriteService extends IService<PmsAwardCoefficientConfigDto> {
    void saveOrderInt(PmsAwardCoefficientConfigDto dto);

    /**
     * 保存配置并处理默认项目来源
     *
     * @param dto
     */
    void saveConfig(PmsAwardCoefficientConfigDto dto);


    /**
     * 批量关联指标与模板
     */
    void batchLinkItemTemplates(PmsItemTemplateRelationDto dto);

    /**
     * 批量取消关联指标与模板
     */
    void batchUnlinkItemTemplates(PmsItemTemplateRelationDto dto);

    /**
     * 根据模板ID和指标编码修改绩效配置
     * @param dto
     */
    void updateByTemplateIdAndItemCode(PmsAwardCoefficientConfigDto dto);

    /**
     * 批量更新指标详情查看权限
     * @param dto 指标与模板关联DTO，包含itemCode、templateIds和allowViewItemDetail
     */
    void batchUpdateAllowViewItemDetail(PmsItemTemplateRelationDto dto);
}
