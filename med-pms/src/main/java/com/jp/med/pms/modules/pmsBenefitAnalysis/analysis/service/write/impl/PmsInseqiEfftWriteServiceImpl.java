package com.jp.med.pms.modules.pmsBenefitAnalysis.analysis.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.pms.modules.pmsBenefitAnalysis.analysis.dto.PmsInseqiEfftDto;
import com.jp.med.pms.modules.pmsBenefitAnalysis.analysis.mapper.write.PmsInseqiEfftWriteMapper;
import com.jp.med.pms.modules.pmsBenefitAnalysis.analysis.service.write.PmsInseqiEfftWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 设备效益
 * <AUTHOR>
 * @email -
 * @date 2024-02-04 11:46:24
 */
@Service
@Transactional(readOnly = false)
public class PmsInseqiEfftWriteServiceImpl extends ServiceImpl<PmsInseqiEfftWriteMapper, PmsInseqiEfftDto> implements PmsInseqiEfftWriteService {
}
