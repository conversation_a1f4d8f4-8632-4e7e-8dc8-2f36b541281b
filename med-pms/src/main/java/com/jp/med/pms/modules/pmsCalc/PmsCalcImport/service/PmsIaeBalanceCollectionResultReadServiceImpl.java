package com.jp.med.pms.modules.pmsCalc.PmsCalcImport.service;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.dto.PmsIaeBalanceCollectionResultDto;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.mapper.PmsIaeBalanceCollectionResultReadMapper;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.vo.PmsIaeBalanceCollectionResultVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class PmsIaeBalanceCollectionResultReadServiceImpl
        extends ServiceImpl<PmsIaeBalanceCollectionResultReadMapper, PmsIaeBalanceCollectionResultDto>
        implements PmsIaeBalanceCollectionResultReadService {

    @Autowired
    private PmsIaeBalanceCollectionResultReadMapper pmsIaeBalanceCollectionResultReadMapper;

    @Autowired
    private PmsIaeBalanceImportMetadataReadServiceImpl pmsIaeBalanceImportMetadataReadServiceImpl;

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<PmsIaeBalanceCollectionResultVo> queryList(PmsIaeBalanceCollectionResultDto dto) {
        // 如果dto的所有查询条件都为空，直接返回空列表
        if ((dto.getId() == null) &&
                (dto.getItemName() == null) &&
                (dto.getItemCode() == null) &&
                (dto.getBatchCode() == null)) {
            return new ArrayList<>();
        }
        return pmsIaeBalanceCollectionResultReadMapper.queryList(dto);
    }

    /**
     * 分页查询列表
     *
     * @param dto
     * @return
     */
    @Override
    public List<PmsIaeBalanceCollectionResultVo> queryPageList(PmsIaeBalanceCollectionResultDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return pmsIaeBalanceCollectionResultReadMapper.queryList(dto);
    }


    /**
     * 按月 + itemCode 获取最新的成本采集数据
     *
     * @param calcMonth 月份（格式：yyyy-MM）
     * @param itemCode  指标代码
     * @return 最新的采集数据VO，若无则返回null
     */
    public List<PmsIaeBalanceCollectionResultVo> getIaeBalanceCollectionResult(String calcMonth, String itemCode) {

        // 获取 当前指标最大的版本代码
        Long batchCode = pmsIaeBalanceImportMetadataReadServiceImpl.getLatestVersionCode(calcMonth, itemCode);
        if (batchCode == null) {
            return null;
        }

        PmsIaeBalanceCollectionResultDto queryDto = new PmsIaeBalanceCollectionResultDto();
        queryDto.setBatchCode(batchCode);
        queryDto.setItemCode(itemCode);
        List<PmsIaeBalanceCollectionResultVo> voList = queryList(queryDto);
        return voList;

    }


    /**
     * 按科室+月+itemCode 获取最新的成本采集数据
     *
     * @param deptCode  科室代码
     * @param calcMonth 月份（格式：yyyy-MM）
     * @param itemCode  指标代码
     * @return 最新的采集数据VO，若无则返回null
     */
    public PmsIaeBalanceCollectionResultVo getIaeBalanceCollectionResult(String deptCode, String calcMonth,
                                                                         String itemCode) {
        // 计算本月的起止日期
        Date startDate = DateUtil.beginOfMonth(DateUtil.parse(calcMonth, "yyyy-MM"));
        Date endDate = DateUtil.endOfMonth(DateUtil.parse(calcMonth, "yyyy-MM"));


        LambdaQueryWrapper<PmsIaeBalanceCollectionResultDto> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PmsIaeBalanceCollectionResultDto::getHrpDeptCode, deptCode);
        queryWrapper.eq(PmsIaeBalanceCollectionResultDto::getItemCode, itemCode);
        queryWrapper.between(PmsIaeBalanceCollectionResultDto::getCollectionDate, startDate, endDate);
        queryWrapper.orderByDesc(PmsIaeBalanceCollectionResultDto::getCollectionDate);
        queryWrapper.orderByDesc(PmsIaeBalanceCollectionResultDto::getId);
        List<PmsIaeBalanceCollectionResultDto> dtoList = this.list(queryWrapper);
        if (dtoList == null || dtoList.isEmpty()) {
            return null;
        }

        PmsIaeBalanceCollectionResultVo vo = new PmsIaeBalanceCollectionResultVo();
        BeanUtils.copyProperties(dtoList.get(0), vo);
        return vo;
    }
}
