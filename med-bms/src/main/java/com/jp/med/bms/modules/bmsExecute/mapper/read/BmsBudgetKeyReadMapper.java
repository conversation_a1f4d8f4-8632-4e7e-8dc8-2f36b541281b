package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetKeyDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetKeyVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 重点学专科预算表
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:15:52
 */
@Mapper
public interface BmsBudgetKeyReadMapper extends BaseMapper<BmsBudgetKeyDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetKeyVo> queryList(BmsBudgetKeyDto dto);
}
