package com.jp.med.bms.modules.dispose.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTypeDto;
import org.springframework.web.multipart.MultipartFile;

/**
 * 预算编制项类别
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-10 15:11:57
 */
public interface BmsDisposeBudgetTypeWriteService extends IService<BmsDisposeBudgetTypeDto> {
    /**
     * 修改
     * @param dto
     */
    void updateBmsBudgetType(BmsDisposeBudgetTypeDto dto);

    /**
     * 新增
     * @param dto
     */
    void saveBmsBudgetType(BmsDisposeBudgetTypeDto dto);

    /**
     * 删除
     * @param dto
     */
    void deleteBmsBudgetType(BmsDisposeBudgetTypeDto dto);

    /**
     * 文件上传
     * @param dto
     * @param files
     */
    void uploadFile(BmsDisposeBudgetTypeDto dto, MultipartFile[] files);
}

