package com.jp.med.bms.modules.config.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.config.dto.BmsBudgetDeptMappingDto;
import com.jp.med.bms.modules.config.service.read.BmsBudgetDeptMappingReadService;
import com.jp.med.bms.modules.config.service.write.BmsBudgetDeptMappingWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 预算科室映射
 * <AUTHOR>
 * @email -
 * @date 2025-03-25 11:00:18
 */
@Api(value = "预算科室映射", tags = "预算科室映射")
@RestController
@RequestMapping("bmsBudgetDeptMapping")
public class BmsBudgetDeptMappingController {

    @Autowired
    private BmsBudgetDeptMappingReadService bmsBudgetDeptMappingReadService;

    @Autowired
    private BmsBudgetDeptMappingWriteService bmsBudgetDeptMappingWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询预算科室映射")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody BmsBudgetDeptMappingDto dto){
        return CommonResult.paging(bmsBudgetDeptMappingReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询预算科室映射")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsBudgetDeptMappingDto dto){
        return CommonResult.success(bmsBudgetDeptMappingReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增预算科室映射")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsBudgetDeptMappingDto dto){
        bmsBudgetDeptMappingWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改预算科室映射")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsBudgetDeptMappingDto dto){
        bmsBudgetDeptMappingWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除预算科室映射")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsBudgetDeptMappingDto dto){
        bmsBudgetDeptMappingWriteService.removeById(dto);
        return CommonResult.success();
    }

}
