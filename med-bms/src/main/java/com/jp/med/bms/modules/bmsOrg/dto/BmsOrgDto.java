package com.jp.med.bms.modules.bmsOrg.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

import java.util.List;

/**
 * 组织架构表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-17 15:49:13
 */
@Data
@TableName("bms_org" )
public class BmsOrgDto extends CommonQueryDto {

    /** 组织架构ID */
    @TableId("org_id")
    private String orgId;

    /** 父组织架构ID */
    @TableField("org_parent_id")
    private String orgParentId;

    /** 组织架构名称 */
    @TableField("org_name")
    private String orgName;

    /** 类型 */
    @TableField("dept_type")
    private String deptType;

    /** 组织架构查询名称 */
    private String queryOrgName;

    /** 用户名称/编码 */
    @TableField(exist = false)
    private String userInfo;

    /** 批量删除时指定，指定后以当前数据批量操作 */
    List<BmsOrgDto> deleteList;

    /** 批量新增时指定，指定后以当前数据批量操作 */
    List<BmsOrgDto> addList;

    /** 员工编号 */
    List<String> empCodes;

    /** 原始的员工编号 */
    List<String> oriEmpCodes;
}
