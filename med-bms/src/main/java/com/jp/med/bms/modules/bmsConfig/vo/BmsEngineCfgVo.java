package com.jp.med.bms.modules.bmsConfig.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 信息化系统预算配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-15 10:39:28
 */
@Data
public class BmsEngineCfgVo {

	/** ID */
	private Integer id;

	/** 信息设备编码 */
	private String engineCode;

	/** 信息设备名称 */
	private String engineName;

	/** 有效标志 */
	private String activeFlag;

}
