package com.jp.med.bms.modules.dispose.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetProjDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetProjVo;

import java.util.List;
import java.util.Map;

/**
 * 预算编制项类别
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-10 15:11:57
 */
public interface BmsDisposeBudgetProjReadService extends IService<BmsDisposeBudgetProjDto> {
    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsDisposeBudgetProjVo> queryList(BmsDisposeBudgetProjDto dto);

    /**
     * 查询归口科室列表
     * @param dto
     * @return
     */
    Map<String, List<?>> queryCentralizedDept(BmsDisposeBudgetProjDto dto);

    /**
     * 校验编制项
     * @param dto
     */
    void checkBudgetProj(BmsDisposeBudgetProjDto dto);

    /**
     * 查询预算编制项目
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetProjVo> queryByYear(BmsDisposeBudgetProjDto dto);
}

