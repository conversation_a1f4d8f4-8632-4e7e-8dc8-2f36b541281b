<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.purms.modules.supplier.mapper.read.PurmsEmpnumCfgReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.purms.modules.supplier.vo.PurmsEmpnumCfgVo" id="empnumCfgMap">
        <result property="id" column="id"/>
        <result property="scope" column="scope"/>
        <result property="scopeCode" column="scope_code"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.purms.modules.supplier.vo.PurmsEmpnumCfgVo">
        select
            id as id,
            scope as scope,
            scope_code as scopeCode,
            is_deleted as isDeleted,
            hospital_id as hospitalId
        from purms_empnum_cfg
        order by scope_code asc
    </select>
    <select id="queryCfgList" resultType="com.jp.med.purms.modules.supplier.vo.PurmsEmpnumCfgVo">
        select
            id as id,
            scope as scope,
            scope_code as scopeCode,
            hospital_id as hospitalId
        from purms_empnum_cfg
        where (is_deleted != 1 OR is_deleted IS NULL)
    </select>

    <select id="queryMaxCode"  resultType="com.jp.med.purms.modules.supplier.vo.PurmsEmpnumCfgVo">
        select
        scope_code,
            hospital_id as hospitalId
        from purms_empnum_cfg
        order by scope_code desc
        limit 1
    </select>
</mapper>
