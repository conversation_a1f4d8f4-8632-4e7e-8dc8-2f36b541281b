<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.purms.modules.supplier.mapper.read.PurmsSupplierInfoReadMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.purms.modules.supplier.vo.PurmsSupplierInfoVo" id="supplierInfoMap">
        <result property="id" column="id" />
        <result property="supplierName" column="supplier_name" />
        <result property="unifiedSocialCreditCode" column="unified_social_credit_code" />
        <result property="registeredAddr" column="registered_addr" />
        <result property="legalRepresentative" column="legal_representative" />
        <result property="regCapital" column="reg_capital" />
        <result property="companyType" column="company_type" />
        <result property="estDate" column="est_date" />
        <result property="businessModel" column="business_model" />
        <result property="industryCode" column="industry_code" />
        <result property="empNum" column="emp_num" />
        <result property="location" column="location" />
        <result property="companyWeb" column="company_web" />
        <result property="mainProds" column="main_prods" />
        <result property="companyIntr" column="company_intr" />
        <result property="createTime" column="create_time" />
        <result property="updtr" column="updtr" />
        <result property="updateTime" column="update_time" />
        <result property="delter" column="delter" />
        <result property="deleteTime" column="delete_time" />
        <result property="isDeleted" column="is_deleted" />
        <result property="hospitalId" column="hospital_id" />
        <result property="logo" column="logo" />
    </resultMap>
    <select id="queryList" resultType="com.jp.med.purms.modules.supplier.vo.PurmsSupplierInfoVo">
        select id as id, supplier_name as supplierName, unified_social_credit_code as
        unifiedSocialCreditCode, registered_addr as registeredAddr, legal_representative as
        legalRepresentative, reg_capital as regCapital, company_type as companyType, est_date as
        estDate, business_model as businessModel, industry_code as industryCode, emp_num as empNum,
        location as location, company_web as companyWeb, main_prods as mainProds, company_intr as
        companyIntr, create_time as createTime, updtr as updtr, update_time as updateTime, delter as
        delter, delete_time as deleteTime, is_deleted as isDeleted, hospital_id as hospitalId, logo
        as logo, management_forms as managementForms, business_endtime as businessEndtime,
        legal_representative_phone as legalRepresentativePhone, legal_representative_email as
        legalRepresentativeEmail ,admission_flag as admissionFlag , supplier_type as  supplierType  from purms_supplier_info <where>

            <if test="isDeleted != null"> AND is_deleted =
        #{isDeleted,jdbcType=INTEGER} </if>
        <if test="admissionFlag !=null or admissionFlag !=''">
            AND admission_flag = #{admissionFlag,jdbcType=VARCHAR}
        </if>
        </where> ORDER BY update_time DESC </select>

    <!--  根据统一社会信用码查询公司id  -->
    <select id="queryIdByName" parameterType="string" resultType="string"> SELECT id :: VARCHAR FROM
        purms_supplier_info <where> (is_deleted IS NULL OR is_deleted != 1) <if
                test="supplierName != null or supplierName != ''"> AND supplier_name =
        #{supplierName,jdbcType=VARCHAR} </if>
        </where> ORDER BY id DESC LIMIT 1 </select>

    <select id="querySupplier" resultType="com.jp.med.purms.modules.supplier.vo.PurmsSupplierInfoVo">
        select id as id, supplier_name as suppliername, hospital_id as hospitalid from
        purms_supplier_info where (is_deleted is null or is_deleted != 1) </select>


    <select id="queryMinPro" resultType="com.jp.med.purms.modules.supplier.vo.PurmsSupplierInfoVo">
        select id as id, supplier_name as suppliername, main_prods as mainprods, hospital_id as
        hospitalid from purms_supplier_info </select>

    <select id="queryYearMonthSupplierNum" resultType="java.util.Map">
        select substring(a.create_time, 1, 7) as "month",
               count(1)                       as "num"
        from purms_supplier_info a
        <where>
            and a.admission_flag = '1'
            and a.is_deleted != 1
        </where>
        group by substring(a.create_time, 1, 7)
        order by substring(a.create_time, 1, 7)
    </select>
</mapper>
