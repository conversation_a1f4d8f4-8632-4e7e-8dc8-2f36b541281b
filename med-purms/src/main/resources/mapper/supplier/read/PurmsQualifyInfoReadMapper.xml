<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.purms.modules.supplier.mapper.read.PurmsQualifyInfoReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.purms.modules.supplier.vo.PurmsQualifyInfoVo" id="qualifyInfoMap">
        <result property="id" column="id"/>
        <result property="qualifyName" column="qualify_name"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="photoName" column="photo_name"/>
        <result property="photoPath" column="photo_path"/>
        <result property="unifiedSocialCreditCode" column="unified_social_credit_code"/>
        <result property="regAddr" column="reg_addr"/>
        <result property="legalRepresentative" column="legal_representative"/>
        <result property="regCapital" column="reg_capital"/>
        <result property="fileName" column="file_name"/>
        <result property="filePath" column="file_path"/>
        <result property="submitTime" column="submit_time"/>
        <result property="passTime" column="pass_time"/>
        <result property="categoryCode" column="category_code"/>
        <result property="awardInstitute" column="award_institute"/>
        <result property="releaseDate" column="release_date"/>
        <result property="deadline" column="deadline"/>
        <result property="userId" column="user_id"/>
        <result property="userUpDate" column="user_up_date"/>
        <result property="userDelDate" column="user_del_date"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.purms.modules.supplier.vo.PurmsQualifyInfoVo">
        select
            id as id,
            qualify_name as qualifyName,
            supplier_id as supplierId,
            photo_name as photoName,
            photo_path as photoPath,
            unified_social_credit_code as unifiedSocialCreditCode,
            reg_addr as regAddr,
            legal_representative as legalRepresentative,
            reg_capital as regCapital,
            file_name as fileName,
            file_path as filePath,
            submit_time as submitTime,
            pass_time as passTime,
            category_code as categoryCode,
            award_institute as awardInstitute,
            release_date as releaseDate,
            deadline as deadline,
            user_id as userId,
            user_up_date as userUpDate,
            user_del_date as userDelDate,
            hospital_id as hospitalId
        from purms_qualify_info
    </select>

</mapper>
