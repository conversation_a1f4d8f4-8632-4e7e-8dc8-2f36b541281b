<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.purms.modules.supplier.mapper.read.PurmsCompanyTypeCfgReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.purms.modules.supplier.vo.PurmsCompanyTypeCfgVo" id="companyTypeCfgMap">
        <result property="id" column="id"/>
        <result property="typeName" column="type_name"/>
        <result property="typeCode" column="type_code"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.purms.modules.supplier.vo.PurmsCompanyTypeCfgVo">
        select
            id as id,
            type_name as typeName,
            type_code as typeCode,
            is_deleted as isDeleted,
            hospital_id as hospitalId
        from purms_company_type_cfg
    </select>
    <select id="queryCfgList" resultType="com.jp.med.purms.modules.supplier.vo.PurmsCompanyTypeCfgVo">
        select
            type_name as typeName,
            type_code as typeCode,
            hospital_id as hospitalId
        from purms_company_type_cfg
    </select>
</mapper>
