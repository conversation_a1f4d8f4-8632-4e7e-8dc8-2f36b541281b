<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.purms.modules.sampleManage.mapper.read.PurmsSupplierSampleReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.purms.modules.sampleManage.vo.PurmsSupplierSampleVo" id="supplierSampleMap">
        <result property="id" column="id"/>
        <result property="sampleType" column="sample_type"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="easyCode" column="easy_code"/>
        <result property="mtrType" column="mtr_type"/>
        <result property="modspec" column="modspec"/>
        <result property="orplc" column="orplc"/>
        <result property="prdr" column="prdr"/>
        <result property="asetBrad" column="aset_brad"/>
        <result property="mfgDate" column="mfg_date"/>
        <result property="exprinDate" column="exprin_date"/>
        <result property="rgtBrad" column="rgt_brad"/>
        <result property="minReserve" column="min_reserve"/>
        <result property="maxReserve" column="max_reserve"/>
        <result property="refPrice" column="ref_price"/>
        <result property="isKit" column="is_kit"/>
        <result property="att" column="att"/>
        <result property="attName" column="att_name"/>
        <result property="crter" column="crter"/>
        <result property="crterTime" column="crter_time"/>
        <result property="updtr" column="updtr"/>
        <result property="updtrTime" column="updtr_time"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="expressNum" column="express_num"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="descr" column="descr"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.purms.modules.sampleManage.vo.PurmsSupplierSampleVo">
        select
            id as id,
            sample_type as sampleType,
            code as code,
            name as name,
            easy_code as easyCode,
            mtr_type as mtrType,
            modspec as modspec,
            orplc as orplc,
            prdr as prdr,
            aset_brad as asetBrad,
            mfg_date as mfgDate,
            exprin_date as exprinDate,
            rgt_brad as rgtBrad,
            min_reserve as minReserve,
            max_reserve as maxReserve,
            ref_price as refPrice,
            is_kit as isKit,
            att as att,
            att_name as attName,
            crter as crter,
            crter_time as crterTime,
            updtr as updtr,
            updtr_time as updtrTime,
            hospital_id as hospitalId,
            is_deleted as isDeleted,
            express_num as expressNum,
            status as status,
            remark as remark,
            descr as descr
        from purms_supplier_sample
    </select>

</mapper>
