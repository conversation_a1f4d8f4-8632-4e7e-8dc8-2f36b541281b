<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.purms.modules.applyMgt.mapper.write.PurmsPurcReqWriteMapper">
    <update id="updatePurcReqChkState">
        update purms_purc_req
        set chk_state= #{chkState,jdbcType=VARCHAR}
        where audit_bchno = #{auditBchno,jdbcType=VARCHAR}
    </update>

    <update id="updateReallySumamt">
        update purms_purc_req
        set really_sumamt=#{reallySumamt},
            exe_status=#{exeStatus}
        where item_no = #{itemNo}
    </update>

    <update id="updateItemReimId">
      update purms_purc_req_detail
        set reim_id=#{reimId}
      where id in
      <foreach collection="purcDetailIds" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
  </update>

    <update id="callGoods">
        update purms_purc_req
        set to_buy      = '1',
            buy_creater = #{crter},
            buy_date    = #{crteTime}
        where id = #{id}
    </update>
</mapper>
