package com.jp.med.purms.modules.supplier.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-01-11 22:21:43
 */
@Data
@TableName("purms_user_info")
public class PurmsUserInfoEntity {

	/** id */
	@TableId(value="id",type = IdType.AUTO)
	private Integer id;

	/** 所属供应商 */
	@TableField("supplier_id")
	private Integer supplierId;

	/** 用户名 */
	@TableField("user_name")
	private String userName;

	/** 联系人 */
	@TableField("contact_name")
	private String contactName;

	/** 手机 */
	@TableField("phone")
	private String phone;

	/** 邮箱 */
	@TableField("email")
	private String email;

	/** 性别 */
	@TableField("sex")
	private String sex;

	/** 通讯地址 */
	@TableField("contact_addr")
	private String contactAddr;

	/** 手机号归属地（通常，国家/地区代码由1到3位数字组成） */
	@TableField("country_code")
	private String countryCode;

	/** 注册日期 */
	@TableField("reg_date")
	private String regDate;

	/** 操作人 */
	@TableField("updter")
	private String updter;

	/** 操作时间 */
	@TableField("update_time")
	private String updateTime;

	/** 删除人 */
	@TableField("delter")
	private String delter;

	/** 删除时间 */
	@TableField("delete_time")
	private String deleteTime;

	/** 状态 */
	/** 状态(逻辑删除) */
	@TableField("is_deleted")
	private Integer isDeleted;
	/** 医院组织id */
	@TableField("hospital_id")
	private String hospitalId;

}
