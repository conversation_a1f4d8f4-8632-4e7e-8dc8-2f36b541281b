package com.jp.med.purms.modules.supplier.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.purms.modules.supplier.dto.PurmsSupplierInfoDto;
import com.jp.med.purms.modules.supplier.vo.PurmsSupplierInfoVo;

import java.util.List;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-01-11 22:21:43
 */
public interface PurmsSupplierInfoReadService extends IService<PurmsSupplierInfoDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<PurmsSupplierInfoVo> queryList(PurmsSupplierInfoDto dto);

    List<PurmsSupplierInfoVo>  querySupplierList(PurmsSupplierInfoDto dto);

	Object listMinPro(PurmsSupplierInfoDto dto);

	PurmsSupplierInfoVo supplierDetails(PurmsSupplierInfoDto dto);

	PurmsSupplierInfoVo supplierSelfDetails(PurmsSupplierInfoDto dto);

	List<PurmsSupplierInfoVo>  querySupplierDetailsList(PurmsSupplierInfoDto dto);

	PurmsSupplierInfoVo admittedSupplierfDetails(PurmsSupplierInfoDto dto);

	String getUrl(PurmsSupplierInfoDto dto);
}

