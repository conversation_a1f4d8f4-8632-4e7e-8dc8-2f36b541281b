package com.jp.med.purms.modules.integration.mapper.write;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 采购系统集成写Mapper
 */
@Mapper
public interface PurmsIntegrationWriteMapper {

	/**
	 * 保存采购申请日志
	 *
	 * @param requestId    申请ID
	 * @param hospitalId   医院ID
	 * @param requestData  请求数据
	 * @param responseData 响应数据
	 * @param status       状态
	 * @param remark       备注
	 * @return 影响行数
	 */
	int savePurchaseRequestLog(@Param("requestId") String requestId,
			@Param("hospitalId") String hospitalId,
			@Param("requestData") String requestData,
			@Param("responseData") String responseData,
			@Param("status") String status,
			@Param("remark") String remark);
}