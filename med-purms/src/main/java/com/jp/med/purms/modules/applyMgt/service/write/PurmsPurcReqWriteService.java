package com.jp.med.purms.modules.applyMgt.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.purms.modules.applyMgt.dto.PurmsPurcReqDto;

/**
 * 采购需求
 * <AUTHOR>
 * @email -
 * @date 2024-01-08 20:38:29
 */
public interface PurmsPurcReqWriteService extends IService<PurmsPurcReqDto> {
    void savePurcReq(PurmsPurcReqDto dto);

    /**
     * 逻辑删除采购申请
     * @param dto
     */
    void deleteByIds(PurmsPurcReqDto dto);

    /**
     * 报销完成更新采购申请
     *
     * @param dto
     * @return
     */
    void updateItemReimId(PurmsPurcReqDto dto);

    /**
     * 采购喊货
     * @param dto
     */
    void callGoods(PurmsPurcReqDto dto);

    /**
     * 取消审核通过还未执行的采购需求
     * @param dto
     */
    void calcPurcReq(PurmsPurcReqDto dto);
}

