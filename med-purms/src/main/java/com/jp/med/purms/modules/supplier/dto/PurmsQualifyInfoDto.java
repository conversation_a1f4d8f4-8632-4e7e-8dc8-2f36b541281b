package com.jp.med.purms.modules.supplier.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;
import java.util.List;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-01-11 22:21:43
 */
@Data
@TableName("purms_qualify_info" )
public class PurmsQualifyInfoDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 资质名称 */
    @TableField("qualify_name")
    private String qualifyName;

    /** 供应商id */
    @TableField("supplier_id")
    private Integer supplierId;

    /** 图片名称 */
    @TableField("photo_name")
    private String photoName;

    /** 图片路径 */
    @TableField("photo_path")
    private String photoPath;

    @TableField(exist = false)
    private String[] photoPaths;

    /** 统一社会信用代码 */
    @TableField("unified_social_credit_code")
    private String unifiedSocialCreditCode;

    /** 注册地址 */
    @TableField("reg_addr")
    private String regAddr;

    /** 法人 */
    @TableField("legal_representative")
    private String legalRepresentative;

    /** 注册资本 */
    @TableField("reg_capital")
    private BigDecimal regCapital;

    /** 说明文件 */
    @TableField("file_name")
    private String fileName;

    /** 文件地址 */
    @TableField("file_path")
    private String filePath;

    /** 提交时间(日期+时间) */
    @TableField("submit_time")
    private String submitTime;

    /** 认证通过时间(日期+时间) */
    @TableField("pass_time")
    private String passTime;

    /** 证书分类 */
    @TableField("category_code")
    private String categoryCode;

    /** 颁发机构 */
    @TableField("award_institute")
    private String awardInstitute;

    /** 发布日期 */
    @TableField("release_date")
    private String releaseDate;

    /** 截止日期 */
    @TableField("deadline")
    private String deadline;

    /** 创建人(用户) */
    @TableField("user_id")
    private String userId;

    /** 用户编辑时间 */
    @TableField("user_up_date")
    private String userUpDate;

    /** 用户删除时间 */
    @TableField("user_del_date")
    private String userDelDate;

    /** 医院组织id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 资质图片 */
    @TableField(exist = false)
    private MultipartFile imgFile;

    /**
     * 附件
     */
    @TableField(exist = false)
    private List<MultipartFile> attFiles;

    @TableField(exist = false)
    private String supplierName;

    @TableField(exist = false)
    private String userName;

    @TableField(exist = false)
    private int filePathIndex;

}
