# 采购系统对外接口文档

## 接口编号规范

采购系统对外接口统一以"PUR"开头，格式为"PURxxxx"，例如 PUR1001、PUR1002 等。

## 接口列表

### PUR1001：获取二级库采购单详情 (新)

- **功能**：获取所有与二级库物资（aset_type 以'03'开头）相关的采购单明细，每条明细包含采购单主信息和物资主数据。
- **数据来源**：通过mmis_aset_info_assist表关联获取物资唯一编码(mat_unique_code)等物资主数据信息。
- **请求参数 (`input` Map<String, Object>)**：
  - startTime: 开始时间 (可选，格式：YYYY-MM-DD HH:mm:ss)
  - endTime: 结束时间 (可选，格式：YYYY-MM-DD HH:mm:ss)
  - 其他参数用于未来扩展，例如按采购单状态等进行过滤。
- **响应数据**：`List<IntegrationPurReqDetailVO>` 结构，包含详细的采购单和物资信息。
- **关键字段**：tripartiteConsumableId来源于mmis_aset_info_assist.mat_unique_code，确保与M1002接口数据一致。

### PUR1002：获取采购分类 (原 PUR1001)

- **功能**：获取所有采购分类信息
- **请求参数**：无需额外参数
- **响应数据**：采购分类列表

### PUR1003：获取采购项目 (原 PUR1002)

- **功能**：获取所有采购项目信息
- **请求参数**：无需额外参数
- **响应数据**：采购项目列表

### PUR1004：提交采购申请 (原 PUR1003)

- **功能**：创建新的采购申请
- **请求参数**：
  - purchaseName: 采购名称
  - purchaseType: 采购类型
  - departmentId: 部门 ID
  - applicant: 申请人
  - description: 描述
  - purchaseItems: 采购明细列表
- **响应数据**：采购申请结果，包含申请 ID

### PUR1005：查询采购申请状态 (原 PUR1004)

- **功能**：查询指定采购申请的状态
- **请求参数**：
  - requestId: 申请 ID
- **响应数据**：采购申请状态信息

### PUR1006：查询采购执行状态 (原 PUR1005)

- **功能**：查询指定采购申请的执行状态
- **请求参数**：
  - requestId: 申请 ID
- **响应数据**：采购执行状态信息

## 接口调用示例

**调用 PUR1001 (获取二级库采购单详情):**

```json
{
  "hospitalId": "zjxrmyy",
  "encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
  "infno": "PUR1001",
  "input": {
    // "startDate": "2023-01-01", // 示例：未来可添加的参数
    // "endDate": "2023-12-31"
  }
}
```

**调用其他接口 (示例 PUR1002):**

```json
{
  "hospitalId": "zjxrmyy",
  "encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
  "infno": "PUR1002",
  "input": {}
}
```

## 响应格式

```json
{
  "code": 200,
  "message": "成功",
  "data": {} // 根据具体接口返回对应的数据结构
}
```
