package com.jp.med.purms.modules.supplier.mapper.read;

import com.jp.med.purms.modules.supplier.dto.PurmsEmpnumCfgDto;
import com.jp.med.purms.modules.supplier.dto.PurmsIndustryCfgDto;
import com.jp.med.purms.modules.supplier.vo.PurmsIndustryCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import feign.Param;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 供应商所属行业配置
 * <AUTHOR>
 * @email -
 * @date 2024-01-11 22:21:43
 */
@Mapper
public interface PurmsIndustryCfgReadMapper extends BaseMapper<PurmsIndustryCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<PurmsIndustryCfgVo> queryList(PurmsIndustryCfgDto dto);

    List<PurmsIndustryCfgVo> queryCfgList(PurmsIndustryCfgDto dto);

	String queryMaxCode(@Param("parentCode") String parentCode);

	Integer selectChildren(PurmsIndustryCfgDto dto);
}
