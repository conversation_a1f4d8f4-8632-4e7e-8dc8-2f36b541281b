package com.jp.med.purms.modules.applyMgt.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.purms.modules.applyMgt.dto.PurmsPurcReqDto;
import com.jp.med.purms.modules.applyMgt.service.read.PurmsPurcReqReadService;
import com.jp.med.purms.modules.applyMgt.service.write.PurmsPurcReqWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 采购需求
 *
 * <AUTHOR>
 * @email -
 * @date 2024-01-08 20:38:29
 */
@Api(value = "采购需求", tags = "采购需求")
@RestController
@RequestMapping("purmsPurcReq")
public class PurmsPurcReqController {

    @Autowired
    private PurmsPurcReqReadService purmsPurcReqReadService;

    @Autowired
    private PurmsPurcReqWriteService purmsPurcReqWriteService;

    /**
     * 查询报销人付款信息
     */
    @ApiOperation("查询报销人付款信息")
    @PostMapping("/queryEmpPurcPaymentInfo")
    public CommonResult<?> queryEmpPurcPaymentInfo(@RequestBody PurmsPurcReqDto dto) {
        return CommonResult.success(purmsPurcReqReadService.queryEmpPurcPaymentInfo(dto));
    }


    /**
     * 查询采购实例编号
     */
    @ApiOperation("查询采购实例编号")
    @GetMapping("/queryPurcProcessInstanceCode")
    public CommonResult<?> queryPurcProcessInstanceCode(@RequestParam("itemNo") String itemNo) {
        return CommonResult.success(purmsPurcReqReadService.queryPurcProcessInstanceCode(itemNo));
    }

    /**
     * 取消审核通过还未执行的采购需求
     */
    @ApiOperation("取消审核通过还未执行的采购需求")
    @PostMapping("/calcPurcReq")
    public CommonResult<?> calcPurcReq(@RequestBody PurmsPurcReqDto dto) {
        purmsPurcReqWriteService.calcPurcReq(dto);
        return CommonResult.success();
    }

    /**
     * 获取采购单PDF
     */
    @ApiOperation("获取采购单PDF")
    @PostMapping("/getPurcReqPdf")
    public CommonResult<?> getPurcReqPdf(@RequestBody PurmsPurcReqDto dto) {
        return CommonResult.success(purmsPurcReqReadService.getPurcReqPdf(dto));
    }

    /**
     * 采购喊货
     */
    @ApiOperation("采购喊货")
    @PostMapping("/callGoods")
    public CommonResult<?> callGoods(@RequestBody PurmsPurcReqDto dto) {
        purmsPurcReqWriteService.callGoods(dto);
        return CommonResult.success();
    }


    /**
     * 根据项目编号更新采购报销Id
     */
    @ApiOperation("根据项目编号更新采购报销Id")
    @PostMapping("/updateItemReimId")
    public CommonResult<?> updateItemReimId(@RequestBody PurmsPurcReqDto dto) {
        purmsPurcReqWriteService.updateItemReimId(dto);
        return CommonResult.success();
    }


    /**
     * 列表
     */
    @ApiOperation("分页查询待执行采购")
    @PostMapping("/queryNoExePurc")
    public CommonResult<?> noExePurcPageList(@RequestBody PurmsPurcReqDto dto) {
        return CommonResult.paging(purmsPurcReqReadService.noExePurcPageList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询审核通过的采购需求")
    @PostMapping("/queryPurcReq")
    public CommonResult<?> queryPurcReq(@RequestBody PurmsPurcReqDto dto) {
        return CommonResult.paging(purmsPurcReqReadService.queryPurcReq(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询采购需求")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody PurmsPurcReqDto dto) {
        return CommonResult.paging(purmsPurcReqReadService.queryList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询采购需求及详情")
    @GetMapping("/queryPurcReqById")
    public CommonResult<?> queryPurcReqById(@RequestParam("id") Integer id) {
        return CommonResult.success(purmsPurcReqReadService.queryPurcReqById(id));
    }

    /**
     * 保存
     */
    @ApiOperation("新增采购需求")
    @PostMapping("/save")
    public CommonResult<?> save(PurmsPurcReqDto dto) {
        purmsPurcReqWriteService.savePurcReq(dto);
        return CommonResult.success();
    }

}
