package com.jp.med.purms.modules.applyMgt.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.purms.modules.applyMgt.dto.PurmsPurcExeDto;
import com.jp.med.purms.modules.applyMgt.dto.PurmsPurcReqDetailDto;
import org.apache.ibatis.annotations.Mapper;

/**
 * 采购需求详情
 * <AUTHOR>
 * @email -
 * @date 2024-01-08 20:38:29
 */
@Mapper
public interface PurmsPurcReqDetailWriteMapper extends BaseMapper<PurmsPurcReqDetailDto> {
    /**
     * 更新采购详情实际单价和实际总金额
     */
    void updateDetailReallyPrice(PurmsPurcReqDetailDto dto);

    /**
     * 更新采购签收数据
     * @param dto
     */
    void updatePurcDetailCfm(PurmsPurcExeDto dto);
}
