package com.jp.med.purms.modules.integration.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购项目DTO
 */
@Data
@ApiModel("采购项目DTO")
public class PurchaseProjectDTO {

	@ApiModelProperty("项目ID")
	private String projectId;

	@ApiModelProperty("项目编码")
	private String code;

	@ApiModelProperty("项目名称")
	private String name;

	@ApiModelProperty("项目类型")
	private String type;

	@ApiModelProperty("预算金额")
	private BigDecimal budget;

	@ApiModelProperty("规格型号")
	private String specification;

	@ApiModelProperty("项目状态 (1: 进行中, 2: 已完成, 3: 已取消)")
	private String status;

	@ApiModelProperty("删除标志 (1: 已删除, 0: 未删除)")
	private Integer deleteFlag;

	@ApiModelProperty("分类ID")
	private String categoryId;
}