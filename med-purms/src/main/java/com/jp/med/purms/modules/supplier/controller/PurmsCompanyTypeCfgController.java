package com.jp.med.purms.modules.supplier.controller;

import com.jp.med.purms.modules.supplier.dto.PurmsIndustryCfgDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.purms.modules.supplier.dto.PurmsCompanyTypeCfgDto;
import com.jp.med.purms.modules.supplier.service.read.PurmsCompanyTypeCfgReadService;
import com.jp.med.purms.modules.supplier.service.write.PurmsCompanyTypeCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 企业性质配置
 * <AUTHOR>
 * @email -
 * @date 2024-01-15 17:08:48
 */
@Api(value = "企业性质配置", tags = "企业性质配置")
@RestController
@RequestMapping("purmsCompanyTypeCfg")
public class PurmsCompanyTypeCfgController {

    @Autowired
    private PurmsCompanyTypeCfgReadService purmsCompanyTypeCfgReadService;

    @Autowired
    private PurmsCompanyTypeCfgWriteService purmsCompanyTypeCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询企业性质配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody PurmsCompanyTypeCfgDto dto){
        return CommonResult.paging(purmsCompanyTypeCfgReadService.queryList(dto));
    }
    /**
     * 选项列表
     */
    @ApiOperation("查询企业性质配置")
    @PostMapping("/cfgList")
    public CommonResult<?> cfgList(@RequestBody PurmsCompanyTypeCfgDto dto){
        return CommonResult.success(purmsCompanyTypeCfgReadService.queryCfgList(dto));
    }


    /**
     * 保存
     */
    @ApiOperation("新增企业性质配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody PurmsCompanyTypeCfgDto dto){
        purmsCompanyTypeCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改企业性质配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody PurmsCompanyTypeCfgDto dto){
        purmsCompanyTypeCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除企业性质配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody PurmsCompanyTypeCfgDto dto){
        purmsCompanyTypeCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

}
