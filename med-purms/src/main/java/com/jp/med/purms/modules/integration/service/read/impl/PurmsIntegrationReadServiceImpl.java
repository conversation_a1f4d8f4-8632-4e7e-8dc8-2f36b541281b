package com.jp.med.purms.modules.integration.service.read.impl;

import com.jp.med.purms.modules.applyMgt.mapper.read.PurmsPurcReqReadMapper;
import com.jp.med.purms.modules.applyMgt.mapper.read.PurmsPurcExeReadMapper;
import com.jp.med.purms.modules.integration.dto.PurchaseCategoryDTO;
import com.jp.med.purms.modules.integration.dto.PurchaseProjectDTO;
import com.jp.med.purms.modules.integration.mapper.read.PurmsIntegrationReadMapper;
import com.jp.med.purms.modules.integration.service.read.PurmsIntegrationReadService;
import com.jp.med.purms.modules.integration.vo.PurchaseExecutionStatusVO;
import com.jp.med.purms.modules.integration.vo.PurchaseRequestStatusVO;
import com.jp.med.purms.modules.integration.vo.IntegrationPurReqDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 采购系统集成读服务实现
 */
@Slf4j
@Service
@Transactional(readOnly = true)
public class PurmsIntegrationReadServiceImpl implements PurmsIntegrationReadService {

	@Autowired
	private PurmsIntegrationReadMapper purmsIntegrationReadMapper;

	@Autowired
	private PurmsPurcReqReadMapper purmsPurcReqReadMapper;

	@Autowired
	private PurmsPurcExeReadMapper purmsPurcExeReadMapper;

	@Override
	public List<PurchaseCategoryDTO> getAllPurchaseCategories(String hospitalId) {
		log.info("获取采购分类, hospitalId: {}", hospitalId);
		// 调用Mapper查询数据
		return purmsIntegrationReadMapper.getAllPurchaseCategories(hospitalId);
	}

	@Override
	public List<PurchaseProjectDTO> getAllPurchaseProjects(String hospitalId) {
		log.info("获取采购项目, hospitalId: {}", hospitalId);
		// 调用Mapper查询数据
		return purmsIntegrationReadMapper.getAllPurchaseProjects(hospitalId);
	}

	@Override
	public PurchaseRequestStatusVO getPurchaseRequestStatus(String requestId, String hospitalId) {
		log.info("获取采购申请状态, requestId: {}, hospitalId: {}", requestId, hospitalId);
		// 调用Mapper查询数据
		return purmsIntegrationReadMapper.getPurchaseRequestStatus(requestId, hospitalId);
	}

	@Override
	public PurchaseExecutionStatusVO getPurchaseExecutionStatus(String requestId, String hospitalId) {
		log.info("获取采购执行状态, requestId: {}, hospitalId: {}", requestId, hospitalId);
		// 调用Mapper查询数据
		return purmsIntegrationReadMapper.getPurchaseExecutionStatus(requestId, hospitalId);
	}

	@Override
	public List<IntegrationPurReqDetailVO> getSecondaryWarehousePurchaseDetails(String hospitalId,
			Map<String, Object> params) {
		log.info("获取二级库物资相关的采购单明细, hospitalId: {}, params: {}", hospitalId, params);
		// 后续可以根据 params 中的具体参数进行更复杂的查询条件组装
		return purmsIntegrationReadMapper.getSecondaryWarehousePurchaseDetails(hospitalId, params);
	}
}