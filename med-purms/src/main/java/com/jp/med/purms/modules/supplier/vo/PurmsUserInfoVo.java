package com.jp.med.purms.modules.supplier.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-01-11 22:21:43
 */
@Data
public class PurmsUserInfoVo {

	/** id */
	private Integer id;

	/** 所属供应商 */
	private Integer supplierId;

	/** 用户名 */
	private String userName;

	/** 联系人 */
	private String contactName;

	/** 手机 */
	private String phone;

	/** 邮箱 */
	private String email;

	/** 性别 */
	private String sex;

	/** 通讯地址 */
	private String contactAddr;

	/** 手机号归属地（通常，国家/地区代码由1到3位数字组成） */
	private String countryCode;

	/** 注册日期 */
	private String regDate;

	/** 操作人 */
	private String updter;

	/** 操作时间 */
	private String updateTime;

	/** 删除人 */
	private String delter;

	/** 删除时间 */
	private String deleteTime;

	/** 状态 */
	private Integer isDeleted;

	/** 医院组织id */
	private String hospitalId;

}
