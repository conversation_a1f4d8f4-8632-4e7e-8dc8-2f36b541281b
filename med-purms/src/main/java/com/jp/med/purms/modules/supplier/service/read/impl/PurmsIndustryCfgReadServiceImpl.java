package com.jp.med.purms.modules.supplier.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.purms.modules.supplier.mapper.read.PurmsIndustryCfgReadMapper;
import com.jp.med.purms.modules.supplier.dto.PurmsIndustryCfgDto;
import com.jp.med.purms.modules.supplier.vo.PurmsIndustryCfgVo;
import com.jp.med.purms.modules.supplier.service.read.PurmsIndustryCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class PurmsIndustryCfgReadServiceImpl extends ServiceImpl<PurmsIndustryCfgReadMapper, PurmsIndustryCfgDto> implements PurmsIndustryCfgReadService {

    @Autowired
    private PurmsIndustryCfgReadMapper purmsIndustryCfgReadMapper;

    @Override
    public List<PurmsIndustryCfgVo> queryList(PurmsIndustryCfgDto dto) {
        return purmsIndustryCfgReadMapper.queryList(dto);
    }

    @Override
    public List<PurmsIndustryCfgVo> queryCfgList(PurmsIndustryCfgDto dto) {
        return purmsIndustryCfgReadMapper.queryCfgList(dto);
    }
}
