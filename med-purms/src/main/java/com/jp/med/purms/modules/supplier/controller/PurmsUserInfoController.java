package com.jp.med.purms.modules.supplier.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.purms.modules.supplier.dto.PurmsUserInfoDto;
import com.jp.med.purms.modules.supplier.service.read.PurmsUserInfoReadService;
import com.jp.med.purms.modules.supplier.service.write.PurmsUserInfoWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-01-11 22:21:43
 */
@Api(value = "${comments}", tags = "${comments}")
@RestController
@RequestMapping("purmsUserInfo")
public class PurmsUserInfoController {

    @Autowired
    private PurmsUserInfoReadService purmsUserInfoReadService;

    @Autowired
    private PurmsUserInfoWriteService purmsUserInfoWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询${comments}")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody PurmsUserInfoDto dto){
        return CommonResult.paging(purmsUserInfoReadService.queryList(dto));
    }

//    /**
//     * 根据供应商id 查用户列表及联系方式
//     */
//    @ApiOperation("查询供应商id，查用户列表及联系方式")
//    @PostMapping("/queryBySupplier")
//    public CommonResult<?> queryBySupplier(@RequestBody PurmsUserInfoDto dto){
//        return CommonResult.success(purmsUserInfoReadService.queryListBySupplierId(dto.getSupplierId()));
//    }


    /**
     * 保存
     */
    //接收formData的数据
    @ApiOperation("新增${comments}")
    @PostMapping("/save")
    public CommonResult<?> save(PurmsUserInfoDto dto){
        purmsUserInfoWriteService.saveUser(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改${comments}")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody PurmsUserInfoDto dto){
        purmsUserInfoWriteService.updateDto(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除${comments}")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody PurmsUserInfoDto dto){
        purmsUserInfoWriteService.removeById(dto);
        return CommonResult.success();
    }

}
