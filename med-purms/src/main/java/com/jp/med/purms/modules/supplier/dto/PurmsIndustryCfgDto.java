package com.jp.med.purms.modules.supplier.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 供应商所属行业配置
 * <AUTHOR>
 * @email -
 * @date 2024-01-11 22:21:43
 */
@Data
@TableName("purms_industry_cfg" )
public class PurmsIndustryCfgDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 行业名称 */
    @TableField("industry_name")
    private String industryName;

    /** 行业代码 */
    @TableField("industry_code")
    private String industryCode;

    /** 上级编码 */
    @TableField("parent_code")
    private String parentCode;

    /** 状态 */
    /** 状态(逻辑删除) */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 医院组织id */
    @TableField("hospital_id")
    private String hospitalId;

}
