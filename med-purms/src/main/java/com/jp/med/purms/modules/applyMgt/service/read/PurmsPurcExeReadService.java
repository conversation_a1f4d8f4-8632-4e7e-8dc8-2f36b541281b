package com.jp.med.purms.modules.applyMgt.service.read;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.purms.modules.applyMgt.dto.PurmsPurcExeDto;
import com.jp.med.purms.modules.applyMgt.vo.PurmsPurcExeVo;

import java.util.List;

/**
 * 采购执行
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-16 09:31:56
 */
public interface PurmsPurcExeReadService extends IService<PurmsPurcExeDto> {

    /**
     * 分页查询列表
     *
     * @param dto
     * @return
     */
    Page<Object> queryPageList(PurmsPurcExeDto dto);

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<PurmsPurcExeVo> queryList(PurmsPurcExeDto dto);

    /**
     * 分页查询科室采购回执签收
     *
     * @param dto
     * @return
     */
    Page<Object> orgPurcCfmPageList(PurmsPurcExeDto dto);

}

