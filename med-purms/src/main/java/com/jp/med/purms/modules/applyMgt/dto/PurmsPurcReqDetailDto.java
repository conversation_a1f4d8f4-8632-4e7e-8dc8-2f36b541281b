package com.jp.med.purms.modules.applyMgt.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 采购需求详情
 *
 * <AUTHOR>
 * @email -
 * @date 2024-04-17 15:15:30
 */
@Data
@TableName("purms_purc_req_detail")
public class PurmsPurcReqDetailDto extends CommonQueryDto implements Serializable {
    private static final long serialVersionUID = 1L; // 建议显式定义 serialVersionUID


    /**
     * $column.comments
     */
    @TableId("id")
    private Integer id;

    /**
     * 采购执行id
     */
    @TableField("exe_id")
    private Integer exeId;

    /** 签收人 */
    @TableField("cfm_emp_code")
    private String cfmEmpCode;

    /** 签收人签名 */
    @TableField("cfm_sign")
    private String cfmSign;


    /** 签收时间 */
    @TableField("cfm_time")
    private String cfmTime;

    /**
     * 签收备注
     */
    @TableField("cfm_remark")
    private String cfmRemark;

    /**
     * 申请明细ids
     */
    @TableField(exist = false)
    private List<Integer> ids;

    /**
     * 项目编号
     */
    @TableField("item_no")
    private String itemNo;

    /**
     * 物资名称
     */
    @TableField("prod_name")
    private String prodName;

    /**
     * 规格
     */
    @TableField("spec")
    private String spec;

    /**
     * 型号
     */
    @TableField("mol")
    private String mol;

    /**
     * 单位
     */
    @TableField("unt")
    private String unt;

    /**
     * 数量
     */
    @TableField("cnt")
    private BigDecimal cnt;

    /**
     * 实际数量数量
     */
    @TableField("really_cnt")
    private BigDecimal reallyCnt;

    /**
     * 单价
     */
    @TableField("pric")
    private BigDecimal pric;

    /**
     * 实际单价
     */
    @TableField("really_price")
    private BigDecimal reallyPrice;

    /**
     * 总金额
     */
    @TableField("sumamt")
    private BigDecimal sumamt;

    /**
     * 实际总金额
     */
    @TableField("really_sumamt")
    private BigDecimal reallySumamt;

    /**
     * 描述/备注
     */
    @TableField("dscr")
    private String dscr;

    /**
     * 参与医保
     */
    @TableField("join_hi")
    private String joinHi;

    /**
     * 创建人
     */
    @TableField("crter")
    private String crter;

    /**
     * 创建时间
     */
    @TableField("crte_time")
    private String crteTime;

    /**
     * 有效标志
     */
    @TableField("active_flag")
    private String activeFlag;

    /**
     * 医疗机构id
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 是否已经加入库
     */
    @TableField("add_mmis")
    private String addMmis;

    /**
     * 入库数量
     */
    @TableField("add_mmis_cnt")
    private BigDecimal addMmisCnt;
    /**
     * 物资样品库编码
     */
    @TableField("mmis_code")
    private String mmisCode;
}
