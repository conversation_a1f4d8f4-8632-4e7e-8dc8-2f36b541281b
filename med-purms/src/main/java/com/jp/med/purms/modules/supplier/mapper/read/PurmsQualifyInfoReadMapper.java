package com.jp.med.purms.modules.supplier.mapper.read;

import com.jp.med.purms.modules.supplier.dto.PurmsQualifyInfoDto;
import com.jp.med.purms.modules.supplier.vo.PurmsQualifyInfoVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * ${comments}
 * <AUTHOR>
 * @email -
 * @date 2024-01-11 22:21:43
 */
@Mapper
public interface PurmsQualifyInfoReadMapper extends BaseMapper<PurmsQualifyInfoDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<PurmsQualifyInfoVo> queryList(PurmsQualifyInfoDto dto);
}
