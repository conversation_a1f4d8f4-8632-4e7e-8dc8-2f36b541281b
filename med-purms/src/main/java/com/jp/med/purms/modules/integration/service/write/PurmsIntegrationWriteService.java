package com.jp.med.purms.modules.integration.service.write;

import com.jp.med.purms.modules.integration.vo.PurchaseRequestResultVO;

import java.util.Map;

/**
 * 采购系统集成写服务
 */
public interface PurmsIntegrationWriteService {

	/**
	 * 处理采购申请
	 *
	 * @param requestMap 请求数据Map
	 * @param hospitalId 医院ID
	 * @param encryptKey 加密密钥
	 * @return 处理结果
	 */
	PurchaseRequestResultVO processPurchaseRequest(Map<String, Object> requestMap, String hospitalId,
			String encryptKey);

	/**
	 * 取消采购申请
	 *
	 * @param requestId  申请ID
	 * @param reason     取消原因
	 * @param hospitalId 医院ID
	 * @return 处理结果
	 */
	boolean cancelPurchaseRequest(String requestId, String reason, String hospitalId);
}