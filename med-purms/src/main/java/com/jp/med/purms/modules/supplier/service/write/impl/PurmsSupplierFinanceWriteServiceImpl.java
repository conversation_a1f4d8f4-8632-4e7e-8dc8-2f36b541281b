package com.jp.med.purms.modules.supplier.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.purms.modules.supplier.mapper.write.PurmsSupplierFinanceWriteMapper;
import com.jp.med.purms.modules.supplier.dto.PurmsSupplierFinanceDto;
import com.jp.med.purms.modules.supplier.service.write.PurmsSupplierFinanceWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 采购供应商财务信息表
 * <AUTHOR>
 * @email -
 * @date 2024-12-17 22:56:10
 */
@Service
@Transactional(readOnly = false)
public class PurmsSupplierFinanceWriteServiceImpl extends ServiceImpl<PurmsSupplierFinanceWriteMapper, PurmsSupplierFinanceDto> implements PurmsSupplierFinanceWriteService {
}
