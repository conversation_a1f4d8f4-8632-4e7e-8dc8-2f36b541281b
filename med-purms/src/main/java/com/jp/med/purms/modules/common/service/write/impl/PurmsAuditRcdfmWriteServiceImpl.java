package com.jp.med.purms.modules.common.service.write.impl;

import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.purms.modules.applyMgt.mapper.write.PurmsGwApplyWriteMapper;
import com.jp.med.purms.modules.applyMgt.mapper.write.PurmsPurcReqWriteMapper;
import com.jp.med.purms.modules.common.service.write.PurmsAuditRcdfmWriteService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 审核明细表
 *
 * <AUTHOR>
 * @email -
 * @date 2024-01-09 11:18:59
 */
@Service
@Transactional(readOnly = false)
public class PurmsAuditRcdfmWriteServiceImpl implements PurmsAuditRcdfmWriteService {

    @Autowired
    private PurmsPurcReqWriteMapper purmsPurcReqWriteMapper;
    @Autowired
    private PurmsGwApplyWriteMapper purmsGwApplyWriteMapper;

    @Override
    public void complete(AuditDetail dto) {
        String bchno = dto.getAuditBchno();

        if (StringUtils.isNotEmpty(dto.getAuditResult()) && StringUtils.isNotEmpty(bchno)) {

            switch (bchno.substring(0, 6)) {
                //4001-采购申请 审核完成
                case AuditConst.PURMS_PURC_APPLY:

                    if (AuditConst.STATE_COMPLETE.equals(dto.getAuditResult())) {
                        //1.更新物资采购申请状态
                        purmsPurcReqWriteMapper.updatePurcReqChkState(bchno, MedConst.TYPE_1);

                        //2.通知申请人-申请成功 TODO
                    } else if (AuditConst.STATE_FAIL.equals(dto.getAuditResult())) {
                        //1.审核失败，更新物资申请状态
                        purmsPurcReqWriteMapper.updatePurcReqChkState(dto.getAuditBchno(), MedConst.TYPE_2);

                        //2.通知申请人-申请失败 TODO
                    }

                //4002-挂网申请
                case AuditConst.PURMS_GW_APPLY:
                    if (AuditConst.STATE_COMPLETE.equals(dto.getAuditResult())) {
                        //1.更新挂网申请状态
                        purmsGwApplyWriteMapper.updateGwReqChkState(bchno, MedConst.TYPE_1);

                        //2.通知申请人-申请成功 TODO
                    } else if (AuditConst.STATE_FAIL.equals(dto.getAuditResult())) {
                        //1.审核失败，更新挂网申请状态
                        purmsGwApplyWriteMapper.updateGwReqChkState(dto.getAuditBchno(), MedConst.TYPE_2);

                        //2.通知申请人-申请失败 TODO
                    }
            }
        }

    }
}
