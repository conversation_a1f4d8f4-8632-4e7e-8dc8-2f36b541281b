//package cn.iocoder.yudao.module.bpm.framework.rabbitMq;
//
//import cn.iocoder.yudao.module.bpm.event.BpmProcessInstanceStatusEvent;
//import com.jp.med.common.dto.bpm.BpmProcessInstanceStatus;
//import lombok.Getter;
//import org.springframework.amqp.core.Binding;
//import org.springframework.amqp.core.BindingBuilder;
//import org.springframework.amqp.core.Queue;
//import org.springframework.amqp.core.TopicExchange;
//import org.springframework.amqp.rabbit.annotation.RabbitListener;
//import org.springframework.amqp.rabbit.core.RabbitAdmin;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.PostConstruct;
//
//import static com.jp.med.common.constant.RabbitMQConfig.BPM_EXCHANGE_NAME;
//import static com.jp.med.common.constant.RabbitMQConfig.BPM_MESSAGE_KEY;
//
////@Service
//public class DynamicConsumerService2 {
//
//
//    @Getter
//    private final String queueName = "hrpDemo22222";
////    @Autowired
//    private RabbitAdmin rabbitAdmin;
////    @Autowired
//    private RabbitTemplate rabbitTemplate;
//
//    @PostConstruct
//    public void init() {
//        Queue queue = new Queue(queueName, false);
//        rabbitAdmin.declareQueue(queue);
//
//        // Bind queue to the specific routing key pattern
//        String bindingKey = BPM_MESSAGE_KEY + "." + queueName;
//        Binding binding = BindingBuilder.bind(queue).to(new TopicExchange(BPM_EXCHANGE_NAME)).with(bindingKey);
//        rabbitAdmin.declareBinding(binding);
//    }
//
//    @RabbitListener(queues = "#{dynamicConsumerService2.queueName}")
//    public void receiveMessage(BpmProcessInstanceStatus message) {
//        System.out.println("Received Message: " + message.getProcessDefinitionKey() + " - " + message.getStatus());
//        // 执行业务逻辑
//    }
//
//}