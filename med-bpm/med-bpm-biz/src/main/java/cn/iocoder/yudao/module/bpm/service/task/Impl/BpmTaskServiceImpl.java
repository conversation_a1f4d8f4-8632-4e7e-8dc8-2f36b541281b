package cn.iocoder.yudao.module.bpm.service.task.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.bpm.common.pojo.PageResult;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.task.*;
import cn.iocoder.yudao.module.bpm.convert.task.BpmTaskConvert;
import cn.iocoder.yudao.module.bpm.dal.dataobject.task.BpmTaskTimeEditDO;
import cn.iocoder.yudao.module.bpm.dal.mysql.task.BpmTaskTimeEditMapper;
import cn.iocoder.yudao.module.bpm.enums.task.BpmCommentTypeEnum;
import cn.iocoder.yudao.module.bpm.enums.task.BpmDeleteReasonEnum;
import cn.iocoder.yudao.module.bpm.enums.task.BpmTaskSignTypeEnum;
import cn.iocoder.yudao.module.bpm.enums.task.BpmTaskStatusEnum;
import cn.iocoder.yudao.module.bpm.framework.flowable.config.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.enums.BpmConstants;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.BpmnModelUtils;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.FlowableUtils;
import cn.iocoder.yudao.module.bpm.mock.user.AdminUserApi;
import cn.iocoder.yudao.module.bpm.mock.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.bpm.service.definition.BpmModelService;
import cn.iocoder.yudao.module.bpm.service.message.Impl.BpmMessageServiceImpl;
import cn.iocoder.yudao.module.bpm.service.task.BpmProcessInstanceCopyService;
import cn.iocoder.yudao.module.bpm.service.task.BpmProcessInstanceService;
import cn.iocoder.yudao.module.bpm.service.task.BpmTaskService;
import cn.iocoder.yudao.module.bpm.utils.PageUtils;
import cn.iocoder.yudao.module.bpm.utils.date.DateUtils;
import cn.iocoder.yudao.module.bpm.utils.json.JsonUtils;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.OSSUtil;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ManagementService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.iocoder.yudao.module.bpm.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.bpm.framework.flowable.core.util.CurrentUserInfo.getLoginUserId;
import static cn.iocoder.yudao.module.bpm.utils.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.bpm.utils.collection.CollectionUtils.*;

/**
 * 流程任务实例 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BpmTaskServiceImpl implements BpmTaskService {

    @Resource
    private TaskService taskService;
    @Resource
    private HistoryService historyService;
    @Resource
    private RuntimeService runtimeService;
    @Resource
    private ManagementService managementService;

    @Resource
    private BpmProcessInstanceService processInstanceService;
    @Resource
    private BpmProcessInstanceCopyService processInstanceCopyService;
    @Resource
    private BpmModelService bpmModelService;
    @Resource
    private BpmMessageServiceImpl messageService;

    @Resource
    private BpmTaskTimeEditMapper bpmTaskTimeEditMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private JsonUtils jsonUtils;

    @Override
    public PageResult<Task> getTaskTodoPage(String userId, BpmTaskPageReqVO pageVO) {
        TaskQuery taskQuery = taskService.createTaskQuery()
                .taskAssignee(String.valueOf(userId)) // 分配给自己
                .active()
                .includeProcessVariables()
                .orderByTaskCreateTime().desc(); // 创建时间倒序

        if (CollUtil.isNotEmpty(pageVO.getProcessCategory())) {
            taskQuery.processCategoryIn(pageVO.getProcessCategory());
        }

        if (StrUtil.isNotBlank(pageVO.getName())) {
            taskQuery.taskNameLike("%" + pageVO.getName() + "%");
        }

        if  (StrUtil.isNotBlank(pageVO.getCreateReason())) {
            taskQuery.taskVariableValueLike("createReason", "%" + pageVO.getCreateReason() + "%");
        }

        if (StrUtil.isNotEmpty(pageVO.getBusinessKey())) {
            taskQuery.processInstanceBusinessKeyLike("%" + pageVO.getBusinessKey() + "%");
        }

        if (ArrayUtil.isNotEmpty(pageVO.getCreateTime())) {
            taskQuery.taskCreatedAfter(DateUtils.of(pageVO.getCreateTime()[0]));
            taskQuery.taskCreatedBefore(DateUtils.of(pageVO.getCreateTime()[1]));
        }
        long count = taskQuery.count();
        if (count == 0) {
            return PageResult.empty();
        }
        List<Task> tasks = taskQuery.listPage(PageUtils.getStart(pageVO), pageVO.getPageSize());
        return new PageResult<>(tasks, count);
    }

    /**
     * 获取已办任务分页
     *
     * @param userId 查询用户
     * @param pageVO 分页请求
     * @return 已办任务分页
     */
    @Override
    public PageResult<HistoricTaskInstance> getTaskDonePage(String userId, BpmTaskPageReqVO pageVO) {
        // 使用原生SQL查询方式，避免historyService.createNativeHistoricTaskInstanceQuery()调用问题

        // 1. 构建基础查询SQL
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT t.*,h2.org_name FROM ACT_HI_TASKINST t ");
        sqlBuilder.append("LEFT JOIN ACT_HI_PROCINST p ON t.PROC_INST_ID_ = p.PROC_INST_ID_ ");
        sqlBuilder.append("LEFT JOIN ACT_RE_PROCDEF d ON p.PROC_DEF_ID_ = d.ID_ ");
        sqlBuilder.append("LEFT JOIN ACT_RE_MODEL m ON d.KEY_ = m.KEY_ ");
        sqlBuilder.append("LEFT JOIN hrm_employee_info h1 ON p.START_USER_ID_ = h1.emp_code ");
        sqlBuilder.append("LEFT JOIN hrm_org h2 ON h1.org_id = h2.org_id ");
        sqlBuilder.append("LEFT JOIN ACT_HI_VARINST v ON p.PROC_INST_ID_ = v.PROC_INST_ID_ AND v.NAME_ = 'createReason' ");

        // 2. 构建查询条件
        List<Object> params = new ArrayList<>();
        StringBuilder whereSql = new StringBuilder(" WHERE t.ASSIGNEE_ = ? AND t.END_TIME_ IS NOT NULL ");
        params.add(userId);

        // 2.1 流程分类条件
        if (CollUtil.isNotEmpty(pageVO.getProcessCategory())) {
            whereSql.append(" AND m.CATEGORY_ IN (");
            for (int i = 0; i < pageVO.getProcessCategory().size(); i++) {
                if (i > 0) {
                    whereSql.append(",");
                }
                whereSql.append("?");
                params.add(pageVO.getProcessCategory().get(i));
            }
            whereSql.append(") ");
        }

        // 2.2 流程名称条件
        if (StrUtil.isNotBlank(pageVO.getName())) {
            whereSql.append(" AND p.NAME_ LIKE ? ");
            params.add("%" + pageVO.getName() + "%");
        }

        // 2.3 业务标识条件
        if (StrUtil.isNotEmpty(pageVO.getBusinessKey())) {
            whereSql.append(" AND p.BUSINESS_KEY_ LIKE ? ");
            params.add("%" + pageVO.getBusinessKey() + "%");
        }

        // 2.4 任务名称条件
        if (StrUtil.isNotBlank(pageVO.getTaskName())) {
            whereSql.append(" AND t.NAME_ LIKE ? ");
            params.add("%" + pageVO.getTaskName() + "%");
        }

        // 2.5 发起人条件
        if (StrUtil.isNotEmpty(pageVO.getEmpCode())) {
            whereSql.append(" AND p.START_USER_ID_ = ? ");
            params.add(pageVO.getEmpCode());
        }

        // 2.5.1 发起人科室条件
        if (StrUtil.isNotEmpty(pageVO.getDeptCode())) {
            whereSql.append(" AND h1.org_id = ? ");
            params.add(pageVO.getDeptCode());
        }

        // 2.6 创建原因条件
        if (StrUtil.isNotBlank(pageVO.getCreateReason())) {
            whereSql.append(" AND v.TEXT_ LIKE ? ");
            params.add("%" + pageVO.getCreateReason() + "%");
        }

        // 2.7 创建时间条件
        if (ArrayUtil.isNotEmpty(pageVO.getCreateTime())) {
            whereSql.append(" AND t.CREATE_TIME_ >= ? AND t.CREATE_TIME_ <= ? ");
            params.add(DateUtils.of(pageVO.getCreateTime()[0]));
            params.add(DateUtils.of(pageVO.getCreateTime()[1]));
        }

        // 3. 获取总数
        StringBuilder countSql = new StringBuilder("SELECT COUNT(DISTINCT t.ID_) FROM ACT_HI_TASKINST t ");
        countSql.append("LEFT JOIN ACT_HI_PROCINST p ON t.PROC_INST_ID_ = p.PROC_INST_ID_ ");
        countSql.append("LEFT JOIN ACT_RE_PROCDEF d ON p.PROC_DEF_ID_ = d.ID_ ");
        countSql.append("LEFT JOIN ACT_RE_MODEL m ON d.KEY_ = m.KEY_ ");
        countSql.append("LEFT JOIN hrm_employee_info h1 ON p.START_USER_ID_ = h1.emp_code ");
        countSql.append("LEFT JOIN hrm_org h2 ON h1.org_id = h2.org_id ");
        countSql.append("LEFT JOIN ACT_HI_VARINST v ON p.PROC_INST_ID_ = v.PROC_INST_ID_ AND v.NAME_ = 'createReason' ");
        countSql.append(whereSql);

        Long count = jdbcTemplate.queryForObject(countSql.toString(), params.toArray(), Long.class);
        if (count == 0) {
            return PageResult.empty();
        }

        // 4. 构建排序和分页
        sqlBuilder.append(whereSql);
        sqlBuilder.append(" ORDER BY t.END_TIME_ DESC ");
        sqlBuilder.append(" LIMIT ? OFFSET ? ");
        params.add(pageVO.getPageSize());
        params.add(PageUtils.getStart(pageVO));

        // 5. 执行查询
        String string = sqlBuilder.toString();
        System.out.println(string);
        List<Map<String, Object>> taskMaps = jdbcTemplate.queryForList(string, params.toArray());

        // 6. 构建结果
        List<HistoricTaskInstance> tasks = new ArrayList<>();
        for (Map<String, Object> taskMap : taskMaps) {
            String processInstanceId = (String) taskMap.get("PROC_INST_ID_");

            // 获取历史任务实例
            HistoricTaskInstance task = historyService.createHistoricTaskInstanceQuery()
                    .taskId((String) taskMap.get("ID_"))
                    .includeProcessVariables()
                    .includeTaskLocalVariables()
                    .singleResult();

            if (task != null) {
                tasks.add(task);
            }
        }

        return new PageResult<>(tasks, count);
    }

    @Override
    public PageResult<HistoricTaskInstance> getTaskPage(String userId, BpmTaskPageReqVO pageVO) {
        HistoricTaskInstanceQuery taskQuery = historyService.createHistoricTaskInstanceQuery()
                .includeTaskLocalVariables()
                .taskTenantId(FlowableUtils.getTenantId())
                .orderByHistoricTaskInstanceEndTime().desc(); // 审批时间倒序
        if (StrUtil.isNotBlank(pageVO.getName())) {
            taskQuery.taskNameLike("%" + pageVO.getName() + "%");
        }

        if (StrUtil.isNotEmpty(pageVO.getBusinessKey())) {
            taskQuery.processInstanceBusinessKey(pageVO.getBusinessKey());
        }

        if (StrUtil.isNotBlank(pageVO.getCreateReason())) {
            taskQuery.processVariableValueLike("createReason", "%" + pageVO.getCreateReason() + "%");
        }

        if (ArrayUtil.isNotEmpty(pageVO.getCreateTime())) {
            taskQuery.taskCreatedAfter(DateUtils.of(pageVO.getCreateTime()[0]));
            taskQuery.taskCreatedAfter(DateUtils.of(pageVO.getCreateTime()[1]));
        }
        // 执行查询
        long count = taskQuery.count();
        if (count == 0) {
            return PageResult.empty();
        }
        List<HistoricTaskInstance> tasks = taskQuery.listPage(PageUtils.getStart(pageVO), pageVO.getPageSize());
        return new PageResult<>(tasks, count);
    }

    @Override
    public List<Task> getTasksByProcessInstanceIds(List<String> processInstanceIds) {
        if (CollUtil.isEmpty(processInstanceIds)) {
            return Collections.emptyList();
        }
        return taskService.createTaskQuery().processInstanceIdIn(processInstanceIds).list();
    }

    @Override
    public List<HistoricTaskInstance> getTaskListByProcessInstanceId(String processInstanceId) {
        List<HistoricTaskInstance> tasks = historyService.createHistoricTaskInstanceQuery()
                .includeTaskLocalVariables()
                .processInstanceId(processInstanceId)
                .orderByHistoricTaskInstanceStartTime().desc() // 创建时间倒序
                .list();
        if (CollUtil.isEmpty(tasks)) {
            return Collections.emptyList();
        }
        return tasks;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveTask(String userId, @Valid BpmTaskApproveReqVO reqVO) {
        // 1.1 校验任务存在
        Task task = validateTask(userId, reqVO.getId());
        // 1.2 校验流程实例存在
        ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
        if (instance == null) {
            throw exception(PROCESS_INSTANCE_NOT_EXISTS);
        }

        // 2. 抄送用户
        if (CollUtil.isNotEmpty(reqVO.getCopyUserIds())) {
            processInstanceCopyService.createProcessInstanceCopy(reqVO.getCopyUserIds(), reqVO.getId());
        }

        // 如果自定义审批时间不为空，则插入当前时间
        BpmTaskTimeEditDO timeEditDO = new BpmTaskTimeEditDO();
        timeEditDO.setProcessInstanceId(task.getProcessInstanceId())
                .setTaskId(task.getId())
                // .setBusinessKey() 暂时不设置businessKey
                .setStartTime(LocalDateTime.now())
                .setEndTime(LocalDateTime.now());
        if (null != reqVO.getAuditTime()) {
            timeEditDO.setStartTime(reqVO.getAuditTime());
            timeEditDO.setEndTime(reqVO.getAuditTime());
        }
        insertTaskMeditTime(timeEditDO);

        // 情况一：被委派的任务，不调用 complete 去完成任务
        if (DelegationState.PENDING.equals(task.getDelegationState())) {
            approveDelegateTask(reqVO, task);
            return;
        }

        // 情况二：审批有【后】加签的任务
        if (BpmTaskSignTypeEnum.AFTER.getType().equals(task.getScopeType())) {
            log.info("情况二 审批有后加签的任务");
            approveAfterSignTask(task, reqVO);
            return;
        }

        /**
         * 情况二.一 审批有前加签的任务
         */

//        if (BpmTaskSignTypeEnum.BEFORE.getType().equals(task.getScopeType())) {
//            log.info("情况二.一 审批有前加签的任务");
//            // 有前加签任务需要当前任务继承父节点的变量（优先使用本节点的）
//            Map<String, Object> parentTaskVariables = taskService.getVariables(task.getParentTaskId());
//            if (CollUtil.isNotEmpty(parentTaskVariables)) {
//                // 循环父节点变量 判断本节点是否存在 不存在那么put
//                parentTaskVariables.forEach((key, value) -> {
//                    if (!taskService.getVariables(task.getId()).containsKey(key)) {
//                        taskService.setVariableLocal(task.getId(), key, value);
//                    }
//                });
//            }
//        }

        // 情况三：审批普通的任务。大多数情况下，都是这样
        // 3.1 更新 task 状态、原因
        updateTaskStatusAndReason(task.getId(), BpmTaskStatusEnum.APPROVE.getStatus(), reqVO.getReason());
        // 3.2 添加评论
        taskService.addComment(task.getId(), task.getProcessInstanceId(), BpmCommentTypeEnum.APPROVE.getType(),
                BpmCommentTypeEnum.APPROVE.formatComment(reqVO.getReason()));
        // 3.3 调用 BPM complete 去完成任务
        // 其中，variables 是存储动态表单到 local 任务级别。过滤一下，避免 ProcessInstance 系统级的变量被占用
        Map<String, Object> reqVOVariables = reqVO.getVariables();
        if (CollUtil.isNotEmpty(reqVOVariables)) {
            // 3.4 处理签名
            if (reqVOVariables.containsKey(BpmConstants.TASK_VARIABLE_SIGN_URL)) {
                String urlPath = (String) reqVOVariables.get(BpmConstants.TASK_VARIABLE_SIGN_URL);
                if (StrUtil.isNotBlank(urlPath)) {
                    // 正则表达式匹配 URL 中的文件名
                    String regex = "/core/user/([^?]+)";
                    Pattern pattern = Pattern.compile(regex);
                    Matcher matcher = pattern.matcher(urlPath);

                    if (matcher.find()) {
                        String fileName = matcher.group(1); // 提取文件名
                        String newFileName = IdUtil.fastUUID() + "." + FileUtil.getSuffix(fileName);

                        OSSUtil.copyFile("core", "user/" + fileName, "bpm", "user/sign/" + newFileName);
                        // reqVO..setVariableLocal(task.getId(), BpmConstants.TASK_VARIABLE_SIGN_URL,
                        // "bpm/user/sign/");
                        reqVOVariables.put(BpmConstants.TASK_VARIABLE_SIGN_URL, "/user/sign/" + newFileName);
                    } else {
                        throw new AppException("请先设置签名，签名失败");
                    }
                }
            }
            // 3.5 处理附件

            List<BpmTaskApproveReqVO.Attachment> attachmentList = reqVO.getAttachmentList();
            if (attachmentList != null && !attachmentList.isEmpty()) {
                // 保存签名和附件
                attachmentList.forEach(att -> {
                    String path = OSSUtil.uploadFile("bpm", "audit/att/", att.getFile());
                    att.setFile(null);
                    att.setFileUrl(path);
                });
            }
            // 3.6下一审批人
            runtimeService.setVariables(instance.getProcessInstanceId(), reqVOVariables);
            if (reqVOVariables.containsKey(BpmConstants.NEXT_APPROVER)) {
                String o = (String) reqVOVariables.get(BpmConstants.NEXT_APPROVER);
                runtimeService.setVariable(instance.getProcessInstanceId(), BpmConstants.NEXT_APPROVER, o);
            } else {
                runtimeService.setVariable(instance.getProcessInstanceId(), BpmConstants.NEXT_APPROVER, null);
            }
            reqVOVariables.put(BpmConstants.TASK_VARIABLE_ATTACHMENT_LIST, JsonUtils.toJsonString(attachmentList));

            Map<String, Object> variables = FlowableUtils.filterTaskFormVariable(reqVOVariables);
            taskService.complete(task.getId(), variables, true);

        } else {
            taskService.complete(task.getId());
        }

        // 【加签专属】处理加签任务
        handleParentTaskIfSign(task.getParentTaskId());
    }

    /**
     * 审批通过存在"后加签"的任务。
     * <p>
     * 注意：该任务不能马上完成，需要一个中间状态（APPROVING），并激活剩余所有子任务（PROCESS）为可审批处理
     * 如果马上完成，则会触发下一个任务，甚至如果没有下一个任务则流程实例就直接结束了！
     *
     * @param task  当前任务
     * @param reqVO 前端请求参数
     */
    private void approveAfterSignTask(Task task, BpmTaskApproveReqVO reqVO) {
        // 更新父 task 状态 + 原因
        updateTaskStatusAndReason(task.getId(), BpmTaskStatusEnum.APPROVING.getStatus(), reqVO.getReason());

        // 2. 激活子任务
        List<Task> childrenTaskList = getTaskListByParentTaskId(task.getId());
        for (Task childrenTask : childrenTaskList) {
            taskService.resolveTask(childrenTask.getId());
            // 更新子 task 状态
            updateTaskStatus(childrenTask.getId(), BpmTaskStatusEnum.RUNNING.getStatus());
        }
    }

    /**
     * 如果父任务是有前后【加签】的任务，如果它【加签】出来的子任务都被处理，需要处理父任务：
     * <p>
     * 1. 如果是【向前】加签，则需要重新激活父任务，让它可以被审批
     * 2. 如果是【向后】加签，则需要完成父任务，让它完成审批
     *
     * @param parentTaskId 父任务编号
     */
    private void handleParentTaskIfSign(String parentTaskId) {
        if (StrUtil.isBlank(parentTaskId)) {
            return;
        }
        // 1.1 判断是否还有子任务。如果没有，就不处理
        Long childrenTaskCount = getTaskCountByParentTaskId(parentTaskId);
        if (childrenTaskCount > 0) {
            return;
        }
        // 1.2 只处理加签的父任务
        Task parentTask = validateTaskExist(parentTaskId);
        String scopeType = parentTask.getScopeType();
        if (BpmTaskSignTypeEnum.of(scopeType) == null) {
            return;
        }

        // 2. 子任务已处理完成，清空 scopeType 字段，修改 parentTask 信息，方便后续可以继续向前后向后加签
        TaskEntityImpl parentTaskImpl = (TaskEntityImpl) parentTask;
        parentTaskImpl.setScopeType(null);
        taskService.saveTask(parentTaskImpl);

        // 3.1 情况一：处理向【向前】加签
        if (BpmTaskSignTypeEnum.BEFORE.getType().equals(scopeType)) {
            // 3.1.1 owner 重新赋值给父任务的 assignee，这样它就可以被审批
            taskService.resolveTask(parentTaskId);
            // 3.1.2 更新流程任务 status
            updateTaskStatus(parentTaskId, BpmTaskStatusEnum.RUNNING.getStatus());
            // 3.2 情况二：处理向【向后】加签
        } else if (BpmTaskSignTypeEnum.AFTER.getType().equals(scopeType)) {
            // 只有 parentTask 处于 APPROVING 的情况下，才可以继续 complete 完成
            // 否则，一个未审批的 parentTask 任务，在加签出来的任务都被减签的情况下，就直接完成审批，这样会存在问题
            Integer status = (Integer) parentTask.getTaskLocalVariables().get(BpmConstants.TASK_VARIABLE_STATUS);
            if (ObjectUtil.notEqual(status, BpmTaskStatusEnum.APPROVING.getStatus())) {
                return;
            }
            // 3.2.2 完成自己（因为它已经没有子任务，所以也可以完成）
            updateTaskStatus(parentTaskId, BpmTaskStatusEnum.APPROVE.getStatus());
            taskService.complete(parentTaskId);
        }

        // 4. 递归处理父任务
        handleParentTaskIfSign(parentTask.getParentTaskId());
    }

    /**
     * 审批被委派的任务
     *
     * @param reqVO 前端请求参数，包含当前任务ID，审批意见等
     * @param task  当前被审批的任务
     */
    private void approveDelegateTask(BpmTaskApproveReqVO reqVO, Task task) {
        // 1. 添加审批意见
        AdminUserRespDTO currentUser = adminUserApi.getUser(getLoginUserId());
        AdminUserRespDTO ownerUser = adminUserApi.getUser((task.getOwner())); // 发起委托的用户
        Assert.notNull(ownerUser, "委派任务找不到原审批人，需要检查数据");
        taskService.addComment(reqVO.getId(), task.getProcessInstanceId(), BpmCommentTypeEnum.DELEGATE_END.getType(),
                BpmCommentTypeEnum.DELEGATE_END.formatComment(currentUser.getEmpName(), ownerUser.getEmpName(),
                        reqVO.getReason()));

        // 2.1 调用 resolveTask 完成任务。
        // 底层调用 TaskHelper.changeTaskAssignee(task, task.getOwner())：将 owner 设置为
        // assignee
        taskService.resolveTask(task.getId());
        // 2.2 更新 task 状态 + 原因
        updateTaskStatusAndReason(task.getId(), BpmTaskStatusEnum.RUNNING.getStatus(), reqVO.getReason());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectTask(String userId, @Valid BpmTaskRejectReqVO reqVO) {
        // 1.1 校验任务存在
        Task task = validateTask(userId, reqVO.getId());
        // 1.2 校验流程实例存在
        ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
        if (instance == null) {
            throw exception(PROCESS_INSTANCE_NOT_EXISTS);
        }

        // 2.1 更新流程实例为不通过
        updateTaskStatusAndReason(task.getId(), BpmTaskStatusEnum.REJECT.getStatus(), reqVO.getReason());
        // 2.2 添加评论
        taskService.addComment(task.getId(), task.getProcessInstanceId(), BpmCommentTypeEnum.REJECT.getType(),
                BpmCommentTypeEnum.REJECT.formatComment(reqVO.getReason()));

        // 3. 更新流程实例，审批不通过！
        processInstanceService.updateProcessInstanceReject(instance.getProcessInstanceId(), reqVO.getReason());
    }

    /**
     * 新增任务手动修改审批时间
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertTaskMeditTime(BpmTaskTimeEditDO bteDO) {
        bpmTaskTimeEditMapper.insert(bteDO);
    }

    /**
     * 删除任务手动修改审批时间
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTaskMeditTime(BpmTaskTimeEditDO bteDO) {
        LambdaQueryWrapperX<BpmTaskTimeEditDO> bpmTaskTimeEditDOLambdaQueryWrapperX = new LambdaQueryWrapperX<BpmTaskTimeEditDO>()
                .eq(BpmTaskTimeEditDO::getProcessInstanceId, bteDO.getProcessInstanceId())
                .eq(BpmTaskTimeEditDO::getTaskId, bteDO.getTaskId());
        bpmTaskTimeEditMapper.delete(bpmTaskTimeEditDOLambdaQueryWrapperX);
    }

    /**
     * 更新流程任务的 status 状态
     *
     * @param id     任务编号
     * @param status 状态
     */
    private void updateTaskStatus(String id, Integer status) {
        taskService.setVariableLocal(id, BpmConstants.TASK_VARIABLE_STATUS, status);
    }

    /**
     * 更新流程任务的 status 状态、reason 理由
     *
     * @param id     任务编号
     * @param status 状态
     * @param reason 理由（审批通过、审批不通过的理由）
     */
    private void updateTaskStatusAndReason(String id, Integer status, String reason) {
        updateTaskStatus(id, status);
        taskService.setVariableLocal(id, BpmConstants.TASK_VARIABLE_REASON, reason);
    }

    /**
     * 校验任务是否存在，并且是否是分配给自己的任务
     *
     * @param userId 用户 id
     * @param taskId task id
     */
    private Task validateTask(String userId, String taskId) {
        Task task = validateTaskExist(taskId);
        if (!Objects.equals(userId, (task.getAssignee()))) {
            throw exception(TASK_OPERATE_FAIL_ASSIGN_NOT_SELF);
        }
        return task;
    }

    @Override
    public void updateTaskStatusWhenCreated(Task task) {
        Integer status = (Integer) task.getTaskLocalVariables().get(BpmConstants.TASK_VARIABLE_STATUS);
        if (status != null) {
            log.error("[updateTaskStatusWhenCreated][taskId({}) 已经有状态({})]", task.getId(), status);
            return;
        }
        updateTaskStatus(task.getId(), BpmTaskStatusEnum.RUNNING.getStatus());
        ProcessInstance processInstance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
        if (processInstance == null) {
            log.error("[updateTaskExtAssign][taskId({}) 任务不存在]", task.getId());
            return;
        }
        if (processInstance.getStartUserId() == null) {
            log.info("[updateTaskExtAssign][getStartUserId({}) getStartUserId不存在]", task.getId());
            return;
        }
        AdminUserRespDTO startUser = adminUserApi.getUser((processInstance.getStartUserId()));
        messageService.sendMessageWhenTaskAssigned(BpmTaskConvert.INSTANCE.convert(processInstance, startUser, task));
    }

    @Override
    public void updateTaskStatusWhenCanceled(String taskId) {
        Task task = getTask(taskId);
        // 1. 可能只是活动，不是任务，所以查询不到
        if (task == null) {
            log.error("[updateTaskStatusWhenCanceled][taskId({}) 任务不存在]", taskId);
            return;
        }

        // 2. 更新 task 状态 + 原因
        Integer status = (Integer) task.getTaskLocalVariables().get(BpmConstants.TASK_VARIABLE_STATUS);
        if (BpmTaskStatusEnum.isEndStatus(status)) {
            log.error("[updateTaskStatusWhenCanceled][taskId({}) 处于结果({})，无需进行更新]", taskId,
                    BpmTaskStatusEnum.getByStatus(status).getName());
            return;
        }
        updateTaskStatusAndReason(taskId, BpmTaskStatusEnum.CANCEL.getStatus(),
                BpmDeleteReasonEnum.CANCEL_BY_SYSTEM.getReason());
        // 补充说明：由于 Task 被删除成 HistoricTask 后，无法通过 taskService.addComment
        // 添加理由，所以无法存储具体的取消理由
    }

    @Override
    public void updateTaskExtAssign(Task task) {
        // 发送通知。在事务提交时，批量执行操作，所以直接查询会无法查询到 ProcessInstance，所以这里是通过监听事务的提交来实现。

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {

            @Override
            public void afterCommit() {
                if (StrUtil.isEmpty(task.getAssignee())) {
                    return;
                }
                ProcessInstance processInstance = processInstanceService
                        .getProcessInstance(task.getProcessInstanceId());
                if (processInstance == null) {
                    log.error("[updateTaskExtAssign][taskId({}) 任务不存在]", task.getId());
                    return;
                }
                if (processInstance.getStartUserId() == null) {
                    log.info("[updateTaskExtAssign][getStartUserId({}) getStartUserId不存在]", task.getId());
                    return;
                }
                AdminUserRespDTO startUser = adminUserApi.getUser((processInstance.getStartUserId()));
                // 在 updateTaskStatusWhenCreated 时发送
                // messageService.sendMessageWhenTaskAssigned(BpmTaskConvert.INSTANCE.convert(processInstance,
                // startUser, task));
            }

        });
    }

    private Task validateTaskExist(String id) {
        Task task = getTask(id);
        if (task == null) {
            throw exception(TASK_NOT_EXISTS);
        }
        return task;
    }

    @Override
    public Task getTask(String id) {
        return taskService.createTaskQuery().taskId(id).includeTaskLocalVariables().singleResult();
    }

    private HistoricTaskInstance getHistoricTask(String id) {
        return historyService.createHistoricTaskInstanceQuery().taskId(id).includeTaskLocalVariables().singleResult();
    }

    @Override
    public List<UserTask> getUserTaskListByReturn(String id) {
        // 1.1 校验当前任务 task 存在
        Task task = validateTaskExist(id);
        // 1.2 根据流程定义获取流程模型信息
        BpmnModel bpmnModel = bpmModelService.getBpmnModelByDefinitionId(task.getProcessDefinitionId());
        FlowElement source = BpmnModelUtils.getFlowElementById(bpmnModel, task.getTaskDefinitionKey());
        if (source == null) {
            throw exception(TASK_NOT_EXISTS);
        }

        // 2.1 查询该任务的前置任务节点的 key 集合
        List<UserTask> previousUserList = BpmnModelUtils.getPreviousUserTaskList(source, null, null);
        if (CollUtil.isEmpty(previousUserList)) {
            return Collections.emptyList();
        }
        // 2.2 过滤：只有串行可到达的节点，才可以回退。类似非串行、子流程无法退回
        previousUserList.removeIf(userTask -> !BpmnModelUtils.isSequentialReachable(source, userTask, null));
        return previousUserList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void returnTask(String userId, BpmTaskReturnReqVO reqVO) {
        // 1.1 当前任务 task
        Task task = validateTask(userId, reqVO.getId());
        if (task.isSuspended()) {
            throw exception(TASK_IS_PENDING);
        }
        // 1.2 校验源头和目标节点的关系，并返回目标元素
        FlowElement targetElement = validateTargetTaskCanReturn(task.getTaskDefinitionKey(),
                reqVO.getTargetTaskDefinitionKey(), task.getProcessDefinitionId());

        // 2. 调用 Flowable 框架的回退逻辑
        returnTaskLogic(task, targetElement, reqVO);
    }

    public void resetTask(String userId, BpmTaskResetReqVO reqVO) {
        // 1. 参数校验
        if (StrUtil.isBlank(userId)) {
            throw exception(USER_ID_REQUIRED);
        }
        if (StrUtil.isBlank(reqVO.getId())) {
            throw exception(TASK_ID_REQUIRED);
        }
        if (StrUtil.isBlank(reqVO.getProcessInstanceId())) {
            throw exception(PROCESS_INSTANCE_ID_REQUIRED);
        }

        // 2. 获取流程实例，校验是否存在且未结束
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(reqVO.getProcessInstanceId())
                .singleResult();
        if (processInstance == null) {
            throw exception(PROCESS_INSTANCE_NOT_EXISTS);
        }
        if (processInstance.isEnded()) {
            throw exception(PROCESS_INSTANCE_ALREADY_ENDED);
        }

        // 3. 查询要回退的历史任务
        HistoricTaskInstance historicTask = historyService.createHistoricTaskInstanceQuery()
                .taskId(reqVO.getId())
                .finished()
                .singleResult();
        if (historicTask == null) {
            throw exception(HISTORIC_TASK_NOT_EXISTS);
        }

        // 4. 校验当前用户是否为历史任务的处理人
        if (!userId.equals(historicTask.getAssignee())) {
            throw exception(TASK_RESET_FAIL_NOT_ORIGINAL_ASSIGNEE);
        }

        // 5. 查询该历史任务之后的所有已完成任务
        List<HistoricTaskInstance> subsequentTasks = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(reqVO.getProcessInstanceId())
                .finished()
                .orderByHistoricTaskInstanceEndTime().asc()
                .list()
                .stream()
                .filter(task -> task.getEndTime().after(historicTask.getEndTime()))
                .collect(Collectors.toList());

        // 6. 检查是否存在后续已完成的任务
        if (!subsequentTasks.isEmpty()) {
            throw exception(TASK_RESET_FAIL_SUBSEQUENT_TASK_COMPLETED);
        }

        // 7. 执行任务重置
        // 7.1 获取当前活动的任务
        Task currentTask = taskService.createTaskQuery()
                .processInstanceId(reqVO.getProcessInstanceId())
                .active()
                .singleResult();
        if (currentTask == null) {
            throw exception(CURRENT_TASK_NOT_EXISTS);
        }

        // 7.2 获取目标节点信息
        FlowElement targetElement = validateTargetTaskCanReturn(currentTask.getTaskDefinitionKey(),
                historicTask.getTaskDefinitionKey(), historicTask.getProcessDefinitionId());

        // 7.3 构建回退请求
        BpmTaskReturnReqVO returnReqVO = new BpmTaskReturnReqVO();
        returnReqVO.setTargetTaskDefinitionKey(historicTask.getTaskDefinitionKey());
        returnReqVO.setReason(StrUtil.blankToDefault(reqVO.getReason(), "任务重置"));

        // 7.4 执行回退
        returnTaskLogic(currentTask, targetElement, returnReqVO);
    }

    /**
     * 校验任务重置权限
     */
    private void validateTaskResetPermission(String userId, Task task, String processInstanceId) {
        // 1. 校验任务处理人权限
        if (!userId.equals(task.getAssignee())) {
            throw exception(TASK_OPERATE_FAIL_NOT_ASSIGNEE);
        }

        // 2. 查询已完成的任务
        List<HistoricTaskInstance> completedTasks = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .finished()
                .orderByHistoricTaskInstanceEndTime().desc()
                .list();

        // 3. 检查是否存在后续已完成的任务
        if (CollUtil.isNotEmpty(completedTasks)) {
            for (HistoricTaskInstance completedTask : completedTasks) {
                if (completedTask.getEndTime().after(task.getCreateTime())) {
                    throw exception(TASK_RESET_FAIL_SUBSEQUENT_TASK_COMPLETED);
                }
            }
        }
    }

    /**
     * 回退流程节点时，校验目标任务节点是否可回退
     *
     * @param sourceKey           当前任务节点 Key
     * @param targetKey           目标任务节点 key
     * @param processDefinitionId 当前流程定义 ID
     * @return 目标任务节点元素
     */
    private FlowElement validateTargetTaskCanReturn(String sourceKey, String targetKey, String processDefinitionId) {
        // 1.1 获取流程模型信息
        BpmnModel bpmnModel = bpmModelService.getBpmnModelByDefinitionId(processDefinitionId);
        // 1.3 获取当前任务节点元素
        FlowElement source = BpmnModelUtils.getFlowElementById(bpmnModel, sourceKey);
        // 1.3 获取跳转的节点元素
        FlowElement target = BpmnModelUtils.getFlowElementById(bpmnModel, targetKey);
        if (target == null) {
            throw exception(TASK_TARGET_NODE_NOT_EXISTS);
        }

        // 2.2 只有串行可到达的节点，才可以回退。类似非串行、子流程无法退回
        if (!BpmnModelUtils.isSequentialReachable(source, target, null)) {
            throw exception(TASK_RETURN_FAIL_SOURCE_TARGET_ERROR);
        }
        return target;
    }

    /**
     * 执行回退逻辑
     *
     * @param currentTask   当前回退的任务
     * @param targetElement 需要回退到的目标任务
     * @param reqVO         前端参数封装
     */
    public void returnTaskLogic(Task currentTask, FlowElement targetElement, BpmTaskReturnReqVO reqVO) {
        // 1. 获得所有需要回撤的任务 taskDefinitionKey，用于稍后的 moveActivityIdsToSingleActivityId 回撤
        // 1.1 获取所有正常进行的任务节点 Key
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(currentTask.getProcessInstanceId())
                .list();
        List<String> runTaskKeyList = convertList(taskList, Task::getTaskDefinitionKey);
        // 1.2 通过 targetElement 的出口连线，计算在 runTaskKeyList 有哪些 key 需要被撤回
        // 为什么不直接使用 runTaskKeyList 呢？因为可能存在多个审批分支，例如说：A -> B -> C 和 D -> F，而只要 C 撤回到
        // A，需要排除掉 F
        List<UserTask> returnUserTaskList = BpmnModelUtils.iteratorFindChildUserTasks(targetElement, runTaskKeyList,
                null, null);
        List<String> returnTaskKeyList = convertList(returnUserTaskList, UserTask::getId);

        // 2. 给当前要被回退的 task 数组，设置回退意见
        taskList.forEach(task -> {
            // 需要排除掉，不需要设置回退意见的任务
            if (!returnTaskKeyList.contains(task.getTaskDefinitionKey())) {
                return;
            }
            // 2.1 添加评论
            taskService.addComment(task.getId(), currentTask.getProcessInstanceId(),
                    BpmCommentTypeEnum.RETURN.getType(),
                    BpmCommentTypeEnum.RETURN.formatComment(reqVO.getReason()));
            // 2.2 更新 task 状态 + 原因
            updateTaskStatusAndReason(task.getId(), BpmTaskStatusEnum.RETURN.getStatus(), reqVO.getReason());
        });

        // 获取目标节点的历史变量
        String targetTaskDefinitionKey = reqVO.getTargetTaskDefinitionKey();
        Map<String, Object> targetNodeVariables = getTargetNodeHistoricVariables(currentTask.getProcessInstanceId(),
                targetTaskDefinitionKey);

        // 3. 执行驳回前，先将目标节点的变量设置到当前流程实例
        if (!targetNodeVariables.isEmpty()) {
            runtimeService.setVariables(currentTask.getProcessInstanceId(), targetNodeVariables);
        }

        // 4. 执行驳回
        runtimeService.createChangeActivityStateBuilder()
                .processInstanceId(currentTask.getProcessInstanceId())
                .moveActivityIdsToSingleActivityId(returnTaskKeyList, // 当前要跳转的节点列表( 1 或多)
                        targetTaskDefinitionKey) // targetKey 跳转到的节点(1)
                .changeState();
    } // 这里确保有结束花括号

    /**
     * 获取目标节点的历史变量
     * 先获取本地变量，如果没有再获取全局变量
     *
     * @param processInstanceId 流程实例ID
     * @param taskDefinitionKey 任务定义Key
     * @return 变量Map
     */
    private Map<String, Object> getTargetNodeHistoricVariables(String processInstanceId, String taskDefinitionKey) {
        // 1. 查询目标节点的历史任务实例
        HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .taskDefinitionKey(taskDefinitionKey)
                .orderByTaskCreateTime()
                .desc()
                .list()
                .stream()
                .findFirst()
                .orElse(null);

        if (historicTaskInstance == null) {
            return Collections.emptyMap();
        }

        // 2. 获取历史任务的本地变量
        Map<String, Object> localVariables = historyService.createHistoricVariableInstanceQuery()
                .taskId(historicTaskInstance.getId())
                .list()
                .stream()
                .collect(Collectors.toMap(HistoricVariableInstance::getVariableName,
                        HistoricVariableInstance::getValue));

        // 3. 获取流程实例的全局变量（如果本地变量中找不到某些变量）
        Map<String, Object> processVariables = historyService.createHistoricVariableInstanceQuery()
                .processInstanceId(processInstanceId)
                .excludeTaskVariables()
                .list()
                .stream()
                .collect(Collectors.toMap(
                        HistoricVariableInstance::getVariableName,
                        HistoricVariableInstance::getValue,
                        (existingValue, newValue) -> existingValue // 如果有重复，保留已存在的值
                ));

        // 4. 合并变量，本地变量优先
        Map<String, Object> result = new HashMap<>(processVariables);
        result.putAll(localVariables); // 本地变量覆盖全局变量

        return result;
    } // 这里确保有结束花括号

    /**
     * 执行回退逻辑
     *
     * @param currentTask   当前回退的任务
     * @param targetElement 需要回退到的目标任务
     * @param reqVO         前端参数封装
     */
    public void returnTaskOld(Task currentTask, FlowElement targetElement, BpmTaskReturnReqVO reqVO) {
        // 1. 获得所有需要回撤的任务 taskDefinitionKey，用于稍后的 moveActivityIdsToSingleActivityId 回撤
        // 1.1 获取所有正常进行的任务节点 Key
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(currentTask.getProcessInstanceId())
                .list();
        List<String> runTaskKeyList = convertList(taskList, Task::getTaskDefinitionKey);
        // 1.2 通过 targetElement 的出口连线，计算在 runTaskKeyList 有哪些 key 需要被撤回
        // 为什么不直接使用 runTaskKeyList 呢？因为可能存在多个审批分支，例如说：A -> B -> C 和 D -> F，而只要 C 撤回到
        // A，需要排除掉 F
        List<UserTask> returnUserTaskList = BpmnModelUtils.iteratorFindChildUserTasks(targetElement, runTaskKeyList,
                null, null);
        List<String> returnTaskKeyList = convertList(returnUserTaskList, UserTask::getId);

        // 2. 给当前要被回退的 task 数组，设置回退意见
        taskList.forEach(task -> {
            // 需要排除掉，不需要设置回退意见的任务
            if (!returnTaskKeyList.contains(task.getTaskDefinitionKey())) {
                return;
            }
            // 2.1 添加评论
            taskService.addComment(task.getId(), currentTask.getProcessInstanceId(),
                    BpmCommentTypeEnum.RETURN.getType(),
                    BpmCommentTypeEnum.RETURN.formatComment(reqVO.getReason()));
            // 2.2 更新 task 状态 + 原因
            updateTaskStatusAndReason(task.getId(), BpmTaskStatusEnum.RETURN.getStatus(), reqVO.getReason());
        });

        // 3. 执行驳回
        runtimeService.createChangeActivityStateBuilder()
                .processInstanceId(currentTask.getProcessInstanceId())
                .moveActivityIdsToSingleActivityId(returnTaskKeyList, // 当前要跳转的节点列表( 1 或多)
                        reqVO.getTargetTaskDefinitionKey()) // targetKey 跳转到的节点(1)
                .changeState();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delegateTask(String userId, BpmTaskDelegateReqVO reqVO) {
        String taskId = reqVO.getId();
        // 1.1 校验任务
        Task task = validateTask(userId, reqVO.getId());
        if (task.getAssignee().equals(reqVO.getDelegateUserId().toString())) { // 校验当前审批人和被委派人不是同一人
            throw exception(TASK_DELEGATE_FAIL_USER_REPEAT);
        }
        // 1.2 校验目标用户存在
        AdminUserRespDTO delegateUser = adminUserApi.getUser(reqVO.getDelegateUserId());
        if (delegateUser == null) {
            throw exception(TASK_DELEGATE_FAIL_USER_NOT_EXISTS);
        }

        // 2. 添加委托意见
        AdminUserRespDTO currentUser = adminUserApi.getUser(userId);
        taskService.addComment(taskId, task.getProcessInstanceId(), BpmCommentTypeEnum.DELEGATE_START.getType(),
                BpmCommentTypeEnum.DELEGATE_START.formatComment(currentUser.getEmpName(), delegateUser.getEmpName(),
                        reqVO.getReason()));

        // 3.1 设置任务所有人 (owner) 为原任务的处理人 (assignee)
        taskService.setOwner(taskId, task.getAssignee());
        // 3.2 执行委派，将任务委派给 delegateUser
        taskService.delegateTask(taskId, reqVO.getDelegateUserId());
        // 3.3 更新 task 状态。
        // 为什么不更新原因？因为原因目前主要给审批通过、不通过时使用
        updateTaskStatus(taskId, BpmTaskStatusEnum.DELEGATE.getStatus());
    }

    @Override
    public void transferTask(String userId, BpmTaskTransferReqVO reqVO) {
        String taskId = reqVO.getId();
        // 1.1 校验任务
        Task task = validateTask(userId, reqVO.getId());
        if (task.getAssignee().equals(reqVO.getAssigneeUserId())) { // 校验当前审批人和被转办人不是同一人
            throw exception(TASK_TRANSFER_FAIL_USER_REPEAT);
        }
        // 1.2 校验目标用户存在
        AdminUserRespDTO assigneeUser = adminUserApi.getUser(reqVO.getAssigneeUserId());
        if (assigneeUser == null) {
            throw exception(TASK_TRANSFER_FAIL_USER_NOT_EXISTS);
        }

        // 2. 添加委托意见
        AdminUserRespDTO currentUser = adminUserApi.getUser(userId);
        taskService.addComment(taskId, task.getProcessInstanceId(), BpmCommentTypeEnum.TRANSFER.getType(),
                BpmCommentTypeEnum.TRANSFER.formatComment(currentUser.getEmpName(), assigneeUser.getEmpName(),
                        reqVO.getReason()));

        // 3.1 设置任务所有人 (owner) 为原任务的处理人 (assignee)
        taskService.setOwner(taskId, task.getAssignee());
        // 3.2 执行转办（审批人），将任务转办给 assigneeUser
        // 委托（ delegate）和转办（transfer）的差别，就在这块的调用！！！！
        taskService.setAssignee(taskId, reqVO.getAssigneeUserId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSignTask(String userId, BpmTaskSignCreateReqVO reqVO) {
        // 1. 获取和校验任务
        TaskEntityImpl taskEntity = validateTaskCanCreateSign(userId, reqVO);
        List<AdminUserRespDTO> userList = adminUserApi.getUserList(reqVO.getUserIds());
        if (CollUtil.isEmpty(userList)) {
            throw exception(TASK_SIGN_CREATE_USER_NOT_EXIST);
        }

        // 2. 处理当前任务
        // 2.1 开启计数功能，主要用于为了让表 ACT_RU_TASK 中的 SUB_TASK_COUNT_ 字段记录下总共有多少子任务，后续可能有用
        taskEntity.setCountEnabled(true);
        // 2.2 向前加签，设置 owner，置空 assign。等子任务都完成后，再调用 resolveTask 重新将 owner 设置为 assign
        // 原因是：不能和向前加签的子任务一起审批，需要等前面的子任务都完成才能审批
        if (reqVO.getType().equals(BpmTaskSignTypeEnum.BEFORE.getType())) {
            taskEntity.setOwner(taskEntity.getAssignee());
            taskEntity.setAssignee(null);
        }
        // 2.4 记录加签方式，完成任务时需要用到判断
        taskEntity.setScopeType(reqVO.getType());
        // 2.5 保存当前任务修改后的值
        taskService.saveTask(taskEntity);
        // 2.6 更新 task 状态为 WAIT，只有在向前加签的时候
        if (reqVO.getType().equals(BpmTaskSignTypeEnum.BEFORE.getType())) {
            updateTaskStatus(taskEntity.getId(), BpmTaskStatusEnum.WAIT.getStatus());
        }

        // 3. 创建加签任务
        createSignTaskList(convertList(reqVO.getUserIds(), String::valueOf), taskEntity);

        // 4. 记录加签的评论到 task 任务
        AdminUserRespDTO currentUser = adminUserApi.getUser(userId);
        String comment = StrUtil.format(BpmCommentTypeEnum.ADD_SIGN.getComment(),
                currentUser.getEmpName(), BpmTaskSignTypeEnum.nameOfType(reqVO.getType()),
                String.join(",", convertList(userList, AdminUserRespDTO::getEmpName)), reqVO.getReason());
        taskService.addComment(reqVO.getId(), taskEntity.getProcessInstanceId(), BpmCommentTypeEnum.ADD_SIGN.getType(),
                comment);
    }

    /**
     * 校验任务是否可以加签，主要校验加签类型是否一致：
     * <p>
     * 1. 如果存在"向前加签"的任务，则不能"向后加签"
     * 2. 如果存在"向后加签"的任务，则不能"向前加签"
     *
     * @param userId 当前用户 ID
     * @param reqVO  请求参数，包含任务 ID 和加签类型
     * @return 当前任务
     */
    private TaskEntityImpl validateTaskCanCreateSign(String userId, BpmTaskSignCreateReqVO reqVO) {
        TaskEntityImpl taskEntity = (TaskEntityImpl) validateTask(userId, reqVO.getId());
        // 向前加签和向后加签不能同时存在
        if (taskEntity.getScopeType() != null
                && ObjectUtil.notEqual(taskEntity.getScopeType(), reqVO.getType())) {
            throw exception(TASK_SIGN_CREATE_TYPE_ERROR,
                    BpmTaskSignTypeEnum.nameOfType(taskEntity.getScopeType()),
                    BpmTaskSignTypeEnum.nameOfType(reqVO.getType()));
        }

        // 同一个 key 的任务，审批人不重复
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(taskEntity.getProcessInstanceId())
                .taskDefinitionKey(taskEntity.getTaskDefinitionKey()).list();
        List<String> currentAssigneeList = convertListByFlatMap(taskList, task -> // 需要考虑 owner 的情况，因为向后加签时，它暂时没
                // assignee 而是 owner
                Stream.of((task.getAssignee()), (task.getOwner())));
        if (CollUtil.containsAny(currentAssigneeList, reqVO.getUserIds())) {
            List<AdminUserRespDTO> userList = adminUserApi
                    .getUserList(CollUtil.intersection(currentAssigneeList, reqVO.getUserIds()));
            throw exception(TASK_SIGN_CREATE_USER_REPEAT,
                    String.join(",", convertList(userList, AdminUserRespDTO::getEmpName)));
        }
        return taskEntity;
    }

    /**
     * 创建加签子任务
     *
     * @param userIds    被加签的用户 ID
     * @param taskEntity 被加签的任务
     */
    private void createSignTaskList(List<String> userIds, TaskEntityImpl taskEntity) {
        if (CollUtil.isEmpty(userIds)) {
            return;
        }
        // 创建加签人的新任务，全部基于 taskEntity 为父任务来创建
        for (String addSignId : userIds) {
            if (StrUtil.isBlank(addSignId)) {
                continue;
            }
            createSignTask(taskEntity, addSignId);
        }
    }

    /**
     * 创建加签子任务
     *
     * @param parentTask 父任务
     * @param assignee   子任务的执行人
     */
    private void createSignTask(TaskEntityImpl parentTask, String assignee) {
        // 1. 生成子任务
        TaskEntityImpl task = (TaskEntityImpl) taskService.newTask(IdUtil.fastSimpleUUID());
        BpmTaskConvert.INSTANCE.copyTo(parentTask, task);

        // 2.1 向前加签，设置审批人
        if (BpmTaskSignTypeEnum.BEFORE.getType().equals(parentTask.getScopeType())) {
            task.setAssignee(assignee);
            // 2.2 向后加签，设置 owner 不设置 assignee 是因为不能同时审批，需要等父任务完成
        } else {
            task.setOwner(assignee);
        }
        // 2.3 保存子任务
        taskService.saveTask(task);

        // 3. 向后前签，设置子任务的状态为 WAIT，因为需要等父任务审批完
        if (BpmTaskSignTypeEnum.AFTER.getType().equals(parentTask.getScopeType())) {
            updateTaskStatus(task.getId(), BpmTaskStatusEnum.WAIT.getStatus());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSignTask(String userId, BpmTaskSignDeleteReqVO reqVO) {
        // 1.1 校验 task 可以被减签
        Task task = validateTaskCanSignDelete(reqVO.getId());
        // 1.2 校验取消人存在
        AdminUserRespDTO cancelUser = null;
        if (StrUtil.isNotBlank(task.getAssignee())) {
            cancelUser = adminUserApi.getUser((task.getAssignee()));
        }
        if (cancelUser == null && StrUtil.isNotBlank(task.getOwner())) {
            cancelUser = adminUserApi.getUser((task.getOwner()));
        }
        Assert.notNull(cancelUser, "任务中没有所有者和审批人，数据错误");

        // 2.1 获得子任务列表，包括子任务的子任务
        List<Task> childTaskList = getAllChildTaskList(task);
        childTaskList.add(task);
        // 2.2 更新子任务为已取消
        String cancelReason = StrUtil.format("任务被取消，原因：由于[{}]操作[减签]，", cancelUser.getEmpName());
        childTaskList.forEach(childTask -> updateTaskStatusAndReason(childTask.getId(),
                BpmTaskStatusEnum.CANCEL.getStatus(), cancelReason));
        // 2.3 删除任务和所有子任务
        taskService.deleteTasks(convertList(childTaskList, Task::getId));

        // 3. 记录日志到父任务中。先记录日志是因为，通过 handleParentTask 方法之后，任务可能被完成了，并且不存在了，会报异常，所以先记录
        AdminUserRespDTO user = adminUserApi.getUser(userId);
        taskService.addComment(task.getParentTaskId(), task.getProcessInstanceId(),
                BpmCommentTypeEnum.SUB_SIGN.getType(),
                StrUtil.format(BpmCommentTypeEnum.SUB_SIGN.getComment(), user.getEmpName(), cancelUser.getEmpName()));

        // 4. 处理当前任务的父任务
        handleParentTaskIfSign(task.getParentTaskId());
    }

    /**
     * 校验任务是否能被减签
     *
     * @param id 任务编号
     * @return 任务信息
     */
    private Task validateTaskCanSignDelete(String id) {
        Task task = validateTaskExist(id);
        if (task.getParentTaskId() == null) {
            throw exception(TASK_SIGN_DELETE_NO_PARENT);
        }
        Task parentTask = getTask(task.getParentTaskId());
        if (parentTask == null) {
            throw exception(TASK_SIGN_DELETE_NO_PARENT);
        }
        if (BpmTaskSignTypeEnum.of(parentTask.getScopeType()) == null) {
            throw exception(TASK_SIGN_DELETE_NO_PARENT);
        }
        return task;
    }

    /**
     * 获得所有子任务列表
     *
     * @param parentTask 父任务
     * @return 所有子任务列表
     */
    private List<Task> getAllChildTaskList(Task parentTask) {
        List<Task> result = new ArrayList<>();
        // 1. 递归获取子级
        Stack<Task> stack = new Stack<>();
        stack.push(parentTask);
        // 2. 递归遍历
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            if (stack.isEmpty()) {
                break;
            }
            // 2.1 获取子任务们
            Task task = stack.pop();
            List<Task> childTaskList = getTaskListByParentTaskId(task.getId());
            // 2.2 如果非空，则添加到 stack 进一步递归
            if (CollUtil.isNotEmpty(childTaskList)) {
                stack.addAll(childTaskList);
                result.addAll(childTaskList);
            }
        }
        return result;
    }

    @Override
    public List<Task> getTaskListByParentTaskId(String parentTaskId) {
        String tableName = managementService.getTableName(TaskEntity.class);
        // taskService.createTaskQuery() 没有 parentId 参数，所以写 sql 查询
        String sql = "select ID_,NAME_,OWNER_,ASSIGNEE_ from " + tableName + " where PARENT_TASK_ID_=#{parentTaskId}";
        return taskService.createNativeTaskQuery().sql(sql).parameter("parentTaskId", parentTaskId).list();
    }

    /**
     * 获取子任务个数
     *
     * @param parentTaskId 父任务 ID
     * @return 剩余子任务个数
     */
    private Long getTaskCountByParentTaskId(String parentTaskId) {
        String tableName = managementService.getTableName(TaskEntity.class);
        String sql = "SELECT COUNT(1) from " + tableName + " WHERE PARENT_TASK_ID_=#{parentTaskId}";
        return taskService.createNativeTaskQuery().sql(sql).parameter("parentTaskId", parentTaskId).count();
    }

    @Override
    public Map<String, String> getTaskNameByTaskIds(Collection<String> taskIds) {
        if (CollUtil.isEmpty(taskIds)) {
            return Collections.emptyMap();
        }
        List<Task> tasks = taskService.createTaskQuery().taskIds(taskIds).list();
        return convertMap(tasks, Task::getId, Task::getName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updTaskAuditTime(BpmTaskTimeUpdReqVO reqVO) {
        BpmTaskTimeEditDO timeEditDO = new BpmTaskTimeEditDO();
        LocalDateTime endTime = reqVO.getAuditTime();
        LocalDateTime startTime = reqVO.getAuditTime().withHour(0).withMinute(0).withSecond(0);
        timeEditDO.setProcessInstanceId(reqVO.getProcessInstanceId())
                .setTaskId(reqVO.getTaskId())
                .setStartTime(startTime)
                .setEndTime(endTime);
        deleteTaskMeditTime(timeEditDO);
        insertTaskMeditTime(timeEditDO);
    }

    /**
     * 获取父任务的作用域类型
     *
     * @param task 当前任务
     * @return 父任务的作用域类型
     */
    public String getParentTaskScopeType(Task task) {
        // 1. 获取父任务ID
        String parentTaskId = task.getParentTaskId();

        // 2. 如果没有父任务，则返回null
        if (parentTaskId == null || parentTaskId.isEmpty()) {
            return null;
        }

        // 3. 通过父任务ID查询父任务实例
        Task parentTask = taskService.createTaskQuery()
                .taskId(parentTaskId)
                .singleResult();

        // 4. 如果父任务不存在（可能已完成），尝试从历史记录中查询
        if (parentTask == null) {
            HistoricTaskInstance historicParentTask = historyService.createHistoricTaskInstanceQuery()
                    .taskId(parentTaskId)
                    .singleResult();

            if (historicParentTask != null) {
                return historicParentTask.getScopeType();
            }
            return null;
        }

        // 5. 返回父任务的作用域类型
        return parentTask.getScopeType();
    }
}
