package cn.iocoder.yudao.module.bpm.controller.admin.task.vo.task;

import cn.iocoder.yudao.module.bpm.common.pojo.PageParam;
import cn.iocoder.yudao.module.bpm.utils.date.DateUtils;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

//@Schema(description = "管理后台 - 流程任务的的分页 Request VO") // 待办、已办，都使用该分页
@Data
public class BpmTaskPageReqVO extends PageParam {

    // @Schema(description = "流程名称", example = "请假申请")
    private String name;

    /**
     * task名称
     */
    private String taskName;

    // @Schema(description = "流程分类", example = "1")
    private List<String> processCategory;

    /** 业务标识 **/
    private String businessKey;

    /** 发起人 **/
    private String empCode;

    /**
     * 发起科室
     **/
    private String deptCode;

    // @Schema(description = "创建时间")
    @DateTimeFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private String createReason;
}
