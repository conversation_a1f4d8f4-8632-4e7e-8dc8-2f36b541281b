package cn.iocoder.yudao.module.bpm.controller.admin.task.vo.workbench;

import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceRespVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel("管理后台 - 工作台我的流程 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class BpmWorkbenchMyProcessRespVO extends BpmProcessInstanceRespVO {
    // 继承 BpmProcessInstanceRespVO 的所有属性
    // 如有额外需要的工作台特殊属性，可在此添加
}