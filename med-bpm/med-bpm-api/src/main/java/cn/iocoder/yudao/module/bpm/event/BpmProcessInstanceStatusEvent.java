package cn.iocoder.yudao.module.bpm.event;

import cn.iocoder.yudao.module.bpm.enums.task.BpmTaskStatusEnum;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationEvent;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 流程实例的状态（结果）发生变化的 Event
 *

 */
@SuppressWarnings("ALL")
@Data
@Accessors(chain = true)
@ToString
public class BpmProcessInstanceStatusEvent extends ApplicationEvent {

    /**
     * 流程实例的编号
     */
    @NotNull(message = "流程实例的编号不能为空")
    private String id;
    /**
     * 流程实例的 key
     */
    @NotNull(message = "流程实例的 key 不能为空")
    private String processDefinitionKey;
    /**
     * 流程实例的结果
     */
    /**
     * 审批结果
     * <p>
     * 枚举 {@link BpmTaskStatusEnum}
     * 考虑到简单，所以直接复用了 BpmProcessInstanceStatusEnum 枚举，也可以自己定义一个枚举哈
     */
    @NotNull(message = "流程实例的状态不能为空")
    private Integer status;

    /**
     * 全局流程变量
     */
    private Map<String, String> variables;
    /**
     * 任务变量
     */
    private Map<String, String> variablesLocal;


    /**
     * 流程实例对应的业务标识
     * 业务ID
     */
    private String businessKey;

    private String processInstanceId;
    private String processInstanceName;


    private String taskId;
    private String taskName;

    public BpmProcessInstanceStatusEvent(Object source) {
        super(source);
    }


}
