package com.jp.med.core.modules.user.service;

import com.jp.med.core.modules.user.service.write.impl.SmsWriteServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 短信写入服务测试类
 * <AUTHOR>
 * @version 1.0
 * @date 2024/01/01
 */
@ExtendWith(MockitoExtension.class)
public class SmsWriteServiceTest {

    @InjectMocks
    private SmsWriteServiceImpl smsWriteService;

    @Test
    public void testGenerateSmsCode() {
        String code = smsWriteService.generateSmsCode();
        assertNotNull(code);
        assertEquals(6, code.length());
        assertTrue(code.matches("\\d{6}"));
    }

    @Test
    public void testStoreSmsCode() {
        String phoneNumber = "13800138000";
        String code = "123456";
        int expireMinutes = 5;

        // 测试存储验证码（需要Redis环境）
        smsWriteService.storeSmsCode(phoneNumber, code, expireMinutes);

        // 验证存储的验证码
        boolean result = smsWriteService.verifySmsCode(phoneNumber, code);
        assertTrue(result);
    }

    @Test
    public void testSendResetPasswordCode() {
        String phoneNumber = "13800138000";

        // 测试发送重置密码验证码（需要配置短信服务）
        // 注意：这个测试需要Mock SmsFeignService
        // boolean result = smsWriteService.sendResetPasswordCode(phoneNumber);
        // assertTrue(result);

        // 当前只测试参数验证
        boolean result = smsWriteService.sendResetPasswordCode("");
        assertFalse(result);
    }

    @Test
    public void testSendLoginCode() {
        String phoneNumber = "13800138000";

        // 测试发送登录验证码（需要配置短信服务）
        // 注意：这个测试需要Mock SmsFeignService
        // boolean result = smsWriteService.sendLoginCode(phoneNumber);
        // assertTrue(result);

        // 当前只测试参数验证
        boolean result = smsWriteService.sendLoginCode("");
        assertFalse(result);
    }
}