package com.jp.med.core.modules.user.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.common.CommonFeignDto;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.entity.sys.SysMenu;
import com.jp.med.common.entity.user.SysUser;
import com.jp.med.core.modules.user.dto.UserDto;
import com.jp.med.core.modules.user.entity.CustomUserDetails;
import com.jp.med.core.modules.user.feign.gateway.EncryFeignService;
import com.jp.med.core.modules.user.service.read.UserReadService;
import com.jp.med.core.modules.user.service.write.UserWriteService;
import com.jp.med.core.modules.user.service.write.SmsWriteService;
import com.jp.med.core.modules.user.vo.Transfer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/15 10:33
 * @description: 用户
 */
@Api(value = "用户接口", tags = {"用户接口"})
@RestController
@RequestMapping("user")
public class UserController {

    @Autowired
    private UserReadService userReadService;

    @Autowired
    private UserWriteService userWriteService;

    @Autowired
    private EncryFeignService encryFeignService;

    @Autowired
    private SmsWriteService smsWriteService;

    private final static String RAW_PWD="rawPwd";
    private final static String ENCRY_PWD="encryPwd";


    @ApiOperation("查询用户菜单")
    @PostMapping("/queryUserMenu")
    public CommonResult<List<SysMenu>> queryUserMenu(@RequestBody UserDto dto){
        return CommonResult.success(userReadService.queryUserMenu(dto));
    }

    @ApiOperation("查询缓存中用户信息")
    @PostMapping("/queryCacheUserInfo")
    public CommonResult<SysUser> queryCacheUserInfo(@RequestBody UserDto dto){
        // 用户信息是根据redis中存储的转换而来，这样放置修改了浏览器pinia的存储值导致权限变化
        return CommonResult.success(userReadService.queryCacheUserInfo(dto));
    }


    @ApiOperation("根据用户选择科室修改科室")
    @PostMapping("/modifyDept")
    public CommonResult<SysUser> modifyDept(@RequestBody UserDto dto, HttpServletRequest request){
        // 用户信息是根据redis中存储的转换而来，这样放置修改了浏览器pinia的存储值导致权限变化
        userReadService.modifyDept(dto, request);
        return CommonResult.success();
    }

    @ApiOperation("通过用户名查询用户信息")
    @PostMapping("/queryUserByUsername")
    public CommonFeignResult queryUserByUsername(@RequestParam("username") String username){
        return CommonFeignResult.build().put(CommonFeignResult.DATA_KEY, userReadService.queryUserByUsername(username));
    }

    @ApiOperation("通过用户名查询用户所拥有的的菜单权限")
    @PostMapping("/queryUserAuthByUsername")
    public CommonFeignResult queryUserAuthByUsername(@RequestBody CommonFeignDto dto){
        return CommonFeignResult.build().put(CommonFeignResult.DATA_KEY, userReadService.queryUserAuthByUsername(dto));
    }

    @ApiOperation("更改用户密码错误次数")
    @PostMapping("/updatePasswordErrorNum")
    public CommonFeignResult updatePasswordErrorNum(@RequestParam("username") String username){
        return CommonFeignResult.build().put(CommonFeignResult.DATA_KEY, userWriteService.updatePasswordErrorNum(username));
    }

    @ApiOperation("保存用户token")
    @PostMapping("/saveUserToken")
    public CommonFeignResult saveUserToken(@RequestBody CommonFeignDto dto){
        userWriteService.saveUserToken(dto);
        return CommonFeignResult.build();
    }

    @ApiOperation("删除用户token")
    @PostMapping("/deleteUserToken")
    public CommonFeignResult deleteUserToken(@RequestBody CommonFeignDto dto){
        userWriteService.deleteUserToken(dto);
        return CommonFeignResult.build();
    }

    @ApiOperation("解锁")
    @PostMapping("/updateUserInfo")
    public CommonResult<?> updateUserInfo(@RequestBody SysUser dto){
        userWriteService.updateById(dto);
        return CommonResult.success();
    }

    @ApiOperation("通过集合删除用户token")
    @DeleteMapping("/deleteUserTokenByIds")
    public CommonResult<?> deleteUserToken(@RequestBody UserDto dto){
//        userWriteService.deleteUserToken(dto);
        return CommonResult.success();
    }

    @ApiOperation("修改用户密码")
    @PostMapping("/updatePwd")
    public CommonResult<?> updateUserPwd(@RequestBody SysUser dto){
        String password=dto.getPassword();
        HashMap map=encryFeignService.encryPwd(password);
        String encryPwd = (String)map.get(CommonFeignResult.DATA_KEY);
        dto.setPassword(encryPwd);
        dto.setPasswordErrorNum(0);
        dto.setIsLock(MedConst.TYPE_0);
        userWriteService.updateUserPwd(dto);
        return CommonResult.success();
    }


    @ApiOperation("查询所有用户")
    @PostMapping("/list")
    public CommonResult<IPage<SysUser>> list(@RequestBody UserDto dto){
        return CommonResult.paging(userReadService.queryUserList(dto));
    }

    @ApiOperation("用户名是否存在")
    @PostMapping("/existUser")
    public CommonResult<?> existUser(@RequestBody UserDto dto){

        return CommonResult.success(userReadService.existUser(dto));
    }

    @ApiOperation("添加用户")
    @PostMapping("/save")
    public CommonResult<?> saveUser(@RequestBody UserDto dto){
        setPassword(dto);
        userWriteService.saveUser(dto);
        return CommonResult.success();
    }

    @ApiOperation("删除用户")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody UserDto dto){
        userWriteService.deleteUser(dto);
        return CommonResult.success();
    }

    @ApiOperation("修改用户")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody UserDto dto){
        if (StringUtils.isNotEmpty(dto.getPassword())) {
            setPassword(dto);
        }
        userWriteService.updateUser(dto);
        return CommonResult.success();
    }

    @ApiOperation("修改用户签名密码")
    @PutMapping("/updateSignPassword")
    public CommonResult<?> updateSignPassword(@RequestBody UserDto dto){
        if (StringUtils.isNotEmpty(dto.getPassword())) {
            setPassword(dto);
        }
        SysUser sysUser = new SysUser();
        sysUser.setId(dto.getSysUser().getId());
        sysUser.setSignPassword(dto.getPassword());
        userWriteService.updateById(sysUser);
        return CommonResult.success();
    }

    private void setPassword(UserDto dto) {
        String password= dto.getPassword();
        HashMap map=encryFeignService.encryPwd(password);
        String encryPwd = (String)map.get(CommonFeignResult.DATA_KEY);
        dto.setPassword(encryPwd);
    }


    @ApiOperation("判断旧密码是否正确")
    @PostMapping("/isOldPwdTrue")
    public CommonResult<?> isOldPwdTrue(@RequestBody UserDto dto){
        String username = dto.getUsername();
        CommonFeignDto pwdFeign = CommonFeignDto.build();
        CustomUserDetails users=userReadService.queryUserByUsername(username);
        String encryOldPwd=users.getSysUser().getPassword();
        String inputOldPwd=dto.getOldPassword();
        pwdFeign.put(ENCRY_PWD,encryOldPwd);
        pwdFeign.put(RAW_PWD,inputOldPwd);
        HashMap map=encryFeignService.match(pwdFeign);
        return  CommonResult.success(map.get(CommonFeignResult.DATA_KEY));
    }

    @ApiOperation("生成同步用户穿梭框的 options")
    @PostMapping("/genSynUserOptions")
    public CommonResult<?> genSynUserOptions(@RequestBody UserDto dto){
        List<Transfer> list = userReadService.genSynUserOptions(dto);
        return CommonResult.success(list);
    }

    @ApiOperation("将指定员工同步到用户")
    @PutMapping("/synchEmpToUser")
    public CommonResult<?> synchEmpToUser(@RequestBody UserDto dto){
        userWriteService.synchEmpToUser(dto);
        return CommonResult.success();
    }

    @ApiOperation("注册供应商用户")
    @PutMapping("/register")
    public  CommonResult<?> registerUser(@RequestBody UserDto dto){
        setPassword(dto);
        userWriteService.saveSupplierUser(dto);
        return CommonResult.success();
    }



    @ApiOperation("发送短信验证码")
    @PostMapping("/sendSmsCode")
    public CommonResult<?> sendSmsCode(@RequestBody UserDto dto){
        CustomUserDetails customUserDetails = userReadService.queryUserByUsername(dto.getUsername());
        SysUser sysUser = customUserDetails.getSysUser();
        if (StringUtils.isEmpty(dto.getMobile())) {
            return CommonResult.failed("手机号不能为空");
        }
        if (sysUser == null|| !sysUser.getMobile().equals(dto.getMobile()) || !sysUser.getNickname().equals(dto.getNickname()) ){
            return CommonResult.failed("用户不存在");
        }
        // 生成验证码
        String smsCode = smsWriteService.generateSmsCode();

        // 存储验证码到Redis，有效期5分钟
        smsWriteService.storeSmsCode(dto.getMobile(), smsCode, 5);

        // 发送短信验证码
        boolean sendResult = smsWriteService.sendSmsCode(dto.getMobile(), smsCode);

        if (sendResult) {
            return CommonResult.success("短信验证码发送成功");
        } else {
            return CommonResult.failed("短信验证码发送失败");
        }
    }

    @ApiOperation("重置用户密码")
    @PostMapping("/resetPwd")
    public CommonResult<?> resetPwd(@RequestBody UserDto dto){
        // 验证必要参数
        if (StringUtils.isEmpty(dto.getMobile())) {
            return CommonResult.failed("手机号不能为空");
        }
        if (StringUtils.isEmpty(dto.getSmsCode())) {
            return CommonResult.failed("短信验证码不能为空");
        }
        if (StringUtils.isEmpty(dto.getPassword())) {
            return CommonResult.failed("新密码不能为空");
        }
        if (StringUtils.isEmpty(dto.getUsername())) {
            return CommonResult.failed("用户名不能为空");
        }

        // 验证短信验证码
        boolean isValidCode = smsWriteService.verifySmsCode(dto.getMobile(), dto.getSmsCode());
        if (!isValidCode) {
            return CommonResult.failed("短信验证码错误或已过期");
        }

        // 验证用户是否存在且手机号匹配
        CustomUserDetails userDetails = userReadService.queryUserByUsername(dto.getUsername());
        if (userDetails == null || userDetails.getSysUser() == null) {
            return CommonResult.failed("用户不存在");
        }

        SysUser sysUser = userDetails.getSysUser();
        if (!dto.getMobile().equals(sysUser.getMobile())) {
            return CommonResult.failed("手机号与用户信息不匹配");
        }

        // 加密新密码
        String password = dto.getPassword();
        HashMap map = encryFeignService.encryPwd(password);
        String encryPwd = (String)map.get(CommonFeignResult.DATA_KEY);

        // 更新用户密码
        SysUser updateUser = new SysUser();
        updateUser.setId(sysUser.getId());
        updateUser.setPassword(encryPwd);
        updateUser.setPasswordErrorNum(0);
        updateUser.setIsLock(MedConst.TYPE_0);

        userWriteService.updateUserPwd(updateUser);

        return CommonResult.success("密码重置成功");
    }

}
