package com.jp.med.core.modules.common.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.app.AppMsgSup;
import com.jp.med.common.entity.audit.AuditBatchDetail;
import com.jp.med.common.entity.audit.AuditBatchMultipleDetail;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.entity.audit.AuditRes;
import com.jp.med.common.entity.payload.AuditPayload;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.FeignExecuteUtil;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.common.constant.OSSConst;

import com.jp.med.core.modules.sys.service.write.SysMessageWriteService;
import com.jp.med.common.dto.message.SysMessageDto;
import com.jp.med.common.ienum.SysMessageTypeEnum;
import com.jp.med.core.modules.common.feign.*;
import com.jp.med.core.modules.common.mapper.read.CommonAuditReadMapper;
import com.jp.med.core.modules.common.mapper.write.CommonAuditWriteMapper;
import com.jp.med.core.modules.common.service.write.CommonAuditWriteService;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLSession;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Comparator;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/11 14:55
 * @description:
 */
// @Service
@Slf4j
@Transactional(readOnly = false)
public class OldCommonAuditWriteServiceImpl implements CommonAuditWriteService {

	@Autowired
	private CommonAuditReadMapper commonAuditReadMapper;

	@Autowired
	private CommonAuditWriteMapper commonAuditWriteMapper;

	@Autowired
	private SysMessageWriteService sysMessageWriteService;

	@Autowired
	private EcsAuditFeignService auditFeignService;

	@Autowired
	private HrmAuditFeignService hrmAuditFeignService;

	@Autowired
	private MmisAuditFeignService mmisAuditFeignService;

	@Autowired
	private PurmsAuditFeignService purmsAuditFeignService;

	@Autowired
	private AmsAuditFeignService amsAuditFeignService;

	@Autowired
	private CmsAuditFeignService cmsAuditFeignService;

	@Autowired
	private RmsAuditFeignService rmsAuditFeignService;

	@Override
	@GlobalTransactional
	public void saveAuditDetail(AuditDetail dto) {
		log.info("saveAuditDetail{}", dto.getAuditBchno());
		List<AuditDetail> details = dto.getAuditDetails();
		AppMsgSup appMsgSup = dto.getAppMsgSup();
		AuditPayload auditPayload = dto.getAuditPayload();
		int chkSeq = 1;
		if (CollectionUtil.isNotEmpty(details)) {
			for (AuditDetail auditDetail : details) {
				auditDetail.setChkSeq(chkSeq);
				if (auditDetail.getChkState() != null) {
					auditDetail.setChkState(auditDetail.getChkState());
				} else {
					auditDetail.setChkState(MedConst.ACTIVE_FLAG_0);
				}
				auditDetail.setBchno(dto.getAuditBchno());
				auditDetail.setMessageSup(JSON.toJSONString(appMsgSup));
				auditDetail.setMessagePayload(JSON.toJSONString(auditPayload));
				auditDetail.setRecordTableName(dto.getRecordTableName());
				auditDetail.setResultTableName(dto.getResultTableName());
				chkSeq++;
			}

			BatchUtil.batch("saveAuditDetail", details, CommonAuditWriteMapper.class);

			saveAuditRes(dto.getAuditBchno(), appMsgSup.getAppyer(), dto.getResultTableName());

			pushMessage(details, appMsgSup, auditPayload, dto);
		}
	}

	@Override
	public void notPushSaveAuditDetail(AuditDetail dto, List<AuditDetail> details, String empCode) {
		log.info("notPushSaveAuditDetail{}", dto.getAuditBchno());
		if (CollectionUtil.isNotEmpty(details)) {
			details.forEach(auditDetail -> {
				if (auditDetail.getChkState() != null) {
					auditDetail.setChkState(auditDetail.getChkState());
					auditDetail.setChkSignPath(auditDetail.getChkSignPath());
					log.info("设置已审批信息{}", JSON.toJSONString(auditDetail));
				} else {
					auditDetail.setChkState(MedConst.ACTIVE_FLAG_0);
				}
				auditDetail.setRecordTableName(dto.getRecordTableName());
				auditDetail.setResultTableName(dto.getResultTableName());
				auditDetail.setBchno(dto.getAuditBchno());
			});
			BatchUtil.batch("saveAuditDetail", details, CommonAuditWriteMapper.class);

			saveAuditRes(dto.getAuditBchno(), empCode, dto.getResultTableName());
		}
	}

	private void saveAuditRes(String ulid, String crter, String resultTableName) {
		AuditRes auditRes = new AuditRes();
		auditRes.setBchno(ulid);
		auditRes.setAuditRes(AuditConst.RES_IN);
		auditRes.setCreateTime(DateUtil.getCurrentTime(null));
		auditRes.setCrter(crter);
		auditRes.setResultTableName(resultTableName);
		log.info("写入审核结果表{}", resultTableName);
		// 写入审核结果表
		commonAuditWriteMapper.saveAuditRes(auditRes);
	}

	@Override
	@GlobalTransactional
	public void pushMessage(List<AuditDetail> details, AppMsgSup appMsgSup,
			AuditPayload auditPayload, AuditDetail dto) {
		// 获取所有未审批数据
		List<AuditDetail> notAuditDetails = details.stream()
				.filter(auditDetail -> StringUtils.isEmpty(auditDetail.getChkTime())
						&& MedConst.TYPE_0.equals(auditDetail.getChkState()))
				.collect(Collectors.toList());
		if (CollectionUtil.isNotEmpty(notAuditDetails)) {
			// 中间节点完成，通知下一审批人
			sendNextAuditorMessage(notAuditDetails, dto);

			// 系统调用
			switch (dto.getSys()) {
				case OSSConst.BUCKET_ECS:
					FeignExecuteUtil.execute(auditFeignService.complete(
							new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_SUCCESS)));
					break;
				case OSSConst.BUCKET_HRM:
					FeignExecuteUtil.execute(hrmAuditFeignService.complete(
							new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_SUCCESS)));
					break;
				case OSSConst.BUCKET_MMIS:
					FeignExecuteUtil.execute(mmisAuditFeignService.complete(
							new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_SUCCESS)));
					break;
				case OSSConst.BUCKET_PURMS:
					FeignExecuteUtil.execute(purmsAuditFeignService.complete(
							new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_SUCCESS)));
					break;
				case OSSConst.BUCKET_AMS:
					FeignExecuteUtil.execute(amsAuditFeignService.complete(
							new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_SUCCESS,
									details)));
					break;
				case OSSConst.BUCKET_CMS:
					FeignExecuteUtil.execute(cmsAuditFeignService.complete(
							new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_SUCCESS,
									details)));
					break;
				case OSSConst.BUCKET_RMS:
					FeignExecuteUtil.execute(rmsAuditFeignService.complete(
							new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_SUCCESS,
									details)));
					break;
			}
		} else {
			// 如果详情有数据但无审核数据则表示所有审核已全部成功且完成
			if (CollectionUtil.isNotEmpty(details)) {
				// 审批完成，发送消息通知
				sendAuditCompleteMessage(dto, details);

				// 差旅报销
				String bchno = details.get(0).getBchno();
				// 更新审核结果表
				AuditRes auditRes = new AuditRes();
				auditRes.setBchno(bchno);
				auditRes.setAuditRes(AuditConst.RES_SUCCESS);
				auditRes.setResultTableName(dto.getResultTableName());
				commonAuditWriteMapper.updateAuditRes(auditRes);
				switch (dto.getSys()) {
					case OSSConst.BUCKET_ECS:
						FeignExecuteUtil.execute(auditFeignService.complete(
								new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_COMPLETE)));
						break;
					case OSSConst.BUCKET_HRM:
						FeignExecuteUtil.execute(hrmAuditFeignService.complete(
								new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_COMPLETE)));
						break;
					case OSSConst.BUCKET_MMIS:
						FeignExecuteUtil.execute(mmisAuditFeignService.complete(
								new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_COMPLETE)));
						break;
					case OSSConst.BUCKET_PURMS:
						FeignExecuteUtil.execute(purmsAuditFeignService.complete(
								new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_COMPLETE)));
						break;
					case OSSConst.BUCKET_AMS:
						FeignExecuteUtil.execute(amsAuditFeignService.complete(
								new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_COMPLETE)));
						break;
					case OSSConst.BUCKET_CMS:
						FeignExecuteUtil.execute(cmsAuditFeignService.complete(
								new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_COMPLETE)));
						break;
					case OSSConst.BUCKET_RMS:
						FeignExecuteUtil.execute(rmsAuditFeignService.complete(
								new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_COMPLETE)));
						break;
				}
			}
		}
	}

	@Override
	@GlobalTransactional
	public void updateAuditDetail(AuditDetail dto) {
		if (StringUtils.isEmpty(dto.getChkState())) {
			throw new AppException("更新的审核状态不能为空");
		}
		dto.setSqlAutowiredHospitalCondition(true);
		// 查询是否已经审核过
		Integer num = commonAuditReadMapper.queryAlreadyAudit(dto);
		if (num > 0) {
			throw new AppException("已审核过");
		}
		setSign(dto);
		// 保存签名和附件
		if (dto.getSignFile() != null) {
			String signFilePath = OSSUtil.uploadFile(dto.getSys(), "audit/", dto.getSignFile());
			dto.setChkSignPath(signFilePath);
		}
		if (dto.getAttFiles() != null && CollectionUtil.isNotEmpty(dto.getAttFiles())) {
			StringBuilder attStr = new StringBuilder();
			dto.getAttFiles().forEach(a -> {
				String attFilePath = OSSUtil.uploadFile(dto.getSys(), "audit/", a);
				attStr.append(attFilePath);
				attStr.append(",");
			});
			dto.setChkAttPath(attStr.toString());
		}
		// 移除旧的APP消息更新逻辑，改为使用系统消息
		dto.setActChker(dto.getSysUser().getHrmUser().getEmpCode());
		if (!StringUtils.isEmpty(dto.getSelfSetTime())) {
			dto.setChkTime(dto.getSelfSetTime());
		} else {
			dto.setChkTime(DateUtil.getCurrentTime(null));
		}
		commonAuditWriteMapper.updateAuditDetail(dto);
		// 审核失败不推送消息，为1时才往下推送消息
		if (AuditConst.STATE_SUCCESS.equals(dto.getChkState())) {
			// 查询
			AuditDetail auditDetail = new AuditDetail();
			auditDetail.setAuditBchno(dto.getAuditBchno());
			auditDetail.setChker(dto.getSysUser().getHrmUser().getEmpCode());
			auditDetail.setRecordTableName(dto.getRecordTableName());
			List<AuditDetail> details = commonAuditReadMapper.queryAuditDetail(auditDetail);
			if (CollectionUtil.isEmpty(details)) {
				throw new AppException("您不在审核名单中");
			}
			// 排序
			details.sort(Comparator.comparing(AuditDetail::getChkSeq));
			pushMessage(details, null, null, dto);
		} else {
			// 审批拒绝，发送消息通知
			sendAuditRejectMessage(dto);
			updateAuditRes(dto.getAuditBchno(), AuditConst.RES_FAIL, dto.getResultTableName());
			switch (dto.getSys()) {
				case OSSConst.BUCKET_ECS:
					FeignExecuteUtil.execute(auditFeignService.complete(
							new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_FAIL)));
					break;
				case OSSConst.BUCKET_HRM:
					FeignExecuteUtil.execute(hrmAuditFeignService.complete(
							new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_FAIL)));
					break;
				case OSSConst.BUCKET_MMIS:
					FeignExecuteUtil.execute(mmisAuditFeignService.complete(
							new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_FAIL)));
				case OSSConst.BUCKET_PURMS:
					FeignExecuteUtil.execute(purmsAuditFeignService.complete(
							new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_FAIL)));
					break;
				case OSSConst.BUCKET_AMS:
					FeignExecuteUtil.execute(amsAuditFeignService.complete(
							new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_FAIL)));
					break;
				case OSSConst.BUCKET_CMS:
					FeignExecuteUtil.execute(cmsAuditFeignService.complete(
							new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_FAIL)));
					break;
				case OSSConst.BUCKET_RMS:
					FeignExecuteUtil.execute(rmsAuditFeignService.complete(
							new AuditDetail(dto.getAuditBchno(), AuditConst.STATE_FAIL)));
					break;
			}
		}
	}

	private void updateAuditRes(String bchno, String state, String resultTableName) {
		// 更新审核结果表
		AuditRes auditRes = new AuditRes();
		auditRes.setBchno(bchno);
		auditRes.setAuditRes(state);
		auditRes.setResultTableName(resultTableName);
		commonAuditWriteMapper.updateAuditRes(auditRes);
	}

	/**
	 * 批量修改审核详情（纵向） ： 一条审核流程走通， 同意或不同意走到底
	 *
	 * @param dto
	 */
	@Override
	@GlobalTransactional
	public void batchUpdateAuditDetail(AuditDetail dto) {
		if (CollectionUtil.isEmpty(dto.getBatchDetails())) {
			throw new AppException("批量审核数据为空");
		}
		List<String> bchnoList = new ArrayList<>();
		dto.getBatchDetails().forEach(auditBatchDetail -> {
			if (auditBatchDetail.getId() != null) {
				AuditDetail auditDetail = new AuditDetail();
				auditDetail.setId(auditBatchDetail.getId());
				auditDetail.setActChker(dto.getSysUser().getHrmUser().getEmpCode());
				// auditDetail.setChkTime(DateUtil.getCurrentTime(null));
				auditDetail.setChkState(auditBatchDetail.getState());
				auditDetail.setRecordTableName(dto.getRecordTableName());
				setSign(dto);
				// 保存签名和附件
				if (dto.getSignFile() != null) {
					String signFilePath = OSSUtil.uploadFile(dto.getSys(), "audit/",
							dto.getSignFile());
					auditDetail.setChkSignPath(signFilePath);
				}
				commonAuditWriteMapper.updateAuditDetail(auditDetail);

				if (AuditConst.STATE_FAIL.equals(auditBatchDetail.getState())) {
					updateAuditRes(auditBatchDetail.getAuditBchno(), AuditConst.RES_FAIL,
							dto.getResultTableName());
				} else {
					bchnoList.add(auditBatchDetail.getAuditBchno());
				}
			}
		});
		if (CollectionUtil.isNotEmpty(bchnoList)) {
			// 查询是否审核完成
			List<AuditRes> auditRes = commonAuditReadMapper.queryAuditComplete(bchnoList,
					dto.getRecordTableName());
			auditRes.forEach(a -> {
				if (AuditConst.STATE_SUCCESS.equals(a.getAuditRes())) {
					updateAuditRes(a.getBchno(), AuditConst.STATE_SUCCESS,
							dto.getResultTableName());
				}
			});
		}
		// 如果全部都是失败的，审核成功都改成审核失败
		boolean allFail = true;
		for (AuditBatchDetail batchDetail : dto.getBatchDetails()) {
			if (AuditConst.STATE_SUCCESS.equals(batchDetail.getState())) {
				allFail = false;
			}
		}
		if (allFail) {
			dto.setChkState(AuditConst.STATE_FAIL);
		}
		updateAuditDetail(dto);
	}

	/**
	 * 批量修改审核详情（横向处理模式）
	 * <p>
	 * 功能说明：同时处理多条审核流程中当前选中节点的状态
	 * 目前主要用于MMIS系统的审核需求
	 * <p>
	 * 必要参数说明：
	 * - chkState：审核状态（必填）
	 * - messageId：APP消息ID（必填）
	 * - sys：系统标识（如'mmis'，必填）
	 * - batchMultipleDetails：审核明细列表，包含：
	 * - id：审核记录ID
	 * - auditBchno：审核批次号
	 * - chkSeq：当前节点序号
	 * <p>
	 * 可选参数：
	 * - chkRemarks：审核备注
	 * - signFile：签名文件
	 * - attFiles：附件列表
	 * <p>
	 * 特殊处理逻辑：
	 * 当审核状态为不通过(0)时，相关节点将全部标记为不通过
	 * 无需检查是否为最终节点或审批是否已完成
	 *
	 * @param dto 审核详情数据传输对象
	 */
	@Override
	public void batchUpdateAuditDetailMulti(AuditDetail dto) {
		if (CollectionUtil.isEmpty(dto.getBatchMultipleDetails())) {
			throw new AppException("批量审核数据为空");
		}
		List<String> bchnoList = new ArrayList<>();
		dto.getBatchMultipleDetails().forEach(batchMultipleDetail -> {
			if (batchMultipleDetail.getId() != null) {
				AuditDetail auditDetail = new AuditDetail();
				auditDetail.setId(batchMultipleDetail.getId());
				auditDetail.setActChker(dto.getSysUser().getHrmUser().getEmpCode());
				// auditDetail.setChkTime(DateUtil.getCurrentTime(null));
				// 审核状态都是一样的
				auditDetail.setChkState(dto.getChkState());
				auditDetail.setChkSeq(batchMultipleDetail.getChkSeq());
				auditDetail.setRecordTableName(dto.getRecordTableName());
				// 设置签名与附件
				setSign(dto);
				if (dto.getSignFile() != null) {
					String signFilePath = OSSUtil.uploadFile(dto.getSys(), "audit/",
							dto.getSignFile());
					auditDetail.setChkSignPath(signFilePath);
				}
				commonAuditWriteMapper.updateAuditDetail(auditDetail);
				if (AuditConst.STATE_FAIL.equals(dto.getChkState())) {
					updateAuditRes(batchMultipleDetail.getAuditBchno(), AuditConst.RES_FAIL,
							dto.getResultTableName());
				} else {
					bchnoList.add(batchMultipleDetail.getAuditBchno());
				}
			}

		});
		if (CollectionUtil.isNotEmpty(bchnoList)) {
			// 查询是否审核完成
			List<AuditRes> auditRes = commonAuditReadMapper.queryAuditComplete(bchnoList,
					dto.getRecordTableName());
			auditRes.forEach(a -> {
				if (AuditConst.STATE_SUCCESS.equals(a.getAuditRes())) {
					updateAuditRes(a.getBchno(), AuditConst.STATE_SUCCESS,
							dto.getResultTableName());
				}
			});
		}
		// 如果审核状态为成功，则不是全部失败
		boolean allFail = !AuditConst.STATE_SUCCESS.equals(dto.getChkState());
		if (allFail) {
			dto.setChkState(AuditConst.STATE_FAIL);
		}
		updateAuditDetail(dto);
	}

	@Override
	public void updateParentBecho(AuditDetail dto) {
		commonAuditWriteMapper.updateParentBecho(dto.getAuditBchno(), dto.getAuditBchnoChildren(),
				dto.getRecordTableName());
	}

	/**
	 * 设置审核流程的审核人信息
	 *
	 * @param dto
	 */
	@Override
	public void setAuditorInProcess(AuditDetail dto) {
		commonAuditWriteMapper.setAuditorInProcess(dto);
	}

	// 定制Verifier
	public static class TrustAnyHostnameVerifier implements HostnameVerifier {

		public boolean verify(String hostname, SSLSession session) {
			return true;
		}
	}

	/**
	 * 设置签名信息
	 * <p>
	 * 根据签名URL获取签名图片，并将其转换为MultipartFile对象
	 * 如果URL包含特定域名，会进行替换
	 * 支持http和https协议
	 *
	 * @param dto 审核详情对象，包含签名URL和系统用户信息
	 */
	private void setSign(AuditDetail dto) {
		if (StringUtils.isNotEmpty(dto.getSignUrl())) {
			try {
				if (dto.getSignUrl().contains("hrp.zjxrmyy.cn:18090")) {
					dto.setSignUrl(
							dto.getSignUrl().replace("hrp.zjxrmyy.cn:18090", "***********:8090"));
				}
				URL url = new URL(dto.getSignUrl());
				InputStream in = null;
				if (dto.getSignUrl().startsWith("https")) {
					HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();

					// 新增部分
					conn.setHostnameVerifier(new cn.hutool.http.ssl.TrustAnyHostnameVerifier());

					conn.connect();

					in = conn.getInputStream();
				} else {
					in = url.openStream();
				}

				byte[] bytes = IOUtils.toByteArray(in);
				in.close();

				MultipartFile multipartFile = new MultipartFile() {
					@Override
					public String getName() {
						return dto.getSysUser().getNickname() + ".png";
					}

					@Override
					public String getOriginalFilename() {
						return dto.getSysUser().getNickname() + ".png";
					}

					@Override
					public String getContentType() {
						return "image/png";
					}

					@Override
					public boolean isEmpty() {
						return false;
					}

					@Override
					public long getSize() {
						return bytes.length;
					}

					@Override
					public byte[] getBytes() throws IOException {
						return bytes;
					}

					@Override
					public InputStream getInputStream() throws IOException {
						return new ByteArrayInputStream(bytes);
					}

					@Override
					public Resource getResource() {
						return MultipartFile.super.getResource();
					}

					@Override
					public void transferTo(File file) throws IOException, IllegalStateException {

					}

					@Override
					public void transferTo(Path dest) throws IOException, IllegalStateException {
						MultipartFile.super.transferTo(dest);
					}
				};
				dto.setSignFile(multipartFile);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * 发送审批拒绝消息通知
	 *
	 * @param dto 审核详情
	 */
	private void sendAuditRejectMessage(AuditDetail dto) {
		try {
			// 获取审核信息
			String messageSup = dto.getMessageSup();
			AppMsgSup appMsgSup = null;
			if (!StringUtils.isEmpty(messageSup)) {
				appMsgSup = JSON.parseObject(messageSup, AppMsgSup.class);
			}

			if (appMsgSup != null) {
				// 构建系统消息
				SysMessageDto sysMessageDto = new SysMessageDto();
				sysMessageDto.setUsername(dto.getSysUser().getHrmUser().getEmpCode());

				// 格式化消息标题和内容
				String auditorName = dto.getSysUser().getHrmUser().getEmpName();
				String taskName = appMsgSup.getTitle();
				String currentTime = DateUtil.getCurrentTime(null);

				sysMessageDto.setTitle("审批拒绝通知");
				sysMessageDto.setPushText(String.format("您提交的【%s】已被【%s】拒绝，时间：%s",
						taskName, auditorName, currentTime));
				sysMessageDto.setUsers(new String[] { appMsgSup.getAppyer() });
				sysMessageDto.setType(SysMessageTypeEnum.BPM_NOT_PASS.getCode());
				sysMessageDto.setGotoUrl(dto.getRoutePath());

				// 发送消息
				sysMessageWriteService.addMessage(sysMessageDto);
			}
		} catch (Exception e) {
			log.error("发送审批拒绝消息失败：" + e.getMessage(), e);
		}
	}

	/**
	 * 发送审批完成消息通知
	 *
	 * @param dto     审核详情
	 * @param details 审核详情列表
	 */
	private void sendAuditCompleteMessage(AuditDetail dto, List<AuditDetail> details) {
		try {
			if (CollectionUtil.isNotEmpty(details)) {
				AuditDetail firstDetail = details.get(0);
				String messageSup = firstDetail.getMessageSup();
				AppMsgSup appMsgSup = null;
				if (!StringUtils.isEmpty(messageSup)) {
					appMsgSup = JSON.parseObject(messageSup, AppMsgSup.class);
				}

				if (appMsgSup != null) {
					// 构建系统消息
					SysMessageDto sysMessageDto = new SysMessageDto();
					sysMessageDto.setUsername("system");

					// 格式化消息标题和内容
					String taskName = appMsgSup.getTitle();
					String currentTime = DateUtil.getCurrentTime(null);

					sysMessageDto.setTitle("审批通过通知");
					sysMessageDto.setPushText(String.format("您提交的【%s】已审批通过，时间：%s",
							taskName, currentTime));
					sysMessageDto.setUsers(new String[] { appMsgSup.getAppyer() });
					sysMessageDto.setType(SysMessageTypeEnum.BPM_PASS.getCode());
					sysMessageDto.setGotoUrl(dto.getRoutePath());

					// 发送消息
					sysMessageWriteService.addMessage(sysMessageDto);
				}
			}
		} catch (Exception e) {
			log.error("发送审批完成消息失败：" + e.getMessage(), e);
		}
	}

	/**
	 * 发送下一审批人消息通知
	 *
	 * @param notAuditDetails 未审批的详情列表
	 * @param dto             当前审核详情
	 */
	private void sendNextAuditorMessage(List<AuditDetail> notAuditDetails, AuditDetail dto) {
		try {
			if (CollectionUtil.isNotEmpty(notAuditDetails)) {
				// 排序获取下一个审批人
				notAuditDetails.sort(Comparator.comparing(AuditDetail::getChkSeq));
				AuditDetail nextAuditDetail = notAuditDetails.get(0);

				// 获取审核信息
				String messageSup = nextAuditDetail.getMessageSup();
				AppMsgSup appMsgSup = null;
				if (!StringUtils.isEmpty(messageSup)) {
					appMsgSup = JSON.parseObject(messageSup, AppMsgSup.class);
				}

				if (appMsgSup != null) {
					// 构建系统消息
					SysMessageDto sysMessageDto = new SysMessageDto();
					sysMessageDto.setUsername("system");

					// 格式化消息标题和内容
					String taskName = appMsgSup.getTitle();
					String currentTime = DateUtil.getCurrentTime(null);
					String submitterName = appMsgSup.getAppyerName();

					sysMessageDto.setTitle("待审批通知");
					sysMessageDto.setPushText(String.format("【%s】提交的【%s】等待您审批，时间：%s",
							submitterName, taskName, currentTime));
					sysMessageDto.setUsers(new String[] { nextAuditDetail.getChker() });
					sysMessageDto.setType(SysMessageTypeEnum.BPM_PASS.getCode());
					sysMessageDto.setGotoUrl(dto.getRoutePath());

					// 发送消息
					sysMessageWriteService.addMessage(sysMessageDto);
				}
			}
		} catch (Exception e) {
			log.error("发送下一审批人消息失败：" + e.getMessage(), e);
		}
	}
}
