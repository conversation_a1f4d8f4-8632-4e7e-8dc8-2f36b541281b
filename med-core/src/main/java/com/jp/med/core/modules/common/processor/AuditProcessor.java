package com.jp.med.core.modules.common.processor;

import com.jp.med.core.modules.common.context.AuditContext;

/**
 * 审批处理器接口
 * 使用责任链模式处理审批流程中的各个环节
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface AuditProcessor {

    /**
     * 设置下一个处理器
     * 
     * @param nextProcessor 下一个处理器
     */
    void setNext(AuditProcessor nextProcessor);

    /**
     * 处理审批逻辑
     * 
     * @param context 审批上下文
     */
    void process(AuditContext context);

    /**
     * 获取处理器优先级
     * 数字越小优先级越高
     * 
     * @return 优先级
     */
    int getPriority();

    /**
     * 是否可以处理当前上下文
     * 
     * @param context 审批上下文
     * @return 是否可以处理
     */
    boolean canProcess(AuditContext context);
}
