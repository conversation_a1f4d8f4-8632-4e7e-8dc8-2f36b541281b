package com.jp.med.core.modules.common.enums;

/**
 * 审批状态枚举
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public enum AuditStatusEnum {

    /**
     * 审批通过
     */
    APPROVED("1", "审批通过"),

    /**
     * 审批拒绝
     */
    REJECTED("0", "审批拒绝"),

    /**
     * 审批完成
     */
    COMPLETED("2", "审批完成"),

    /**
     * 审批中
     */
    IN_PROGRESS("3", "审批中");

    private final String code;
    private final String description;

    AuditStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static AuditStatusEnum fromCode(String code) {
        for (AuditStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的审批状态代码: " + code);
    }

    /**
     * 是否为成功状态
     */
    public boolean isSuccess() {
        return this == APPROVED || this == COMPLETED;
    }

    /**
     * 是否为失败状态
     */
    public boolean isFailure() {
        return this == REJECTED;
    }
}
