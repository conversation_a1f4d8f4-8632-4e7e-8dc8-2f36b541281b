package com.jp.med.core.modules.common.processor;

import com.jp.med.core.modules.common.context.AuditContext;
import lombok.extern.slf4j.Slf4j;

/**
 * 抽象审批处理器
 * 实现责任链的基本逻辑
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
public abstract class AbstractAuditProcessor implements AuditProcessor {

    protected AuditProcessor nextProcessor;

    @Override
    public void setNext(AuditProcessor nextProcessor) {
        this.nextProcessor = nextProcessor;
    }

    @Override
    public void process(AuditContext context) {
        try {
            // 如果当前处理器可以处理，则执行处理逻辑
            if (canProcess(context)) {
                log.info("处理器 {} 开始处理审批，批次号：{}",
                        this.getClass().getSimpleName(), context.getAuditBatchNo());

                doProcess(context);

                log.info("处理器 {} 处理完成，批次号：{}",
                        this.getClass().getSimpleName(), context.getAuditBatchNo());
            }

            // 继续传递给下一个处理器
            if (nextProcessor != null) {
                nextProcessor.process(context);
            }

        } catch (Exception e) {
            log.error("处理器 {} 处理失败，批次号：{}，错误：{}",
                    this.getClass().getSimpleName(), context.getAuditBatchNo(), e.getMessage(), e);

            // 处理异常，可以选择继续传递或中断链条
            handleException(context, e);
        }
    }

    /**
     * 具体的处理逻辑，由子类实现
     * 
     * @param context 审批上下文
     */
    protected abstract void doProcess(AuditContext context);

    /**
     * 处理异常
     * 
     * @param context 审批上下文
     * @param e       异常
     */
    protected void handleException(AuditContext context, Exception e) {
        // 默认实现：记录日志并继续传递
        log.warn("处理器 {} 发生异常，继续传递给下一个处理器", this.getClass().getSimpleName());

        if (nextProcessor != null) {
            nextProcessor.process(context);
        }
    }

    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    protected String getProcessorName() {
        return this.getClass().getSimpleName();
    }
}
