package com.jp.med.core.modules.common.strategy.impl;

import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.util.FeignExecuteUtil;
import com.jp.med.core.modules.common.context.AuditContext;
import com.jp.med.core.modules.common.feign.RmsAuditFeignService;
import com.jp.med.core.modules.common.strategy.SystemAuditStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * RMS系统审批策略实现
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Component
@Slf4j
public class RmsAuditStrategy implements SystemAuditStrategy {

    @Autowired
    private RmsAuditFeignService rmsAuditFeignService;

    @Override
    public String getSupportedSystemCode() {
        return OSSConst.BUCKET_RMS;
    }

    @Override
    public void handleAuditSuccess(AuditContext context) {
        log.info("RMS系统处理审批成功逻辑，批次号：{}", context.getAuditBatchNo());

        AuditDetail auditDetail = new AuditDetail(
                context.getAuditBatchNo(),
                AuditConst.STATE_SUCCESS,
                context.getAuditDetailList());

        FeignExecuteUtil.execute(rmsAuditFeignService.complete(auditDetail));
    }

    @Override
    public void handleAuditFailure(AuditContext context) {
        log.info("RMS系统处理审批失败逻辑，批次号：{}", context.getAuditBatchNo());

        AuditDetail auditDetail = new AuditDetail(
                context.getAuditBatchNo(),
                AuditConst.STATE_FAIL);

        FeignExecuteUtil.execute(rmsAuditFeignService.complete(auditDetail));
    }

    @Override
    public void handleAuditComplete(AuditContext context) {
        log.info("RMS系统处理审批完成逻辑，批次号：{}", context.getAuditBatchNo());

        AuditDetail auditDetail = new AuditDetail(
                context.getAuditBatchNo(),
                AuditConst.STATE_COMPLETE);

        FeignExecuteUtil.execute(rmsAuditFeignService.complete(auditDetail));
    }
}
